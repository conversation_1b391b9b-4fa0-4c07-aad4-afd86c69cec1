// noinspection SpellCheckingInspection

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        myTheme: {
          primary: "#0F172A",
          secondary: "#1E293B",
          accent: "#334155",
          light: "#F8FAFC",
          lightbg: "#F1F5F9",
          dark: "#020617",
          white: "#FFFFFF",
          lightTextBody: "#334155",
          darkTextBody: "#F8FAFC",
          lightTextHeading: "#0F172A",
          darkTextHeading: "#FFFFFF",
        },
      },
      fontSize: {
        // Mobile-first typography
        xs: ["0.75rem", { lineHeight: "1rem" }],
        sm: ["0.875rem", { lineHeight: "1.25rem" }],
        base: ["1rem", { lineHeight: "1.5rem", letterSpacing: "-0.01em" }],
        lg: ["1.125rem", { lineHeight: "1.75rem", letterSpacing: "-0.01em" }],
        xl: ["1.25rem", { lineHeight: "1.75rem", letterSpacing: "-0.01em" }],
        "2xl": ["1.5rem", { lineHeight: "2rem", letterSpacing: "-0.02em" }],
        // Larger screens
        "md:base": ["1.125rem", { lineHeight: "1.75rem" }],
        "md:lg": ["1.25rem", { lineHeight: "2rem" }],
        "md:xl": ["1.5rem", { lineHeight: "2rem" }],
        "md:2xl": ["1.875rem", { lineHeight: "2.25rem" }],
      },
      spacing: {
        // Mobile-friendly spacing
        "safe-top": "env(safe-area-inset-top)",
        "safe-bottom": "env(safe-area-inset-bottom)",
        touch: "0.75rem", // Minimum touch target size
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        shimmer: {
          "0%": { backgroundPosition: "200% 0" },
          "100%": { backgroundPosition: "-200% 0" },
        },
        // Removed GPU-intensive animations
        'fadeIn': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        shimmer: "shimmer 2s infinite linear",
        // Removed GPU-intensive animations
        'fadeIn': 'fadeIn 0.3s ease-out forwards',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
