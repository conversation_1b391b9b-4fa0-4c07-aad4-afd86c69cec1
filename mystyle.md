# Modern UI Styling Guide

## Container Layouts
```css
/* Main container - centered with max width */
container mx-auto p-6 max-w-4xl

/* Flex column container with gap */
flex flex-col gap-4

/* Grid container for stats - responsive */
grid grid-cols-1 sm:grid-cols-3 gap-3
```

## Card Styles
```css
/* Base card style */
bg-white rounded-xl border border-gray-100 p-4 hover:shadow-md transition-all

/* Stats card with flex layout */
bg-white rounded-xl border border-gray-100 p-4 flex items-center justify-between
```

## Typography
```css
/* Main heading */
text-3xl font-bold mb-6

/* Card title */
font-medium text-gray-900

/* Subtitle/Description text */
text-sm text-gray-600 mb-2

/* Small text (timestamps, metadata) */
text-xs text-gray-500
```

## Status Indicators
```css
/* Status badge (e.g., Read status) */
bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full
```

## Interactive Elements
```css
/* Tab container */
w-full grid grid-cols-2 bg-gray-100/80 p-1 rounded-lg

/* Tab button */
rounded-md py-2 px-4 data-[state=active]:bg-white data-[state=active]:text-myTheme-primary data-[state=active]:shadow-sm transition-all

/* Link with icon */
flex items-center gap-1 text-myTheme-primary hover:text-myTheme-primary/80 transition-colors
```

## Icons
```css
/* Primary icon (large) */
w-5 h-5 text-myTheme-primary

/* Small icon (in links) */
w-4 h-4
```

## Spacing
```css
/* Standard margin bottom */
mb-2 mb-4 mb-6

/* Padding variations */
p-4 p-6
px-2 py-1
px-4 py-2
```

## Responsive Design
```css
/* Grid responsiveness */
grid-cols-1 sm:grid-cols-3

/* Padding responsiveness */
p-3 sm:p-6
```

## Transitions & Animations
```css
/* Hover and state transitions */
transition-all
transition-colors

/* Hover effects */
hover:shadow-md
hover:text-myTheme-primary/80
```

## Theme Colors
```css
/* Primary theme color usage */
text-myTheme-primary
hover:text-myTheme-primary/80

/* Text colors */
text-gray-900  /* Main text */
text-gray-600  /* Secondary text */
text-gray-500  /* Tertiary text */
```

## Common Patterns

### Stats Card Pattern
```css
/* Container */
stat bg-white rounded-xl border border-gray-100 p-4 flex items-center justify-between

/* Content */
<div>
  <div className="text-sm text-gray-500">Label</div>
  <div className="text-2xl font-bold text-gray-900">Value</div>
</div>
<Icon className="w-5 h-5 text-myTheme-primary" />
```

### Notification Card Pattern
```css
/* Container */
bg-white rounded-lg border border-gray-100 p-4 hover:shadow-md transition-all

/* Header */
<div className="flex items-center justify-between mb-2">
  <h3 className="font-medium text-gray-900">Title</h3>
  <span className="bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full">Status</span>
</div>

/* Content */
<p className="text-sm text-gray-600 mb-2">Content</p>

/* Footer */
<div className="flex items-center justify-between text-xs text-gray-500">
  <span>Timestamp</span>
  <Link className="flex items-center gap-1 text-myTheme-primary hover:text-myTheme-primary/80 transition-colors">
    <Icon className="w-4 h-4" />
    Action
  </Link>
</div>
```

## Usage Instructions

1. Start with the outermost container using the container layout styles
2. Apply card styles for individual sections or items
3. Use typography styles consistently for different text elements
4. Add interactive elements with proper hover states and transitions
5. Implement responsive design using the sm: breakpoint where needed
6. Use theme colors for maintaining consistency
7. Follow the common patterns for similar components

Note: All styles use Tailwind CSS classes. The `text-myTheme-primary` color should be defined in your Tailwind config. 