# Design Document

## Overview

This design outlines the implementation approach for enhancing the notification system in the navbar. The current system displays notifications in a dropdown but lacks functionality for navigating to notification targets and marking notifications as read. The solution will implement these missing features by integrating with the backend API endpoints and handling real-time updates through Server-Sent Events (SSE).

## Architecture

### High-Level Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as Frontend UI
    participant API as Backend API
    participant SSE as SSE Connection
    
    User->>UI: Opens notification dropdown
    UI->>API: Fetches notifications
    API->>UI: Returns notification data
    
    alt Mark Single Notification as Read
        User->>UI: Clicks "Mark as Read" on notification
        UI->>API: PUT /notifications/{id}/read
        API->>UI: Returns success response
        API->>SSE: Broadcasts notification_read event
        SSE->>UI: Receives notification_read event
        UI->>UI: Updates notification UI
    else Mark All Notifications as Read
        User->>UI: Clicks "Mark All as Read"
        UI->>API: PUT /notifications/read-all
        API->>UI: Returns success response
        API->>SSE: Broadcasts notification_read events
        SSE->>UI: Receives notification_read events
        UI->>UI: Updates all notifications UI
    end
    
    User->>UI: Clicks on notification
    UI->>UI: Navigates to target content
    UI->>API: PUT /notifications/{id}/read (auto-mark as read)
    API->>UI: Returns success response
    API->>SSE: Broadcasts notification_read event
```

### Component Architecture

```mermaid
graph TD
    A[Navbar] --> B[NotificationDropdown]
    B --> C[NotificationList]
    C --> D[NotificationItem]
    D --> E[MarkAsReadButton]
    B --> F[MarkAllAsReadButton]
    G[useSSENotifications Hook] --> B
    H[notificationService] --> B
    H --> D
```

## Components and Interfaces

### 1. Enhanced NotificationDropdown Component

The `NotificationDropdown` component will be enhanced to handle both navigation and mark-as-read functionality:

```typescript
interface NotificationDropdownProps {
  userId: string;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ userId }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Fetch notifications
  // Handle mark as read
  // Handle mark all as read
  // Listen for SSE updates
};
```

### 2. Enhanced NotificationItem Component

The `NotificationItem` component will be updated to handle navigation and individual mark-as-read:

```typescript
interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string, type: NotificationType) => Promise<void>;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onMarkAsRead }) => {
  const router = useRouter();
  
  const handleClick = () => {
    // Navigate to target content based on notification type and data
    navigateToNotificationTarget(notification);
    // Auto-mark as read when clicked
    onMarkAsRead(notification.id, notification.type);
  };
  
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent navigation
    onMarkAsRead(notification.id, notification.type);
  };
  
  return (
    <div 
      className={`notification-item ${notification.read ? 'read' : 'unread'}`}
      onClick={handleClick}
    >
      {/* Notification content */}
      <button onClick={handleMarkAsRead}>Mark as read</button>
    </div>
  );
};
```

### 3. Notification Service

A dedicated service will handle API interactions for notifications:

```typescript
// notificationService.ts
export const markAsRead = async (notificationId: string, userId: string, type: NotificationType): Promise<ApiResponse> => {
  try {
    const response = await fetch(`/notifications/${notificationId}/read?user_id=${userId}&type=${type}`, {
      method: 'PUT',
    });
    return await response.json();
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

export const markAllAsRead = async (userId: string, type?: NotificationType): Promise<ApiResponse> => {
  try {
    const url = type 
      ? `/notifications/read-all?user_id=${userId}&type=${type}`
      : `/notifications/read-all?user_id=${userId}`;
    
    const response = await fetch(url, {
      method: 'PUT',
    });
    return await response.json();
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

export const navigateToNotificationTarget = (notification: Notification): void => {
  // Logic to determine target URL based on notification type and data
};
```

### 4. Enhanced SSE Integration

The existing SSE hook will be enhanced to handle notification_read events:

```typescript
// useSSENotifications.ts
export const useSSENotifications = (userId: string) => {
  // Existing SSE connection logic
  
  useEffect(() => {
    if (!eventSource) return;
    
    eventSource.addEventListener('message', (event) => {
      const data = JSON.parse(event.data);
      
      // Handle notification_read events
      if (data.event === 'notification_read') {
        // Update UI for read notification
        updateNotificationReadStatus(data.notification.notification_id);
      }
      
      // Handle other notification events
    });
    
    return () => {
      eventSource.close();
    };
  }, [eventSource, userId]);
  
  // Return methods and state
};
```

## Data Models

### Notification Interface

```typescript
interface Notification {
  id: string;
  type: NotificationType; // 'user' | 'owner' | 'like' | 'system'
  message: string;
  read: boolean;
  createdAt: string;
  targetType: 'review' | 'comment' | 'product' | 'system';
  targetId: string;
  additionalData?: {
    reviewId?: string;
    productId?: string;
    commentId?: string;
    userId?: string;
    [key: string]: any;
  };
}

type NotificationType = 'user' | 'owner' | 'like' | 'system';
```

### API Response Interfaces

```typescript
interface MarkAsReadResponse {
  message: string;
  notification_id: string;
  success: boolean;
}

interface MarkAllAsReadResponse {
  message: string;
  user_notifications_updated: number;
  owner_notifications_updated: number;
  like_notifications_updated: number;
  system_notifications_updated: number;
  total_updated: number;
  success: boolean;
}

interface ApiError {
  success: false;
  error: string;
  message: string;
  status: number;
}

type ApiResponse = MarkAsReadResponse | MarkAllAsReadResponse | ApiError;
```

## Navigation Logic

### Target URL Generation

The system will determine the appropriate URL based on notification type and target:

```typescript
const getNotificationTargetUrl = (notification: Notification): string => {
  const { targetType, targetId, additionalData } = notification;
  
  switch (targetType) {
    case 'review':
      return `/reviews/${targetId}`;
      
    case 'comment':
      // Navigate to the specific comment within its parent review
      const reviewId = additionalData?.reviewId || '';
      return `/reviews/${reviewId}?commentId=${targetId}`;
      
    case 'product':
      return `/products/${targetId}`;
      
    case 'system':
      // System notifications might have custom URLs or default to a dashboard
      return additionalData?.url || '/dashboard';
      
    default:
      return '/';
  }
};
```

### Auto-scroll to Comment

For comment notifications, the system will auto-scroll to the specific comment:

```typescript
const navigateToComment = (reviewId: string, commentId: string) => {
  router.push(`/reviews/${reviewId}?commentId=${commentId}`);
  
  // After navigation, scroll to comment (handled by page component)
};
```

## Error Handling

### API Error Handling

```typescript
const handleApiError = (error: any, action: string): void => {
  console.error(`Error ${action}:`, error);
  
  // Determine error type and message
  let errorMessage = 'An error occurred. Please try again.';
  
  if (error.status === 404) {
    errorMessage = 'Notification not found. It may have been deleted.';
  } else if (error.status === 400) {
    errorMessage = 'Invalid request. Please try again.';
  } else if (error.status === 403) {
    errorMessage = 'You do not have permission to perform this action.';
  }
  
  // Display error toast
  toast.error(errorMessage);
};
```

### SSE Connection Error Handling

```typescript
const handleSSEConnectionError = () => {
  console.warn('SSE connection lost. Notifications may not update in real-time.');
  
  // Attempt to reconnect after delay
  setTimeout(() => {
    initializeSSEConnection();
  }, 5000);
};
```

## UI States

### Notification Item States

1. **Unread**: Highlighted background, bold text, unread indicator
2. **Read**: Standard background, regular text
3. **Loading**: Disabled interaction, loading spinner on mark as read button
4. **Error**: Error indicator, retry option

### Dropdown States

1. **Empty**: Message indicating no notifications
2. **Loading**: Loading spinner while fetching notifications
3. **Error**: Error message with retry option
4. **Populated**: List of notification items with scroll for overflow

## Testing Strategy

### Unit Tests

1. **Component Tests**
   - Test NotificationDropdown rendering with different notification states
   - Test NotificationItem click behavior and mark as read functionality
   - Test navigation URL generation for different notification types

2. **Service Tests**
   - Test API interaction functions with mocked responses
   - Test error handling for different API error scenarios
   - Test SSE event handling for notification_read events

### Integration Tests

1. **End-to-End Flow Tests**
   - Test complete notification flow from receiving to marking as read
   - Test navigation to different content types
   - Test real-time updates via SSE

2. **Error Scenario Tests**
   - Test behavior when API calls fail
   - Test UI recovery when SSE connection is lost and restored

## Performance Considerations

1. **Optimistic UI Updates**
   - Update UI immediately on user action before API response
   - Revert changes if API call fails

2. **Notification Batching**
   - Batch API calls when marking multiple notifications as read
   - Debounce rapid user interactions

3. **SSE Efficiency**
   - Filter SSE events on the client to process only relevant notifications
   - Handle reconnection with exponential backoff

## Accessibility Considerations

1. **Keyboard Navigation**
   - Ensure dropdown and notifications are keyboard navigable
   - Implement proper focus management

2. **Screen Reader Support**
   - Add appropriate ARIA attributes
   - Ensure notification status changes are announced

3. **Visual Indicators**
   - Use both color and icons to indicate read/unread status
   - Ensure sufficient contrast for all notification states