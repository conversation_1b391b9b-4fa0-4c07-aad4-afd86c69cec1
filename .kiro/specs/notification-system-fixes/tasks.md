# Implementation Plan

- [x] 1. Create notification service utilities
  - Create a dedicated service file for notification API interactions
  - Implement markAsRead function for single notifications
  - Implement markAllAsRead function for batch operations
  - Add helper functions for notification target URL generation
  - _Requirements: 2.3, 3.2, 5.2, 5.3_

- [ ] 2. Enhance notification data model
  - [x] 2.1 Update notification interfaces
    - Define comprehensive Notification interface with all required fields
    - Add NotificationType enum/type for proper type checking
    - Create response interfaces for API interactions
    - Ensure interfaces support all notification types (user, owner, like, system)
    - _Requirements: 5.1, 5.3_

  - [x] 2.2 Implement notification target URL generation
    - Create utility function to generate target URLs based on notification type
    - Add support for different content types (reviews, comments, products)
    - Implement special handling for comment notifications to support auto-scrolling
    - Add fallback URLs for edge cases
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 5.2_

- [ ] 3. Update NotificationDropdown component
  - [x] 3.1 Enhance dropdown state management
    - Add state for tracking unread count
    - Implement loading and error states
    - Ensure dropdown closes after navigation
    - _Requirements: 1.5, 2.4, 3.3_

  - [x] 3.2 Add mark all as read functionality
    - Add "Mark all as read" button to dropdown header
    - Implement click handler that calls markAllAsRead service
    - Add loading state during API call
    - Update notification list and count on success
    - Display error message on failure
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 4. Enhance NotificationItem component
  - [x] 4.1 Implement navigation functionality
    - Add click handler to navigate to notification target
    - Use router to navigate to generated target URL
    - Auto-mark notification as read when clicked
    - Close dropdown after navigation
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

  - [x] 4.2 Add mark as read button
    - Add individual mark as read button to each notification
    - Implement click handler that calls markAsRead service
    - Prevent event propagation to avoid triggering navigation
    - Update notification state on success
    - Display error message on failure
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

  - [x] 4.3 Enhance visual styling
    - Add distinct styling for read vs unread notifications
    - Implement loading state for mark as read operation
    - Add appropriate icons for different notification types
    - Ensure proper accessibility attributes
    - _Requirements: 2.2, 5.5_

- [ ] 5. Implement SSE integration for real-time updates
  - [x] 5.1 Enhance useSSENotifications hook
    - Add handler for notification_read events
    - Implement notification state updates based on SSE events
    - Add reconnection logic for dropped connections
    - Filter events to only process relevant notifications
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

  - [x] 5.2 Add notification count synchronization
    - Update unread count when notifications are marked as read
    - Recalculate count on SSE events
    - Ensure count is consistent across UI components
    - _Requirements: 2.4, 3.3, 4.3_

- [ ] 6. Implement error handling and recovery
  - [x] 6.1 Add comprehensive error handling
    - Implement error handling for API calls
    - Add user-friendly error messages
    - Create retry mechanisms for failed operations
    - Log errors for debugging
    - _Requirements: 2.5, 3.5_

  - [x] 6.2 Add fallback behavior
    - Implement graceful degradation when API is unavailable
    - Add offline detection and recovery
    - Ensure UI remains usable during connection issues
    - _Requirements: 4.4_

- [x] 7. Implement notification type-specific handling
  - Add specialized handling for different notification types
  - Implement appropriate icons and styling per notification type
  - Ensure correct API parameters for each notification type
  - Add type-specific navigation logic
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 8. Add accessibility enhancements
  - Ensure keyboard navigation for notification dropdown
  - Add proper ARIA attributes for screen readers
  - Implement focus management
  - Add appropriate contrast for visual indicators
  - _Requirements: 2.2, 5.5_

- [x] 9. Optimize performance
  - Implement optimistic UI updates
  - Add debouncing for rapid interactions
  - Optimize SSE event processing
  - Batch API calls when possible
  - _Requirements: 2.4, 3.3, 4.3_

- [ ] 10. Final integration and testing
  - [x] 10.1 Integrate with existing notification components
    - Ensure compatibility with existing notification system
    - Update any dependent components
    - _Requirements: 5.1, 5.4, 5.5_