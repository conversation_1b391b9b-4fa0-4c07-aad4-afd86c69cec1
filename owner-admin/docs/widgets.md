# Widget Domain Management System

This document describes the complete widget domain management system in the ReviewIt platform.

## Overview
The widget domain management system allows business owners to control which domains can embed their widgets, providing security and preventing unauthorized usage. The system includes both creation-time domain setup and post-creation domain management.

## System Architecture

### Core Components

#### 1. DomainManagement Component
- **Path**: `src/app/components/widgets/DomainManagement.tsx`
- **Features**:
  - Modal-based domain management interface
  - Add/remove allowed domains with validation
  - Real-time domain list updates
  - Visual feedback with success/error messages
  - Domain count badge display

#### 2. WidgetList Integration
- **Path**: `src/app/components/widgets/WidgetList.tsx`
- **Integration**: DomainManagement component is embedded in each widget card
- **Location**: Inline with other action buttons (Preview, Copy Code, Analytics)
- **Visual Indicator**: Globe icon with domain count badge

#### 3. API Endpoints
- **Path**: `src/app/api/widgets/[widgetId]/domains/route.ts`
- **Methods**:
  - `GET`: Retrieve current allowed domains
  - `PUT`: Update complete domains list
  - `POST`: Add/remove single domain
- **Response Format**: All endpoints return `{ success: true, ... }` for consistency

### Database Schema
```typescript
model Widget {
  // ... other fields
  allowedDomains: String[] // Array of allowed domain strings
}
```

## Usage Instructions

### Managing Domains After Widget Creation
1. Navigate to `/owner-admin/widgets`
2. Find the widget you want to manage
3. Click the **"Domain Settings"** button (globe icon)
4. In the modal dialog:
   - Add domains using the input field (format: `example.com`)
   - Remove domains using the trash icon
   - Click "Add" to confirm new domains
5. Changes take effect immediately

### Domain Management During Widget Creation
1. During widget creation (Step 3 in WidgetCreator)
2. Configure initial allowed domains
3. Domains can be modified later using the management interface

## Technical Implementation

### API Response Format
All domain management endpoints return consistent responses:
```typescript
{
  success: true,
  message: string,
  widgetId: string,
  allowedDomains: string[],
  totalDomains: number,
  isPublic: boolean
}
```

### Domain Validation
- **Client-side**: Real-time format validation
- **Server-side**: Zod schema validation
- **Format**: Standard domain format (e.g., `example.com`)
- **Limits**: Maximum 50 domains per widget
- **Special**: Use `*` to allow all domains

### CORS Configuration
- **Development**: Allows all origins (`*`) for easier testing
- **Production**: Restricted to specific domains
- **Middleware**: `restrictedCorsMiddleware` handles CORS headers

## Security Features

### Domain Validation
- Server-side validation prevents malicious domains
- Duplicate domain prevention
- Format validation with helpful error messages

### Authentication & Authorization
- User must be authenticated
- Business ownership verification required
- Rate limiting on API endpoints

### Preview Mode
- Widget previews bypass domain restrictions
- Same-domain preview detection
- Iframe-based secure rendering

## Recent Fixes & Improvements

### Fixed Issues (Latest Update)
1. **API Response Format**: Fixed inconsistency where component expected `success` field
2. **CORS Configuration**: Improved development environment support
3. **Component Placement**: Better visual integration in widget management interface
4. **Error Handling**: Enhanced debugging with detailed console logging

### Enhanced Features
- Visual domain count badges
- Improved error messages
- Real-time validation feedback
- Better mobile responsiveness

## Troubleshooting

### Common Issues
1. **Domain Not Working**: Check format (use `example.com`, not `https://example.com`)
2. **CORS Errors**: Verify domain is in allowed list
3. **Preview Issues**: Preview mode should always work regardless of domain settings
4. **API Errors**: Check browser console for detailed error messages

### Debug Steps
1. Open browser developer tools
2. Check Network tab for API responses
3. Verify authentication status
4. Test with preview mode first
5. Check domain format and validation

### Development Testing
- Use `localhost:3000` or `127.0.0.1:3000` for local testing
- CORS is automatically configured for development
- Console logging available for debugging

## Best Practices

### Domain Configuration
- Start with specific domains for security
- Use `*` only if you want public embedding
- Regularly review and update domain lists
- Test embedding on target domains

### Security Considerations
- Limit domains to trusted websites only
- Monitor widget analytics for unauthorized usage
- Use domain restrictions as part of security strategy
- Keep domain lists updated as business needs change