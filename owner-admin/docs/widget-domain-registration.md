# Widget Domain Registration System

This document describes the in-memory domain registration system for widgets in the ReviewIt platform.

## Overview

The widget domain registration system automatically captures and validates domains that embed ReviewIt widgets. It uses an in-memory cache to store domain information, providing a lightweight solution that doesn't require database changes while ensuring widgets only appear on authorized domains.

## System Architecture

### Core Components

#### 1. Embed Script
- **Path**: `public/widgets/embed.js`
- **Features**:
  - Automatically detects the embedding domain
  - Stores domain information in localStorage for persistence
  - Sends domain registration to server via API
  - Passes domain information to iframe via URL parameters

#### 2. Domain Registration Endpoint
- **Path**: `src/app/api/public/widgets/register-domain/route.ts`
- **Features**:
  - Receives domain registration requests from embed script
  - Stores domains in an in-memory cache
  - Updates widget analytics with domain information
  - Provides domain validation functions

#### 3. Widget API Integration
- **Path**: `src/app/api/public/widgets/[widgetId]/route.ts`
- **Features**:
  - Checks domains against in-memory registration cache
  - Enhanced domain validation logic for cross-origin iframes
  - Special handling for self-referential iframes

### In-Memory Cache Implementation

```typescript
// In-memory cache for domain registrations
const domainRegistrationCache = new Map<string, Set<string>>();

// Store domain in memory cache
function storeDomainInfo(widgetId: string, domain: string) {
  if (!domainRegistrationCache.has(widgetId)) {
    domainRegistrationCache.set(widgetId, new Set());
  }
  
  const domains = domainRegistrationCache.get(widgetId);
  if (domains) {
    domains.add(domain);
  }
}

// Check if domain is registered for widget
export function isDomainRegistered(widgetId: string, domain: string): boolean {
  const domains = domainRegistrationCache.get(widgetId);
  if (!domains) return false;
  return domains.has(domain);
}
```

## How It Works

1. **Domain Registration Process**:
   - When a page embeds a widget, the embed.js script executes
   - The script immediately sends the parent domain to the registration endpoint
   - The server stores this domain in the in-memory cache
   - The server also updates widget analytics with the domain information

2. **Domain Validation Process**:
   - When the iframe loads and requests widget data
   - The server checks if the domain was previously registered
   - If registered, the widget is allowed to load
   - If not registered but in the allowed domains list, the widget is allowed
   - Otherwise, access is denied

3. **Self-Healing Mechanism**:
   - The embed script sends registration on every page load
   - This ensures domains are re-registered after server restarts
   - localStorage backup provides additional resilience

## Technical Considerations

### Advantages
- **No Database Changes**: Works without requiring schema changes
- **Low Latency**: In-memory validation is extremely fast
- **Self-Healing**: Automatically re-registers domains after server restarts
- **Analytics Integration**: Leverages existing analytics for domain tracking

### Limitations
- **Server Restarts**: Domain registrations are lost on server restart (but quickly recovered)
- **Multiple Instances**: In-memory cache is not shared between server instances
- **Persistence**: Long-term domain history relies on analytics data

### Analytics Integration
The system updates the widget analytics `topReferrers` field with domain information, providing a persistent record of embedding domains even if the in-memory cache is cleared.

## Security Features

### Domain Validation
- Server-side validation prevents unauthorized domains
- Cross-origin protection with fallback mechanisms
- Special handling for development and preview modes

### CORS Configuration
- Proper CORS headers for cross-origin requests
- Middleware handles preflight requests
- Security logging for suspicious activities

## Troubleshooting

### Common Issues
- **Widget Not Loading**: Check if domain is properly registered
- **Domain Registration Failures**: Verify CORS settings and network connectivity
- **Cross-Origin Issues**: Ensure embed script is properly included on the page

### Debugging
- Check server logs for domain registration messages
- Look for "Domain registered for widget" log entries
- Verify domain appears in widget analytics under Top Referrers

## Future Enhancements
A future enhancement could include a persistent database solution using a dedicated `WidgetDomainRegistration` table, which would provide better persistence across server restarts and support for multiple server instances.
