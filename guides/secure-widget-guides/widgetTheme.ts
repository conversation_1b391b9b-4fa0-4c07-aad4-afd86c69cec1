/**
 * Widget Security Theme System
 * 
 * This module provides consistent theming and visual differentiation
 * for Simple and Secure widgets across the ReviewIt platform.
 */

export type SecurityLevel = 'SIMPLE' | 'SECURE';

export interface SecurityTheme {
  primary: string;
  background: string;
  border: string;
  text: string;
  icon: string;
  label: string;
  description: string;
  badgeVariant: 'default' | 'secondary' | 'outline' | 'destructive';
  features: readonly string[];
  useCase: string;
  warningLevel: 'info' | 'warning' | 'success';
}

/**
 * Security theme definitions for widget visual differentiation
 */
export const WIDGET_SECURITY_THEMES: Record<SecurityLevel, SecurityTheme> = {
  SIMPLE: {
    primary: '#f97316', // Orange-500
    background: '#fff7ed', // Orange-50
    border: '#fed7aa', // Orange-200
    text: '#ea580c', // Orange-600
    icon: '🌐',
    label: 'Simple Widget',
    description: 'Public - Can be embedded anywhere',
    badgeVariant: 'outline',
    features: [
      '✅ Quick setup - no configuration needed',
      '✅ Works on any website immediately',
      '⚠️ No domain restrictions',
      '⚠️ Can be copied by anyone'
    ],
    useCase: 'Perfect for public websites and quick implementations',
    warningLevel: 'warning'
  },
  SECURE: {
    primary: '#059669', // Emerald-600
    background: '#ecfdf5', // Emerald-50
    border: '#a7f3d0', // Emerald-200
    text: '#047857', // Emerald-700
    icon: '🔒',
    label: 'Secure Widget',
    description: 'Domain-Restricted - Requires verification',
    badgeVariant: 'default',
    features: [
      '🔒 Domain verification required',
      '🔒 Token-based authentication',
      '🔒 Rate limiting protection',
      '🔒 Real-time security validation'
    ],
    useCase: 'Ideal for business websites requiring security control',
    warningLevel: 'success'
  }
} as const;

/**
 * Get security theme for a given security level
 */
export const getSecurityTheme = (securityLevel: SecurityLevel): SecurityTheme => {
  return WIDGET_SECURITY_THEMES[securityLevel];
};

/**
 * Get CSS classes for security-themed components
 */
export const getSecurityClasses = (securityLevel: SecurityLevel) => {
  const theme = getSecurityTheme(securityLevel);

  return {
    // Card styling
    cardBorder: `border-l-4`,
    cardBorderColor: theme.primary,
    cardBackground: theme.background,

    // Badge styling
    badgeClasses: `text-xs border`,
    badgeStyle: {
      backgroundColor: theme.background,
      borderColor: theme.border,
      color: theme.text
    },

    // Button styling
    buttonClasses: `border-2 hover:shadow-md transition-all`,
    buttonStyle: {
      borderColor: theme.border,
      backgroundColor: theme.background,
      color: theme.text
    },

    // Icon styling
    iconClasses: `w-10 h-10 rounded-full flex items-center justify-center text-lg`,
    iconStyle: {
      backgroundColor: theme.primary,
      color: 'white'
    }
  };
};

/**
 * Get security status indicator props
 */
export const getSecurityStatusProps = (securityLevel: SecurityLevel, isActive: boolean = true) => {
  const theme = getSecurityTheme(securityLevel);

  return {
    icon: theme.icon,
    label: theme.label,
    description: theme.description,
    color: isActive ? theme.primary : '#6b7280', // Gray-500 for inactive
    backgroundColor: isActive ? theme.background : '#f9fafb', // Gray-50 for inactive
    borderColor: isActive ? theme.border : '#d1d5db', // Gray-300 for inactive
    textColor: isActive ? theme.text : '#6b7280' // Gray-500 for inactive
  };
};

/**
 * Generate security-themed inline styles
 */
export const getSecurityInlineStyles = (securityLevel: SecurityLevel, isActive: boolean = true) => {
  const props = getSecurityStatusProps(securityLevel, isActive);

  return {
    container: {
      backgroundColor: props.backgroundColor,
      borderColor: props.borderColor,
      borderLeftColor: props.color,
      borderLeftWidth: '4px'
    },
    badge: {
      backgroundColor: props.backgroundColor,
      borderColor: props.borderColor,
      color: props.textColor
    },
    icon: {
      backgroundColor: props.color,
      color: 'white'
    },
    text: {
      color: props.textColor
    }
  };
};

/**
 * Security level comparison data
 */
export const SECURITY_COMPARISON = {
  features: [
    {
      name: 'Setup Complexity',
      simple: 'Copy & paste - instant',
      secure: 'Domain verification required',
      simpleIcon: '⚡',
      secureIcon: '🔧'
    },
    {
      name: 'Domain Control',
      simple: 'Works on any domain',
      secure: 'Restricted to verified domains',
      simpleIcon: '🌍',
      secureIcon: '🔒'
    },
    {
      name: 'Security Level',
      simple: 'Basic - public access',
      secure: 'Advanced - token authentication',
      simpleIcon: '🔓',
      secureIcon: '🛡️'
    },
    {
      name: 'Rate Limiting',
      simple: 'Standard limits',
      secure: 'Configurable limits',
      simpleIcon: '📊',
      secureIcon: '⚙️'
    },
    {
      name: 'Analytics',
      simple: 'Basic tracking',
      secure: 'Enhanced security analytics',
      simpleIcon: '📈',
      secureIcon: '🔍'
    }
  ],
  recommendations: {
    simple: [
      'Personal websites',
      'Blogs and portfolios',
      'Public marketing pages',
      'Quick implementations'
    ],
    secure: [
      'Business websites',
      'E-commerce platforms',
      'Corporate portals',
      'Sensitive applications'
    ]
  }
};

/**
 * Get embed code styling based on security level
 */
export const getEmbedCodeStyling = (securityLevel: SecurityLevel) => {
  const theme = getSecurityTheme(securityLevel);

  return {
    containerClasses: `rounded-lg border-2 p-4`,
    containerStyle: {
      backgroundColor: theme.background,
      borderColor: theme.border
    },
    headerStyle: {
      color: theme.text
    },
    codeBlockClasses: `font-mono text-xs bg-white rounded border p-3`,
    codeBlockStyle: {
      borderColor: theme.border
    }
  };
};

/**
 * Security warning messages
 */
export const SECURITY_WARNINGS = {
  SIMPLE: {
    title: 'Security Notice',
    message: 'This embed code can be copied and used on any website. Consider upgrading to a Secure Widget for better control.',
    icon: '⚠️',
    severity: 'warning' as const
  },
  SECURE: {
    title: 'Security Features Active',
    message: 'This widget is protected with domain verification, token authentication, and rate limiting.',
    icon: '🛡️',
    severity: 'success' as const
  }
};

/**
 * Get security warning for a given security level
 */
export const getSecurityWarning = (securityLevel: SecurityLevel) => {
  return SECURITY_WARNINGS[securityLevel];
};