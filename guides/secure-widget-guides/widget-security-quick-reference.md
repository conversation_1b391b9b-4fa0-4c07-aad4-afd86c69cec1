# Widget Security Visual Differentiation - Quick Reference

## Overview

This quick reference guide provides essential information for implementing and maintaining the widget security visual differentiation system in ReviewIt.

## Color System

### Primary Colors

| Security Level | Primary | Background | Border | Text | Icon |
|----------------|---------|------------|--------|------|----- |
| **Simple** | `#3b82f6` | `#eff6ff` | `#93c5fd` | `#1e40af` | 🌐 |
| **Secure** | `#059669` | `#ecfdf5` | `#6ee7b7` | `#065f46` | 🔒 |

### Usage Examples

```typescript
// Get theme for a widget
const theme = getSecurityTheme(widget.securityLevel);

// Apply inline styles
const styles = getSecurityInlineStyles(widget.securityLevel, widget.isActive);

// Get security status props
const statusProps = getSecurityStatusProps(widget.securityLevel);
```

## Component Patterns

### Security Badge

```typescript
const SecurityBadge = ({ securityLevel }: { securityLevel: 'SIMPLE' | 'SECURE' }) => {
  const theme = getSecurityTheme(securityLevel);
  
  return (
    <Badge 
      variant="outline"
      style={{
        backgroundColor: theme.background,
        borderColor: theme.border,
        color: theme.text
      }}
    >
      <span className="mr-1">{theme.icon}</span>
      {theme.label}
    </Badge>
  );
};
```

### Security Indicator

```typescript
const SecurityIndicator = ({ widget }: { widget: iWidget }) => {
  const theme = getSecurityTheme(widget.securityLevel);
  
  return (
    <div 
      className="flex items-center gap-2 p-3 rounded-lg border"
      style={{
        backgroundColor: theme.background,
        borderColor: theme.border
      }}
    >
      <span className="text-lg">{theme.icon}</span>
      <div>
        <h4 className="font-medium" style={{ color: theme.text }}>
          {theme.label} Widget
        </h4>
        <p className="text-sm text-gray-600">{theme.description}</p>
      </div>
    </div>
  );
};
```

### Security Warning

```typescript
const SecurityWarning = ({ securityLevel }: { securityLevel: 'SIMPLE' | 'SECURE' }) => {
  const warning = getSecurityWarning(securityLevel);
  const isSimple = securityLevel === 'SIMPLE';
  
  return (
    <div className={`p-3 rounded-lg border ${
      isSimple ? 'bg-orange-50 border-orange-200' : 'bg-emerald-50 border-emerald-200'
    }`}>
      <div className="flex items-start gap-2">
        <span className="text-lg">{warning.icon}</span>
        <div>
          <p className={`font-medium text-sm ${
            isSimple ? 'text-orange-800' : 'text-emerald-800'
          }`}>
            {warning.title}
          </p>
          <p className={`text-sm ${
            isSimple ? 'text-orange-700' : 'text-emerald-700'
          }`}>
            {warning.message}
          </p>
        </div>
      </div>
    </div>
  );
};
```

## Embed Code Templates

### Simple Widget Embed

```html
<!-- 🌐 SIMPLE WIDGET - Public Access -->
<iframe 
  src="https://reviewit.gy/widgets/iframe/{widgetId}" 
  width="100%" 
  height="400" 
  frameborder="0"
  title="ReviewIt Widget"
  style="border: none; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
</iframe>

<!-- 
  ⚠️ SECURITY NOTICE:
  This widget can be embedded on any website.
  No setup or verification required.
  Consider upgrading to Secure Widget for better control.
-->
```

### Secure Widget Embed

```html
<!-- 🔒 SECURE WIDGET - Domain Restricted -->
<script src="https://reviewit.gy/widgets/secure-embed.js"></script>
<div 
  data-reviewit-secure-widget="{widgetId}" 
  data-token="{domainToken}"
  data-domain="{verifiedDomain}"
  style="border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
></div>

<!-- 
  🛡️ SECURITY FEATURES ACTIVE:
  ✅ Domain verification required
  ✅ Token-based authentication  
  ✅ Rate limiting protection
  ✅ Real-time validation
-->
```

## CSS Classes

### Security-Themed Classes

```css
/* Simple Widget Styles */
.widget-simple {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(to right, #eff6ff, #ffffff);
}

.widget-simple-badge {
  background-color: #eff6ff;
  border-color: #93c5fd;
  color: #1e40af;
}

.widget-simple-button {
  border-color: #93c5fd;
  color: #1e40af;
}

.widget-simple-button:hover {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

/* Secure Widget Styles */
.widget-secure {
  border-left: 4px solid #059669;
  background: linear-gradient(to right, #ecfdf5, #ffffff);
}

.widget-secure-badge {
  background-color: #ecfdf5;
  border-color: #6ee7b7;
  color: #065f46;
}

.widget-secure-button {
  border-color: #6ee7b7;
  color: #065f46;
}

.widget-secure-button:hover {
  background-color: #ecfdf5;
  border-color: #059669;
}
```

## Utility Functions

### Theme Utilities

```typescript
// Get security theme
const getSecurityTheme = (securityLevel: 'SIMPLE' | 'SECURE') => {
  return WIDGET_SECURITY_THEMES[securityLevel];
};

// Get security classes
const getSecurityClasses = (securityLevel: 'SIMPLE' | 'SECURE') => {
  const base = 'widget-security';
  const level = securityLevel.toLowerCase();
  return `${base} ${base}-${level}`;
};

// Get inline styles
const getSecurityInlineStyles = (securityLevel: 'SIMPLE' | 'SECURE', isActive: boolean) => {
  const theme = getSecurityTheme(securityLevel);
  
  return {
    container: {
      borderLeftColor: theme.primary,
      background: isActive 
        ? `linear-gradient(to right, ${theme.background}, #ffffff)`
        : '#f8fafc'
    },
    badge: {
      backgroundColor: theme.background,
      borderColor: theme.border,
      color: theme.text
    },
    button: {
      borderColor: theme.border,
      color: theme.text
    }
  };
};
```

### Validation Utilities

```typescript
// Validate security level
const isValidSecurityLevel = (level: string): level is 'SIMPLE' | 'SECURE' => {
  return level === 'SIMPLE' || level === 'SECURE';
};

// Check if widget is secure
const isSecureWidget = (widget: iWidget): boolean => {
  return widget.securityLevel === 'SECURE';
};

// Get security requirements
const getSecurityRequirements = (securityLevel: 'SIMPLE' | 'SECURE') => {
  if (securityLevel === 'SECURE') {
    return {
      domainVerification: true,
      tokenRequired: true,
      rateLimit: true,
      setupRequired: true
    };
  }
  
  return {
    domainVerification: false,
    tokenRequired: false,
    rateLimit: false,
    setupRequired: false
  };
};
```

## Best Practices

### Visual Design

1. **Consistency**: Always use the defined color palette
2. **Hierarchy**: Security level should be immediately apparent
3. **Accessibility**: Ensure sufficient color contrast (4.5:1 minimum)
4. **Responsiveness**: Test on mobile devices

### Code Organization

1. **Centralized Theming**: Use the `widgetTheme.ts` utility file
2. **Reusable Components**: Create shared security components
3. **Type Safety**: Use TypeScript interfaces for security levels
4. **Performance**: Minimize inline style calculations

### User Experience

1. **Clear Communication**: Security implications should be obvious
2. **Progressive Disclosure**: Show advanced options when needed
3. **Error Prevention**: Guide users to correct security setup
4. **Feedback**: Provide immediate visual feedback for actions

## Common Patterns

### Widget Card with Security

```typescript
const WidgetCard = ({ widget }: { widget: iWidget }) => {
  const theme = getSecurityTheme(widget.securityLevel);
  const styles = getSecurityInlineStyles(widget.securityLevel, widget.isActive);
  
  return (
    <Card 
      className="border-2 transition-all hover:shadow-lg"
      style={styles.container}
    >
      {/* Security indicator strip */}
      <div 
        className="h-1 w-full"
        style={{ backgroundColor: theme.primary }}
      />
      
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">{theme.icon}</span>
            <h3 className="font-semibold">{widget.name}</h3>
            <SecurityBadge securityLevel={widget.securityLevel} />
          </div>
          
          {/* Actions */}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Widget details */}
      </CardContent>
    </Card>
  );
};
```

### Security Level Selector

```typescript
const SecurityLevelSelector = ({ value, onChange }: {
  value: 'SIMPLE' | 'SECURE',
  onChange: (level: 'SIMPLE' | 'SECURE') => void
}) => {
  const options = ['SIMPLE', 'SECURE'] as const;
  
  return (
    <div className="grid grid-cols-2 gap-4">
      {options.map((level) => {
        const theme = getSecurityTheme(level);
        const isSelected = value === level;
        
        return (
          <Card 
            key={level}
            className={`cursor-pointer border-2 transition-all ${
              isSelected ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
            }`}
            style={{
              borderColor: isSelected ? theme.primary : theme.border,
              backgroundColor: isSelected ? theme.background : 'white'
            }}
            onClick={() => onChange(level)}
          >
            <CardContent className="p-4 text-center">
              <div className="text-2xl mb-2">{theme.icon}</div>
              <h4 className="font-medium" style={{ color: theme.text }}>
                {theme.label}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {theme.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
```

## Troubleshooting

### Common Issues

1. **Colors not applying**: Check if `widgetTheme.ts` is properly imported
2. **Icons not showing**: Verify emoji support or use icon components
3. **Layout breaking**: Test responsive behavior on different screen sizes
4. **Performance issues**: Minimize inline style calculations

### Debug Utilities

```typescript
// Debug security theme
const debugSecurityTheme = (securityLevel: 'SIMPLE' | 'SECURE') => {
  const theme = getSecurityTheme(securityLevel);
  console.log('Security Theme:', {
    level: securityLevel,
    theme,
    styles: getSecurityInlineStyles(securityLevel, true)
  });
};

// Validate widget security setup
const validateWidgetSecurity = (widget: iWidget) => {
  const issues = [];
  
  if (widget.securityLevel === 'SECURE') {
    if (!widget.allowedDomains || widget.allowedDomains.length === 0) {
      issues.push('No verified domains configured');
    }
    if (!widget.apiKey) {
      issues.push('No API key generated');
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};
```

## Testing Checklist

### Visual Testing
- [ ] Security colors display correctly
- [ ] Icons are visible and appropriate
- [ ] Hover states work properly
- [ ] Mobile layout is responsive
- [ ] Color contrast meets accessibility standards

### Functional Testing
- [ ] Security level changes update visuals
- [ ] Embed code generation works for both types
- [ ] Security warnings display appropriately
- [ ] Copy/download functionality works

### Browser Testing
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers

## Resources

### Files to Reference
- `src/app/util/widgetTheme.ts` - Theme system
- `src/app/components/widgets/WidgetList.tsx` - Widget display
- `src/app/components/widgets/WidgetCreator.tsx` - Widget creation
- `src/app/components/widgets/EmbedCodeGenerator.tsx` - Embed codes

### Documentation
- [Secure Widget Implementation Guide](./secure-widget-implementation-guide.md)
- [Implementation Checklist](./secure-widget-implementation-checklist.md)
- [Visual Differentiation Guide](./secure-widget-visual-differentiation-guide.md)

---

*This quick reference should be updated as the system evolves. Keep it current with any changes to the security theme system.*