# Secure Widget Visual Differentiation - Complete Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Design System](#design-system)
3. [Component Updates](#component-updates)
4. [Embed Code Enhancements](#embed-code-enhancements)
5. [Testing Strategy](#testing-strategy)
6. [Deployment Guide](#deployment-guide)

## Overview

This guide provides complete implementation details for visually differentiating secure widgets from simple widgets throughout the ReviewIt application. The implementation focuses on:

- **Visual Consistency**: Unified color scheme and iconography
- **Clear Communication**: Obvious security level indicators
- **Enhanced UX**: Intuitive understanding of widget capabilities
- **Professional Appearance**: Polished, enterprise-ready interface

### Key Visual Elements

| Element | Simple Widget | Secure Widget |
|---------|---------------|---------------|
| **Primary Color** | `#3b82f6` (Blue) | `#059669` (Emerald) |
| **Icon** | 🌐 (Globe) | 🔒 (Lock) |
| **Border** | Blue accent | Green accent |
| **Background** | Light blue tint | Light green tint |
| **Badge** | "Simple" | "Secure" |

## Design System

### Color Palette

```typescript
// Primary Colors
const SECURITY_COLORS = {
  SIMPLE: {
    primary: '#3b82f6',      // Blue-500
    background: '#eff6ff',   // Blue-50
    border: '#93c5fd',       // Blue-300
    text: '#1e40af',         // Blue-800
    hover: '#2563eb'         // Blue-600
  },
  SECURE: {
    primary: '#059669',      // Emerald-600
    background: '#ecfdf5',   // Emerald-50
    border: '#6ee7b7',       // Emerald-300
    text: '#065f46',         // Emerald-800
    hover: '#047857'         // Emerald-700
  }
};
```

### Typography Scale

```css
/* Security Level Labels */
.security-label {
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

/* Security Descriptions */
.security-description {
  font-size: 0.75rem;
  line-height: 1.5;
  opacity: 0.8;
}

/* Security Features */
.security-feature {
  font-size: 0.6875rem;
  font-weight: 500;
}
```

## Component Updates

### 1. WidgetList Component Enhancement

**File:** `src/app/components/widgets/WidgetList.tsx`

#### Step 1: Import Security Theme System

```typescript
// Add to existing imports
import { 
  getSecurityTheme, 
  getSecurityInlineStyles, 
  getSecurityStatusProps,
  WIDGET_SECURITY_THEMES 
} from '@/app/util/widgetTheme';
import { Shield, Globe, AlertTriangle, CheckCircle } from 'lucide-react';
```

#### Step 2: Create Security Badge Component

```typescript
const SecurityBadge = ({ securityLevel, className = "" }: { 
  securityLevel: 'SIMPLE' | 'SECURE', 
  className?: string 
}) => {
  const theme = getSecurityTheme(securityLevel);
  
  return (
    <Badge 
      variant="outline"
      className={`text-xs font-medium ${className}`}
      style={{
        backgroundColor: theme.background,
        borderColor: theme.border,
        color: theme.text
      }}
    >
      <span className="mr-1">{theme.icon}</span>
      {theme.label}
    </Badge>
  );
};
```

#### Step 3: Create Security Status Indicator

```typescript
const SecurityStatusIndicator = ({ widget }: { widget: iWidget }) => {
  const theme = getSecurityTheme(widget.securityLevel);
  const isSecure = widget.securityLevel === 'SECURE';
  const hasVerifiedDomains = widget.allowedDomains && widget.allowedDomains.length > 0;
  
  return (
    <div className="flex items-center gap-2 mt-2">
      <SecurityBadge securityLevel={widget.securityLevel} />
      
      {isSecure && (
        <div className="flex items-center gap-1 text-xs">
          <Globe className="w-3 h-3" style={{ color: theme.text }} />
          <span style={{ color: theme.text }}>
            {widget.allowedDomains?.length || 0} domains
          </span>
          
          {!hasVerifiedDomains && (
            <Badge variant="destructive" className="text-xs ml-1">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Setup Required
            </Badge>
          )}
          
          {hasVerifiedDomains && (
            <CheckCircle className="w-3 h-3 text-green-600" />
          )}
        </div>
      )}
    </div>
  );
};
```

#### Step 4: Update Widget Card Rendering

```typescript
const renderWidgetCard = (widget: iWidget) => {
  const securityTheme = getSecurityTheme(widget.securityLevel);
  const securityStyles = getSecurityInlineStyles(widget.securityLevel, widget.isActive);
  
  return (
    <Card 
      key={widget.id} 
      className={`group hover:shadow-xl transition-all duration-300 border-2 relative overflow-hidden`}
      style={{
        ...securityStyles.container,
        borderLeftWidth: '4px',
        borderLeftColor: securityTheme.primary,
        ...(widget.isActive 
          ? { borderColor: '#22c55e' } 
          : { borderColor: '#d1d5db' }
        )
      }}
    >
      {/* Security Level Indicator Strip */}
      <div 
        className="absolute top-0 left-0 right-0 h-1"
        style={{ backgroundColor: securityTheme.primary }}
      />
      
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2 mb-2">
              <span 
                className="text-lg p-1 rounded"
                style={{ 
                  backgroundColor: securityTheme.background,
                  color: securityTheme.primary 
                }}
              >
                {securityTheme.icon}
              </span>
              {widget.name}
              <Badge 
                variant={widget.isActive ? "default" : "secondary"}
                className="text-xs"
              >
                {widget.isActive ? "Active" : "Inactive"}
              </Badge>
            </CardTitle>
            
            <SecurityStatusIndicator widget={widget} />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handlePreview(widget)}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleCopyEmbedCode(widget)}>
                <Copy className="mr-2 h-4 w-4" />
                Copy Embed Code
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAnalytics(widget)}>
                <BarChart className="mr-2 h-4 w-4" />
                Analytics
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleToggleStatus(widget)}
                className={widget.isActive ? "text-orange-600" : "text-green-600"}
              >
                {widget.isActive ? (
                  <>
                    <PauseCircle className="mr-2 h-4 w-4" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <PlayCircle className="mr-2 h-4 w-4" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleDelete(widget)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Type</p>
              <p className="font-medium">{widget.type}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Product</p>
              <p className="font-medium">{widget.product?.name || 'N/A'}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Views</p>
              <p className="font-medium">{widget.viewCount || 0}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Clicks</p>
              <p className="font-medium">{widget.clickCount || 0}</p>
            </div>
            <div>
              <p className="text-muted-foreground">CTR</p>
              <p className="font-medium">
                {widget.viewCount > 0 
                  ? `${((widget.clickCount || 0) / widget.viewCount * 100).toFixed(1)}%`
                  : '0%'
                }
              </p>
            </div>
          </div>
          
          <div className="text-sm">
            <p className="text-muted-foreground">Last Used</p>
            <p className="font-medium">
              {widget.lastUsed 
                ? new Date(widget.lastUsed).toLocaleDateString()
                : 'Never'
              }
            </p>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-3 border-t">
        <div className="flex gap-2 w-full">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handlePreview(widget)}
            className="flex-1"
          >
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleCopyEmbedCode(widget)}
            className="flex-1"
            style={{
              borderColor: securityTheme.border,
              color: securityTheme.text
            }}
          >
            <Copy className="mr-2 h-4 w-4" />
            Copy Code
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleAnalytics(widget)}
            className="flex-1"
          >
            <BarChart className="mr-2 h-4 w-4" />
            Analytics
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
```

### 2. WidgetCreator Component Enhancement

**File:** `src/app/components/widgets/WidgetCreator.tsx`

#### Step 1: Import Security Theme System

```typescript
// Add to existing imports
import { 
  WIDGET_SECURITY_THEMES, 
  SECURITY_COMPARISON,
  getSecurityTheme 
} from '@/app/util/widgetTheme';
import { Shield, Globe, AlertCircle, CheckCircle, Info } from 'lucide-react';
```

#### Step 2: Create Security Level Selection Component

```typescript
const SecurityLevelSelector = ({ 
  selectedLevel, 
  onLevelChange 
}: { 
  selectedLevel: 'SIMPLE' | 'SECURE',
  onLevelChange: (level: 'SIMPLE' | 'SECURE') => void
}) => {
  const securityLevels = [
    {
      value: 'SIMPLE' as const,
      ...WIDGET_SECURITY_THEMES.SIMPLE,
      recommended: false
    },
    {
      value: 'SECURE' as const,
      ...WIDGET_SECURITY_THEMES.SECURE,
      recommended: true
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Shield className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold">Security Level</h3>
        <Badge variant="outline" className="text-xs">
          Choose widget security type
        </Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {securityLevels.map((level) => {
          const isSelected = selectedLevel === level.value;
          
          return (
            <Card 
              key={level.value}
              className={`cursor-pointer transition-all duration-200 border-2 hover:shadow-lg ${
                isSelected 
                  ? 'ring-2 ring-blue-500 shadow-md' 
                  : 'hover:bg-gray-50'
              }`}
              style={{
                borderColor: isSelected ? level.primary : level.border,
                backgroundColor: isSelected ? level.background : 'white'
              }}
              onClick={() => onLevelChange(level.value)}
            >
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="p-3 rounded-lg text-xl"
                        style={{ 
                          backgroundColor: level.background,
                          color: level.primary 
                        }}
                      >
                        {level.icon}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold text-lg" style={{ color: level.text }}>
                            {level.label}
                          </h4>
                          {level.recommended && (
                            <Badge 
                              variant="default" 
                              className="text-xs bg-blue-600 text-white"
                            >
                              Recommended
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {level.description}
                        </p>
                      </div>
                    </div>
                    
                    {isSelected && (
                      <CheckCircle className="w-6 h-6 text-blue-600" />
                    )}
                  </div>
                  
                  {/* Features */}
                  <div className="space-y-2">
                    <h5 className="font-medium text-sm text-gray-700">Features:</h5>
                    <div className="space-y-1">
                      {level.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-green-600 flex-shrink-0" />
                          <span className="text-xs text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Use Cases */}
                  <div className="space-y-2">
                    <h5 className="font-medium text-sm text-gray-700">Best For:</h5>
                    <div className="space-y-1">
                      {level.useCases.map((useCase, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <div 
                            className="w-2 h-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: level.primary }}
                          />
                          <span className="text-xs text-gray-600">{useCase}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Warning/Info */}
                  {level.value === 'SIMPLE' && (
                    <div className="flex items-start gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <AlertCircle className="w-4 h-4 text-orange-600 flex-shrink-0 mt-0.5" />
                      <div className="text-xs">
                        <p className="font-medium text-orange-800">Security Notice</p>
                        <p className="text-orange-700">
                          This widget can be embedded on any website. Consider using Secure Widget for better control.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {level.value === 'SECURE' && (
                    <div className="flex items-start gap-2 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
                      <Shield className="w-4 h-4 text-emerald-600 flex-shrink-0 mt-0.5" />
                      <div className="text-xs">
                        <p className="font-medium text-emerald-800">Enhanced Security</p>
                        <p className="text-emerald-700">
                          Domain verification required. Provides maximum security and control.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      {/* Security Comparison */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Info className="w-4 h-4 text-blue-600" />
            <h4 className="font-medium text-sm">Security Comparison</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
            {Object.entries(SECURITY_COMPARISON).map(([feature, comparison]) => (
              <div key={feature} className="space-y-1">
                <p className="font-medium text-gray-700 capitalize">
                  {feature.replace(/([A-Z])/g, ' $1').trim()}
                </p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Globe className="w-3 h-3 text-blue-600" />
                    <span className="text-gray-600">Simple: {comparison.SIMPLE}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="w-3 h-3 text-emerald-600" />
                    <span className="text-gray-600">Secure: {comparison.SECURE}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
```

#### Step 3: Update Form Integration

```typescript
// In the main form component, replace the security level selection with:
<SecurityLevelSelector 
  selectedLevel={formData.securityLevel}
  onLevelChange={(level) => setFormData(prev => ({ ...prev, securityLevel: level }))}
/>
```

### 3. EmbedCodeGenerator Component Enhancement

**File:** `src/app/components/widgets/EmbedCodeGenerator.tsx`

#### Step 1: Create Enhanced Security Indicator

```typescript
const EnhancedSecurityIndicator = ({ widget }: { widget: iWidget }) => {
  const theme = getSecurityTheme(widget.securityLevel);
  const warning = getSecurityWarning(widget.securityLevel);
  
  return (
    <div className="space-y-4">
      {/* Main Security Indicator */}
      <div 
        className="flex items-center justify-between p-4 rounded-lg border-2"
        style={{
          backgroundColor: theme.background,
          borderColor: theme.border
        }}
      >
        <div className="flex items-center space-x-4">
          <div 
            className="w-12 h-12 rounded-full flex items-center justify-center text-xl"
            style={{ backgroundColor: theme.primary, color: 'white' }}
          >
            {theme.icon}
          </div>
          <div>
            <h3 className="font-semibold text-lg" style={{ color: theme.text }}>
              {theme.label} Widget
            </h3>
            <p className="text-sm text-gray-600">{theme.description}</p>
          </div>
        </div>
        
        <div className="text-right">
          <Badge 
            variant="outline"
            className="text-sm font-medium"
            style={{
              backgroundColor: theme.background,
              borderColor: theme.border,
              color: theme.text
            }}
          >
            {widget.securityLevel}
          </Badge>
          <p className="text-xs text-gray-500 mt-1">
            Widget ID: {widget.id}
          </p>
        </div>
      </div>
      
      {/* Security Warning/Info */}
      <div 
        className={`p-4 rounded-lg border ${
          widget.securityLevel === 'SIMPLE' 
            ? 'bg-orange-50 border-orange-200' 
            : 'bg-emerald-50 border-emerald-200'
        }`}
      >
        <div className="flex items-start space-x-3">
          <span className="text-xl">{warning.icon}</span>
          <div className="flex-1">
            <p className={`font-medium text-sm ${
              widget.securityLevel === 'SIMPLE' 
                ? 'text-orange-800' 
                : 'text-emerald-800'
            }`}>
              {warning.title}
            </p>
            <p className={`text-sm mt-1 ${
              widget.securityLevel === 'SIMPLE' 
                ? 'text-orange-700' 
                : 'text-emerald-700'
            }`}>
              {warning.message}
            </p>
            
            {widget.securityLevel === 'SECURE' && (
              <div className="mt-3 space-y-2">
                <h4 className="font-medium text-sm text-emerald-800">Active Security Features:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {theme.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <CheckCircle className="w-3 h-3 text-emerald-600" />
                      <span className="text-xs text-emerald-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### Step 2: Enhanced Embed Code Display

```typescript
const EnhancedEmbedCodeDisplay = ({ 
  widget, 
  selectedDomain, 
  token 
}: { 
  widget: iWidget, 
  selectedDomain?: string, 
  token?: string 
}) => {
  const theme = getSecurityTheme(widget.securityLevel);
  const embedStyling = getEmbedCodeStyling(widget.securityLevel);
  
  const generateEmbedCode = () => {
    if (widget.securityLevel === 'SIMPLE') {
      return `<!-- 🌐 SIMPLE WIDGET - Public Access -->
<iframe 
  src="https://reviewit.gy/widgets/iframe/${widget.id}" 
  width="100%" 
  height="400" 
  frameborder="0"
  title="ReviewIt Widget"
  style="border: none; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
</iframe>

<!-- 
  ⚠️ SECURITY NOTICE:
  This widget can be embedded on any website.
  No setup or verification required.
  Consider upgrading to Secure Widget for better control.
  
  Widget Features:
  ✅ Easy integration
  ✅ No domain restrictions
  ✅ Instant deployment
  ⚠️ Public access (anyone can embed)
  ⚠️ No usage analytics by domain
-->`;
    } else {
      return `<!-- 🔒 SECURE WIDGET - Domain Restricted -->
<script src="https://reviewit.gy/widgets/secure-embed.js"></script>
<div 
  data-reviewit-secure-widget="${widget.id}" 
  data-token="${token || 'YOUR_DOMAIN_TOKEN'}"
  data-domain="${selectedDomain || 'your-verified-domain.com'}"
  style="border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
></div>

<!-- 
  🛡️ SECURITY FEATURES ACTIVE:
  ✅ Domain verification required
  ✅ Token-based authentication  
  ✅ Rate limiting protection
  ✅ Real-time validation
  ✅ Usage analytics by domain
  ✅ Unauthorized access prevention
  
  Security Details:
  Token expires: ${token ? 'Check dashboard for expiry' : 'Generate token first'}
  Valid for domain: ${selectedDomain || 'Select verified domain'}
  Max requests/hour: ${widget.maxRequestsPerHour || 1000}
  
  Setup Required:
  1. Verify your domain in the dashboard
  2. Generate a domain-specific token
  3. Replace YOUR_DOMAIN_TOKEN with actual token
  4. Replace your-verified-domain.com with your domain
-->`;
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-lg">{theme.icon}</span>
          <h3 className="font-semibold">Embed Code</h3>
          <Badge 
            variant="outline"
            style={{
              backgroundColor: theme.background,
              borderColor: theme.border,
              color: theme.text
            }}
          >
            {theme.label}
          </Badge>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => navigator.clipboard.writeText(generateEmbedCode())}
            style={{
              borderColor: theme.border,
              color: theme.text
            }}
          >
            <Copy className="w-4 h-4 mr-2" />
            Copy Code
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              const blob = new Blob([generateEmbedCode()], { type: 'text/html' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `reviewit-widget-${widget.securityLevel.toLowerCase()}-${widget.id}.html`;
              a.click();
            }}
          >
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
        </div>
      </div>
      
      <div 
        className="relative rounded-lg border-2 overflow-hidden"
        style={{ borderColor: theme.border }}
      >
        <div 
          className="px-3 py-2 text-xs font-medium flex items-center gap-2"
          style={{ 
            backgroundColor: theme.background,
            color: theme.text 
          }}
        >
          <span>{theme.icon}</span>
          {theme.label} Widget Embed Code
          <Badge variant="outline" className="text-xs">
            HTML
          </Badge>
        </div>
        
        <pre 
          className="p-4 text-sm overflow-x-auto"
          style={{
            backgroundColor: '#f8fafc',
            color: '#334155',
            fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
          }}
        >
          <code>{generateEmbedCode()}</code>
        </pre>
      </div>
    </div>
  );
};
```

## Testing Strategy

### Visual Testing Checklist

1. **Color Consistency**
   - [ ] Simple widgets use blue theme consistently
   - [ ] Secure widgets use green theme consistently
   - [ ] Hover states maintain theme colors
   - [ ] Active/inactive states are clearly differentiated

2. **Icon Usage**
   - [ ] Globe icon (🌐) appears for Simple widgets
   - [ ] Lock icon (🔒) appears for Secure widgets
   - [ ] Icons are consistent across all components
   - [ ] Icons scale properly on mobile devices

3. **Typography**
   - [ ] Security labels are clearly readable
   - [ ] Font weights create proper hierarchy
   - [ ] Text colors meet accessibility standards
   - [ ] Mobile text remains legible

4. **Layout**
   - [ ] Security indicators don't break layouts
   - [ ] Components remain responsive
   - [ ] Spacing is consistent
   - [ ] No visual overflow issues

### Functional Testing Checklist

1. **Widget Creation**
   - [ ] Security level selection works properly
   - [ ] Visual feedback updates immediately
   - [ ] Form validation includes security requirements
   - [ ] Created widgets display correct security theme

2. **Widget Management**
   - [ ] Security badges appear in widget lists
   - [ ] Filtering by security level works
   - [ ] Security status updates in real-time
   - [ ] Domain management integrates properly

3. **Embed Code Generation**
   - [ ] Correct embed code generated for each type
   - [ ] Security warnings display appropriately
   - [ ] Copy functionality works
   - [ ] Download functionality works

### Accessibility Testing

1. **Color Contrast**
   - [ ] All text meets WCAG AA standards (4.5:1 ratio)
   - [ ] Security indicators are distinguishable
   - [ ] Focus states are clearly visible

2. **Screen Reader Support**
   - [ ] Security levels are announced properly
   - [ ] Icons have appropriate alt text
   - [ ] Form labels are descriptive

3. **Keyboard Navigation**
   - [ ] All interactive elements are focusable
   - [ ] Tab order is logical
   - [ ] Keyboard shortcuts work

## Deployment Guide

### Pre-Deployment Checklist

1. **Code Quality**
   - [ ] All TypeScript errors resolved
   - [ ] ESLint warnings addressed
   - [ ] Code follows existing patterns
   - [ ] Comments added for complex logic

2. **Testing**
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Visual regression tests pass
   - [ ] Accessibility tests pass

3. **Performance**
   - [ ] Bundle size impact measured
   - [ ] Load time impact assessed
   - [ ] Memory usage checked
   - [ ] Mobile performance verified

### Deployment Steps

1. **Staging Deployment**
   ```bash
   # Deploy to staging environment
   npm run build
   npm run deploy:staging
   
   # Run automated tests
   npm run test:e2e:staging
   ```

2. **Production Deployment**
   ```bash
   # Deploy to production
   npm run build:production
   npm run deploy:production
   
   # Monitor deployment
   npm run monitor:deployment
   ```

3. **Post-Deployment Verification**
   - [ ] All widgets display correctly
   - [ ] Security themes apply properly
   - [ ] Embed codes generate correctly
   - [ ] No console errors
   - [ ] Performance metrics within acceptable range

### Rollback Plan

1. **Immediate Rollback**
   ```bash
   # Rollback to previous version
   npm run rollback:immediate
   ```

2. **Partial Rollback**
   - Disable new security theme features via feature flags
   - Revert specific components if needed
   - Maintain data integrity

### Monitoring

1. **Key Metrics**
   - Widget creation success rate
   - Embed code generation success rate
   - User engagement with security features
   - Support ticket volume

2. **Error Tracking**
   - JavaScript errors
   - API failures
   - Performance degradation
   - User experience issues

## Conclusion

This implementation guide provides a comprehensive approach to visually differentiating secure widgets from simple widgets. The system ensures:

- **Clear Visual Hierarchy**: Users can immediately identify widget security levels
- **Consistent Experience**: Unified design language across all components
- **Enhanced Security Communication**: Clear understanding of security implications
- **Professional Appearance**: Enterprise-ready interface design

Follow the implementation steps in order, test thoroughly at each stage, and monitor the deployment for optimal results.