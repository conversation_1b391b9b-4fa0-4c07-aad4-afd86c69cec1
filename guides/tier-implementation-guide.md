# Tier Implementation Guide

Last updated: 2025-07-13

This document records the design decisions and incremental steps taken to add **Starter (Free), Pro, and Enterprise** subscription tiers to Review-It.  Use it as a reference when continuing Phase 3 or onboarding new contributors.

---
## 0  Feature Matrix (v0.1)
| Capability | Starter | Pro | Enterprise |
|------------|---------|-----|------------|
| Record & display reviews | ✔︎ | ✔︎ | ✔︎ |
| Reply **as business owner** | — | ✔︎ | ✔︎ |
| Basic analytics (views, avg rating) | — | ✔︎ | ✔︎ |
| Advanced analytics & export | — | — | ✔︎ |
| Monthly review quota | 200 | 2 000 | ∞ |
| Team members | 1 | 5 | ∞ + roles |
| Integrations (Slack, Zapier) | — | ✔︎ | ✔︎ |
| SSO / SCIM | — | — | ✔︎ |
| Priority support SLA | — | — | ✔︎ |

“—” = gated; limits controlled by feature flag `ENABLE_TIER_LIMITS` (off by default).

---
## 1  Data-Model Changes (Phase 1 – DONE)
1. `enum Tier { starter pro enterprise }` added to `schema.prisma`.
2. `Business` model gains:
   ```prisma
   tier          Tier     @default(starter)
   tierExpiresAt DateTime?
   ```
3. Migration applied (`npx prisma migrate dev --name add-tier`).

---
## 2  Tier Utilities (Phase 1 – DONE)
File: `src/app/util/tier.ts`

Key exports:
* `hasRequiredTier(current, required)`
* `assertTier(current, required, featureKey?)` – throws 402.
* `maybeAssertTier(...)` – calls `assertTier` only if `ENABLE_TIER_LIMITS=true`.
* `TierProvider`, `useTier()` – simple React context (currently defaulting to `'starter'`).
* `TIER_LIMITS_ENABLED` flag helper.

---
## 3  Global Provider (Phase 1 – DONE)
`src/app/layout.tsx` now wraps the app:
```tsx
<TierProvider value="starter">  // TODO: replace with real tier fetch
  <ClerkProvider> ...
```

---
## 4  First Gated Feature (Phase 2 – DONE)
Route `src/app/api/create/comment/route.ts` (owner reply):
```ts
const bizTier = (createdComment.review.product?.business?.tier || 'starter') as Tier;
maybeAssertTier(bizTier, 'pro', 'reply_as_business');
```
If limits are enabled and the business tier is **Starter**, the API returns 402 *Payment Required*.

---
## 5  Next Milestones (Phase 2 → Phase 3)
1. **Wire real tier into `TierProvider`**  
   Fetch business tier server-side in `layout.tsx`.
2. **Gate basic analytics** via `maybeAssertTier(bizTier, 'pro', 'basic_analytics')`.
3. **Stripe Billing** scaffolding  
   * Checkout Session endpoint (`/api/billing/checkout`).
   * Webhook to update `tier`/`tierExpiresAt` on payment success.
   * Prices in **GYD** & **USD**.
4. **Admin toggle** for `ENABLE_TIER_LIMITS` (DB or Redis flag).
5. **Seed data & QA** – Starter vs Pro businesses.

---
## 6  Environment Variables
Add to `.env` (defaults shown):
```env
# Feature gating flag – keep OFF until ready
ENABLE_TIER_LIMITS=false

# Stripe (example)
STRIPE_SECRET_KEY=sk_...
STRIPE_PRICE_PRO_USD=price_...
STRIPE_PRICE_PRO_GYD=price_...
```

---
## 7  Testing Checklist
- [ ] Starter business can read reviews but cannot reply when limits enabled.
- [ ] Pro business can reply normally.
- [ ] Analytics tab hides/shows based on tier.
- [ ] 402 errors are caught and surface upgrade CTA.

---
## 8  FAQs
**Q:** Do we need data before enabling tiers?  
**A:** Schema works on an empty DB; seed data is only required for manual QA.

**Q:** Are Cypress/E2E tests planned?  
**A:** Per decision, no Cypress flows for upgrade/downgrade.

---
End of guide.
