# Widget System Guide for Business Owners

## 1. Overview
Welcome to the Review-it Widget System! This guide will walk you through everything you need to know about creating, customizing, and embedding widgets on your own website. Our widgets are designed to help you showcase your hard-earned reviews, build trust with potential customers, and drive more business.

This system allows you to:
- Display reviews for your entire business or for specific products.
- Choose from various widget types like carousels, grids, and badges.
- Customize the look and feel to match your brand.
- Track how many people see and interact with your widgets.

## 2. Getting Started: Creating Your First Widget
Creating a widget is a simple process. Follow these steps to get your first widget up and running.

### Step 1: Navigate to the Widget Management Page
- Go to your **Owner Admin** dashboard.
- In the sidebar navigation, click on **"Widgets"**.
- This will take you to the main widget management page, where you'll see a list of your existing widgets (if any) and the option to create a new one.

### Step 2: Create a New Widget
- Click the **"Create New Widget"** button.
- This will launch the Widget Creator, a step-by-step wizard that guides you through the process.

### Step 3: Choose a Widget Type
You'll be presented with several widget types. Choose the one that best fits your needs:
- **Carousel:** A rotating display of your best reviews.
- **Grid:** A clean, organized grid layout of multiple reviews.
- **Badge:** A compact badge that shows off your overall rating.
- **And more...** Select a type to see a live preview.

### Step 4: Customize Your Widget
Here's where you can make the widget your own. The preview will update in real-time as you make changes.
- **Name:** Give your widget a descriptive name (e.g., "Homepage Review Carousel").
- **Content:** Choose to show reviews for the whole business or a single product.
- **Theme & Colors:** Select a light or dark theme, and set a primary color to match your brand.
- **Display Options:** Choose what information to show or hide, such as the business logo, review text, date, and more.

### Step 5: Save Your Widget
- Once you're happy with the preview, click **"Save Widget"**.
- Your new widget will now appear in your widget list.

## 3. Embedding Your Widget on Your Website
Now that you've created a widget, it's time to add it to your website.

### Step 1: Get the Embed Code
- On the **Widget Management Page**, find the widget you want to embed.
- Click the **"Get Code"** button next to it.
- A dialog will appear with two code snippets.

### Step 2: Add the Script to Your Website
- Copy the first code snippet, which looks like this:
  ```html
  <script src="https://reviewit.gy/widgets/embed.js" async defer></script>
  ```
- Paste this script tag just before the closing `</body>` tag in your website's HTML. You only need to do this once, even if you have multiple widgets.

### Step 3: Place the Widget
- Copy the second code snippet. It will be a `<div>` element with a unique ID for your widget:
  ```html
  <div data-reviewit-widget="YOUR_WIDGET_ID"></div>
  ```
- Paste this `<div>` exactly where you want the widget to appear on your page.

That's it! The script will automatically load your widget into the `<div>`.

### Optional: Customizing Widget Size
You can control the widget's size directly from the `<div>` element if needed.
- `data-width="500px"` or `data-width="100%"`
- `data-height="600px"`

Example:
```html
<div data-reviewit-widget="YOUR_WIDGET_ID" data-width="100%" data-height="500px"></div>
```

## 4. Tracking Widget Performance (Analytics)
Understanding how your widgets are performing is crucial.
- On the **Widget Management Page**, you can see key stats for each widget at a glance:
    - **Views:** How many times your widget has been loaded on a page.
    - **Clicks:** How many times users have clicked on your widget.
- For a more detailed breakdown, click the **"Analytics"** button for any widget.

## 5. Managing Your Widgets
From the main widget page, you can easily:
- **Edit:** Click the edit icon to open the Widget Creator and make changes.
- **Delete:** Click the delete icon to permanently remove a widget.
- **Preview:** See how your widget looks and functions.
