# Cron Job Analysis & Setup Guide

## 📋 Current Cron Job Status

### ✅ What You Have (The Good News)

**1. Cron Job API Endpoint** - `src/app/api/cron/calculate-analytics/route.ts`
```typescript
export async function GET() {
  const businesses = await prisma.business.findMany({ select: { id: true } });
  for (const business of businesses) {
    await calculateBusinessGrowthMetrics(business.id);
  }
  return NextResponse.json({ ok: true });
}
```

**What it does:**
- ✅ Fetches all businesses from the database
- ✅ Runs analytics calculations for each business
- ✅ Updates the BusinessAnalytics table with fresh metrics
- ✅ Returns a success response

**2. Analytics Engine** - Fully implemented and working
- ✅ Calculates average response time
- ✅ Calculates negative review response rate  
- ✅ Calculates content freshness score
- ✅ Saves results to database

### ⚠️ What's Missing (The Setup)

**Cron Job Scheduling Configuration**

You're using **Azure Static Web Apps** (based on your GitHub workflows), but your app appears to be a full Next.js app with API routes, not a static site. Here are your options:

## 🛠️ Cron Job Setup Options

### Option 1: Linux Cron (RECOMMENDED for Ubuntu + PM2 + Caddy)
Since you're deploying on Ubuntu server, use the built-in Linux cron system:

**Setup Steps:**

1. **Edit the crontab:**
```bash
sudo crontab -e
```

2. **Add the cron job:**
```bash
# Daily analytics calculation at 2 AM
0 2 * * * curl -s http://localhost:3000/api/cron/calculate-analytics >/dev/null 2>&1

# Alternative with logging:
0 2 * * * curl -s http://localhost:3000/api/cron/calculate-analytics >> /var/log/analytics-cron.log 2>&1
```

3. **Verify cron is running:**
```bash
sudo systemctl status cron
sudo crontab -l  # List current cron jobs
```

**Why this is perfect for your setup:**
- ✅ No external dependencies
- ✅ Runs locally on your server
- ✅ Free and reliable
- ✅ Easy to monitor and debug
- ✅ Works with your PM2 + Caddy setup

### Option 2: Move to Vercel (If Possible)
If you can deploy to Vercel instead, create `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/cron/calculate-analytics",
      "schedule": "0 2 * * *"
    }
  ]
}
```

### Option 3: Azure Functions (More Complex)
Create a separate Azure Function that calls your API endpoint on a schedule.

## 🔧 Quick Setup Steps for Ubuntu Server

**Recommended: Linux Cron (Perfect for your PM2 + Caddy setup)**

1. **SSH into your Ubuntu server**
2. **Edit the system crontab:**
   ```bash
   sudo crontab -e
   ```
3. **Add the analytics cron job:**
   ```bash
   # Analytics calculation - daily at 2 AM
   0 2 * * * curl -s http://localhost:3000/api/cron/calculate-analytics >> /var/log/analytics-cron.log 2>&1
   ```
4. **Save and exit** (Ctrl+X, then Y, then Enter in nano)
5. **Verify it's scheduled:**
   ```bash
   sudo crontab -l
   ```

**Advanced Setup with Better Logging:**
```bash
# Create log directory
sudo mkdir -p /var/log/reviewit

# Add to crontab with better logging
0 2 * * * /usr/bin/curl -s -w "Time: \%{time_total}s, Status: \%{http_code}\n" http://localhost:3000/api/cron/calculate-analytics >> /var/log/reviewit/analytics-cron.log 2>&1
```

## 🔒 Security Considerations

Your current endpoint is public. Consider adding authentication:

```typescript
export async function GET(request: NextRequest) {
  // Add API key authentication
  const authHeader = request.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Your existing logic...
}
```

## 🧪 Testing Your Cron Job

You can test it manually right now:
```bash
curl https://yourdomain.com/api/cron/calculate-analytics
```

## 📝 Implementation Notes

### Current Hosting Setup
- Using Azure Static Web Apps (based on GitHub workflows)
- Multiple deployment environments:
  - azure-static-web-apps-white-ocean-0b3b1460f.yml
  - azure-static-web-apps-wonderful-rock-0fcb07710.yml  
  - azure-static-web-apps-yellow-moss-06ebc3610.yml

### Ecosystem Configuration
- PM2 ecosystem.config.js present
- Next.js app with API routes
- PostgreSQL database with Prisma

### Cron Schedule Explanation
- `0 2 * * *` = Every day at 2:00 AM UTC
- `0 */6 * * *` = Every 6 hours
- `0 0 * * 0` = Every Sunday at midnight
- `0 1 1 * *` = First day of every month at 1 AM

### Performance Considerations
- Current implementation processes businesses sequentially
- For large numbers of businesses, consider:
  - Batch processing
  - Parallel processing with Promise.all()
  - Rate limiting to avoid database overload
  - Timeout handling for long-running calculations

### Monitoring & Logging
Consider adding:
- Execution time logging
- Success/failure tracking
- Error notifications
- Metrics on number of businesses processed

### Alternative Approaches
1. **Real-time Updates**: Trigger calculations immediately after events
2. **Incremental Updates**: Only recalculate for businesses with recent activity
3. **Background Jobs**: Use a job queue system like Bull/BullMQ
4. **Serverless Functions**: Separate analytics service

## 🚀 Next Steps
1. Choose a cron scheduling option
2. Add security to the endpoint
3. Test the implementation
4. Monitor execution and performance
5. Consider optimizations for scale