# Cache Invalidation Fix Guide

## Issue
The `topReviewers` cache is not being properly invalidated when a new review is created. This is because the wrong cache invalidation function is being used in the review creation endpoint.

## Current Implementation
Currently, `src/app/api/create/review/route.ts` is using a non-existent function `invalidateAdminCacheOnReviewChange` instead of the correct function `invalidateAggregatedCachesOnReviewChange`.

## Required Changes

### 1. Update Import Statement
In `src/app/api/create/review/route.ts`, change:
```typescript
import { invalidateAdminCacheOnReviewChange } from "@/app/util/databaseAnalytics";
```
to:
```typescript
import { invalidateAggregatedCachesOnReviewChange } from "@/app/util/databaseAnalytics";
```

### 2. Update Function Call
In the same file, change:
```typescript
await invalidateAdminCacheOnReviewChange();
```
to:
```typescript
await invalidateAggregatedCachesOnReviewChange();
```

## Why This Fixes the Issue
The `invalidateAggregatedCachesOnReviewChange` function properly invalidates all relevant caches, including:
- Admin recent reviews
- Admin review stats
- Admin dashboard metrics
- Popular reviews
- Trending reviews
- Latest reviews
- Top reviewers (with limit 6)

## Verification
After making these changes:
1. Create a new review
2. Check the Redis cache to verify the `topReviewers` cache is invalidated
3. Verify the TopReviewers component shows updated data

## Additional Notes
- The `invalidateAggregatedCachesOnReviewChange` function is already implemented in `src/app/util/databaseAnalytics.ts`
- This function uses `safeRedisDel` to handle cache invalidation safely
- The cache TTL for top reviewers is set to 30 minutes (1800 seconds) 