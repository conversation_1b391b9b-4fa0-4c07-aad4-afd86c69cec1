# Widget System Documentation

This guide provides a comprehensive overview of the widget system, from creation to embedding and analytics.

## 1. Overview

The widget system allows business owners to create, customize, and embed various types of widgets on their websites. These widgets display business information, reviews, and ratings, helping to build trust and attract customers.

## 2. Key Features

*   **Multiple Widget Types:** A variety of widget types are available, including carousels, grids, and badges.
*   **Customization:** Widgets can be customized to match the look and feel of the host website.
*   **Easy Embedding:** Widgets can be easily embedded on any website with a simple script.
*   **Analytics:** The system tracks widget views and clicks, providing valuable insights into performance.
*   **Business & Product Specific:** Widgets can be created for a whole business or for specific products.

## 3. Components

The widget system is composed of several key components:

*   **Widget Management Page (`/owner-admin/widgets`):** The central hub for business owners to manage their widgets.
    *   **Widget List:** Displays all widgets for the selected business, along with key stats.
    *   **Widget Creator:** A step-by-step wizard for creating new widgets.
    *   **Widget Preview:** Allows users to see how their widget will look before embedding it.
    *   **Widget Analytics:** Shows detailed analytics for each widget.

*   **Widget Renderer (`/src/app/components/widgets/WidgetRenderer.tsx`):** This component is responsible for rendering the correct widget type based on the widget's configuration.

*   **Widget Types (`/src/app/components/widgets/types/`):** Each widget type has its own component that defines its structure and appearance.
    *   `BusinessCardWidget.tsx`
    *   `MiniReviewWidget.tsx`
    *   `RatingSummaryWidget.tsx`
    *   `ReviewCarouselWidget.tsx`
    *   `ReviewGridWidget.tsx`
    *   `ReviewPopupWidget.tsx`
    *   `TrustBadgeWidget.tsx`

*   **Embed Script (`/public/widgets/embed.js`):** A JavaScript file that handles the embedding of widgets on external websites.

## 4. How it Works

1.  **Creation:** A business owner creates a new widget using the **Widget Creator** on the **Widget Management Page**. They select a widget type, customize its appearance, and choose what content to display.
2.  **Configuration:** The widget's configuration is saved to the database.
3.  **Embedding:** The business owner is provided with a code snippet to embed the widget on their website.
4.  **Rendering:** When a user visits the website with the embedded widget, the `embed.js` script creates an `iframe` that loads the widget from the Review-it platform. The `WidgetRenderer` component then renders the appropriate widget type within the `iframe`.
5.  **Analytics:** The `embed.js` script and the `WidgetRenderer` work together to track widget views and clicks, which are then displayed on the **Widget Analytics** page.

## 5. Embedding

To embed a widget, add the following script tag to your website's HTML:

```html
<script src="https://reviewit.gy/widgets/embed.js" async defer></script>
```

Then, create a `div` element with the `data-reviewit-widget` attribute set to the ID of your widget:

```html
<div data-reviewit-widget="YOUR_WIDGET_ID"></div>
```

The `embed.js` script will automatically find this `div` and load the widget into it.

### Customization via Data Attributes

You can customize the widget's dimensions directly from the `div` element:

*   `data-width`: Sets the width of the widget (e.g., "100%", "300px").
*   `data-height`: Sets the height of the widget (e.g., "auto", "500px").
*   `data-max-height`: Sets the maximum height of the widget (e.g., "600px").

## 6. Customization

Widgets can be customized in the **Widget Creator**. The following options are available:

*   **Name:** A descriptive name for the widget.
*   **Product:** Choose to display reviews for the entire business or a specific product.
*   **Theme:** Light, dark, or custom.
*   **Primary Color:** The main color used for buttons, links, and other interactive elements.
*   **Max Reviews:** The maximum number of reviews to display.
*   **Content Options:**
    *   Show/hide the business logo.
    *   Show/hide the "Powered by Review-it" attribution.
    *   Show/hide the rating.
    *   Show/hide the review text.
    *   Show/hide the review date.
    *   Show/hide the reviewer's name.

## 7. Analytics

The widget system tracks the following metrics:

*   **Views:** The number of times a widget has been loaded on a page.
*   **Clicks:** The number of times a user has clicked on a widget.
*   **Click Rate:** The percentage of views that result in a click.

These analytics are available on the **Widget Management Page** and on the dedicated **Widget Analytics** page for each widget.

## 8. For Developers

### Creating a New Widget Type

To create a new widget type, follow these steps:

1.  **Create the component:** Create a new React component in the `/src/app/components/widgets/types/` directory. This component will define the structure and appearance of your new widget.
2.  **Add to `WidgetRenderer`:** Import your new component into `/src/app/components/widgets/WidgetRenderer.tsx` and add a new `case` to the `switch` statement to render your component when the corresponding widget type is selected.
3.  **Add to `WidgetCreator`:** Add the new widget type to the `WIDGET_TYPES` array in `/src/app/components/widgets/WidgetCreator.tsx`. This will make the new widget type available in the **Widget Creator**.
4.  **Update `WidgetType` enum:** Add the new widget type to the `WidgetType` enum in `/src/app/util/Interfaces.ts`.
