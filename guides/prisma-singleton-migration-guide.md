# Prisma Singleton Migration Guide

## ✅ COMPLETED - Implementation Status
**Migration completed successfully on [Current Date]**

All 13 problematic files have been migrated to use the singleton pattern. The unused database pool file has also been removed.

## Overview
This guide documents the migration from multiple PrismaClient instances to a single singleton pattern to prevent PostgreSQL connection pool exhaustion and improve performance.

## Problem
Multiple files are creating new `PrismaClient()` instances instead of using the existing singleton pattern, which can lead to:
- Connection pool exhaustion
- Performance degradation
- Inconsistent database connection management
- Potential memory leaks

## Solution
Replace direct PrismaClient instantiation with imports from the singleton wrapper.

## Current Singleton Implementation
The correct singleton pattern is already implemented in `src/app/util/prismaClient.ts`:

```typescript
import { PrismaClient } from "@prisma/client";

const prismaClientSingleton = () => {
  return new PrismaClient();
};

declare global {
  var prisma: undefined | ReturnType<typeof prismaClientSingleton>;
}

export const prisma = globalThis.prisma ?? prismaClientSingleton();

if (process.env.NODE_ENV !== "production") globalThis.prisma = prisma;
```

## Files Requiring Migration

### Admin API Routes (High Priority)

#### 1. `src/app/api/admin/reviews/analytics/route.ts`
**Current (Lines 2-7):**
```typescript
import { PrismaClient } from '@prisma/client';
import { subDays, startOfDay, endOfDay } from 'date-fns';

export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { subDays, startOfDay, endOfDay } from 'date-fns';
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';
```

#### 2. `src/app/api/admin/reviews/queue/route.ts`
**Current (Lines 2-6):**
```typescript
import { PrismaClient } from '@prisma/client';

export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';
```

#### 3. `src/app/api/admin/users/[id]/route.ts`
**Current (Lines 2-4):**
```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```

#### 4. `src/app/api/admin/users/[id]/role/route.ts`
**Current (Line 4):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 5. `src/app/api/admin/users/[id]/status/route.ts`
**Current (Line 4):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 6. `src/app/api/admin/users/[id]/activity/route.ts`
**Current (Line 6):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 7. `src/app/api/admin/auth/login/route.ts`
**Current (Line 8):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 8. `src/app/api/admin/auth/verify/route.ts`
**Current (Line 7):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 9. `src/app/api/admin/reports/route.ts`
**Current (Line 8):**
```typescript
const prismaClient = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also update variable name from `prismaClient` to `prisma` throughout the file*

#### 10. `src/app/api/admin/products/[id]/status/route.ts`
**Current (Line 5):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

#### 11. `src/app/api/admin/products/[id]/route.ts`
**Current (Line 7):**
```typescript
const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```
*Note: Also remove the PrismaClient import*

### Utility Files

#### 12. `src/app/util/admin-setup.ts`
**Current (Lines 0-3):**
```typescript
import { PrismaClient } from '@prisma/client';
import { UserDATA } from './Interfaces';

const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { UserDATA } from './Interfaces';
import { prisma } from './prismaClient';
```

### Test Files (Lower Priority)

#### 13. `src/app/api/test-prisma.ts`
**Current (Lines 0-2):**
```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
```

**Should be:**
```typescript
import { prisma } from '@/app/util/prismaClient';
```

## Migration Steps

### For each file:

1. **Remove the PrismaClient import:**
   ```typescript
   // Remove this line
   import { PrismaClient } from '@prisma/client';
   ```

2. **Remove the prisma instantiation:**
   ```typescript
   // Remove this line
   const prisma = new PrismaClient();
   ```

3. **Add the singleton import:**
   ```typescript
   // Add this line
   import { prisma } from '@/app/util/prismaClient';
   ```

4. **Special case for admin/reports/route.ts:**
   - Variable is named `prismaClient` instead of `prisma`
   - Need to rename all references from `prismaClient` to `prisma`

## Additional Cleanup

### Remove Unused Database Pool
The file `src/app/util/db.ts` contains an unused PostgreSQL pool connection:

```typescript
import { Pool } from "pg";

const pool = new Pool({
  connectionString: process.env.BUG_DATABASE_URL,
});

export default pool;
```

**Action:** This file can be deleted if not used elsewhere, or documented if it serves a specific purpose.

## Verification Steps

After migration:

1. **Search for remaining instances:**
   ```bash
   grep -r "new PrismaClient" src/
   ```

2. **Verify imports:**
   ```bash
   grep -r "from '@/app/util/prismaClient'" src/
   ```

3. **Test database connections:**
   - Run the application
   - Check for connection pool warnings
   - Monitor database connection count

## Benefits After Migration

- ✅ Single database connection pool
- ✅ Improved performance
- ✅ Consistent codebase
- ✅ Follows Prisma best practices
- ✅ Prevents connection exhaustion
- ✅ Better memory management

## Files Already Using Correct Pattern

The following files already correctly use the singleton pattern (no changes needed):

- `src/app/util/addUserToDb.ts`
- `src/app/api/create/review/route.ts`
- `src/app/api/create/product/route.ts`
- `src/app/api/get/product/route.ts`
- And 40+ other files

## Estimated Time

- **Total migration time:** 15-20 minutes
- **Per file:** 1-2 minutes
- **Testing:** 5 minutes

## Risk Assessment

- **Risk Level:** Very Low
- **Impact:** Performance improvement only
- **Rollback:** Simple (revert changes)
- **Dependencies:** None