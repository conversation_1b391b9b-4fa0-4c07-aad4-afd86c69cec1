# Widget Domain Management Implementation Guide

## Current Status Assessment

### ✅ What's Already Implemented

#### Backend Infrastructure (Complete)
- **Domain Management API**: Full CRUD operations at `/api/widgets/[widgetId]/domains/route.ts`
- **Database Schema**: `allowedDomains` field in Widget model (string array)
- **Domain Validation**: Referrer checking in public widget API
- **CORS Support**: Full cross-origin support for widget embedding
- **Preview Mode**: Domain bypass for same-domain previews
- **Auto-Configuration**: New widgets automatically include current domain

#### Widget System Core (Complete)
- **Widget Management Page**: `/owner-admin/widgets` with full CRUD
- **Widget Creator**: Step-by-step creation wizard
- **Widget Types**: 7 different widget types implemented
- **Analytics Tracking**: View/click tracking with referrer data
- **Embed Script**: Dynamic domain-aware embedding

### ❌ What's Missing: Frontend Domain Management UI

The **only missing piece** is the user interface for business owners to manage allowed domains for their widgets.

## Required Implementation

### 1. Domain Management Component

**File to Create**: `src/app/components/widgets/DomainManagement.tsx`

```typescript
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Globe, 
  Plus, 
  Trash2, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { iWidget } from "@/app/util/Interfaces";

interface DomainManagementProps {
  widget: iWidget;
  onDomainsUpdated: (domains: string[]) => void;
}

export function DomainManagement({ widget, onDomainsUpdated }: DomainManagementProps) {
  const [domains, setDomains] = useState<string[]>(widget.allowedDomains || []);
  const [newDomain, setNewDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const validateDomain = (domain: string): boolean => {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain.toLowerCase());
  };

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast.error("Please enter a domain");
      return;
    }

    const cleanDomain = newDomain.toLowerCase().trim();
    
    if (!validateDomain(cleanDomain)) {
      toast.error("Please enter a valid domain (e.g., example.com)");
      return;
    }

    if (domains.includes(cleanDomain)) {
      toast.error("Domain already exists");
      return;
    }

    if (domains.length >= 50) {
      toast.error("Maximum 50 domains allowed per widget");
      return;
    }

    setValidating(true);
    try {
      const updatedDomains = [...domains, cleanDomain];
      await updateDomains(updatedDomains);
      setNewDomain("");
      toast.success("Domain added successfully");
    } catch (error) {
      toast.error("Failed to add domain");
    } finally {
      setValidating(false);
    }
  };

  const removeDomain = async (domainToRemove: string) => {
    try {
      const updatedDomains = domains.filter(d => d !== domainToRemove);
      await updateDomains(updatedDomains);
      setDeleteConfirm(null);
      toast.success("Domain removed successfully");
    } catch (error) {
      toast.error("Failed to remove domain");
    }
  };

  const updateDomains = async (newDomains: string[]) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/domains`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ domains: newDomains }),
      });

      if (!response.ok) {
        throw new Error('Failed to update domains');
      }

      const data = await response.json();
      if (data.success) {
        setDomains(newDomains);
        onDomainsUpdated(newDomains);
      } else {
        throw new Error(data.error || 'Failed to update domains');
      }
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Globe className="w-4 h-4" />
          Domain Settings
          {domains.length > 0 && (
            <Badge variant="secondary" className="ml-1">
              {domains.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Domain Management - {widget.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Info Section */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Globe className="w-4 h-4 text-blue-600" />
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Domain Allowlist</h4>
                  <p className="text-sm text-muted-foreground">
                    Control which websites can embed this widget. Only domains in this list will be able to display your widget.
                    {domains.length === 0 && " Currently, any domain can embed this widget."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Add Domain Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Add New Domain</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="domain">Domain</Label>
                  <Input
                    id="domain"
                    placeholder="example.com"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addDomain()}
                    disabled={validating || loading}
                  />
                </div>
                <div className="flex items-end">
                  <Button 
                    onClick={addDomain} 
                    disabled={validating || loading || !newDomain.trim()}
                    className="gap-2"
                  >
                    {validating ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Plus className="w-4 h-4" />
                    )}
                    Add
                  </Button>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Enter domain without protocol (e.g., "example.com" not "https://example.com")
              </p>
            </CardContent>
          </Card>

          {/* Current Domains */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Allowed Domains
                <Badge variant="outline">
                  {domains.length}/50
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {domains.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h4 className="font-medium mb-2">No Domain Restrictions</h4>
                  <p className="text-sm text-muted-foreground">
                    This widget can be embedded on any website. Add domains to restrict embedding.
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {domains.map((domain) => (
                    <div key={domain} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="font-mono text-sm">{domain}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeleteConfirm(domain)}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <h4 className="font-medium text-yellow-800">Security Notice</h4>
                  <p className="text-sm text-yellow-700">
                    Domain restrictions help prevent unauthorized use of your widgets. 
                    Only add domains you trust and control.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!deleteConfirm} onOpenChange={() => setDeleteConfirm(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Domain</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove <strong>{deleteConfirm}</strong> from the allowed domains?
                This domain will no longer be able to embed this widget.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => deleteConfirm && removeDomain(deleteConfirm)}
                className="bg-red-600 hover:bg-red-700"
              >
                Remove Domain
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DialogContent>
    </Dialog>
  );
}
```

### 2. Integration with Widget List

**Update**: `src/app/components/widgets/WidgetList.tsx`

Add the domain management button to the widget actions dropdown:

```typescript
// Add import
import { DomainManagement } from "./DomainManagement";

// Add to the dropdown menu in the widget card:
<DropdownMenuItem onClick={() => {/* handle domain management */}}>
  <Globe className="w-4 h-4 mr-2" />
  Domain Settings
</DropdownMenuItem>

// Or add as a separate button:
<DomainManagement 
  widget={widget} 
  onDomainsUpdated={(domains) => {
    // Update the widget in the list
    onWidgetUpdated({...widget, allowedDomains: domains});
  }} 
/>
```

### 3. Integration with Widget Creator

**Update**: `src/app/components/widgets/WidgetCreator.tsx`

Add domain configuration as an optional step in the creation wizard:

```typescript
// Add to form state
const [allowedDomains, setAllowedDomains] = useState<string[]>([]);

// Add domain configuration step (optional)
// Include in the CreateWidgetRequest when submitting
```

### 4. Widget Settings/Edit Component

**File to Create**: `src/app/components/widgets/WidgetSettings.tsx`

A comprehensive settings dialog that includes:
- Basic widget configuration
- Domain management
- Analytics settings
- Advanced options

## Implementation Priority

### Phase 1: Essential (1-2 days)
1. ✅ **DomainManagement Component** - Core functionality
2. ✅ **Integration with WidgetList** - Add domain settings button
3. ✅ **Basic validation and error handling**

### Phase 2: Enhanced (1 day)
1. **Widget Settings Dialog** - Comprehensive settings interface
2. **Domain validation improvements** - Real-time validation
3. **Bulk domain operations** - Import/export functionality

### Phase 3: Advanced (Optional)
1. **Domain analytics** - Per-domain performance metrics
2. **Domain-specific configurations** - Different settings per domain
3. **Advanced security features** - IP restrictions, etc.

## Manual Testing Guide

### Core Functionality
- [ ] Add valid domains (e.g., "example.com")
- [ ] Remove domains using the trash icon
- [ ] Test domain format validation (reject invalid formats)
- [ ] Try adding duplicate domains (should be rejected)
- [ ] Test the 50-domain limit
- [ ] Verify empty state displays correctly

### Widget Embedding
- [ ] Test widget embedding on allowed domains
- [ ] Verify widget is blocked on non-allowed domains
- [ ] Confirm preview mode works regardless of domain restrictions
- [ ] Check that blocked attempts are tracked in analytics

### User Experience
- [ ] Test responsive design on mobile/tablet
- [ ] Verify loading states during API calls
- [ ] Test error handling for network failures
- [ ] Confirm deletion confirmation dialog works
- [ ] Check keyboard navigation and accessibility

## Security Considerations

1. **Input Validation**: All domain inputs are validated on both client and server
2. **Rate Limiting**: Domain management API has rate limiting
3. **Authorization**: Only widget owners can manage domains
4. **Audit Trail**: Domain changes are logged for security

## Business Impact

### Benefits
- **Security**: Prevent unauthorized widget usage
- **Brand Protection**: Control where widgets appear
- **Analytics**: Better tracking of legitimate usage
- **Compliance**: Meet enterprise security requirements

### Use Cases
- **Enterprise Clients**: Strict domain controls
- **Brand Protection**: Prevent widget hijacking
- **Partner Networks**: Controlled distribution
- **Testing**: Separate staging/production domains

## Conclusion

The widget domain management system is **99% complete** with only the frontend UI missing. The backend infrastructure is robust and production-ready. Implementing the DomainManagement component will provide business owners with full control over widget embedding while maintaining security and ease of use.

The implementation is straightforward and can be completed in 1-2 days, providing immediate value to business owners who need domain restrictions for security or branding purposes.