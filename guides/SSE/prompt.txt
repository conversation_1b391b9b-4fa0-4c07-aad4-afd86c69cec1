I need you to implement Server-Sent Events (SSE) for real-time notifications in my Next.js application. 

Please read the attached STARTER.md file completely - it contains everything you need to know about:
- Current implementation using polling
- Files that need to be modified
- Step-by-step implementation tasks
- What NOT to break
- Testing requirements

Follow the implementation order exactly as specified in STARTER.md:
1. Environment setup first
2. Replace polling in NavNotification.tsx
3. Update AllNotifications.tsx  
4. Test thoroughly

The implementation files (sseNotificationService.ts and useSSENotifications.ts) are already created and production-ready. Your job is to integrate them into the existing components following the migration guide.

Maintain all existing functionality - this should be invisible to users except for improved performance (instant notifications instead of 5-second delays).

Start with TASK 1 and work through each task systematically. Ask questions if anything in STARTER.md is unclear.