# 🚀 SSE Implementation Starter Guide for LLM

## 📋 Context & Objective

You are tasked with implementing Server-Sent Events (SSE) for real-time notifications in a Next.js ReviewIt application. The app currently uses React Query with 5-second polling, and you need to replace this with SSE while maintaining all existing functionality.

## 🎯 Current State Analysis

### Existing Implementation:
- **Polling-based notifications** in `NavNotification.tsx` (refetchInterval: 5000ms)
- **Jotai state management** with `ownerNotificationsAtom` and `userNotificationsAtom`
- **External notification server** via environment variables
- **Two notification types**: Owner notifications and User notifications
- **React Query** for data fetching with fallback strategies

### Key Files Currently Using Polling:
```
src/app/components/notification-components/NavNotification.tsx
src/app/components/notification-components/AllNotifications.tsx
src/app/util/NotificationFunctions.ts
src/app/util/serverFunctions.ts
```

## 📁 Implementation Files Provided

### Core Implementation Files (Already Created):
1. **`sseNotificationService.ts`** - Complete SSE service class with:
   - Connection management and auto-reconnection
   - Integration with Jotai atoms
   - Error handling and fallback logic
   - Browser notification support

2. **`useSSENotifications.ts`** - React hook that:
   - Manages SSE connection lifecycle
   - Provides fallback to polling
   - Returns connection status and data
   - Integrates with existing React Query setup

### Guide Files (Reference Only):
- **`README.md`** - Project overview and file structure
- **`environment-setup.md`** - Environment configuration requirements
- **`migration-checklist.md`** - Step-by-step implementation plan
- **`frontend-sse-integration.md`** - Detailed technical documentation
- **`quick-frontend-setup.md`** - Testing examples

## 🔧 Implementation Tasks

### TASK 1: Environment Setup (CRITICAL FIRST STEP)
**Objective**: Configure notification server URLs

**Action Required**:
1. Verify `.env.example` has been updated with:
   ```bash
   NEXT_PUBLIC_NOTIFICATION_SERVER="http://localhost:3001"
   NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL="http://localhost:3001"
   ```
2. Ensure user's `.env.local` contains these variables
3. Verify notification server is accessible at the specified URL

### TASK 2: Replace Polling in NavNotification.tsx
**Objective**: Replace React Query polling with SSE hook

**Current Code Pattern to Replace**:
```tsx
const { data: notificationsData, isLoading, isError } = useQuery({
  queryKey: ["notifications", auth.userId],
  queryFn: async () => { /* fetch logic */ },
  refetchInterval: 5000, // ❌ REMOVE THIS POLLING
  // ... other options
});
```

**Replace With**:
```tsx
const { 
  notifications, 
  isSSEConnected, 
  isLoading, 
  error,
  isUsingPolling 
} = useSSENotifications(auth.userId);
```

**Specific Changes Needed**:
1. Import the `useSSENotifications` hook
2. Replace the useQuery call
3. Update variable references (`notificationsData` → `notifications`)
4. Add connection status indicator (optional but recommended)
5. Keep all existing UI logic and state management

### TASK 3: Update AllNotifications.tsx
**Objective**: Add real-time updates and connection status

**Changes Needed**:
1. Import and use `useSSENotifications` hook
2. Add connection status indicator:
   ```tsx
   {isSSEConnected ? (
     <Badge variant="success">🟢 Live</Badge>
   ) : (
     <Badge variant="warning">🟡 Polling</Badge>
   )}
   ```
3. Ensure real-time updates work with existing filtering and search

### TASK 4: Testing & Validation
**Objective**: Verify SSE implementation works correctly

**Testing Checklist**:
1. **Connection Test**: Open browser dev tools → Network tab → Look for EventStream connection
2. **Real-time Test**: Create test notification → Should appear instantly
3. **Fallback Test**: Disconnect internet → Should fallback to polling
4. **Multi-tab Test**: Open multiple tabs → All should receive notifications
5. **Existing Functionality**: Verify all current features still work

## 🚨 Critical Implementation Notes

### DO NOT BREAK:
- **Existing Jotai state management** - The SSE service integrates with existing atoms
- **Current UI components** - All existing components should work unchanged
- **Notification creation logic** - Keep existing `NotificationFunctions.ts` as-is
- **Mark as read functionality** - Should work with both SSE and polling

### MAINTAIN COMPATIBILITY:
- **Fallback to polling** if SSE fails
- **Error handling** for network issues
- **Reconnection logic** for dropped connections
- **Browser compatibility** (EventSource support check)

## 🔍 Key Integration Points

### Jotai Atoms Integration:
The SSE service automatically updates these existing atoms:
```tsx
ownerNotificationsAtom  // For product owner notifications
userNotificationsAtom   // For user reply notifications
```

### Environment Variables Used:
```bash
NEXT_PUBLIC_NOTIFICATION_SERVER      # For reading notifications (SSE endpoint)
NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL # For creating notifications
```

### SSE Endpoint Expected:
```
GET /notifications/stream?user_id={userId}
```

## 📊 Expected Performance Improvements

**Before (Polling)**:
- ~720 requests per hour per user
- 5-second delay for new notifications
- Constant server load

**After (SSE)**:
- ~1 request per hour per user (just the SSE connection)
- Instant notifications (<100ms)
- Minimal server load

## 🛠️ Implementation Order

1. **Start with TASK 1** (Environment setup) - Nothing works without this
2. **Implement TASK 2** (NavNotification.tsx) - Core functionality
3. **Implement TASK 3** (AllNotifications.tsx) - Enhanced features
4. **Execute TASK 4** (Testing) - Validation

## 🔧 Debugging Tips

### If SSE Connection Fails:
1. Check environment variables are set correctly
2. Verify notification server is running and accessible
3. Check browser console for EventSource errors
4. Confirm fallback to polling is working

### If Notifications Don't Appear:
1. Verify Jotai atoms are being updated
2. Check SSE message format matches expected structure
3. Ensure existing UI components are reading from atoms correctly

## 📝 Success Criteria

✅ **SSE connection established** (visible in Network tab as EventStream)
✅ **Real-time notifications working** (instant delivery)
✅ **Fallback to polling functional** (when SSE fails)
✅ **All existing features preserved** (UI, filtering, marking as read)
✅ **Performance improved** (reduced server requests)
✅ **Multi-tab support working** (all tabs receive notifications)

## 🚀 Ready to Start?

1. **Read this entire document** to understand the scope
2. **Examine the provided implementation files** (`sseNotificationService.ts` and `useSSENotifications.ts`)
3. **Start with TASK 1** (environment setup)
4. **Follow the implementation order** strictly
5. **Test after each task** to ensure nothing breaks

The implementation files are production-ready and integrate seamlessly with the existing codebase. Focus on the integration points and maintain backward compatibility.

---

**Remember**: The goal is to enhance the user experience with real-time notifications while keeping all existing functionality intact. The SSE implementation should be invisible to users except for the improved performance.