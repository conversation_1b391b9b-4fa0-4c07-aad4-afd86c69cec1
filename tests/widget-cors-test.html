<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget CORS Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f9fafb;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .widget-demo {
            border: 2px dashed #d1d5db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <h1>ReviewIt Widget CORS Testing</h1>
    <p>This page tests the CORS configuration and widget embedding functionality.</p>
    
    <div class="test-section">
        <h2>API CORS Tests</h2>
        <button onclick="testWidgetAPI()">Test Widget Data API</button>
        <button onclick="testTrackingAPI()">Test Tracking API</button>
        <button onclick="testManagementAPI()">Test Management API</button>
        <div id="api-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Widget Embed Test</h2>
        <p>Enter a widget ID to test embedding:</p>
        <input type="text" id="widget-id" placeholder="Enter widget ID" style="padding: 8px; margin: 5px; border: 1px solid #d1d5db; border-radius: 4px;">
        <button onclick="embedWidget()">Embed Widget</button>
        <button onclick="clearWidget()">Clear Widget</button>
        
        <div class="widget-demo" id="widget-container">
            <p style="text-align: center; color: #6b7280;">Widget will appear here</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Rate Limiting Test</h2>
        <button onclick="testRateLimit()">Test Rate Limiting (10 requests)</button>
        <div id="rate-limit-results"></div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        
        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
        
        async function testWidgetAPI() {
            const container = document.getElementById('api-results');
            container.innerHTML = '';
            
            try {
                logResult('api-results', 'Testing Widget Data API...', 'info');
                
                // Test with a dummy widget ID
                const response = await fetch(`${API_BASE}/api/public/widgets/test-widget-id`, {
                    headers: {
                        'Origin': window.location.origin
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                logResult('api-results', `Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                logResult('api-results', `CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}`, 'info');
                
                if (response.status === 404) {
                    logResult('api-results', 'Widget not found (expected for test ID)', 'success');
                } else if (response.ok) {
                    logResult('api-results', 'Widget API working correctly', 'success');
                }
                
            } catch (error) {
                logResult('api-results', `Error: ${error.message}`, 'error');
            }
        }
        
        async function testTrackingAPI() {
            try {
                logResult('api-results', 'Testing Tracking API...', 'info');
                
                const response = await fetch(`${API_BASE}/api/public/widgets/test-widget-id/track`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    body: JSON.stringify({
                        event: 'test',
                        referrer: window.location.href,
                        userAgent: navigator.userAgent
                    })
                });
                
                logResult('api-results', `Tracking API Status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.status === 404) {
                    logResult('api-results', 'Widget not found for tracking (expected for test ID)', 'success');
                } else if (response.ok) {
                    logResult('api-results', 'Tracking API working correctly', 'success');
                }
                
            } catch (error) {
                logResult('api-results', `Tracking Error: ${error.message}`, 'error');
            }
        }
        
        async function testManagementAPI() {
            try {
                logResult('api-results', 'Testing Management API (should require auth)...', 'info');
                
                const response = await fetch(`${API_BASE}/api/widgets`, {
                    headers: {
                        'Origin': window.location.origin
                    }
                });
                
                logResult('api-results', `Management API Status: ${response.status}`, 'info');
                
                if (response.status === 401) {
                    logResult('api-results', 'Management API correctly requires authentication', 'success');
                } else {
                    logResult('api-results', 'Management API response received', 'info');
                }
                
            } catch (error) {
                logResult('api-results', `Management API Error: ${error.message}`, 'error');
            }
        }
        
        function embedWidget() {
            const widgetId = document.getElementById('widget-id').value.trim();
            if (!widgetId) {
                alert('Please enter a widget ID');
                return;
            }
            
            const container = document.getElementById('widget-container');
            container.innerHTML = `
                <iframe 
                    src="${API_BASE}/widgets/iframe/${widgetId}"
                    style="width: 100%; height: 300px; border: none; border-radius: 8px;"
                    sandbox="allow-scripts allow-same-origin"
                    onload="console.log('Widget iframe loaded')"
                    onerror="console.error('Widget iframe failed to load')"
                ></iframe>
            `;
        }
        
        function clearWidget() {
            const container = document.getElementById('widget-container');
            container.innerHTML = '<p style="text-align: center; color: #6b7280;">Widget will appear here</p>';
        }
        
        async function testRateLimit() {
            const container = document.getElementById('rate-limit-results');
            container.innerHTML = '';
            
            logResult('rate-limit-results', 'Starting rate limit test (10 rapid requests)...', 'info');
            
            const promises = [];
            for (let i = 0; i < 10; i++) {
                promises.push(
                    fetch(`${API_BASE}/api/public/widgets/test-widget-id`)
                        .then(response => ({ request: i + 1, status: response.status }))
                        .catch(error => ({ request: i + 1, error: error.message }))
                );
            }
            
            const results = await Promise.all(promises);
            
            results.forEach(result => {
                if (result.status === 429) {
                    logResult('rate-limit-results', `Request ${result.request}: Rate limited (429)`, 'error');
                } else if (result.status) {
                    logResult('rate-limit-results', `Request ${result.request}: ${result.status}`, 'success');
                } else {
                    logResult('rate-limit-results', `Request ${result.request}: Error - ${result.error}`, 'error');
                }
            });
            
            const rateLimited = results.filter(r => r.status === 429).length;
            if (rateLimited > 0) {
                logResult('rate-limit-results', `Rate limiting working: ${rateLimited} requests blocked`, 'success');
            } else {
                logResult('rate-limit-results', 'No rate limiting detected (may need higher load)', 'info');
            }
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testWidgetAPI();
            }, 1000);
        });
    </script>
</body>
</html>