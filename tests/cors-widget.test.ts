// CORS Widget Testing Suite
// This comprehensive test suite validates CORS configuration for widget embedding
// Run with: node --loader ts-node/esm tests/cors-widget.test.ts

// Test environment configuration
const TEST_BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_WIDGET_ID = 'test-widget-123';

// Simple test utilities to replace Jest
class TestRunner {
  private static testCount = 0;
  private static passCount = 0;
  private static failCount = 0;

  static async runTest(name: string, testFn: () => Promise<void>) {
    this.testCount++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      this.passCount++;
    } catch (error) {
      console.log(`❌ ${name}`);
      console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
      this.failCount++;
    }
  }

  static expect(actual: any) {
    return {
      toBe: (expected: any) => {
        if (actual !== expected) {
          throw new Error(`Expected ${expected}, but got ${actual}`);
        }
      },
      toContain: (expected: any) => {
        if (!actual || !actual.includes(expected)) {
          throw new Error(`Expected "${actual}" to contain "${expected}"`);
        }
      },
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`Expected null, but got ${actual}`);
        }
      },
      toBeGreaterThanOrEqual: (expected: number) => {
        if (actual < expected) {
          throw new Error(`Expected ${actual} to be >= ${expected}`);
        }
      }
    };
  }

  static printSummary() {
    console.log(`\n📊 Test Summary:`);
    console.log(`   Total: ${this.testCount}`);
    console.log(`   Passed: ${this.passCount}`);
    console.log(`   Failed: ${this.failCount}`);
    if (this.failCount === 0) {
      console.log(`\n🎉 All tests passed!`);
    }
  }
}

// Main test execution function
async function runAllTests() {
  console.log('🚀 Starting CORS widget tests...');
  
  // Public Widget API CORS Tests
  console.log('\n📋 Testing Public Widget API CORS...');
  
  await TestRunner.runTest('should allow cross-origin requests to widget data API', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}`, {
      headers: {
        'Origin': 'https://external-website.com',
        'User-Agent': 'Mozilla/5.0 (Test Browser)'
      }
    });
    
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    TestRunner.expect(response.headers.get('Access-Control-Allow-Methods')).toContain('GET');
  });

  await TestRunner.runTest('should handle preflight requests for widget data API', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://external-website.com',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    TestRunner.expect(response.status).toBe(200);
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    TestRunner.expect(response.headers.get('Access-Control-Allow-Methods')).toContain('GET');
    TestRunner.expect(response.headers.get('Access-Control-Max-Age')).toBe('86400');
  });

  await TestRunner.runTest('should allow cross-origin requests to widget tracking API', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
      method: 'POST',
      headers: {
        'Origin': 'https://business-website.com',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event: 'view',
        referrer: 'https://business-website.com/products'
      })
    });
    
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    TestRunner.expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
  });

  await TestRunner.runTest('should handle preflight requests for widget tracking API', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://business-website.com',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, X-Widget-ID'
      }
    });
    
    TestRunner.expect(response.status).toBe(200);
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    TestRunner.expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
    TestRunner.expect(response.headers.get('Access-Control-Allow-Headers')).toContain('Content-Type');
  });

  // Widget Iframe CORS Tests
  console.log('\n📋 Testing Widget Iframe CORS...');
  
  await TestRunner.runTest('should allow iframe embedding from any domain', async () => {
    const response = await fetch(`${TEST_BASE_URL}/widgets/iframe/${TEST_WIDGET_ID}`, {
      headers: {
        'Origin': 'https://any-website.com'
      }
    });
    
    TestRunner.expect(response.headers.get('X-Frame-Options')).toBe('ALLOWALL');
    TestRunner.expect(response.headers.get('Content-Security-Policy')).toContain('frame-ancestors *');
  });

  await TestRunner.runTest('should serve widget iframe with proper CSP headers', async () => {
    const response = await fetch(`${TEST_BASE_URL}/widgets/iframe/${TEST_WIDGET_ID}`);
    
    const csp = response.headers.get('Content-Security-Policy');
    TestRunner.expect(csp).toContain('frame-ancestors *');
    TestRunner.expect(csp).toContain('default-src \'self\'');
    TestRunner.expect(csp).toContain('connect-src \'self\'');
  });

  // Widget Embed Script CORS Tests
  console.log('\n📋 Testing Widget Embed Script CORS...');
  
  await TestRunner.runTest('should allow loading embed script from any domain', async () => {
    const response = await fetch(`${TEST_BASE_URL}/widgets/embed.js`, {
      headers: {
        'Origin': 'https://customer-website.com'
      }
    });
    
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    TestRunner.expect(response.headers.get('Cache-Control')).toContain('public');
  });

  await TestRunner.runTest('should serve embed script with proper caching headers', async () => {
    const response = await fetch(`${TEST_BASE_URL}/widgets/embed.js`);
    
    TestRunner.expect(response.headers.get('Cache-Control')).toContain('max-age=3600');
    TestRunner.expect(response.status).toBe(200);
  });

  // Rate Limiting with CORS Tests
  console.log('\n📋 Testing Rate Limiting with CORS...');
  
  await TestRunner.runTest('should apply rate limiting while maintaining CORS headers', async () => {
    const origin = 'https://test-rate-limit.com';
    
    // Make multiple requests to trigger rate limiting
    const requests = Array.from({ length: 10 }, () => 
      fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}`, {
        headers: { 'Origin': origin }
      })
    );
    
    const responses = await Promise.all(requests);
    
    // All responses should have CORS headers, even rate-limited ones
    responses.forEach(response => {
      TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    });
  });

  // Error Handling with CORS Tests
  console.log('\n📋 Testing Error Handling with CORS...');
  
  await TestRunner.runTest('should include CORS headers in error responses', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/non-existent-widget`, {
      headers: {
        'Origin': 'https://external-site.com'
      }
    });
    
    TestRunner.expect(response.status).toBe(404);
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
  });

  await TestRunner.runTest('should handle malformed requests with proper CORS headers', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
      method: 'POST',
      headers: {
        'Origin': 'https://external-site.com',
        'Content-Type': 'application/json'
      },
      body: 'invalid-json'
    });
    
    TestRunner.expect(response.status).toBeGreaterThanOrEqual(400);
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
  });

  // Security Headers Validation Tests
  console.log('\n📋 Testing Security Headers Validation...');
  
  await TestRunner.runTest('should not expose sensitive headers in CORS responses', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}`, {
      headers: {
        'Origin': 'https://external-site.com'
      }
    });
    
    // Should not expose internal server information
    TestRunner.expect(response.headers.get('X-Powered-By')).toBeNull();
    TestRunner.expect(response.headers.get('Server')).toBeNull();
  });

  await TestRunner.runTest('should validate allowed headers in preflight requests', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://external-site.com',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, X-Widget-ID, X-Referrer'
      }
    });
    
    const allowedHeaders = response.headers.get('Access-Control-Allow-Headers');
    TestRunner.expect(allowedHeaders).toContain('Content-Type');
    TestRunner.expect(allowedHeaders).toContain('X-Widget-ID');
    TestRunner.expect(allowedHeaders).toContain('X-Referrer');
  });

  // Cross-Origin Analytics Tracking Tests
  console.log('\n📋 Testing Cross-Origin Analytics Tracking...');
  
  await TestRunner.runTest('should track widget views from different origins', async () => {
    const origins = [
      'https://site1.com',
      'https://site2.com',
      'https://site3.com'
    ];
    
    const requests = origins.map(origin => 
      fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
        method: 'POST',
        headers: {
          'Origin': origin,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          event: 'view',
          referrer: `${origin}/products`
        })
      })
    );
    
    const responses = await Promise.all(requests);
    
    responses.forEach((response, index) => {
      TestRunner.expect(response.status).toBe(200);
      TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    });
  });

  await TestRunner.runTest('should handle conversion tracking from external domains', async () => {
    const response = await fetch(`${TEST_BASE_URL}/api/public/widgets/${TEST_WIDGET_ID}/track`, {
      method: 'POST',
      headers: {
        'Origin': 'https://business-site.com',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event: 'lead_generated',
        referrer: 'https://business-site.com/contact',
        elementType: 'contact_form'
      })
    });
    
    TestRunner.expect(response.status).toBe(200);
    TestRunner.expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    
    const data = await response.json();
    TestRunner.expect(data.success).toBe(true);
  });
  
  // Print test summary
  TestRunner.printSummary();
  console.log('\n✅ CORS widget tests completed.');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

// Utility functions for CORS testing
export class CorsTestUtils {
  static async testCorsHeaders(url: string, origin: string = 'https://test-origin.com') {
    const response = await fetch(url, {
      headers: { 'Origin': origin }
    });
    
    return {
      allowOrigin: response.headers.get('Access-Control-Allow-Origin'),
      allowMethods: response.headers.get('Access-Control-Allow-Methods'),
      allowHeaders: response.headers.get('Access-Control-Allow-Headers'),
      maxAge: response.headers.get('Access-Control-Max-Age'),
      status: response.status
    };
  }
  
  static async testPreflightRequest(url: string, method: string, headers: string[] = []) {
    const response = await fetch(url, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://test-origin.com',
        'Access-Control-Request-Method': method,
        'Access-Control-Request-Headers': headers.join(', ')
      }
    });
    
    return {
      status: response.status,
      allowOrigin: response.headers.get('Access-Control-Allow-Origin'),
      allowMethods: response.headers.get('Access-Control-Allow-Methods'),
      allowHeaders: response.headers.get('Access-Control-Allow-Headers')
    };
  }
  
  static async validateWidgetEmbedding(widgetId: string, origin: string) {
    const tests = {
      dataApi: await this.testCorsHeaders(`${TEST_BASE_URL}/api/public/widgets/${widgetId}`, origin),
      trackingApi: await this.testCorsHeaders(`${TEST_BASE_URL}/api/public/widgets/${widgetId}/track`, origin),
      iframeEmbed: await this.testCorsHeaders(`${TEST_BASE_URL}/widgets/iframe/${widgetId}`, origin),
      embedScript: await this.testCorsHeaders(`${TEST_BASE_URL}/widgets/embed.js`, origin)
    };
    
    return tests;
  }
}