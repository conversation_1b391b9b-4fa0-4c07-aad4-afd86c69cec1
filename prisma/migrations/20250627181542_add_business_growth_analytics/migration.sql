-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "descriptionLastUpdatedAt" TIMESTAMP(3),
ADD COLUMN     "imagesLastUpdatedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Review" ADD COLUMN     "ownerRespondedAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "BusinessAnalytics" (
    "id" TEXT NOT NULL,
    "businessId" TEXT NOT NULL,
    "averageReviewResponseTime" DOUBLE PRECISION,
    "negativeReviewResponseRate" DOUBLE PRECISION,
    "productContentFreshnessScore" DOUBLE PRECISION,
    "lastCalculated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BusinessAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BusinessAnalytics_businessId_key" ON "BusinessAnalytics"("businessId");

-- CreateIndex
CREATE INDEX "BusinessAnalytics_businessId_idx" ON "BusinessAnalytics"("businessId");

-- AddForeignKey
ALTER TABLE "BusinessAnalytics" ADD CONSTRAINT "BusinessAnalytics_businessId_fkey" FOREIGN KEY ("businessId") REFERENCES "Business"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
