/*
  Warnings:

  - A unique constraint covering the columns `[commentId,clerkUserId]` on the table `CommentVote` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `clerkUserId` to the `CommentVote` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "CommentVote_commentId_userId_key";

-- AlterTable
ALTER TABLE "CommentVote" ADD COLUMN "clerkUserId" TEXT;

-- Update existing records with clerkUserId from User table
UPDATE "CommentVote" cv
SET "clerkUserId" = u."clerkUserId"
FROM "User" u
WHERE cv."userId" = u.id;

-- Make clerkUserId required
ALTER TABLE "CommentVote" ALTER COLUMN "clerkUserId" SET NOT NULL;

-- CreateIndex
CREATE INDEX "CommentVote_clerkUserId_idx" ON "CommentVote"("clerkUserId");

-- CreateIndex
CREATE UNIQUE INDEX "CommentVote_commentId_clerkUserId_key" ON "CommentVote"("commentId", "clerkUserId");

-- CreateIndex
CREATE INDEX "AdminAction_createdAt_idx" ON "AdminAction"("createdAt");

-- CreateIndex
CREATE INDEX "ModerationEvent_createdAt_idx" ON "ModerationEvent"("createdAt");

-- CreateIndex
CREATE INDEX "ModerationEvent_action_idx" ON "ModerationEvent"("action");

-- CreateIndex
CREATE INDEX "Product_isDeleted_rating_idx" ON "Product"("isDeleted", "rating");

-- CreateIndex
CREATE INDEX "Product_viewCount_idx" ON "Product"("viewCount");

-- CreateIndex
CREATE INDEX "Product_featuredPosition_idx" ON "Product"("featuredPosition");

-- CreateIndex
CREATE INDEX "Product_businessId_idx" ON "Product"("businessId");

-- CreateIndex
CREATE INDEX "Review_isDeleted_isVerified_idx" ON "Review"("isDeleted", "isVerified");

-- CreateIndex
CREATE INDEX "Review_rating_idx" ON "Review"("rating");

-- CreateIndex
CREATE INDEX "Review_createdDate_idx" ON "Review"("createdDate");

-- CreateIndex
CREATE INDEX "Review_productId_idx" ON "Review"("productId");

-- CreateIndex
CREATE INDEX "Review_userId_idx" ON "Review"("userId");

-- CreateIndex
CREATE INDEX "User_role_status_idx" ON "User"("role", "status");

-- CreateIndex
CREATE INDEX "User_isDeleted_status_idx" ON "User"("isDeleted", "status");

-- CreateIndex
CREATE INDEX "User_lastLoginAt_idx" ON "User"("lastLoginAt");
