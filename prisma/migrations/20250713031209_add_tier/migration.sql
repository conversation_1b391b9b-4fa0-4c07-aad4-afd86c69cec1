/*
  Warnings:

  - You are about to drop the column `helpfulVotes` on the `Review` table. All the data in the column will be lost.
  - You are about to drop the column `unhelpfulVotes` on the `Review` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "Tier" AS ENUM ('starter', 'pro', 'enterprise');

-- DropIndex
DROP INDEX "Product_isPublic_idx";

-- AlterTable
ALTER TABLE "Business" ADD COLUMN     "tier" "Tier" NOT NULL DEFAULT 'starter',
ADD COLUMN     "tierExpiresAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Review" DROP COLUMN "helpfulVotes",
DROP COLUMN "unhelpfulVotes";

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "usernameChangedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Widget" ADD COLUMN     "allowedDomains" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "blockedCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "conversionCount" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "WidgetAnalytics" ADD COLUMN     "blockedEvents" JSONB NOT NULL DEFAULT '{}',
ADD COLUMN     "dailyConversions" JSONB NOT NULL DEFAULT '{}';

-- CreateTable
CREATE TABLE "SecurityLog" (
    "id" TEXT NOT NULL,
    "eventType" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "userAgent" TEXT NOT NULL,
    "requestPath" TEXT NOT NULL,
    "details" TEXT,
    "severity" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SecurityLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CorsErrorLog" (
    "id" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "origin" TEXT,
    "requestUrl" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "errorType" TEXT NOT NULL,
    "userAgent" TEXT,
    "referrer" TEXT,
    "widgetId" TEXT,
    "errorMessage" TEXT NOT NULL,
    "requestHeaders" JSONB NOT NULL DEFAULT '{}',
    "ipAddress" TEXT NOT NULL,

    CONSTRAINT "CorsErrorLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CorsAlert" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isResolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedAt" TIMESTAMP(3),
    "resolvedBy" TEXT,

    CONSTRAINT "CorsAlert_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SecurityLog_eventType_idx" ON "SecurityLog"("eventType");

-- CreateIndex
CREATE INDEX "SecurityLog_userId_idx" ON "SecurityLog"("userId");

-- CreateIndex
CREATE INDEX "SecurityLog_timestamp_idx" ON "SecurityLog"("timestamp");

-- CreateIndex
CREATE INDEX "CorsErrorLog_timestamp_idx" ON "CorsErrorLog"("timestamp");

-- CreateIndex
CREATE INDEX "CorsErrorLog_errorType_idx" ON "CorsErrorLog"("errorType");

-- CreateIndex
CREATE INDEX "CorsErrorLog_origin_idx" ON "CorsErrorLog"("origin");

-- CreateIndex
CREATE INDEX "CorsErrorLog_ipAddress_idx" ON "CorsErrorLog"("ipAddress");

-- CreateIndex
CREATE INDEX "CorsErrorLog_widgetId_idx" ON "CorsErrorLog"("widgetId");

-- CreateIndex
CREATE INDEX "CorsAlert_timestamp_idx" ON "CorsAlert"("timestamp");

-- CreateIndex
CREATE INDEX "CorsAlert_type_idx" ON "CorsAlert"("type");

-- CreateIndex
CREATE INDEX "CorsAlert_isResolved_idx" ON "CorsAlert"("isResolved");
