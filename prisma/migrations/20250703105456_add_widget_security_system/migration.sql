-- CreateEnum
CREATE TYPE "WidgetSecurityLevel" AS ENUM ('SIMPLE', 'SECURE');

-- CreateEnum
CREATE TYPE "VerificationMethod" AS ENUM ('HTML_FILE', 'DNS_TXT', 'META_TAG');

-- AlterTable
ALTER TABLE "Widget" ADD COLUMN     "apiKey" TEXT,
ADD COLUMN     "maxRequestsPerHour" INTEGER NOT NULL DEFAULT 1000,
ADD COLUMN     "securityLevel" "WidgetSecurityLevel" NOT NULL DEFAULT 'SIMPLE',
ADD COLUMN     "tokenExpiry" INTEGER NOT NULL DEFAULT 3600;

-- CreateTable
CREATE TABLE "WidgetToken" (
    "id" TEXT NOT NULL,
    "widgetId" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastUsed" TIMESTAMP(3),
    "requestCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "WidgetToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DomainVerification" (
    "id" TEXT NOT NULL,
    "widgetId" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "verificationCode" TEXT NOT NULL,
    "method" "VerificationMethod" NOT NULL,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "verifiedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DomainVerification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WidgetToken_token_key" ON "WidgetToken"("token");

-- CreateIndex
CREATE INDEX "WidgetToken_widgetId_idx" ON "WidgetToken"("widgetId");

-- CreateIndex
CREATE INDEX "WidgetToken_token_idx" ON "WidgetToken"("token");

-- CreateIndex
CREATE INDEX "WidgetToken_domain_idx" ON "WidgetToken"("domain");

-- CreateIndex
CREATE INDEX "WidgetToken_expiresAt_idx" ON "WidgetToken"("expiresAt");

-- CreateIndex
CREATE UNIQUE INDEX "WidgetToken_widgetId_domain_key" ON "WidgetToken"("widgetId", "domain");

-- CreateIndex
CREATE INDEX "DomainVerification_widgetId_idx" ON "DomainVerification"("widgetId");

-- CreateIndex
CREATE INDEX "DomainVerification_domain_idx" ON "DomainVerification"("domain");

-- CreateIndex
CREATE INDEX "DomainVerification_verificationCode_idx" ON "DomainVerification"("verificationCode");

-- CreateIndex
CREATE INDEX "DomainVerification_isVerified_idx" ON "DomainVerification"("isVerified");

-- CreateIndex
CREATE UNIQUE INDEX "DomainVerification_widgetId_domain_key" ON "DomainVerification"("widgetId", "domain");

-- CreateIndex
CREATE INDEX "Widget_securityLevel_idx" ON "Widget"("securityLevel");

-- AddForeignKey
ALTER TABLE "WidgetToken" ADD CONSTRAINT "WidgetToken_widgetId_fkey" FOREIGN KEY ("widgetId") REFERENCES "Widget"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DomainVerification" ADD CONSTRAINT "DomainVerification_widgetId_fkey" FOREIGN KEY ("widgetId") REFERENCES "Widget"("id") ON DELETE CASCADE ON UPDATE CASCADE;