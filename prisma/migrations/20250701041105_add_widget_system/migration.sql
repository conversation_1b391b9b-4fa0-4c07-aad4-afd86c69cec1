-- CreateE<PERSON>
CREATE TYPE "WidgetType" AS ENUM ('RE<PERSON><PERSON><PERSON>_CAROUSEL', 'REVIEW_GRID', 'RATING_SUMMARY', 'MINI_REVIEW', 'BUSINESS_CARD', 'TRUST_BADGE', 'REVIEW_POPUP');

-- CreateTable
CREATE TABLE "Widget" (
    "id" TEXT NOT NULL,
    "businessId" TEXT NOT NULL,
    "productId" TEXT,
    "name" TEXT NOT NULL,
    "type" "WidgetType" NOT NULL,
    "config" JSONB NOT NULL DEFAULT '{}',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "theme" TEXT NOT NULL DEFAULT 'light',
    "primaryColor" TEXT,
    "borderRadius" TEXT NOT NULL DEFAULT '8px',
    "showLogo" BOOLEAN NOT NULL DEFAULT true,
    "showPoweredBy" BOOLEAN NOT NULL DEFAULT true,
    "maxReviews" INTEGER NOT NULL DEFAULT 5,
    "showRating" BOOLEAN NOT NULL DEFAULT true,
    "showReviewText" BOOLEAN NOT NULL DEFAULT true,
    "showReviewDate" BOOLEAN NOT NULL DEFAULT true,
    "showReviewerName" BOOLEAN NOT NULL DEFAULT true,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastUsed" TIMESTAMP(3),

    CONSTRAINT "Widget_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WidgetAnalytics" (
    "id" TEXT NOT NULL,
    "widgetId" TEXT NOT NULL,
    "dailyViews" JSONB NOT NULL DEFAULT '{}',
    "dailyClicks" JSONB NOT NULL DEFAULT '{}',
    "topReferrers" JSONB NOT NULL DEFAULT '{}',
    "averageLoadTime" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "errorCount" INTEGER NOT NULL DEFAULT 0,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WidgetAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Widget_businessId_idx" ON "Widget"("businessId");

-- CreateIndex
CREATE INDEX "Widget_productId_idx" ON "Widget"("productId");

-- CreateIndex
CREATE INDEX "Widget_type_idx" ON "Widget"("type");

-- CreateIndex
CREATE INDEX "Widget_isActive_idx" ON "Widget"("isActive");

-- CreateIndex
CREATE INDEX "Widget_createdAt_idx" ON "Widget"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "WidgetAnalytics_widgetId_key" ON "WidgetAnalytics"("widgetId");

-- CreateIndex
CREATE INDEX "WidgetAnalytics_widgetId_idx" ON "WidgetAnalytics"("widgetId");

-- CreateIndex
CREATE INDEX "WidgetAnalytics_lastUpdated_idx" ON "WidgetAnalytics"("lastUpdated");

-- AddForeignKey
ALTER TABLE "Widget" ADD CONSTRAINT "Widget_businessId_fkey" FOREIGN KEY ("businessId") REFERENCES "Business"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Widget" ADD CONSTRAINT "Widget_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WidgetAnalytics" ADD CONSTRAINT "WidgetAnalytics_widgetId_fkey" FOREIGN KEY ("widgetId") REFERENCES "Widget"("id") ON DELETE CASCADE ON UPDATE CASCADE;
