-- CreateTable
CREATE TABLE "ProductViewEvent" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "userId" TEXT,
    "sessionId" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "duration" INTEGER,
    "source" TEXT,
    "deviceType" TEXT,
    "isNewUser" BOOLEAN NOT NULL DEFAULT false,
    "isThrottled" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ProductViewEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAnalytics" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "totalViews" INTEGER NOT NULL DEFAULT 0,
    "uniqueVisitors" INTEGER NOT NULL DEFAULT 0,
    "averageViewDuration" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "peakHours" JSONB NOT NULL,
    "weekdayStats" JSONB NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserProductInteraction" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "lastViewed" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "hasReviewed" BOOLEAN NOT NULL DEFAULT false,
    "hasLiked" BOOLEAN NOT NULL DEFAULT false,
    "averageTimeSpent" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "UserProductInteraction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProductViewEvent_productId_idx" ON "ProductViewEvent"("productId");

-- CreateIndex
CREATE INDEX "ProductViewEvent_userId_idx" ON "ProductViewEvent"("userId");

-- CreateIndex
CREATE INDEX "ProductViewEvent_sessionId_idx" ON "ProductViewEvent"("sessionId");

-- CreateIndex
CREATE INDEX "ProductViewEvent_timestamp_idx" ON "ProductViewEvent"("timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "ProductAnalytics_productId_key" ON "ProductAnalytics"("productId");

-- CreateIndex
CREATE INDEX "ProductAnalytics_productId_idx" ON "ProductAnalytics"("productId");

-- CreateIndex
CREATE INDEX "ProductAnalytics_totalViews_idx" ON "ProductAnalytics"("totalViews");

-- CreateIndex
CREATE INDEX "UserProductInteraction_userId_idx" ON "UserProductInteraction"("userId");

-- CreateIndex
CREATE INDEX "UserProductInteraction_productId_idx" ON "UserProductInteraction"("productId");

-- CreateIndex
CREATE INDEX "UserProductInteraction_lastViewed_idx" ON "UserProductInteraction"("lastViewed");

-- CreateIndex
CREATE UNIQUE INDEX "UserProductInteraction_userId_productId_key" ON "UserProductInteraction"("userId", "productId");

-- AddForeignKey
ALTER TABLE "ProductViewEvent" ADD CONSTRAINT "ProductViewEvent_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductViewEvent" ADD CONSTRAINT "ProductViewEvent_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAnalytics" ADD CONSTRAINT "ProductAnalytics_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserProductInteraction" ADD CONSTRAINT "UserProductInteraction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserProductInteraction" ADD CONSTRAINT "UserProductInteraction_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
