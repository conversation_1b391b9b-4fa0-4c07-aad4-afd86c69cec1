# Admin Guide – System Notifications

> Location: **`/admin/docs/system-notifications.md`**  
> Audience: <PERSON>mins & Dev-Ops who need to publish platform / product updates to users.

---

## 1. Overview
System notifications let ReviewIt administrators broadcast **platform-wide** or **targeted** messages directly to user notification feeds in real-time.

Typical use-cases:
* Product-claim **approved / rejected**
* Review **approved / rejected**
* **Bug-fix** or **maintenance** announcements
* **Billing / plan** status messages
* Any other “admin → user” communication

All messages are delivered through the existing notification infrastructure (database, SSE stream, toast UI, bell dropdown, /notifications page).

---

## 2. REST Endpoints

| Method | Endpoint                              | Description                                       |
| ------ | ------------------------------------- | ------------------------------------------------- |
| POST   | `/notifications/system`               | Create a new system notification (broadcast or targeted) |
| PUT    | `/notifications/{id}/read?type=system`| Mark a system notification as read                |

### 2.1  `POST /notifications/system`
```
Content-Type: application/json
```
#### Payload
```json
{
  "target_user_ids": ["user_abc", "user_xyz"], // Optional; empty or omitted = broadcast
  "title": "Product claim approved!",
  "message": "Your claim for ‘Awesome Widget’ is now live.",
  "cta_url": "/dashboard/claims/awesome-widget", // Optional deep-link
  "icon": "success"                               // info | success | warning | error
}
```
Field notes:
* **`target_user_ids`** – array of DB user IDs ( _not_ Clerk IDs ). When omitted / `[]`, message is sent to **all** users.
* **`cta_url`** – optional link shown in toast / notification card.
* **`icon`** – maps to Sonner toast variant.

#### Success Response
```
HTTP/1.1 201 Created
{
  "id": "sys_1700000000_12ab",
  "created_at": "2025-07-15T13:00:00Z"
}
```

### 2.2 Mark as Read
```
PUT /notifications/{id}/read?type=system
```
Marks the given system notification as read for the calling user.

---

## 3. SSE Stream
System notifications are pushed over the existing SSE endpoint:
```
GET /notifications/stream?user_id={db_user_id}
```
The event payload includes:
```json
{
  "user_id": "user_abc",
  "type": "system",
  "event": "notification",
  "notification": {
    "id": "sys_123abc",
    "title": "Maintenance tonight",
    "message": "Service will be unavailable at 02:00 UTC",
    "cta_url": null,
    "icon": "warning",
    "read": false,
    "created_at": "2025-07-15T10:00:00Z"
  }
}
```
Front-end `NotificationManager` already shows a toast and adds it to notification lists automatically.

---

## 4. Administration Workflows

### 4.1 Post a Broadcast Announcement
1. Open your API client (Insomnia / Postman) or the **Admin Portal → System Notifications** page.
2. Send `POST /notifications/system` with an **empty** `target_user_ids` array:
   ```json
   {
     "target_user_ids": [],
     "title": "New bug-fix deployed",
     "message": "Liking reviews now works reliably.",
     "icon": "success"
   }
   ```
3. All currently connected users receive a toast instantly; offline users see it the next time they open ReviewIt.

### 4.2 Notify a Specific Seller
Example: product claim approved.
```json
{
  "target_user_ids": ["user_2nup3vKqoP3CPAk3ZrWQxieB1y"],
  "title": "Product claim approved!",
  "message": "Your claim for ‘Awesome Widget’ is now live.",
  "cta_url": "/dashboard/claims/awesome-widget",
  "icon": "success"
}
```

### 4.3 Maintenance Warning (Scheduled)
Schedule a cron job or use the Admin Portal to send the following an hour before downtime:
```json
{
  "title": "Scheduled maintenance at 02:00 UTC",
  "message": "Expect up to 10 minutes of downtime while we upgrade the database.",
  "icon": "warning"
}
```

---

## 5. Adding System Notifications in New Features
Whenever a backend micro-service needs to notify users of a *platform* event, call the system endpoint.

1. **Identify recipients** – single user, a group, or broadcast.
2. Ensure each `target_user_id` exists in the notification DB (use `/users` endpoint if needed).
3. Build the payload (`title`, `message`, optional `cta_url`, `icon`).
4. `POST /notifications/system`.

### Example: Review Auto-Approval
```ts
await fetch(`${NOTIF_URL}/notifications/system`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    target_user_ids: [review.userId],
    title: 'Your review has been approved!',
    message: `Your review for ${product.name} is now public.`,
    icon: 'success'
  })
});
```

---

## 6. Troubleshooting
* **400 Bad Request** – missing fields; check JSON keys.
* **404 User not found** – call `/users` first to create the user.
* **SSE not received** – confirm user’s SSE stream is connected and `user_id` matches DB ID.

---

### Quick Reference Cheat-Sheet
```bash
# Broadcast info
curl -XPOST \ 
  -H 'Content-Type: application/json' \ 
  -d '{"title":"Platform update","message":"We just shipped v2.0","icon":"info"}' \ 
  https://notifications.reviewit.gy/notifications/system

# Targeted message
curl -XPOST -H 'Content-Type: application/json' -d @payload.json \ 
  https://notifications.reviewit.gy/notifications/system

# Mark as read
curl -XPUT https://notifications.reviewit.gy/notifications/sys_123abc/read?type=system
```

---
**Document version**: 1.0 • Last updated: 2025-07-15
