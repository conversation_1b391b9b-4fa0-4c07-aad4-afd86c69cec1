# Redis Singleton Refactor Guide

## Overview

This guide addresses the Redis configuration issues in `databaseAnalytics.ts` and provides a step-by-step approach to implement a proper singleton pattern for Redis connections.

## Current Issues Identified

### 1. Multiple Redis Instances
- Global `redisClient` instance
- `DatabaseAnalytics` class with separate cache methods
- Standalone functions (`safeRedisGet`, `safeRedisSet`, `safeRedisDel`)
- Mixed usage patterns throughout the codebase

### 2. Inconsistent Error Handling
- Different error handling strategies in class vs standalone functions
- Redundant health checking logic
- No centralized connection management

### 3. Resource Inefficiency
- Multiple Redis connections consuming unnecessary resources
- Competing health states
- Configuration duplication

## Solution: Proper Redis Singleton

### Step 1: Create Redis Service Singleton

Create a new file: `src/app/lib/redis.ts`

```typescript
import Redis, { RedisOptions } from 'ioredis';

interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  tlsEnabled?: boolean;
}

class RedisService {
  private static instance: RedisService;
  private client: Redis;
  private healthy: boolean = true;
  private lastHealthCheck: number = 0;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private readonly COMMAND_TIMEOUT = 5000; // 5 seconds
  private readonly SET_TIMEOUT = 2000; // 2 seconds

  private constructor() {
    this.client = this.createRedisClient();
    this.setupEventHandlers();
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  private createRedisClient(): Redis {
    const config = this.getRedisConfig();
    
    const options: RedisOptions = {
      host: config.host,
      port: config.port,
      password: config.password,
      connectTimeout: 30000,
      commandTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 1,
      enableOfflineQueue: false,
      retryStrategy: () => null,
      family: 4,
      keepAlive: 30000,
      showFriendlyErrorStack: true
    };

    if (config.tlsEnabled) {
      options.tls = {};
    }

    this.logConfiguration(config, options);
    return new Redis(options);
  }

  private getRedisConfig(): RedisConfig {
    return {
      host: process.env.UPSTASH_REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      tlsEnabled: process.env.REDIS_TLS_ENABLED === 'true'
    };
  }

  private logConfiguration(config: RedisConfig, options: RedisOptions): void {
    console.log('Redis Configuration:');
    console.log('Host:', config.host);
    console.log('Port:', config.port);
    console.log('Password:', config.password ? '[REDACTED]' : 'undefined');
    console.log('TLS:', config.tlsEnabled ? 'enabled' : 'disabled');
    
    const connectionString = `redis${config.tlsEnabled ? 's' : ''}://${config.password ? ':' + config.password + '@' : ''}${config.host}:${config.port}`;
    console.log('Connection String:', connectionString);
  }

  private setupEventHandlers(): void {
    this.client.on('error', (err: any) => {
      console.error('Redis connection error:', {
        message: err?.message,
        code: err?.code,
        errno: err?.errno,
        syscall: err?.syscall,
        address: err?.address,
        port: err?.port
      });
      this.healthy = false;
    });

    this.client.on('connect', () => {
      console.log('Redis connected successfully to', this.client.options.host + ':' + this.client.options.port);
      this.healthy = true;
    });

    this.client.on('ready', () => {
      console.log('Redis client ready');
      this.healthy = true;
    });

    this.client.on('close', () => {
      console.log('Redis connection closed');
      this.healthy = false;
    });

    this.client.on('reconnecting', () => {
      console.log('Redis reconnecting...');
    });

    this.client.on('end', () => {
      console.log('Redis connection ended');
      this.healthy = false;
    });
  }

  public async isHealthy(): Promise<boolean> {
    const now = Date.now();
    if (now - this.lastHealthCheck < this.HEALTH_CHECK_INTERVAL) {
      return this.healthy;
    }

    try {
      console.log('Redis health check - current status:', this.client.status);

      if (this.client.status !== 'ready') {
        console.log('Redis not ready, attempting to connect...');
        await this.client.connect();
        console.log('Redis connection attempt completed, status:', this.client.status);
      }

      console.log('Sending ping to Redis...');
      const pingPromise = this.client.ping();
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Health check timeout after 5 seconds')), this.COMMAND_TIMEOUT)
      );

      const result = await Promise.race([pingPromise, timeoutPromise]);
      console.log('Redis ping result:', result);
      this.healthy = true;
      this.lastHealthCheck = now;
      return true;
    } catch (error: any) {
      console.error('Redis health check failed:', {
        message: error?.message,
        code: error?.code,
        errno: error?.errno,
        syscall: error?.syscall,
        address: error?.address,
        port: error?.port,
        stack: error?.stack
      });
      this.healthy = false;
      this.lastHealthCheck = now;
      return false;
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      if (this.client.status !== 'ready') {
        await this.client.connect();
      }

      const getPromise = this.client.get(key);
      const timeoutPromise = new Promise<null>((_, reject) =>
        setTimeout(() => reject(new Error('Redis get timeout')), this.COMMAND_TIMEOUT)
      );

      const result = await Promise.race([getPromise, timeoutPromise]);
      if (result) {
        console.log(`Redis CACHE HIT for key: ${key}`);
      } else {
        console.log(`Redis CACHE MISS for key: ${key}`);
      }
      return result;
    } catch (error) {
      console.warn(`Redis get failed for key ${key}:`, error);
      this.healthy = false;
      return null;
    }
  }

  public async set(key: string, value: string, ttl: number): Promise<void> {
    console.log(`🔍 Attempting to set cache key: ${key}`);

    const isHealthy = await this.isHealthy();
    console.log(`🔍 Redis health check result: ${isHealthy}`);

    if (!isHealthy) {
      console.warn(`❌ Redis unhealthy - skipping cache set for key: ${key}`);
      return;
    }

    try {
      console.log(`🔍 Setting Redis key with TTL ${ttl}: ${key}`);
      const setPromise = this.client.setex(key, ttl, value);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Redis set timeout')), this.SET_TIMEOUT)
      );

      await Promise.race([setPromise, timeoutPromise]);
      console.log(`✅ Successfully set cache key: ${key}`);
    } catch (error) {
      console.error(`❌ Redis set failed for key ${key}:`, error);
    }
  }

  public async del(key: string): Promise<void> {
    if (!(await this.isHealthy())) {
      return;
    }

    try {
      const delPromise = this.client.del(key);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Redis del timeout')), this.SET_TIMEOUT)
      );

      await Promise.race([delPromise, timeoutPromise]);
      console.log(`Redis key deleted: ${key}`);
    } catch (error) {
      console.warn(`Redis del failed for key ${key}:`, error);
    }
  }

  public async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const data = await this.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn(`Redis cache get failed (non-fatal): ${error}`);
      return null;
    }
  }

  public async setInCache(key: string, value: any, ttl: number): Promise<void> {
    try {
      await this.set(key, JSON.stringify(value), ttl);
    } catch (error) {
      console.warn(`Redis cache set failed (non-fatal): ${error}`);
    }
  }

  // For testing purposes - allows graceful shutdown
  public async disconnect(): Promise<void> {
    await this.client.disconnect();
  }

  // Get raw client for advanced operations (use sparingly)
  public getClient(): Redis {
    return this.client;
  }
}

// Export singleton instance
export const redisService = RedisService.getInstance();
export default redisService;
```

### Step 2: Update DatabaseAnalytics Class

Modify the `DatabaseAnalytics` class in `databaseAnalytics.ts`:

```typescript
// Remove the global Redis imports and configuration
// Replace with:
import { redisService } from '../lib/redis';

class DatabaseAnalytics {
  constructor(
    private prisma: PrismaClient,
    private cacheTtl: number = 300,
    private redis = redisService // Inject Redis service
  ) { }

  // Update cache methods to use the singleton
  private async getFromCache<T>(key: string): Promise<T | null> {
    return await this.redis.getFromCache<T>(key);
  }

  private async setInCache(key: string, value: any, ttl: number): Promise<void> {
    await this.redis.setInCache(key, value, ttl);
  }

  // Rest of the class remains the same...
}
```

### Step 3: Update Standalone Functions

Replace the standalone Redis functions with calls to the singleton:

```typescript
// Remove these functions:
// - safeRedisGet
// - safeRedisSet
// - safeRedisDel
// - isRedisHealthy

// Replace their usage with:
export async function getBusinessAnalyticsFromDB(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iBusinessAnalytics> {
  const startDateKey = period.startDate.toISOString().split('T')[0];
  const endDateKey = period.endDate.toISOString().split('T')[0];
  const cacheKey = `business_analytics:${businessId}:${startDateKey}:${endDateKey}`;

  // Use singleton instead of safeRedisGet
  const cached = await redisService.getFromCache<iBusinessAnalytics>(cacheKey);
  if (cached) {
    return cached;
  }

  // ... existing logic ...

  // Use singleton instead of safeRedisSet
  await redisService.setInCache(cacheKey, analyticsData, 1200);
  
  return analyticsData;
}
```

### Step 4: Remove Global Redis Configuration

Remove these sections from `databaseAnalytics.ts`:

1. Redis imports and options configuration (lines ~10-45)
2. Global `redisClient` instantiation (line ~46)
3. Event handlers setup (lines ~48-85)
4. Global health check variables and functions (lines ~87-140)
5. Standalone safe Redis functions (lines ~142-200)

### Step 5: Update Imports Throughout Codebase

Search for any other files that might be importing or using the old Redis configuration:

```bash
# Search for Redis usage
grep -r "redisClient" src/
grep -r "safeRedis" src/
grep -r "from.*databaseAnalytics" src/
```

Update these files to use the new singleton:

```typescript
// Old
import { safeRedisGet, safeRedisSet } from '../util/databaseAnalytics';

// New
import { redisService } from '../lib/redis';

// Usage
const cached = await redisService.getFromCache<MyType>(key);
await redisService.setInCache(key, data, ttl);
```

## Migration Checklist

### Phase 1: Setup
- [ ] Create `src/app/lib/redis.ts` with the singleton implementation
- [ ] Test the new Redis service in isolation
- [ ] Verify environment variables are correctly loaded

### Phase 2: Update DatabaseAnalytics
- [ ] Remove global Redis configuration from `databaseAnalytics.ts`
- [ ] Update `DatabaseAnalytics` class to use the singleton
- [ ] Replace standalone functions with singleton calls
- [ ] Remove unused imports and variables

### Phase 3: Update Dependencies
- [ ] Search for files importing Redis functions from `databaseAnalytics.ts`
- [ ] Update imports to use the new singleton
- [ ] Test each updated file individually

### Phase 4: Testing
- [ ] Test Redis connection and health checks
- [ ] Verify caching functionality works correctly
- [ ] Test error handling and fallback behavior
- [ ] Monitor logs for any Redis-related issues

### Phase 5: Cleanup
- [ ] Remove any remaining unused Redis code
- [ ] Update documentation and comments
- [ ] Consider adding unit tests for the Redis service

## Benefits After Migration

### Performance
- Single Redis connection pool
- Reduced memory usage
- Consistent connection management

### Maintainability
- Centralized Redis configuration
- Single source of truth for Redis operations
- Easier testing and mocking

### Reliability
- Consistent error handling
- Unified health checking
- Better connection lifecycle management

## Testing Strategy

### Unit Tests
```typescript
// Example test structure
describe('RedisService', () => {
  let redisService: RedisService;
  
  beforeEach(() => {
    redisService = RedisService.getInstance();
  });
  
  it('should maintain singleton pattern', () => {
    const instance1 = RedisService.getInstance();
    const instance2 = RedisService.getInstance();
    expect(instance1).toBe(instance2);
  });
  
  // Add more tests...
});
```

### Integration Tests
- Test with actual Redis instance
- Verify caching behavior
- Test error scenarios and fallbacks

## Monitoring and Observability

### Metrics to Track
- Redis connection status
- Cache hit/miss ratios
- Operation latencies
- Error rates

### Logging
- Connection events
- Health check results
- Cache operations
- Error details

## Security Considerations

- Ensure Redis credentials are properly secured
- Use TLS in production environments
- Implement proper access controls
- Monitor for suspicious Redis activity

## Rollback Plan

If issues arise during migration:

1. Keep the original `databaseAnalytics.ts` as backup
2. Implement feature flags to switch between old/new Redis usage
3. Monitor error rates and performance metrics
4. Have a quick rollback procedure documented

## Conclusion

This refactor will significantly improve the Redis implementation by:
- Eliminating resource waste from multiple connections
- Providing consistent error handling and health checking
- Making the codebase more maintainable and testable
- Following proper singleton design patterns

The migration should be done incrementally with thorough testing at each step to ensure system stability.