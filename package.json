{"name": "review-it-nextjs-v7-remake", "version": "0.1.2", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev", "build": "next build && prisma generate", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^6.12.12", "@clerk/themes": "^1.7.20", "@ducanh2912/next-pwa": "^10.2.8", "@emotion/react": "^11.13.3", "@headlessui/react": "^1.7.19", "@hookform/resolvers": "^5.0.1", "@mantine/core": "^6.0.22", "@mantine/hooks": "^6.0.22", "@mantine/tiptap": "^6.0.22", "@prisma/client": "^5.18.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@ramonak/react-progress-bar": "^5.2.0", "@react-google-maps/api": "^2.20.6", "@tabler/icons-react": "^2.47.0", "@tanstack/react-query": "^5.52.0", "@tanstack/react-query-devtools": "^5.52.0", "@tiptap/extension-link": "^2.6.5", "@tiptap/extension-placeholder": "^2.6.5", "@tiptap/extension-underline": "^2.6.5", "@tiptap/pm": "^2.6.5", "@tiptap/react": "^2.6.5", "@tiptap/starter-kit": "^2.6.5", "@types/formidable": "^3.4.5", "@types/node": "^20.16.1", "@types/react": "18.2.33", "@types/react-dom": "18.2.14", "@types/uuid": "^10.0.0", "accordion": "^3.0.2", "add": "^2.0.6", "autoprefixer": "10.4.16", "axios": "^1.10.0", "bad-words": "^4.0.0", "class-variance-authority": "^0.7.0", "cloudinary": "^1.41.3", "clsx": "^2.1.1", "daisyui": "^3.9.4", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "dompurify": "^3.1.6", "dotenv": "^16.5.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "framer-motion": "^11.11.17", "html-react-parser": "^4.2.10", "http-status-codes": "^2.3.0", "install": "^0.13.0", "ioredis": "^5.6.1", "jotai": "^2.9.3", "jotai-utils": "^0.0.0", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.6", "lucide-react": "^0.294.0", "next": "^14.2.5", "next-pwa": "^5.6.0", "next-themes": "^0.3.0", "npm": "^10.8.2", "pg": "^8.13.0", "postcss": "8.4.31", "prom-client": "^15.1.3", "react": "18.2.0", "react-datepicker": "^4.25.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-icons": "^4.12.0", "react-loading-skeleton": "^3.4.0", "react-spinners": "^0.13.8", "recharts": "^2.15.1", "shadcn": "^2.0.7", "sharp": "^0.33.5", "sonner": "^1.5.0", "svix": "^1.30.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "uuid": "^11.1.0", "workbox-webpack-plugin": "^7.1.0", "zod": "^3.24.2"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.52.0", "@types/dompurify": "^3.0.5", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.19", "@types/pg": "^8.11.10", "@types/react-datepicker": "^4.19.6", "@types/supercluster": "^7.1.3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.5.14", "prisma": "^5.18.0", "webpack": "^5.93.0"}}