# Analytics Implementation Guide 1: Leveraging Existing Database Data

## Overview
This guide focuses on integrating existing database data from the current Prisma schema to replace hardcoded analytics data. We'll utilize the existing `ProductViewEvent`, `ProductAnalytics`, `UserProductInteraction`, `Review`, and `Product` models to create real-time analytics.

## Current Database Schema Analysis

### Existing Analytics-Ready Models
- ✅ **ProductViewEvent** - Tracks individual product views with timestamps, duration, source, device type
- ✅ **ProductAnalytics** - Aggregated analytics per product (views, visitors, duration, peak hours)
- ✅ **UserProductInteraction** - User engagement metrics (view count, review status, time spent)
- ✅ **Review** - Review data with ratings, timestamps, verification status
- ✅ **Product** - Product data with view counts, rating distributions
- ✅ **Business** - Business ownership and subscription data

### Interface Mapping
Current interfaces already defined in `src/app/util/Interfaces.tsx`:
- `iBusinessAnalytics` - Business-level aggregated data
- `iProductPerformance` - Product-specific performance metrics
- `iTrafficSource` - Traffic source analysis
- `iProductViewEvent` - Individual view events
- `iProductAnalytics` - Product analytics aggregation

## Implementation Tasks

### Phase 1: Database Query Layer
#### 1.1 Create Analytics Service Functions
- [ ] **Create `src/app/util/databaseAnalytics.ts`**
  - [ ] `getBusinessAnalyticsFromDB(businessId: string, period: iAnalyticsPeriod)`
  - [ ] `getProductPerformanceFromDB(businessId: string, period: iAnalyticsPeriod)`
  - [ ] `getTrafficSourcesFromDB(businessId: string, period: iAnalyticsPeriod)`
  - [ ] `getReviewAnalyticsFromDB(businessId: string, period: iAnalyticsPeriod)`

#### 1.2 Business Analytics Aggregation
- [ ] **Total Views Calculation**
  ```sql
  SELECT SUM(totalViews) FROM ProductAnalytics 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  ```
- [ ] **Unique Visitors Calculation**
  ```sql
  SELECT COUNT(DISTINCT userId) FROM ProductViewEvent 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  AND timestamp BETWEEN ? AND ?
  ```
- [ ] **Views Per Day Aggregation**
  ```sql
  SELECT DATE(timestamp) as date, COUNT(*) as views 
  FROM ProductViewEvent 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  AND timestamp BETWEEN ? AND ?
  GROUP BY DATE(timestamp)
  ORDER BY date
  ```
- [ ] **Device Type Distribution**
  ```sql
  SELECT deviceType, COUNT(*) as count 
  FROM ProductViewEvent 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  AND timestamp BETWEEN ? AND ?
  GROUP BY deviceType
  ```

#### 1.3 Review Analytics Integration
- [ ] **Total Reviews Count**
  ```sql
  SELECT COUNT(*) FROM Review 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  AND createdDate BETWEEN ? AND ?
  ```
- [ ] **Average Rating Calculation**
  ```sql
  SELECT AVG(rating) FROM Review 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  AND isDeleted = false AND isPublic = true
  ```
- [ ] **Rating Distribution**
  ```sql
  SELECT rating, COUNT(*) as count 
  FROM Review 
  WHERE productId IN (SELECT id FROM Product WHERE businessId = ?)
  GROUP BY rating
### Phase 2: API Endpoint Updates
#### 2.1 Update Business Analytics API
- [ ] **Modify `src/app/api/analytics/business/route.ts`**
  - [ ] Replace mock data generation with real database queries
  - [ ] Add error handling for database failures
  - [ ] Implement caching strategy (Redis/Memory)
  - [ ] Add query optimization for large datasets

#### 2.2 Update Traffic Sources API
- [ ] **Modify `src/app/api/analytics/traffic/route.ts`**
  - [ ] Query `ProductViewEvent.source` field for traffic sources
  - [ ] Calculate bounce rates from session data
  - [ ] Aggregate conversion rates from review creation

#### 2.3 Update Products Analytics API
- [ ] **Modify `src/app/api/analytics/products/route.ts`**
  - [ ] Query real product performance data
  - [ ] Calculate conversion rates (views to reviews)
  - [ ] Generate engagement metrics from `UserProductInteraction`

### Phase 3: Database Query Implementation

#### 3.1 Business Analytics Query
```typescript
// src/app/util/databaseAnalytics.ts
export async function getBusinessAnalyticsFromDB(
  businessId: string, 
  period: iAnalyticsPeriod
): Promise<iBusinessAnalytics> {
  const startDate = period.startDate.toISOString();
  const endDate = period.endDate.toISOString();
  
  // Get all products for this business
  const products = await prisma.product.findMany({
    where: { businessId, isDeleted: false },
    select: { id: true, name: true }
  });
  
  const productIds = products.map(p => p.id);
  
  // Total views from ProductViewEvent
  const totalViewsResult = await prisma.productViewEvent.aggregate({
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { id: true }
  });
  
  // Unique visitors
  const uniqueVisitorsResult = await prisma.productViewEvent.groupBy({
    by: ['userId', 'sessionId'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    }
  });
  
  // Views per day aggregation
  const viewsPerDayRaw = await prisma.$queryRaw`
    SELECT DATE("timestamp") as date, COUNT(*) as views 
    FROM "ProductViewEvent" 
    WHERE "productId" = ANY(${productIds})
    AND "timestamp" BETWEEN ${startDate} AND ${endDate}
    GROUP BY DATE("timestamp")
    ORDER BY date
  `;
  
  // Device type distribution
  const deviceTypesRaw = await prisma.productViewEvent.groupBy({
    by: ['deviceType'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { deviceType: true }
  });
  
  // Traffic sources
  const trafficSourcesRaw = await prisma.productViewEvent.groupBy({
    by: ['source'],
    where: {
      productId: { in: productIds },
      timestamp: { gte: new Date(startDate), lte: new Date(endDate) }
    },
    _count: { source: true }
  });
  
  // Reviews data
  const reviewsData = await prisma.review.aggregate({
    where: {
      productId: { in: productIds },
      createdDate: { gte: new Date(startDate), lte: new Date(endDate) },
      isDeleted: false,
      isPublic: true
    },
    _count: { id: true },
    _avg: { rating: true }
  });
  
  // Top products by views
  const topProductsRaw = await prisma.$queryRaw`
    SELECT p.id, p.name, COUNT(pve.id) as views,
           (COUNT(r.id)::float / COUNT(pve.id)::float * 100) as conversion
    FROM "Product" p
    LEFT JOIN "ProductViewEvent" pve ON p.id = pve."productId"
    LEFT JOIN "Review" r ON p.id = r."productId"
    WHERE p."businessId" = ${businessId}
    AND pve."timestamp" BETWEEN ${startDate} AND ${endDate}
    GROUP BY p.id, p.name
    ORDER BY views DESC
    LIMIT 5
  `;
  
  return {
    id: `ba-${businessId}`,
    businessId,
    totalViews: totalViewsResult._count.id,
    uniqueVisitors: uniqueVisitorsResult.length,
    totalReviews: reviewsData._count.id,
    averageRating: reviewsData._avg.rating || 0,
    viewsPerDay: formatViewsPerDay(viewsPerDayRaw),
    trafficSources: formatTrafficSources(trafficSourcesRaw),
    deviceTypes: formatDeviceTypes(deviceTypesRaw),
    conversionRate: calculateConversionRate(totalViewsResult._count.id, reviewsData._count.id),
    topProducts: formatTopProducts(topProductsRaw),
    lastUpdated: new Date()
  };
}
```

#### 3.2 Traffic Sources Query
```typescript
export async function getTrafficSourcesFromDB(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iTrafficSource[]> {
  const startDate = period.startDate.toISOString();
  const endDate = period.endDate.toISOString();
  
  const products = await prisma.product.findMany({
    where: { businessId, isDeleted: false },
    select: { id: true }
  });
  
  const productIds = products.map(p => p.id);
  
  const trafficData = await prisma.$queryRaw`
    SELECT 
      source,
      'organic' as medium,
      COUNT(DISTINCT "sessionId") as visitors,
      COUNT(*) as sessions,
      AVG(CASE WHEN duration > 0 THEN duration ELSE 30 END) as avg_duration,
      COUNT(DISTINCT CASE WHEN duration < 10 THEN "sessionId" END)::float / 
      COUNT(DISTINCT "sessionId")::float * 100 as bounce_rate
    FROM "ProductViewEvent"
    WHERE "productId" = ANY(${productIds})
    AND "timestamp" BETWEEN ${startDate} AND ${endDate}
    AND source IS NOT NULL
    GROUP BY source
    ORDER BY visitors DESC
  `;
  
  return trafficData.map((row: any, index: number) => ({
    id: `ts-${index}`,
    businessId,
    source: row.source || 'Direct',
    medium: row.medium,
    visitors: parseInt(row.visitors),
    sessions: parseInt(row.sessions),
    bounceRate: parseFloat(row.bounce_rate) || 0,
    conversionRate: calculateSourceConversionRate(row.source, productIds, period),
    lastUpdated: new Date()
  }));
}
```

### Phase 4: Frontend Integration
#### 4.1 Update Analytics Service
- [ ] **Modify `src/app/util/analyticsService.ts`**
  - [ ] Remove mock data fallbacks
  - [ ] Add proper error handling for API failures
  - [ ] Implement retry logic for failed requests
  - [ ] Add loading states management

#### 4.2 Component Updates
- [ ] **Update analytics components to handle real data**
  - [ ] Add loading skeletons for slow queries
  - [ ] Handle empty data states gracefully
  - [ ] Add error boundaries for failed data loads
  - [ ] Implement data refresh mechanisms

### Phase 5: Performance Optimization
#### 5.1 Database Indexing
- [ ] **Add missing indexes for analytics queries**
  ```sql
  CREATE INDEX idx_product_view_event_business_timestamp 
  ON "ProductViewEvent" ("productId", "timestamp");
  
  CREATE INDEX idx_review_business_date 
  ON "Review" ("productId", "createdDate") 
  WHERE "isDeleted" = false AND "isPublic" = true;
  
  CREATE INDEX idx_product_analytics_business 
  ON "ProductAnalytics" ("productId");
  ```

#### 5.2 Caching Strategy
- [ ] **Implement Redis caching**
  - [ ] Cache business analytics for 1 hour
  - [ ] Cache traffic sources for 30 minutes
  - [ ] Cache product performance for 15 minutes
  - [ ] Implement cache invalidation on new data

#### 5.3 Query Optimization
- [ ] **Optimize expensive queries**
  - [ ] Use materialized views for complex aggregations
  - [ ] Implement query result pagination
  - [ ] Add query timeout handling
  - [ ] Monitor query performance

### Phase 6: Testing & Validation
#### 6.1 Data Accuracy Testing
- [ ] **Validate analytics calculations**
  - [ ] Compare aggregated data with raw data
  - [ ] Test edge cases (no data, single data point)
  - [ ] Verify date range filtering
  - [ ] Test business isolation (no cross-business data)

#### 6.2 Performance Testing
- [ ] **Load testing for analytics endpoints**
  - [ ] Test with large datasets (10k+ views)
  - [ ] Measure query response times
  - [ ] Test concurrent user access
  - [ ] Monitor memory usage

#### 6.3 Integration Testing
- [ ] **End-to-end testing**
  - [ ] Test complete analytics flow
  - [ ] Verify real-time data updates
  - [ ] Test error handling scenarios
  - [ ] Validate UI responsiveness

## Technical Considerations

### Performance Optimization
- **Database Connection Pooling**: Ensure proper connection management
- **Query Batching**: Combine multiple queries where possible
- **Lazy Loading**: Load analytics data only when needed
- **Background Processing**: Consider moving heavy calculations to background jobs

### Caching Strategy
- **Multi-level Caching**: Database query cache + API response cache
- **Cache Invalidation**: Smart invalidation based on data changes
- **Cache Warming**: Pre-populate cache for frequently accessed data

### Scalability
- **Horizontal Scaling**: Design queries to work with read replicas
- **Data Partitioning**: Consider partitioning large tables by date
- **Archival Strategy**: Archive old analytics data to maintain performance

## Migration Strategy

### Phase 1: Parallel Implementation (Week 1-2)
- [ ] Implement database analytics alongside mock data
- [ ] Add feature flag to switch between mock and real data
- [ ] Test with subset of businesses

### Phase 2: Gradual Rollout (Week 3-4)
- [ ] Enable real data for 25% of businesses
- [ ] Monitor performance and accuracy
- [ ] Fix any issues discovered

### Phase 3: Full Migration (Week 5-6)
- [ ] Enable real data for all businesses
- [ ] Remove mock data and feature flags
- [ ] Monitor system performance

### Phase 4: Optimization (Week 7-8)
- [ ] Implement advanced caching
- [ ] Optimize slow queries
- [ ] Add advanced analytics features

## Success Metrics
- [ ] **Performance**: Analytics load time < 2 seconds
- [ ] **Accuracy**: 99.9% data accuracy compared to raw data
- [ ] **Reliability**: 99.5% uptime for analytics endpoints
- [ ] **User Experience**: Smooth transitions, no UI breaks