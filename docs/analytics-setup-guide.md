# Analytics System Setup Guide

## 1. Redis Installation & Configuration

### Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server
sudo systemctl enable redis

# macOS (Homebrew)
brew install redis
brew services start redis

# Windows (via WSL)
sudo apt install redis-server
```

### Configuration (`/etc/redis/redis.conf`)
```properties
bind 127.0.0.1
port 6379
protected-mode yes
requirepass YOUR_STRONG_PASSWORD
dir /var/lib/redis
appendonly yes
```


## 3. Environment Variables

Create `.env` file:
```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=YOUR_STRONG_PASSWORD
REDIS_TTL=3600

# Prometheus Configuration
PROMETHEUS_METRICS_ENDPOINT=/metrics
PROMETHEUS_PORT=9090

# Application Configuration
ANALYTICS_CACHE_ENABLED=true
ANALYTICS_DEBUG_MODE=false
```

## 4. Required Dependencies
```bash
pnpm install ioredis prom-client dotenv
```

## 5. Security Considerations
- Always set strong Redis passwords
- Bind Redis to localhost in production
- Use protected mode in Redis
- Enable authentication for Prometheus metrics endpoint
- Set appropriate timeouts and limits in Redis

## 6. Testing Setup
```bash
# Test Redis connection
redis-cli ping
# Should return "PONG" if working

# Test Prometheus metrics
curl http://localhost:3000/metrics
# Should return metrics in Prometheus format
```

## 7. Service Status Checks
```bash
systemctl status redis  # Redis status
redis-cli client list   # Connected clients
redis-cli info memory   # Memory usage