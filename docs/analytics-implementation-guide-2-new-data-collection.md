# Analytics Implementation Guide 2: New Data Collection Requirements

## Overview
This guide details the implementation of new data collection systems for advanced analytics not currently captured in the database. This includes enhanced device tracking, user behavior metrics, geographic data, conversion funnels, and engagement analytics.

## New Data Collection Requirements

### 1. Enhanced Device & Browser Tracking
Currently missing detailed device information beyond basic device type.

#### 1.1 Device Information Schema
```typescript
// New interface for detailed device tracking
export interface iDeviceInfo {
  id?: string;
  sessionId: string;
  userAgent: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  deviceBrand?: string;
  deviceModel?: string;
  operatingSystem: string;
  osVersion: string;
  browser: string;
  browserVersion: string;
  screenResolution: string;
  viewportSize: string;
  colorDepth: number;
  pixelRatio: number;
  touchSupport: boolean;
  connectionType?: string;
  language: string;
  timezone: string;
  createdAt: Date;
}
```

#### 1.2 Database Migration
- [ ] **Create DeviceInfo table**
  ```sql
  CREATE TABLE "DeviceInfo" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" TEXT NOT NULL,
    "userAgent" TEXT NOT NULL,
    "deviceType" TEXT NOT NULL,
    "deviceBrand" TEXT,
    "deviceModel" TEXT,
    "operatingSystem" TEXT NOT NULL,
    "osVersion" TEXT NOT NULL,
    "browser" TEXT NOT NULL,
    "browserVersion" TEXT NOT NULL,
    "screenResolution" TEXT NOT NULL,
    "viewportSize" TEXT NOT NULL,
    "colorDepth" INTEGER NOT NULL,
    "pixelRatio" REAL NOT NULL,
    "touchSupport" BOOLEAN NOT NULL,
    "connectionType" TEXT,
    "language" TEXT NOT NULL,
    "timezone" TEXT NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT now()
  );
  
  CREATE INDEX "idx_device_info_session" ON "DeviceInfo"("sessionId");
  CREATE INDEX "idx_device_info_device_type" ON "DeviceInfo"("deviceType");
  CREATE INDEX "idx_device_info_created_at" ON "DeviceInfo"("createdAt");
  ```

### 2. User Session Tracking
Enhanced session management for better user journey analysis.

#### 2.1 Session Schema
```typescript
export interface iUserSession {
  id?: string;
  sessionId: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  pageViews: number;
  uniquePages: number;
  bounced: boolean;
  converted: boolean;
  conversionType?: string;
  referrer?: string;
  landingPage: string;
  exitPage?: string;
  deviceInfoId: string;
  ipAddress?: string;
  country?: string;
  city?: string;
  region?: string;
  isBot: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 2.2 Database Migration
- [ ] **Create UserSession table**
  ```sql
  CREATE TABLE "UserSession" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" TEXT NOT NULL UNIQUE,
    "userId" TEXT,
    "startTime" TIMESTAMP NOT NULL,
    "endTime" TIMESTAMP,
    "duration" INTEGER,
    "pageViews" INTEGER NOT NULL DEFAULT 0,
    "uniquePages" INTEGER NOT NULL DEFAULT 0,
    "bounced" BOOLEAN NOT NULL DEFAULT false,
    "converted" BOOLEAN NOT NULL DEFAULT false,
    "conversionType" TEXT,
    "referrer" TEXT,
    "landingPage" TEXT NOT NULL,
    "exitPage" TEXT,
    "deviceInfoId" TEXT NOT NULL,
    "ipAddress" TEXT,
    "country" TEXT,
    "city" TEXT,
    "region" TEXT,
    "isBot" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
    
    FOREIGN KEY ("deviceInfoId") REFERENCES "DeviceInfo"("id"),
    FOREIGN KEY ("userId") REFERENCES "User"("id")
  );
  
  CREATE INDEX "idx_user_session_user_id" ON "UserSession"("userId");
  CREATE INDEX "idx_user_session_start_time" ON "UserSession"("startTime");
  CREATE INDEX "idx_user_session_converted" ON "UserSession"("converted");
  CREATE INDEX "idx_user_session_country" ON "UserSession"("country");
  ```

### 3. Page View Events
Detailed page view tracking with scroll depth and engagement metrics.

#### 3.1 Page View Schema
```typescript
export interface iPageViewEvent {
  id?: string;
  sessionId: string;
  userId?: string;
  productId?: string;
  pageUrl: string;
  pageTitle: string;
  referrer?: string;
  timestamp: Date;
  timeOnPage?: number;
  scrollDepth: number;
  maxScrollDepth: number;
  clickCount: number;
  keyboardEvents: number;
  mouseMovements: number;
  idleTime: number;
  exitMethod?: 'navigation' | 'close' | 'refresh' | 'back';
  isEngaged: boolean;
  conversionEvent?: string;
  customEvents: Record<string, any>;
}
```

#### 3.2 Database Migration
- [ ] **Create PageViewEvent table**
  ```sql
  CREATE TABLE "PageViewEvent" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" TEXT NOT NULL,
    "userId" TEXT,
    "productId" TEXT,
    "pageUrl" TEXT NOT NULL,
    "pageTitle" TEXT NOT NULL,
    "referrer" TEXT,
    "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
    "timeOnPage" INTEGER,
    "scrollDepth" REAL NOT NULL DEFAULT 0,
    "maxScrollDepth" REAL NOT NULL DEFAULT 0,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "keyboardEvents" INTEGER NOT NULL DEFAULT 0,
    "mouseMovements" INTEGER NOT NULL DEFAULT 0,
    "idleTime" INTEGER NOT NULL DEFAULT 0,
    "exitMethod" TEXT,
    "isEngaged" BOOLEAN NOT NULL DEFAULT false,
    "conversionEvent" TEXT,
    "customEvents" JSONB DEFAULT '{}',
    
    FOREIGN KEY ("sessionId") REFERENCES "UserSession"("sessionId"),
    FOREIGN KEY ("userId") REFERENCES "User"("id"),
    FOREIGN KEY ("productId") REFERENCES "Product"("id")
  );
  
  CREATE INDEX "idx_page_view_session" ON "PageViewEvent"("sessionId");
  CREATE INDEX "idx_page_view_product" ON "PageViewEvent"("productId");
  CREATE INDEX "idx_page_view_timestamp" ON "PageViewEvent"("timestamp");
  CREATE INDEX "idx_page_view_engaged" ON "PageViewEvent"("isEngaged");
  ```

### 4. Conversion Funnel Tracking
Track user journey through conversion funnels.

#### 4.1 Conversion Funnel Schema
```typescript
export interface iConversionFunnel {
  id?: string;
  businessId: string;
  name: string;
  description?: string;
  steps: Array<{
    stepNumber: number;
    stepName: string;
    pageUrl?: string;
    eventType: string;
    eventData?: Record<string, any>;
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface iConversionEvent {
  id?: string;
  funnelId: string;
  sessionId: string;
  userId?: string;
  stepNumber: number;
  timestamp: Date;
  eventData?: Record<string, any>;
  completed: boolean;
  timeToComplete?: number;
}
```

#### 4.2 Database Migration
- [ ] **Create conversion tracking tables**
  ```sql
  CREATE TABLE "ConversionFunnel" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "businessId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "steps" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
    
    FOREIGN KEY ("businessId") REFERENCES "Business"("id")
  );
  
  CREATE TABLE "ConversionEvent" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "funnelId" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "userId" TEXT,
    "stepNumber" INTEGER NOT NULL,
    "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
    "eventData" JSONB DEFAULT '{}',
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "timeToComplete" INTEGER,
    
    FOREIGN KEY ("funnelId") REFERENCES "ConversionFunnel"("id"),
    FOREIGN KEY ("sessionId") REFERENCES "UserSession"("sessionId"),
    FOREIGN KEY ("userId") REFERENCES "User"("id")
  );
  
  CREATE INDEX "idx_conversion_event_funnel" ON "ConversionEvent"("funnelId");
  CREATE INDEX "idx_conversion_event_session" ON "ConversionEvent"("sessionId");
  CREATE INDEX "idx_conversion_event_timestamp" ON "ConversionEvent"("timestamp");
### 5. Geographic Analytics
Track user locations for geographic insights.

#### 5.1 Geographic Data Schema
```typescript
export interface iGeographicData {
  id?: string;
  sessionId: string;
  ipAddress: string;
  country: string;
  countryCode: string;
  region: string;
  regionCode: string;
  city: string;
  latitude?: number;
  longitude?: number;
  timezone: string;
  isp?: string;
  organization?: string;
  asn?: string;
  createdAt: Date;
}
```

#### 5.2 Database Migration
- [ ] **Create GeographicData table**
  ```sql
  CREATE TABLE "GeographicData" (
    "id" TEXT NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" TEXT NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "countryCode" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "regionCode" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "latitude" REAL,
    "longitude" REAL,
    "timezone" TEXT NOT NULL,
    "isp" TEXT,
    "organization" TEXT,
    "asn" TEXT,
    "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
    
    FOREIGN KEY ("sessionId") REFERENCES "UserSession"("sessionId")
  );
  
  CREATE INDEX "idx_geographic_session" ON "GeographicData"("sessionId");
  CREATE INDEX "idx_geographic_country" ON "GeographicData"("country");
  CREATE INDEX "idx_geographic_city" ON "GeographicData"("city");
  ```

### 6. A/B Testing Framework
Support for A/B testing analytics.

#### 6.1 A/B Test Schema
```typescript
export interface iABTest {
  id?: string;
  businessId: string;
  name: string;
  description?: string;
  status: 'draft' | 'running' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  variants: Array<{
    id: string;
    name: string;
    description?: string;
    trafficAllocation: number;
    config: Record<string, any>;
  }>;
  targetingRules?: Record<string, any>;
  conversionGoals: Array<{
    name: string;
    eventType: string;
    eventData?: Record<string, any>;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface iABTestParticipant {
  id?: string;
  testId: string;
  sessionId: string;
  userId?: string;
  variantId: string;
  assignedAt: Date;
  converted: boolean;
  conversionAt?: Date;
  conversionValue?: number;
}
```

## Implementation Tasks

### Phase 1: Client-Side Data Collection
#### 1.1 Create Analytics SDK
- [ ] **Create `src/app/util/analyticsSDK.ts`**
  - [ ] Device information collection
  - [ ] Session management
  - [ ] Page view tracking
  - [ ] User interaction monitoring
  - [ ] Conversion event tracking

#### 1.2 Device Information Collection
```typescript
// src/app/util/deviceDetection.ts
export function collectDeviceInfo(): Partial<iDeviceInfo> {
  const userAgent = navigator.userAgent;
  const screen = window.screen;
  
  return {
    userAgent,
    deviceType: detectDeviceType(),
    deviceBrand: detectDeviceBrand(userAgent),
    deviceModel: detectDeviceModel(userAgent),
    operatingSystem: detectOS(userAgent),
    osVersion: detectOSVersion(userAgent),
    browser: detectBrowser(userAgent),
    browserVersion: detectBrowserVersion(userAgent),
    screenResolution: `${screen.width}x${screen.height}`,
    viewportSize: `${window.innerWidth}x${window.innerHeight}`,
    colorDepth: screen.colorDepth,
    pixelRatio: window.devicePixelRatio,
    touchSupport: 'ontouchstart' in window,
    connectionType: (navigator as any).connection?.effectiveType,
    language: navigator.language,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
}
```

#### 1.3 Session Tracking Implementation
```typescript
// src/app/util/sessionTracking.ts
export class SessionTracker {
  private sessionId: string;
  private startTime: Date;
  private pageViews: number = 0;
  private uniquePages: Set<string> = new Set();
  
  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = new Date();
    this.initializeSession();
  }
  
  private async initializeSession() {
    const deviceInfo = collectDeviceInfo();
    const geoData = await this.getGeographicData();
    
    await fetch('/api/analytics/session/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId: this.sessionId,
        startTime: this.startTime,
        deviceInfo,
        geoData,
        landingPage: window.location.href,
        referrer: document.referrer
      })
    });
  }
  
  public trackPageView(productId?: string) {
    this.pageViews++;
    this.uniquePages.add(window.location.pathname);
    
    const pageViewData: Partial<iPageViewEvent> = {
      sessionId: this.sessionId,
      productId,
      pageUrl: window.location.href,
      pageTitle: document.title,
      referrer: document.referrer,
      timestamp: new Date()
    };
    
    this.sendPageView(pageViewData);
    this.setupEngagementTracking();
  }
  
  private setupEngagementTracking() {
    let scrollDepth = 0;
    let maxScrollDepth = 0;
    let clickCount = 0;
    let keyboardEvents = 0;
    let mouseMovements = 0;
    let idleTime = 0;
    let lastActivity = Date.now();
    
    // Scroll tracking
    window.addEventListener('scroll', () => {
      const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
      scrollDepth = Math.max(scrollDepth, scrollPercent);
      maxScrollDepth = Math.max(maxScrollDepth, scrollPercent);
      lastActivity = Date.now();
    });
    
    // Click tracking
    document.addEventListener('click', () => {
      clickCount++;
      lastActivity = Date.now();
    });
    
    // Keyboard tracking
    document.addEventListener('keydown', () => {
      keyboardEvents++;
      lastActivity = Date.now();
    });
    
    // Mouse movement tracking (throttled)
    let mouseMoveThrottle = false;
    document.addEventListener('mousemove', () => {
      if (!mouseMoveThrottle) {
        mouseMovements++;
        lastActivity = Date.now();
        mouseMoveThrottle = true;
        setTimeout(() => mouseMoveThrottle = false, 100);
      }
    });
    
    // Idle time tracking
    setInterval(() => {
      const now = Date.now();
      if (now - lastActivity > 5000) { // 5 seconds of inactivity
        idleTime += 1;
      }
    }, 1000);
    
    // Send engagement data on page unload
    window.addEventListener('beforeunload', () => {
      this.sendEngagementData({
        scrollDepth,
        maxScrollDepth,
        clickCount,
        keyboardEvents,
        mouseMovements,
        idleTime,
        timeOnPage: Date.now() - this.startTime.getTime()
      });
    });
  }
}
```

### Phase 2: Server-Side Implementation
#### 2.1 Create Analytics API Endpoints
- [ ] **Create session tracking endpoints**
  - [ ] `POST /api/analytics/session/start` - Initialize session
  - [ ] `PUT /api/analytics/session/update` - Update session data
  - [ ] `POST /api/analytics/session/end` - End session

- [ ] **Create page view tracking endpoints**
  - [ ] `POST /api/analytics/pageview` - Track page view
  - [ ] `PUT /api/analytics/pageview/engagement` - Update engagement metrics

- [ ] **Create conversion tracking endpoints**
  - [ ] `POST /api/analytics/conversion` - Track conversion event
  - [ ] `GET /api/analytics/funnel/:funnelId` - Get funnel analytics

#### 2.2 Geographic Data Integration
```typescript
// src/app/util/geoLocation.ts
export async function getGeographicData(ipAddress: string): Promise<iGeographicData> {
  try {
    // Use IP geolocation service (e.g., MaxMind, IPinfo)
    const response = await fetch(`https://ipinfo.io/${ipAddress}/json?token=${process.env.IPINFO_TOKEN}`);
    const data = await response.json();
    
    return {
      ipAddress,
      country: data.country_name,
      countryCode: data.country,
      region: data.region,
      regionCode: data.region,
      city: data.city,
      latitude: parseFloat(data.loc?.split(',')[0]),
      longitude: parseFloat(data.loc?.split(',')[1]),
      timezone: data.timezone,
      isp: data.org,
      organization: data.org,
      asn: data.asn?.asn
    };
  } catch (error) {
    console.error('Error fetching geographic data:', error);
    return null;
  }
}
```

### Phase 3: Advanced Analytics Features
#### 3.1 Real-time Analytics Dashboard
- [ ] **Implement WebSocket connections for real-time data**
  - [ ] Live visitor count
  - [ ] Real-time conversion tracking
  - [ ] Live geographic distribution
  - [ ] Active page views

#### 3.2 Cohort Analysis
```typescript
export interface iCohortAnalysis {
  cohortPeriod: 'daily' | 'weekly' | 'monthly';
  cohorts: Array<{
    cohortDate: string;
    cohortSize: number;
    retentionRates: Record<string, number>;
  }>;
}
```

#### 3.3 User Journey Mapping
```typescript
export interface iUserJourney {
  sessionId: string;
  userId?: string;
  touchpoints: Array<{
    timestamp: Date;
    pageUrl: string;
    action: string;
    duration: number;
    exitPoint?: boolean;
    conversionPoint?: boolean;
  }>;
  totalDuration: number;
  conversionPath?: string[];
}
```

### Phase 4: Privacy & Compliance
#### 4.1 GDPR Compliance
- [ ] **Implement consent management**
  - [ ] Cookie consent tracking
  - [ ] Data anonymization options
  - [ ] Right to be forgotten implementation
  - [ ] Data export functionality

#### 4.2 Data Retention Policies
- [ ] **Implement data lifecycle management**
  - [ ] Automatic data archival after 2 years
  - [ ] Anonymous data retention for analytics
  - [ ] PII data deletion workflows

### Phase 5: Performance Optimization
#### 5.1 Data Collection Optimization
- [ ] **Implement efficient data collection**
  - [ ] Batch API calls to reduce server load
  - [ ] Client-side data buffering
  - [ ] Compression for large payloads
  - [ ] Error handling and retry logic

#### 5.2 Database Optimization
- [ ] **Optimize for high-volume writes**
  - [ ] Implement write-optimized indexes
  - [ ] Use time-series database for events
  - [ ] Implement data partitioning by date
  - [ ] Add database connection pooling

### Phase 6: Integration with Existing Components
#### 6.1 Update Product Pages
- [ ] **Integrate analytics tracking in product components**
  ```typescript
  // In ProductPageClient.tsx
  useEffect(() => {
    const tracker = new SessionTracker();
    tracker.trackPageView(product.id);
    
    // Track product-specific events
    tracker.trackEvent('product_view', {
      productId: product.id,
      productName: product.name,
      businessId: product.businessId
    });
  }, [product]);
  ```

#### 6.2 Update Analytics Dashboard
- [ ] **Add new analytics visualizations**
  - [ ] Geographic heat maps
  - [ ] User journey flow diagrams
  - [ ] Conversion funnel visualizations
  - [ ] Real-time metrics widgets

## Technical Implementation Details

### Client-Side SDK Architecture
```typescript
// src/app/util/analytics/index.ts
export class ReviewItAnalytics {
  private sessionTracker: SessionTracker;
  private conversionTracker: ConversionTracker;
  private eventQueue: AnalyticsEvent[] = [];
  
  constructor(config: AnalyticsConfig) {
    this.sessionTracker = new SessionTracker(config);
    this.conversionTracker = new ConversionTracker(config);
    this.initializeTracking();
  }
  
  // Public API methods
  public trackPageView(data: PageViewData) { }
  public trackEvent(eventName: string, data: any) { }
  public trackConversion(conversionData: ConversionData) { }
  public setUserProperties(properties: UserProperties) { }
  public identifyUser(userId: string) { }
}
```

### Server-Side Event Processing
```typescript
// src/app/api/analytics/events/route.ts
export async function POST(request: NextRequest) {
  const events = await request.json();
  
  // Process events in batch
  await Promise.all(events.map(async (event: AnalyticsEvent) => {
    switch (event.type) {
      case 'page_view':
        await processPageViewEvent(event);
        break;
      case 'conversion':
        await processConversionEvent(event);
        break;
      case 'engagement':
        await processEngagementEvent(event);
        break;
    }
  }));
  
  return NextResponse.json({ success: true });
}
```

## Migration Strategy

### Phase 1: Infrastructure Setup (Week 1-2)
- [ ] Create new database tables
- [ ] Implement basic analytics SDK
- [ ] Set up geographic data service
- [ ] Create basic API endpoints

### Phase 2: Data Collection (Week 3-4)
- [ ] Deploy client-side tracking
- [ ] Implement session management
- [ ] Add conversion tracking
- [ ] Test data collection accuracy

### Phase 3: Analytics Integration (Week 5-6)
- [ ] Update analytics dashboard
- [ ] Add new visualization components
- [ ] Implement real-time features
- [ ] Performance optimization

### Phase 4: Advanced Features (Week 7-8)
- [ ] A/B testing framework
- [ ] Cohort analysis
- [ ] User journey mapping
- [ ] Advanced reporting

## Success Metrics
- [ ] **Data Collection**: 99.9% event capture rate
- [ ] **Performance**: < 50ms impact on page load
- [ ] **Accuracy**: < 1% data loss in collection pipeline
- [ ] **Privacy**: 100% GDPR compliance
- [ ] **Scalability**: Handle 10k+ concurrent users

## Monitoring & Alerting
- [ ] **Set up monitoring for**
  - [ ] Data collection pipeline health
  - [ ] API endpoint performance
  - [ ] Database query performance
  - [ ] Error rates and data quality
  - [ ] Privacy compliance metrics