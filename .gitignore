# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
+ .turbo
public/sw.js.map
public/sw.js.map
public/workbox-f1770938.js
public/sw.js
public/sw.js.map
public/workbox-fb90b81a.js
public/workbox-fb90b81a.js.map
public/workbox-7144475a.js
public/workbox-7144475a.js.map
public/workbox-3c9d0171.js
.avante_chat_history
public/sw.js.map
public/loaderio-89bca4a08850d0b7460622ffb1161a89.txt
.aider*

# clerk configuration (can include secrets)
/.clerk/
dump.rdb
.aider.tags.cache.v4
.aider.chat.history.md
.aider.tags.cache.v4
.aider.chat.history.md
