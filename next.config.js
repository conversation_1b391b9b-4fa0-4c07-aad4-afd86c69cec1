// Temporarily disabled PWA to test GPU usage
const withPWA = require("@ducanh2912/next-pwa").default({
  // Force disable PWA regardless of environment
  disable: true,
  dest: "public",
  // PWA completely disabled for testing
  // Remove heavy workbox-range-requests chunk
  buildExcludes: [/workbox-range-requests/],
  // Minimum caching rules: keep JS/CSS chunks and Next-optimized images only
  runtimeCaching: [
    {
      urlPattern: /^\/(_next|static)\/static\/.*/, // all build static files
      handler: "CacheFirst",
      options: {
        cacheName: "static-assets",
        expiration: { maxEntries: 50 },
      },
    },
    {
      urlPattern: /^\/_next\/image/, // images served by next/image
      handler: "StaleWhileRevalidate",
      options: {
        cacheName: "images",
        expiration: { maxEntries: 100 },
      },
    },
  ],
  // Simple offline fallback for navigation requests
  fallback: {
    document: "/offline.html",
  },
});

module.exports = withPWA({
  reactStrictMode: false,
  env: {
    IS_DEV: process.env.NODE_ENV === 'development' ? 'true' : 'false',
  },
  images: {
    remotePatterns: [
      { hostname: "i5.walmartimages.com", protocol: "https" },
      { hostname: "newsroom.gy", protocol: "https" },
      { hostname: "img.clerk.com", protocol: "https" },
      { hostname: "res.cloudinary.com", protocol: "https" },
      { hostname: "cloudflare-ipfs.com", protocol: "https" },
      { hostname: "loremflickr.com", protocol: "https" },
      { hostname: "placehold.co", protocol: "https" },
      { hostname: "images.clerk.dev", protocol: "https" },
      { hostname: "images.unsplash.com", protocol: "https" },
      { hostname: "picsum.photos", protocol: "https" },
      { hostname: "img.youtube.com", protocol: "https" },
      // TikTok CDN domains
      { hostname: "p16-sign-va.tiktokcdn.com", protocol: "https" },
      { hostname: "p16-sign-sg.tiktokcdn.com", protocol: "https" },
      { hostname: "p77-sign-va.tiktokcdn.com", protocol: "https" },
      { hostname: "p77-sign-sg.tiktokcdn.com", protocol: "https" },
      { hostname: "p16-sign-useast2a.tiktokcdn.com", protocol: "https" },
    ],
  },
  async redirects() {
    return [
      {
        source: '/widgets/embed.js',
        destination: '/api/widgets/embed.js',
        permanent: false,
      },
      {
        source: '/',
        destination: '/',
        has: [
          {
            type: 'query',
            key: '_rsc',
          }
        ],
        permanent: true,
      },
      {
        source: '/:path*',
        destination: '/:path*',
        has: [
          {
            type: 'query',
            key: '_rsc',
          }
        ],
        permanent: true,
      }
    ]
  },
  async headers() {
    return [
      {
        // Add noindex for API routes
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          }
        ],
      },
      {
        // Specific headers for review pages to ensure proper metadata handling
        source: '/fr',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Only set index headers for specific public routes
        // This allows robots.ts to handle the rest
        source: '/(browse|reviews|category|pricing|submit|install)/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Set index headers for the homepage
        source: '/',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          }
        ],
      },
      {
        // Widget-specific CORS - Allow all origins for public widget APIs
        source: '/api/public/widgets/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*', // Allow all origins for public widget APIs
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, X-Widget-ID, X-Referrer, User-Agent',
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400', // Cache preflight for 24 hours
          },
        ],
      },
      {
        // Widget iframe pages - Allow embedding in any iframe with permissive CSP
        source: '/widgets/iframe/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; font-src 'self' data: https:; img-src 'self' data: blob: https:; connect-src 'self' https:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; frame-ancestors *;",
          },
          {
            key: 'X-Frame-Options',
            value: 'ALLOWALL',
          },
        ],
      },
      {
        // Add CORS headers for all other routes (restricted)
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
  // headers: [
  //   {
  //     source: '/fr',
  //     headers: [
  //       {
  //         key: 'Cache-Control',
  //         value: 'no-cache',
  //       },
  //     ],
  //   },
  // ],
});
