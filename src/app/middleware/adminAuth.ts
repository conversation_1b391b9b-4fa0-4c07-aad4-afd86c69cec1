import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

/**
 * MIDDLEWARE RELATIONSHIP:
 *
 * This specialized middleware is NOT automatically run by Next.js.
 * Unlike the main middleware.ts, this is designed to be imported and used
 * within specific API routes or page handlers that need admin authentication.
 *
 * This middleware uses Clerk's getAuth() function for authentication,
 * while the main middleware.ts uses clerkMiddleware() for global auth.
 *
 * Use this when you need admin auth checks within specific routes,
 * especially in the app directory structure.
 */

export async function adminAuthMiddleware(request: NextRequest) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Check if user has admin role in database
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: userId
      },
      select: {
        role: true,
        status: true
      }
    });

    if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
      return new NextResponse("Forbidden: Admin access required", {
        status: 403,
      });
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Admin auth middleware error:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

type RouteHandler<T = { [key: string]: string | string[] }> = (
  request: NextRequest,
  { params }: { params: T }
) => Promise<NextResponse>;

/**
 * Higher-order function that wraps route handlers with admin authentication
 * This is used to protect specific API routes or page handlers
 */
export function withAdminAuth<T = { [key: string]: string | string[] }>(
  handler: RouteHandler<T>
): RouteHandler<T> {
  return async (request: NextRequest, { params }: { params: T }) => {
    try {
      const { userId } = getAuth(request);

      if (!userId) {
        return new NextResponse("Unauthorized", { status: 401 });
      }

      // Check if user has admin role in database
      const user = await prisma.user.findFirst({
        where: {
          clerkUserId: userId
        },
        select: {
          role: true,
          status: true
        }
      });

      if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
        return new NextResponse("Forbidden: Admin access required", {
          status: 403,
        });
      }

      return handler(request, { params });
    } catch (error) {
      console.error("Admin auth wrapper error:", error);
      return new NextResponse("Internal Server Error", { status: 500 });
    }
  };
}
