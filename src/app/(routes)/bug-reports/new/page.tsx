"use client";

import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import BugReportForm from "@/app/components/bug-reports/BugReportForm";
import { toast } from "sonner";

export default function NewBugReportPage() {
    const { isSignedIn, isLoaded } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (isLoaded && !isSignedIn) {
            toast.error("Please sign in to submit a bug report");
            // Let <PERSON> handle the redirect automatically instead of manual redirect
            // This prevents CORS issues with Account Portal
        }
    }, [isLoaded, isSignedIn, router]);

    if (!isLoaded || !isSignedIn) {
        return (
            <div className="flex flex-col justify-center items-center min-h-screen px-4">
                <div className="text-center max-w-md">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
                    <p className="text-gray-600 mb-6">
                        You need to be signed in to submit a bug report. Please sign in to continue.
                    </p>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                    <p className="text-sm text-gray-500">
                        Redirecting you to the sign-in page...
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-2xl font-bold mb-6">Submit a Bug Report</h1>
            <p className="text-gray-600 mb-8">
                Help us improve by reporting any issues you encounter. Please provide as much detail as possible.
            </p>
            <BugReportForm />
        </div>
    );
} 