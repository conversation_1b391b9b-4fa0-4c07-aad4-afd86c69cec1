import React from 'react';
import { Shield, CheckCircle, Building2, FileText, Phone, Mail, BadgeCheck } from 'lucide-react';
import Link from 'next/link';

export const metadata = {
    title: 'Verification Process | Review It',
    description: 'Learn about Review It&apos;s verification process and how we ensure the trustworthiness of our platform.',
};

export default function VerificationProcess() {
    return (
        <div className="container mx-auto px-4 py-8 max-w-4xl">
            <div className="flex items-center mb-8">
                <Shield className="h-10 w-10 text-blue-600 mr-4" />
                <h1 className="text-3xl font-bold">Our Verification Process</h1>
            </div>

            <div className="prose prose-lg">
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Why We Verify</h2>
                    <p>
                        At Review It, we believe in building a trusted community where users can find reliable information
                        about products and businesses. Our verification process ensures that the information on our platform
                        is accurate, up-to-date, and comes from legitimate sources.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">How We Verify Products</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <CheckCircle className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Initial Verification</h3>
                            </div>
                            <p>
                                When a product is first listed on Review It, our team performs an initial verification
                                to ensure it meets our quality standards. This includes checking for:
                            </p>
                            <ul className="list-disc pl-6 mt-2">
                                <li>Accurate product information</li>
                                <li>Legitimate business association</li>
                                <li>Appropriate categorization</li>
                                <li>Quality of images and descriptions</li>
                            </ul>
                        </div>

                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <Building2 className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Business Verification</h3>
                            </div>
                            <p>
                                When a business owner claims their listing, we verify their identity and ownership through:
                            </p>
                            <ul className="list-disc pl-6 mt-2">
                                <li>Business registration documents</li>
                                <li>Government-issued ID</li>
                                <li>Business address verification</li>
                                <li>Contact information confirmation</li>
                            </ul>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <FileText className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Document Verification</h3>
                            </div>
                            <p>
                                We require specific documents to verify business ownership:
                            </p>
                            <ul className="list-disc pl-6 mt-2">
                                <li>Business registration certificate</li>
                                <li>Tax identification number</li>
                                <li>Proof of address</li>
                                <li>Authorized representative documentation</li>
                            </ul>
                        </div>

                        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                            <div className="flex items-center mb-3">
                                <Phone className="h-6 w-6 text-blue-600 mr-2" />
                                <h3 className="text-xl font-medium">Contact Verification</h3>
                            </div>
                            <p>
                                We verify contact information through:
                            </p>
                            <ul className="list-disc pl-6 mt-2">
                                <li>Phone number verification</li>
                                <li>Email confirmation</li>
                                <li>Physical address verification</li>
                                <li>Website domain ownership</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">The Verification Badge</h2>
                    <div className="flex items-center mb-4">
                        <BadgeCheck className="h-8 w-8 text-blue-600 mr-3" />
                        <p className="text-lg">
                            Once a business completes our verification process, they receive a verification badge
                            that appears on their profile and all associated products.
                        </p>
                    </div>
                    <p>
                        This badge indicates to users that the business has been verified by our team and that
                        the information provided is accurate and trustworthy.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Benefits of Verification</h2>
                    <p>
                        Verified businesses enjoy several benefits on our platform:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Increased trust and credibility with potential customers</li>
                        <li>Ability to respond to reviews and engage with customers</li>
                        <li>Access to analytics and insights about their products</li>
                        <li>Priority support from our customer service team</li>
                        <li>Featured placement in search results</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Claim Your Business</h2>
                    <p>
                        If you&apos;re a business owner and would like to claim your listing on Review It, please
                        follow these steps:
                    </p>
                    <ol className="list-decimal pl-6 mt-2">
                        <li>Create a Review It account</li>
                        <li>Search for your business on our platform</li>
                        <li>Click on &quot;Claim this business&quot;</li>
                        <li>Complete the verification form with required documentation</li>
                        <li>Our team will review your submission within 2-3 business days</li>
                    </ol>
                    <div className="mt-6">
                        <Link href="/claim-business" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                            Claim Your Business
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        </Link>
                    </div>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
                    <p>
                        If you have any questions about our verification process or need assistance with claiming
                        your business, please contact our support team:
                    </p>
                    <div className="flex items-center mt-2">
                        <Mail className="h-5 w-5 text-blue-600 mr-2" />
                        <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                            <EMAIL>
                        </a>
                    </div>
                </section>
            </div>
        </div>
    );
} 