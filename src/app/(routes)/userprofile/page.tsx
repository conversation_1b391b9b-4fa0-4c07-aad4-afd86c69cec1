import UserProfileComponent from "@/app/components/UserProfileComponent";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserDirect } from "@/app/util/getUserDirect";
import { iUser } from "@/app/util/Interfaces";
import { checkUserSecureAccess } from "@/app/util/secureUserCheck";
import { User, Settings, Star, MessageCircle, Sparkles } from "lucide-react";
import Schema from "@/app/components/Schema";


export default async function Page() {
  const { userId } = await auth();
  const  userObject  = await auth();

  // If no user is authenticated, show a message instead of redirecting
  if (!userId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Authentication Required</h1>
          <p className="text-gray-600">Please sign in to view your profile.</p>
        </div>
      </div>
    );
  }

  // Get user data from our database
  const userData = await getUserDirect();

  // If we can't get the user data, show an error message instead of redirecting
  if (!userData.success || !userData.data) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50 flex items-center justify-center px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Profile Not Found</h1>
          <p className="text-gray-600">Unable to load your profile data. Please try again later.</p>
        </div>
      </div>
    );
  }

  const user = userData.data as iUser;

  // Check user security status (banned, suspended, deleted)
  const securityCheck = checkUserSecureAccess(user);
  if (!securityCheck.isAllowed) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-orange-50 flex items-center justify-center px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Access Restricted</h1>
          <p className="text-gray-600">Your account access has been restricted. Please contact support.</p>
        </div>
      </div>
    );
  }

  const reviewCount = user._count?.reviews || 0;
  const reviews = user.reviews || [];
  const totalRating = reviews.reduce((acc, review) => acc + review.rating, 0);
  const averageRating = reviews.length > 0 ? totalRating / reviews.length : 1;

  // Generate random gradients
  const gradientColors = [
    'from-rose-50 via-white to-pink-50',
    'from-blue-50 via-white to-indigo-50', 
    'from-green-50 via-white to-emerald-50',
    'from-purple-50 via-white to-violet-50',
    'from-orange-50 via-white to-amber-50',
    'from-cyan-50 via-white to-teal-50',
    'from-indigo-50 via-white to-cyan-50'
  ];
  
  const animatedGradients = [
    ['from-rose-400/20 to-pink-600/20', 'from-pink-400/20 to-rose-600/20', 'from-rose-400/10 to-pink-600/10'],
    ['from-blue-400/20 to-purple-600/20', 'from-cyan-400/20 to-blue-600/20', 'from-purple-400/10 to-pink-600/10'],
    ['from-green-400/20 to-emerald-600/20', 'from-emerald-400/20 to-teal-600/20', 'from-green-400/10 to-emerald-600/10'],
    ['from-purple-400/20 to-violet-600/20', 'from-violet-400/20 to-purple-600/20', 'from-purple-400/10 to-violet-600/10'],
    ['from-orange-400/20 to-amber-600/20', 'from-amber-400/20 to-yellow-600/20', 'from-orange-400/10 to-amber-600/10'],
    ['from-cyan-400/20 to-teal-600/20', 'from-teal-400/20 to-cyan-600/20', 'from-cyan-400/10 to-teal-600/10'],
    ['from-indigo-400/20 to-blue-600/20', 'from-blue-400/20 to-indigo-600/20', 'from-indigo-400/10 to-blue-600/10']
  ];
  
  const randomIndex = Math.floor(Math.random() * gradientColors.length);
  const selectedGradient = gradientColors[randomIndex];
  const selectedAnimated = animatedGradients[randomIndex];

  return (
    <div className={`min-h-screen bg-gradient-to-br ${selectedGradient} relative`}>
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[0]} rounded-full blur-3xl animate-pulse`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[1]} rounded-full blur-3xl animate-pulse delay-1000`}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br ${selectedAnimated[2]} rounded-full blur-3xl animate-pulse delay-500`}></div>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-cyan-600/5"></div>
        <div className="relative pt-20 pb-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Welcome Message */}
            <div className="text-center mb-8">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent mb-4 leading-tight">
                Your Profile
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Manage your reviews, track your activity, and connect with the community
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative px-4 sm:px-6 lg:px-8 pb-12">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <UserProfileComponent />
          </div>
        </div>
      </div>

      {/* Add Schema.org markup for SEO */}
      <Schema
        productName={`${user.userName || "User"}'s Profile`}
        description={`${user.userName || "User"}'s profile and activity on ReviewIt. ${reviewCount} reviews and counting.`}
        rating={Math.max(1, Math.min(5, averageRating))}
        reviewCount={Math.max(1, reviewCount)}
        reviews={reviews.slice(0, 3).map((review: any) => ({
          author: review.user?.userName || user.userName || "User",
          reviewRating: Math.max(1, Math.min(5, review.rating)),
          reviewBody: review.body,
          datePublished: review.createdDate ? new Date(review.createdDate).toISOString() : new Date().toISOString()
        }))}
        image={user.avatar || "/images/default-avatar.png"}
        category="Profile"
      />
    </div>
  );
}
