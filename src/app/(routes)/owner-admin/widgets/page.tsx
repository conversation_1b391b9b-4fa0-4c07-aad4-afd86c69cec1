"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@clerk/nextjs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Eye, Copy, Settings, BarChart3, Loader2, Code } from "lucide-react";
import { WidgetList } from "@/app/components/widgets/WidgetList";
import { WidgetCreator } from "@/app/components/widgets/WidgetCreator";
import { WidgetPreview } from "@/app/components/widgets/WidgetPreview";
import { WidgetAnalytics } from "@/app/components/widgets/WidgetAnalytics";
import { iWidget, iBusiness } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { baseUrl } from "@/app/util/serverFunctions";

export default function WidgetsPage() {
  const { userId } = useAuth();
  
  const [widgets, setWidgets] = useState<iWidget[]>([]);
  const [businesses, setBusinesses] = useState<iBusiness[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [showCreator, setShowCreator] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<iWidget | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'preview' | 'analytics'>('list');

  // Fetch user's businesses
  useEffect(() => {
    const fetchBusinesses = async () => {
      try {
        const response = await fetch(baseUrl + "/api/get/user", {
          credentials: "include",
        });
        const data = await response.json();
        
        if (data.success && data.data?.businesses) {
          setBusinesses(data.data.businesses);
          if (data.data.businesses.length > 0) {
            setSelectedBusiness(data.data.businesses[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching businesses:", error);
        toast.error("Failed to load businesses");
      }
    };

    if (userId) {
      fetchBusinesses();
    }
  }, [userId]);

  // Fetch widgets for selected business
  useEffect(() => {
    const fetchWidgets = async () => {
      if (!selectedBusiness) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch(`/api/widgets?businessId=${selectedBusiness}`, {
          credentials: "include",
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setWidgets(data.data || []);
        } else {
          toast.error(data.error || "Failed to load widgets");
        }
      } catch (error) {
        console.error("Error fetching widgets:", error);
        if (error instanceof SyntaxError) {
          toast.error("Server returned invalid response. Please try again.");
        } else {
          toast.error("Failed to load widgets");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchWidgets();
  }, [selectedBusiness]);

  const handleWidgetCreated = (newWidget: iWidget) => {
    setWidgets(prev => [newWidget, ...prev]);
    setShowCreator(false);
    toast.success("Widget created successfully!");
  };

  const handleWidgetUpdated = (updatedWidget: iWidget) => {
    setWidgets(prev => prev.map(w => w.id === updatedWidget.id ? updatedWidget : w));
    toast.success("Widget updated successfully!");
  };

  const handleWidgetDeleted = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
    if (selectedWidget?.id === widgetId) {
      setSelectedWidget(null);
      setViewMode('list');
    }
    toast.success("Widget deleted successfully!");
  };

  const selectedBusinessData = businesses.find(b => b.id === selectedBusiness);

  if (loading && !selectedBusiness) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading businesses...</span>
      </div>
    );
  }

  if (businesses.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <h3 className="text-lg font-semibold mb-2">No Businesses Found</h3>
          <p className="text-muted-foreground mb-4">
            You need to have at least one business to create widgets.
          </p>
          <Button asChild>
            <a href="/claim-product">Claim or Create a Business</a>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with business selector */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          {businesses.length > 1 && (
            <select
              value={selectedBusiness}
              onChange={(e) => setSelectedBusiness(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {businesses.map((business) => (
                <option key={business.id} value={business.id}>
                  {business.ownerName || `Business ${business.id.slice(0, 8)}`}
                </option>
              ))}
            </select>
          )}
          
          <Button onClick={() => setShowCreator(true)} className="gap-2">
            <Plus className="w-4 h-4" />
            Create Widget
          </Button>
        </div>
      </div>

      {/* Widget Creator Modal */}
      {showCreator && (
        <WidgetCreator
          businessId={selectedBusiness}
          business={selectedBusinessData}
          onWidgetCreated={handleWidgetCreated}
          onClose={() => setShowCreator(false)}
        />
      )}

      {/* Main Content */}
      {viewMode === 'list' && (
        <>
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Widgets</CardTitle>
                <Code className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{widgets.length}</div>
                <p className="text-xs text-muted-foreground">
                  {widgets.filter(w => w.isActive).length} active
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {widgets.reduce((sum, w) => sum + w.viewCount, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all widgets
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {widgets.length > 0 
                    ? Math.round((widgets.reduce((sum, w) => sum + w.clickCount, 0) / 
                        Math.max(widgets.reduce((sum, w) => sum + w.viewCount, 0), 1)) * 100 * 100) / 100
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Average across widgets
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Widget List */}
          <WidgetList
            widgets={widgets}
            loading={loading}
            onWidgetUpdated={handleWidgetUpdated}
            onWidgetDeleted={handleWidgetDeleted}
            onPreview={(widget) => {
              setSelectedWidget(widget);
              setViewMode('preview');
            }}
            onAnalytics={(widget) => {
              setSelectedWidget(widget);
              setViewMode('analytics');
            }}
          />
        </>
      )}

      {/* Widget Preview */}
      {viewMode === 'preview' && selectedWidget && (
        <WidgetPreview
          widget={selectedWidget}
          onBack={() => {
            setViewMode('list');
            setSelectedWidget(null);
          }}
        />
      )}

      {/* Widget Analytics */}
      {viewMode === 'analytics' && selectedWidget && (
        <WidgetAnalytics
          widget={selectedWidget}
          onBack={() => {
            setViewMode('list');
            setSelectedWidget(null);
          }}
        />
      )}
    </div>
  );
}