"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { getUser } from "@/app/util/serverFunctions";
import {
  getBusinessAnalytics,
  getTrafficSources,
} from "@/app/util/analyticsService";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import {
  iUser,
  iBusiness,
  iBusinessAnalyticsEnhanced,
  iTrafficSourceEnhanced,
  iAnalyticsPeriod,
} from "@/app/util/Interfaces";

// Import our new components
import { AnalyticsHeader } from "@/components/analytics/AnalyticsHeader";
import { OverviewTab } from "@/components/analytics/OverviewTab";
import { TrafficTab } from "@/components/analytics/TrafficTab";
import { ProductsTab } from "@/components/analytics/ProductsTab";
import { ReviewsTab } from "@/components/analytics/ReviewsTab";
// Notification tab disabled as it doesn't make sense at this time
// import { NotificationsTab } from "@/components/analytics/NotificationsTab";
import { ExportPanel } from "@/components/analytics/ExportPanel";

export default function AnalyticsDashboardPage() {
  const auth = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for date range selection
  const [selectedPeriod, setSelectedPeriod] = useState<iAnalyticsPeriod>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    endDate: new Date(),
  });

  // State for active tab
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch user data
  const {
    data: userResponse,
    isLoading: isLoadingUser,
    isError: isErrorUser,
  } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: () => getUser(),
    enabled: !!auth.userId,
  });

  // Get the first business (assuming user has at least one)
  const userData = userResponse?.data;
  const primaryBusiness = userData?.businesses?.[0];

  // Fetch analytics data
  const {
    data: analyticsData,
    isLoading: isLoadingAnalytics,
    isError: isErrorAnalytics,
    refetch: refetchAnalytics,
  } = useQuery({
    queryKey: ["businessAnalytics", primaryBusiness?.id, selectedPeriod],
    queryFn: () => getBusinessAnalytics(primaryBusiness!.id, selectedPeriod),
    enabled: !!primaryBusiness?.id,
    retry: 1, // Only retry once to avoid showing mock data
  });

  // Fetch traffic sources
  const {
    data: trafficSourcesData,
    isLoading: isLoadingTraffic,
    isError: isErrorTraffic,
    refetch: refetchTraffic,
  } = useQuery({
    queryKey: ["trafficSources", primaryBusiness?.id, selectedPeriod],
    queryFn: () => getTrafficSources(primaryBusiness!.id, selectedPeriod),
    enabled: !!primaryBusiness?.id,
    retry: 1, // Only retry once to avoid showing mock data
  });

  // Handle period change
  const handlePeriodChange = useCallback((newPeriod: iAnalyticsPeriod) => {
    setSelectedPeriod(newPeriod);
  }, []);

  // Handle tab change
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  // Show loading state
  if (isLoadingUser || isLoadingAnalytics) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">Loading analytics data...</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  // Show error state if user data fails
  if (isErrorUser || !userData) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Unable to Load User Data</h3>
            <p className="text-muted-foreground mb-4">
              Please try refreshing the page or contact support if the issue persists.
            </p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if no business found
  if (!primaryBusiness) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">No business found</p>
          </div>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Business Found</h3>
            <p className="text-muted-foreground mb-4">
              You need to create a business before viewing analytics.
            </p>
            <Button asChild>
              <Link href="/owner-admin/products">
                Create Your First Product
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if analytics data fails
  if (isErrorAnalytics) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">Unable to load analytics data</p>
          </div>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Analytics Data Unavailable</h3>
            <p className="text-muted-foreground mb-4">
              We're unable to load your analytics data at the moment. This could be due to:
            </p>
            <ul className="text-sm text-muted-foreground mb-6 space-y-1 text-left max-w-md">
              <li>- No analytics data has been collected yet</li>
              <li>- Database connectivity issues</li>
              <li>- Your business may not have any products or reviews</li>
            </ul>
            <div className="space-x-2">
              <Button onClick={() => refetchAnalytics()}>
                Try Again
              </Button>
              <Button variant="outline" asChild>
                <Link href="/owner-admin/products">
                  Manage Products
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show message if no analytics data available
  if (!analyticsData) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">No data available for the selected period</p>
          </div>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">No Analytics Data</h3>
            <p className="text-muted-foreground mb-4">
              There's no analytics data available for the selected time period.
            </p>
            <div className="space-x-2">
              <Button onClick={() => {
                setSelectedPeriod({
                  startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
                  endDate: new Date(),
                });
              }}>
                Try Longer Period
              </Button>
              <Button variant="outline" asChild>
                <Link href="/owner-admin/products">
                  Add Products
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">{primaryBusiness.ownerName || "Business Analytics"}</p>
        </div>
      </div>

      {/* Main Analytics Content */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="flex w-full overflow-x-auto snap-x snap-mandatory sm:grid sm:grid-cols-4 gap-1 sm:gap-0">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="traffic">Traffic</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          {/* Notification tab disabled as it doesn't make sense at this time */}
          {/* <TabsTrigger value="notifications">Notifications</TabsTrigger> */}
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <OverviewTab analyticsData={analyticsData} />
        </TabsContent>

        <TabsContent value="traffic" className="space-y-6">
          <TrafficTab
            analyticsData={analyticsData}
            trafficSourcesData={trafficSourcesData}
            isLoadingTraffic={isLoadingTraffic}
            isErrorTraffic={isErrorTraffic}
          />
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <ProductsTab analyticsData={analyticsData} />
        </TabsContent>

        <TabsContent value="reviews" className="space-y-6">
          <ReviewsTab analyticsData={analyticsData} />
        </TabsContent>

        {/* Notification tab disabled as it doesn't make sense at this time */}
        {/* <TabsContent value="notifications" className="space-y-6">
          <NotificationsTab analyticsData={analyticsData} />
        </TabsContent> */}
      </Tabs>

      {/* Export Panel */}
      <div className="mt-8">
        <ExportPanel
          analyticsData={analyticsData}
          selectedPeriod={selectedPeriod}
        />
      </div>
    </div>
  );
}