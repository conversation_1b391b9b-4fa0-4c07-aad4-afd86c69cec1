"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useSearchParams, useRouter } from "next/navigation";
import Link from "next/link";
import { getUser } from "@/app/util/serverFunctions";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>Content,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import {
    ChevronLeft,
    AlertTriangle,
    CreditCard,
    CheckCircle2,
    AlertCircle,
    Calendar,
    LineChart,
    Box,
    Settings,
    ShoppingBag,
    Megaphone,
    BarChart,
    ArrowRight,
    Clock,
    CheckCircle,
    AlarmClock,
    Receipt,
    Info,
} from "lucide-react";
import {
    iUser,
    iBusiness,
    iSubscriptionTier,
    iSubscriptionUsage,
    iSubscriptionBilling,
    iSubscriptionTransaction
} from "@/app/util/Interfaces";

// Mock subscription tiers
const subscriptionTiers: iSubscriptionTier[] = [
    {
        id: "tier1",
        name: "Free",
        description: "For individuals and small teams just getting started",
        price: 0,
        billingCycle: "monthly",
        features: [
            "5 products",
            "2 promotions per product",
            "Basic analytics",
            "Email support",
        ],
        maxProducts: 5,
        maxPromotions: 10,
        maxReviews: 100,
        analyticsAccess: false,
        apiAccess: false,
        priority: 1,
        isActive: true,
        createdAt: new Date(),
    },
    {
        id: "tier2",
        name: "Lite",
        description: "For growing businesses looking to expand their online presence",
        price: 29,
        billingCycle: "monthly",
        features: [
            "25 products",
            "5 promotions per product",
            "Advanced analytics",
            "Priority email support",
            "Custom branding",
        ],
        maxProducts: 25,
        maxPromotions: 125,
        maxReviews: 500,
        analyticsAccess: true,
        apiAccess: false,
        priority: 2,
        isActive: true,
        createdAt: new Date(),
    },
    {
        id: "tier3",
        name: "Pro",
        description: "For established businesses requiring comprehensive solutions",
        price: 99,
        billingCycle: "monthly",
        features: [
            "Unlimited products",
            "Unlimited promotions",
            "Real-time analytics",
            "24/7 phone & email support",
            "API access",
            "Advanced integrations",
        ],
        maxProducts: -1, // Unlimited
        maxPromotions: -1, // Unlimited
        maxReviews: -1, // Unlimited
        analyticsAccess: true,
        apiAccess: true,
        priority: 3,
        isActive: true,
        createdAt: new Date(),
    },
    {
        id: "tier4",
        name: "Enterprise",
        description: "Tailored solutions for large-scale organizations",
        price: 299,
        billingCycle: "monthly",
        features: [
            "All Pro features",
            "Dedicated account manager",
            "Custom API solutions",
            "On-site training",
            "Service level agreement (SLA)",
        ],
        maxProducts: -1, // Unlimited
        maxPromotions: -1, // Unlimited
        maxReviews: -1, // Unlimited
        analyticsAccess: true,
        apiAccess: true,
        priority: 4,
        isActive: true,
        createdAt: new Date(),
    },
];

// Mock subscription usage data
const mockUsage: iSubscriptionUsage = {
    id: "usage1",
    businessId: "business1",
    productCount: 3,
    promotionCount: 6,
    reviewCount: 48,
    apiCalls: 0,
    monthlyViews: 1200,
    lastUpdated: new Date(),
};

// Mock billing data
const mockBilling: iSubscriptionBilling = {
    id: "billing1",
    businessId: "business1",
    tierId: "tier1",
    nextBillingDate: new Date(new Date().setDate(new Date().getDate() + 15)),
    amount: 0,
    status: "active",
    createdAt: new Date(new Date().setDate(new Date().getDate() - 15)),
};

// Mock transaction history
const mockTransactions: iSubscriptionTransaction[] = [
    {
        id: "trans1",
        businessId: "business1",
        subscriptionId: "billing1",
        amount: 0,
        currency: "USD",
        status: "succeeded",
        paymentMethod: "None (Free Plan)",
        description: "Free Plan Activation",
        createdAt: new Date(new Date().setDate(new Date().getDate() - 15)),
    }
];

export default function SubscriptionPage() {
    const auth = useAuth();
    const router = useRouter();
    const searchParams = useSearchParams();
    const businessId = searchParams.get("id");
    const [activeTab, setActiveTab] = useState("overview");
    const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
    const [selectedTier, setSelectedTier] = useState<string | null>(null);
    const [isPaymentProcessing, setIsPaymentProcessing] = useState(false);

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    // Get current business and subscription information
    const getCurrentBusiness = (): iBusiness | null => {
        if (!data?.data) return null;

        const user: iUser = data.data;
        const businesses = user.businesses || [];

        if (businessId) {
            return businesses.find(b => b.id === businessId) || null;
        }

        return businesses.length > 0 ? businesses[0] : null;
    };

    // Get current subscription tier
    const getCurrentTier = (): iSubscriptionTier | null => {
        const business = getCurrentBusiness();
        if (!business) return null;

        // In a real implementation, we would match the business subscription status to a tier
        // For now, we'll just return the Free tier for demonstration
        return subscriptionTiers.find(tier => tier.name.toLowerCase() === business.subscriptionStatus.toLowerCase()) || subscriptionTiers[0];
    };

    // Handle upgrade plan
    const handleUpgrade = (tierId: string) => {
        setSelectedTier(tierId);
        setIsUpgradeDialogOpen(true);
    };

    // Process payment
    const processPayment = () => {
        setIsPaymentProcessing(true);

        // Simulate payment processing
        setTimeout(() => {
            setIsPaymentProcessing(false);
            setIsUpgradeDialogOpen(false);

            // Show success message
            alert("Subscription upgraded successfully!");

            // In a real app, we would update the subscription via API and then reload the data
        }, 2000);
    };

    // Format date
    const formatDate = (date: Date) => {
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Get usage percentage for a metric
    const getUsagePercentage = (current: number, max: number) => {
        if (max === -1) return 0; // Unlimited
        return Math.min(100, (current / max) * 100);
    };

    // Get tier by ID
    const getTierById = (id: string) => {
        return subscriptionTiers.find(tier => tier.id === id) || null;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading user data</h3>
                <p className="text-muted-foreground">{error?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    const business = getCurrentBusiness();
    const currentTier = getCurrentTier();

    if (!business) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No business selected</h3>
                <p className="text-muted-foreground">Please select a business to manage its subscription</p>
                <Button asChild>
                    <Link href="/owner-admin">Go to Dashboard</Link>
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6 px-2 sm:px-4 md:px-8 max-w-full overflow-x-auto">
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/owner-admin")}
                >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back to dashboard
                </Button>
            </div>

            <div>
                <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Subscription Management</h2>
                <p className="text-muted-foreground">
                    Manage your subscription plan and billing details
                </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList className="w-full flex flex-wrap">
                    <TabsTrigger value="overview" className="flex-1 min-w-[100px]">Overview</TabsTrigger>
                    <TabsTrigger value="plans" className="flex-1 min-w-[100px]">Plans</TabsTrigger>
                    <TabsTrigger value="billing" className="flex-1 min-w-[100px]">Billing</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                    {/* Current Plan Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                <span>Current Plan</span>
                                <Badge className={currentTier?.price === 0 ? "bg-gray-100 text-gray-800" : "bg-green-100 text-green-800"}>
                                    {currentTier?.name}
                                </Badge>
                            </CardTitle>
                            <CardDescription>{currentTier?.description}</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                <div>
                                    <p className="text-2xl font-bold">
                                        ${currentTier?.price}<span className="text-sm font-normal text-muted-foreground">/{currentTier?.billingCycle}</span>
                                    </p>
                                    <p className="text-sm text-muted-foreground">Next billing date: {formatDate(mockBilling.nextBillingDate)}</p>
                                </div>
                                <Button onClick={() => setActiveTab("plans")} className="w-full sm:w-auto">
                                    Upgrade Plan
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Usage Stats */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Usage Statistics</CardTitle>
                            <CardDescription>Your current usage compared to plan limits</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Products Usage */}
                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Products</span>
                                    <span className="text-sm text-muted-foreground">
                                        {mockUsage.productCount} / {currentTier?.maxProducts === -1 ? "Unlimited" : currentTier?.maxProducts}
                                    </span>
                                </div>
                                <Progress value={getUsagePercentage(mockUsage.productCount, currentTier?.maxProducts || 1)} />
                            </div>

                            {/* Promotions Usage */}
                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Promotions</span>
                                    <span className="text-sm text-muted-foreground">
                                        {mockUsage.promotionCount} / {currentTier?.maxPromotions === -1 ? "Unlimited" : currentTier?.maxPromotions}
                                    </span>
                                </div>
                                <Progress value={getUsagePercentage(mockUsage.promotionCount, currentTier?.maxPromotions || 1)} />
                            </div>

                            {/* Reviews Usage */}
                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Reviews</span>
                                    <span className="text-sm text-muted-foreground">
                                        {mockUsage.reviewCount} / {currentTier?.maxReviews === -1 ? "Unlimited" : currentTier?.maxReviews}
                                    </span>
                                </div>
                                <Progress value={getUsagePercentage(mockUsage.reviewCount, currentTier?.maxReviews || 1)} />
                            </div>

                            {/* Monthly Views */}
                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Monthly Views</span>
                                    <span className="text-sm text-muted-foreground">
                                        {mockUsage.monthlyViews.toLocaleString()}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                    <LineChart className="h-4 w-4" />
                                    <span>Usage measured on a rolling 30-day basis</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Plan Features */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Plan Features</CardTitle>
                            <CardDescription>Features included in your current plan</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ul className="grid gap-2">
                                {currentTier?.features.map((feature, index) => (
                                    <li key={index} className="flex items-center">
                                        <CheckCircle2 className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                        <span>{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                        <CardFooter>
                            <Button variant="outline" onClick={() => setActiveTab("plans")} className="w-full">
                                Compare Plans
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="plans" className="space-y-6">
                    {/* Plan Comparison */}
                    <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        {subscriptionTiers.map((tier) => (
                            <Card key={tier.id} className={tier.id === currentTier?.id ? "border-2 border-blue-500" : ""}>
                                <CardHeader>
                                    <CardTitle>
                                        {tier.name}
                                        {tier.id === currentTier?.id && <Badge className="ml-2">Current</Badge>}
                                    </CardTitle>
                                    <CardDescription>{tier.description}</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <p className="text-2xl font-bold">
                                        ${tier.price}<span className="text-sm font-normal text-muted-foreground">/{tier.billingCycle}</span>
                                    </p>
                                    <ul className="space-y-2">
                                        {tier.features.map((feature, index) => (
                                            <li key={index} className="flex items-center text-sm">
                                                <CheckCircle2 className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                                <span>{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                                <CardFooter>
                                    <Button
                                        className="w-full"
                                        variant={tier.id === currentTier?.id ? "outline" : "default"}
                                        disabled={tier.id === currentTier?.id}
                                        onClick={() => handleUpgrade(tier.id)}
                                    >
                                        {tier.id === currentTier?.id ? "Current Plan" : "Upgrade"}
                                    </Button>
                                </CardFooter>
                            </Card>
                        ))}
                    </div>

                    {/* Plan Feature Comparison Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Feature Comparison</CardTitle>
                            <CardDescription>Detailed comparison of features across plans</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {/* Desktop Table View */}
                            <div className="hidden md:block overflow-x-auto">
                                <Table className="w-full text-xs sm:text-sm">
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Feature</TableHead>
                                            {subscriptionTiers.map(tier => (
                                                <TableHead key={tier.id} className="text-center">
                                                    {tier.name}
                                                </TableHead>
                                            ))}
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        <TableRow>
                                            <TableCell className="font-medium">Products</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.maxProducts === -1 ? "Unlimited" : tier.maxProducts}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        <TableRow>
                                            <TableCell className="font-medium">Promotions</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.maxPromotions === -1 ? "Unlimited" : tier.maxPromotions}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        <TableRow>
                                            <TableCell className="font-medium">Review Management</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.maxReviews === -1 ? "Unlimited" : tier.maxReviews}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        <TableRow>
                                            <TableCell className="font-medium">Analytics</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.analyticsAccess ? (
                                                        tier.priority > 2 ? "Advanced" : "Basic"
                                                    ) : "Limited"}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        <TableRow>
                                            <TableCell className="font-medium">API Access</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.apiAccess ? (
                                                        <CheckCircle2 className="h-4 w-4 text-green-500 mx-auto" />
                                                    ) : (
                                                        <AlertCircle className="h-4 w-4 text-red-500 mx-auto" />
                                                    )}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        <TableRow>
                                            <TableCell className="font-medium">Support</TableCell>
                                            {subscriptionTiers.map(tier => (
                                                <TableCell key={tier.id} className="text-center">
                                                    {tier.priority === 1 && "Email"}
                                                    {tier.priority === 2 && "Priority Email"}
                                                    {tier.priority === 3 && "24/7 Phone & Email"}
                                                    {tier.priority === 4 && "Dedicated Manager"}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Mobile Card View */}
                            <div className="md:hidden space-y-4">
                                {subscriptionTiers.map(tier => (
                                    <Card key={tier.id} className="border-2">
                                        <CardHeader className="pb-3">
                                            <CardTitle className="text-lg">{tier.name}</CardTitle>
                                            <CardDescription className="text-2xl font-bold">
                                                ${tier.price}/{tier.billingCycle}
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-3">
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">Products</span>
                                                <span className="text-sm">
                                                    {tier.maxProducts === -1 ? "Unlimited" : tier.maxProducts}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">Promotions</span>
                                                <span className="text-sm">
                                                    {tier.maxPromotions === -1 ? "Unlimited" : tier.maxPromotions}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">Review Management</span>
                                                <span className="text-sm">
                                                    {tier.maxReviews === -1 ? "Unlimited" : tier.maxReviews}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">Analytics</span>
                                                <span className="text-sm">
                                                    {tier.analyticsAccess ? (
                                                        tier.priority > 2 ? "Advanced" : "Basic"
                                                    ) : "Limited"}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">API Access</span>
                                                <span className="text-sm">
                                                    {tier.apiAccess ? (
                                                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                    ) : (
                                                        <AlertCircle className="h-4 w-4 text-red-500" />
                                                    )}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium">Support</span>
                                                <span className="text-sm">
                                                    {tier.priority === 1 && "Email"}
                                                    {tier.priority === 2 && "Priority Email"}
                                                    {tier.priority === 3 && "24/7 Phone & Email"}
                                                    {tier.priority === 4 && "Dedicated Manager"}
                                                </span>
                                            </div>
                                        </CardContent>
                                        <CardFooter>
                                            <Button 
                                                className="w-full" 
                                                variant={getCurrentBusiness()?.subscriptionStatus === tier.name.toUpperCase() ? "outline" : "default"}
                                            >
                                                {getCurrentBusiness()?.subscriptionStatus === tier.name.toUpperCase() ? "Current Plan" : "Upgrade"}
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="billing" className="space-y-6">
                    {/* Billing Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Billing Information</CardTitle>
                            <CardDescription>Manage your payment methods and billing details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium">Current Plan</p>
                                    <div className="flex items-center">
                                        <Badge className="mr-2">{currentTier?.name}</Badge>
                                        <span className="text-sm">${currentTier?.price}/{currentTier?.billingCycle}</span>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <p className="text-sm font-medium">Next Billing Date</p>
                                    <div className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                                        <span>{formatDate(mockBilling.nextBillingDate)}</span>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <p className="text-sm font-medium">Payment Method</p>
                                    <div className="flex items-center">
                                        <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                                        <span>
                                            {currentTier?.price === 0
                                                ? "No payment method required (Free Plan)"
                                                : "Add a payment method"}
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <p className="text-sm font-medium">Status</p>
                                    <div className="flex items-center">
                                        <Badge
                                            variant="outline"
                                            className="bg-green-100 text-green-800"
                                        >
                                            {mockBilling.status === "active" ? "Active" : "Inactive"}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            {/* Add payment method button (disabled for free plan) */}
                            <div className="pt-4">
                                <Button disabled={currentTier?.price === 0} className="w-full sm:w-auto">
                                    <CreditCard className="h-4 w-4 mr-2" />
                                    {currentTier?.price === 0
                                        ? "No Payment Method Required"
                                        : "Add Payment Method"}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Transaction History */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Transaction History</CardTitle>
                            <CardDescription>Record of your past billing transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {/* Desktop Table View */}
                            <div className="hidden md:block overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Date</TableHead>
                                            <TableHead>Description</TableHead>
                                            <TableHead>Amount</TableHead>
                                            <TableHead>Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {mockTransactions.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                                                    No transactions yet
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            mockTransactions.map(transaction => (
                                                <TableRow key={transaction.id}>
                                                    <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                                                    <TableCell>{transaction.description}</TableCell>
                                                    <TableCell>${transaction.amount.toFixed(2)}</TableCell>
                                                    <TableCell>
                                                        <Badge
                                                            variant="outline"
                                                            className={
                                                                transaction.status === "succeeded"
                                                                    ? "bg-green-100 text-green-800"
                                                                    : transaction.status === "pending"
                                                                        ? "bg-yellow-100 text-yellow-800"
                                                                        : "bg-red-100 text-red-800"
                                                            }
                                                        >
                                                            {transaction.status}
                                                        </Badge>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Mobile Card View */}
                            <div className="md:hidden space-y-3">
                                {mockTransactions.length === 0 ? (
                                    <Card>
                                        <CardContent className="p-6 text-center">
                                            <p className="text-muted-foreground">No transactions yet</p>
                                        </CardContent>
                                    </Card>
                                ) : (
                                    mockTransactions.map(transaction => (
                                        <Card key={transaction.id}>
                                            <CardContent className="p-4">
                                                <div className="flex justify-between items-start mb-2">
                                                    <div>
                                                        <p className="font-medium">{transaction.description}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {formatDate(transaction.createdAt)}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-medium">${transaction.amount.toFixed(2)}</p>
                                                        <Badge
                                                            variant="outline"
                                                            className={`mt-1 ${
                                                                transaction.status === "succeeded"
                                                                    ? "bg-green-100 text-green-800"
                                                                    : transaction.status === "pending"
                                                                        ? "bg-yellow-100 text-yellow-800"
                                                                        : "bg-red-100 text-red-800"
                                                            }`}
                                                        >
                                                            {transaction.status}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Invoices and Receipts */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Invoices & Receipts</CardTitle>
                            <CardDescription>Download your invoices and receipts</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6 text-muted-foreground">
                                <Receipt className="h-12 w-12 mx-auto mb-2" />
                                <p>No invoices available yet</p>
                                <p className="text-sm mt-1">
                                    Your invoices will appear here once you have a paid subscription
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            {/* Upgrade Plan Dialog */}
            <Dialog open={isUpgradeDialogOpen} onOpenChange={setIsUpgradeDialogOpen}>
                <DialogContent className="sm:max-w-[425px] w-[95%] max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Upgrade to {getTierById(selectedTier || "")?.name}</DialogTitle>
                        <DialogDescription>
                            You are upgrading from {currentTier?.name} to {getTierById(selectedTier || "")?.name}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4 space-y-4">
                        <div className="space-y-2">
                            <p className="font-medium">Plan Details:</p>
                            <ul className="space-y-1">
                                {getTierById(selectedTier || "")?.features.map((feature, index) => (
                                    <li key={index} className="flex items-center text-sm">
                                        <CheckCircle2 className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                        <span>{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div className="border-t pt-4">
                            <div className="flex justify-between font-medium">
                                <span>Total amount:</span>
                                <span>${getTierById(selectedTier || "")?.price}/{getTierById(selectedTier || "")?.billingCycle}</span>
                            </div>
                        </div>

                        <Alert className="bg-blue-50">
                            <Info className="h-4 w-4" />
                            <AlertTitle>Your subscription will start immediately</AlertTitle>
                            <AlertDescription>
                                You&apos;ll be charged now and your billing date will update accordingly.
                            </AlertDescription>
                        </Alert>
                    </div>
                    <DialogFooter className="flex-col sm:flex-row gap-2">
                        <Button variant="outline" onClick={() => setIsUpgradeDialogOpen(false)} disabled={isPaymentProcessing} className="w-full sm:w-auto">
                            Cancel
                        </Button>
                        <Button onClick={processPayment} disabled={isPaymentProcessing} className="w-full sm:w-auto">
                            {isPaymentProcessing ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                                    Processing...
                                </>
                            ) : (
                                "Confirm Upgrade"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
} 