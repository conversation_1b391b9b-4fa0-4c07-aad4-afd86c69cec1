"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function EngagementAnalyticsDoc() {
  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-8 py-10 space-y-8">
      <header className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Engagement Analytics</h1>
        <p className="text-gray-600">
          Understand which of your products hold visitors’ attention the most, using
          scroll depth × time metrics.
        </p>
      </header>

      <section className="space-y-3 text-gray-700 leading-relaxed">
        <h2 className="text-2xl font-semibold text-gray-900">Where to find it</h2>
        <ol className="list-decimal list-inside space-y-1">
          <li>Go to <strong>Analytics</strong> in your Business Admin sidebar.</li>
          <li>Open the <strong>Products</strong> tab.</li>
          <li>Look for the card titled <em>“Most Attention (Depth × Time)”</em>.</li>
        </ol>
      </section>

      <section className="space-y-3 text-gray-700 leading-relaxed">
        <h2 className="text-2xl font-semibold text-gray-900">What the numbers mean</h2>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>Avg. Scroll Depth</strong> – The average percentage of the page users scrolled.</li>
          <li><strong>Avg. Time</strong> – The average milliseconds visitors kept the page active.</li>
          <li><strong>Attention Score</strong> – Simply depth × time, so higher means deeper + longer engagement.</li>
        </ul>
      </section>

      <section className="space-y-3 text-gray-700 leading-relaxed">
        <h2 className="text-2xl font-semibold text-gray-900">How can I use this?</h2>
        <ul className="list-disc list-inside space-y-1">
          <li>Identify products with strong storytelling or specs that keep customers reading.</li>
          <li>Tweak less engaging listings—move key info higher, add images, or shorten copy.</li>
          <li>Combine with conversions to see if attention translates to sales or reviews.</li>
        </ul>
      </section>

      <Button asChild>
        <Link href="/owner-admin/docs">← Back to Docs</Link>
      </Button>
    </div>
  );
}
