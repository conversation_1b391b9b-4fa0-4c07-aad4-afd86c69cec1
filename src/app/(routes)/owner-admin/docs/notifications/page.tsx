import React from "react";

const OwnerNotificationsDocs = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">Notifications</h1>
        <p className="text-lg text-gray-600">
          Stay informed about customer interactions and platform updates in real
          time.
        </p>
      </div>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">What You Receive</h2>
        <ul className="list-disc ml-6 mt-4 text-gray-700 space-y-1">
          <li>New reviews on your products or business.</li>
          <li>Customers liking (up-voting) your replies or comments.</li>
          <li>Helpful votes on your reviews.</li>
          <li>Platform announcements & policy updates.</li>
        </ul>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Real-Time Delivery</h2>
        <p className="text-gray-700 leading-relaxed">
          Review-it uses Server-Sent Events (SSE) to push notifications instantly
          to your dashboard. No page refresh needed.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Positive-Only Alerts</h2>
        <p className="text-gray-700 leading-relaxed">
          You’re notified when someone <strong>likes</strong> your comment or
          review, but <strong>not</strong> when they dislike it. This keeps the
          signal positive and avoids unnecessary noise.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Managing Notifications</h2>
        <ul className="list-disc ml-6 mt-4 text-gray-700 space-y-1">
          <li>Mark items as read to clear them from your unread counter.</li>
          <li>Click a notification to jump straight to the relevant review or comment.</li>
          <li>
            Use the settings panel (<code>Owner&nbsp;Dashboard → Settings → Notifications</code>) to mute certain categories.
          </li>
        </ul>
      </section>
    </div>
  );
};

export default OwnerNotificationsDocs;
