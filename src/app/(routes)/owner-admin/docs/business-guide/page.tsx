"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import BusinessGuideContent from "@/components/docs/business-owner/BusinessGuideContent";

export default function BusinessGuidePage() {
  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <Link href="/owner-admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>Business Guide</span>
        </div>

        <div className="text-center mb-12 p-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl">
          <h1 className="text-4xl font-bold mb-6 text-gray-900">
            Business Owner Guide
            <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
              Complete
            </span>
          </h1>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto">
            Comprehensive guide for business owners to manage their products,
            analyze performance, engage with customers, and grow their business
            presence on the ReviewIt platform.
          </p>
        </div>
      </div>

      {/* Business Guide Content */}
      <BusinessGuideContent />

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/owner-admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}
