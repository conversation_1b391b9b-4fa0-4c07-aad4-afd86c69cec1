"use client";

import React from "react";
import Link from "next/link";
import {
  BookOpen,
  Package,
  Megaphone,
  BarChart2,
  MessageSquare,
  Users,
  ExternalLink,
  ArrowRight,
  CheckCircle,
  Code,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface DocCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  isExternal?: boolean;
  status?: "complete" | "draft";
}

function DocCard({
  title,
  description,
  icon,
  href,
  isExternal = false,
  status,
}: DocCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">{icon}</div>
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              {status && (
                <span
                  className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full ${
                    status === "complete"
                      ? "bg-green-100 text-green-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {status === "complete" ? "Complete" : "Draft"}
                </span>
              )}
            </div>
          </div>
          {isExternal && <ExternalLink className="w-4 h-4 text-gray-400" />}
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-4">{description}</CardDescription>
        <Button asChild variant="outline" className="w-full">
          <Link
            href={href}
            {...(isExternal && {
              target: "_blank",
              rel: "noopener noreferrer",
            })}
          >
            Read Documentation
            <ArrowRight className="w-4 h-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}

export default function OwnerAdminDocsPage() {
  const businessOwnerDocs = [
    {
      title: "Business Owner Guide",
      description:
        "Complete guide for business owners including setup, management, and best practices for using the platform effectively.",
      icon: <BookOpen className="w-5 h-5 text-blue-600" />,
      href: "/owner-admin/docs/business-guide",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Product Management",
      description:
        "Learn how to add, edit, and manage your products. Includes information about product listings, categories, and optimization.",
      icon: <Package className="w-5 h-5 text-green-600" />,
      href: "/owner-admin/docs/product-management",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Promotions & Marketing",
      description:
        "Create and manage promotional campaigns, discounts, and marketing initiatives to boost your business visibility.",
      icon: <Megaphone className="w-5 h-5 text-purple-600" />,
      href: "/owner-admin/docs/promotions",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Review Management",
      description:
        "Understand how to manage customer reviews, respond to feedback, and maintain your business reputation.",
      icon: <MessageSquare className="w-5 h-5 text-orange-600" />,
      href: "/owner-admin/docs/review-management",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Engagement Analytics",
      description:
        "See which products keep visitors engaged using scroll depth × time metrics.",
      icon: <BarChart2 className="w-5 h-5 text-sky-600" />,
      href: "/owner-admin/docs/engagement-analytics",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Notifications",
      description:
        "Understand the real-time alert system—what you’ll receive and how to manage it.",
      icon: <Megaphone className="w-5 h-5 text-pink-600" />,
      href: "/owner-admin/docs/notifications",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "User Experience Guide",
      description:
        "Learn about the customer-facing features and how users interact with your business on the platform.",
      icon: <Users className="w-5 h-5 text-teal-600" />,
      href: "/owner-admin/docs/user-experience",
      isExternal: false,
      status: "complete" as const,
    },
    {
      title: "Widget System Guide",
      description:
        "Create and manage embeddable widgets to showcase your reviews and business information on external websites.",
      icon: <Code className="w-5 h-5 text-indigo-600" />,
      href: "/owner-admin/docs/widgets",
      isExternal: false,
      status: "complete" as const,
    },
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <span>Documentation</span>
        </div>

        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Business Owner Documentation
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl">
            Everything you need to know to successfully manage your business on
            our platform. These guides will help you maximize your business
            potential and provide the best experience for your customers.
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-1">
                Getting Started
              </h3>
              <p className="text-blue-800 text-sm mb-2">
                New to the platform? Start with the Business Owner Guide to get
                familiar with all the features and capabilities.
              </p>
              <Button
                asChild
                size="sm"
                variant="outline"
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                <Link href="/owner-admin/docs/business-guide">
                  Start Here
                  <ArrowRight className="w-3 h-3 ml-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {businessOwnerDocs.map((doc, index) => (
          <DocCard key={index} {...doc} />
        ))}
      </div>

      <div className="mt-12 bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-3">
          Need More Help?
        </h2>
        <p className="text-gray-600 mb-4">
          Can't find what you're looking for? Our support team is here to help
          you succeed.
        </p>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button asChild variant="outline">
            <Link href="/contact">Contact Support</Link>
          </Button>
          <Button asChild variant="ghost">
            <Link href="/owner-admin">
              Back to Dashboard
              <ArrowRight className="w-4 h-4 ml-1" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
