"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import WidgetGuideContent from "@/components/docs/business-owner/WidgetGuideContent";

export default function WidgetGuidePage() {
  return (
    <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
      {/* Header Section */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <Link href="/owner-admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span className="break-words">Widget System Guide</span>
        </div>

        <div className="text-center mb-8 sm:mb-12 p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-indigo-50 to-purple-100 rounded-xl">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
            Widget System Guide
            <span className="block sm:inline-block mt-2 sm:mt-0 sm:ml-2 px-3 py-1 text-xs sm:text-sm font-medium bg-green-100 text-green-800 rounded-full">
              Complete
            </span>
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-4xl mx-auto">
            Learn how to create, customize, and embed widgets to showcase your business reviews 
            and information on external websites. Boost your online presence and build trust 
            with potential customers.
          </p>
        </div>
      </div>

      {/* Widget Guide Content */}
      <WidgetGuideContent />

      {/* Navigation Back */}
      <div className="text-center pt-6 sm:pt-8 border-t border-gray-200">
        <Link
          href="/owner-admin/docs"
          className="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm sm:text-base"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}