"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft, Megaphone, Target, Calendar, BarChart3, DollarSign, Users } from "lucide-react";

export default function PromotionsPage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <Link href="/owner-admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>Promotions</span>
        </div>

        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Promotions & Marketing
          <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
            Complete
          </span>
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Create and manage promotional campaigns, discounts, and marketing initiatives to boost your business visibility and drive customer engagement.
        </p>
      </div>

      {/* Content */}
      <div className="text-gray-700 leading-relaxed">

        {/* Creating Promotions Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Megaphone className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Creating Promotions
          </h2>
          <p className="mb-6">
            Navigate to the "Promotions" section in your business dashboard to create new promotional campaigns.
            The promotion creation process includes:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-500" />
                Campaign Details
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Campaign Name</strong> - Internal name for tracking</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Description</strong> - Campaign purpose and goals</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Campaign Type</strong> - Discount, flash sale, or special offer</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Target Audience</strong> - Customer segmentation</div>
                </li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Calendar className="w-5 h-5 text-green-500" />
                Scheduling & Timing
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Start Date</strong> - When the promotion begins</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>End Date</strong> - Campaign expiration</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Time Zone</strong> - Campaign timing settings</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Auto-scheduling</strong> - Recurring campaign options</div>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Managing Campaigns Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <BarChart3 className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Managing Active Campaigns
          </h2>
          <p className="mb-6">
            Monitor and manage your active promotional campaigns through the campaign dashboard:
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">Campaign Management Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                  <h4 className="font-semibold text-gray-900">Performance Tracking</h4>
                </div>
                <p className="text-sm text-gray-600">Monitor clicks, conversions, and engagement rates</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-gray-900">Budget Monitoring</h4>
                </div>
                <p className="text-sm text-gray-600">Track spending and ROI for each campaign</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <Users className="w-5 h-5 text-purple-600" />
                  <h4 className="font-semibold text-gray-900">Audience Insights</h4>
                </div>
                <p className="text-sm text-gray-600">Understand who's engaging with your campaigns</p>
              </div>
            </div>
          </div>
        </section>

        {/* Promotion Types Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            Types of Promotions
          </h2>
          <p className="mb-6">
            Choose from various promotion types to match your marketing objectives:
          </p>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Discount Campaigns</h3>
              <p className="text-gray-600 mb-4">Offer percentage or fixed-amount discounts to attract customers.</p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>Percentage discounts (10% off, 25% off, etc.)</li>
                <li>Fixed amount discounts ($5 off, $20 off, etc.)</li>
                <li>Buy-one-get-one (BOGO) offers</li>
                <li>Tiered discounts based on purchase amount</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Flash Sales</h3>
              <p className="text-gray-600 mb-4">Create urgency with time-limited special offers.</p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>24-hour flash sales</li>
                <li>Weekend special offers</li>
                <li>Holiday promotions</li>
                <li>Limited quantity deals</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Loyalty Programs</h3>
              <p className="text-gray-600 mb-4">Reward repeat customers with exclusive offers.</p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>Points-based reward system</li>
                <li>VIP customer discounts</li>
                <li>Referral bonuses</li>
                <li>Anniversary and birthday specials</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Analytics Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            Promotion Analytics
          </h2>
          <p className="mb-6">
            Track the success of your promotional campaigns with detailed analytics:
          </p>

          <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
            <h3 className="text-xl font-bold mb-4 text-gray-900">Key Metrics to Monitor</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span><strong>Click-through Rate (CTR)</strong> - Engagement with promotional content</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span><strong>Conversion Rate</strong> - Visitors who completed desired actions</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span><strong>Cost Per Acquisition</strong> - Cost to acquire each new customer</span>
                </li>
              </ul>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span><strong>Return on Investment</strong> - Revenue generated vs. campaign cost</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span><strong>Customer Lifetime Value</strong> - Long-term value of acquired customers</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span><strong>Brand Awareness</strong> - Reach and impression metrics</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Best Practices */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Promotion Best Practices
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-4">Success Strategies</h3>
              <ul className="space-y-2 text-sm text-green-800">
                <li>• <strong>Clear Value Proposition:</strong> Make the benefit obvious to customers</li>
                <li>• <strong>Create Urgency:</strong> Use limited-time offers to encourage action</li>
                <li>• <strong>Target Appropriately:</strong> Match promotions to your audience</li>
                <li>• <strong>Test and Optimize:</strong> A/B test different campaign elements</li>
                <li>• <strong>Monitor Performance:</strong> Track metrics and adjust as needed</li>
              </ul>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-orange-900 mb-4">Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-orange-800">
                <li>• <strong>Over-promoting:</strong> Too many promotions can devalue your brand</li>
                <li>• <strong>Unclear Terms:</strong> Complex conditions confuse customers</li>
                <li>• <strong>Poor Timing:</strong> Wrong timing can reduce campaign effectiveness</li>
                <li>• <strong>Ignoring Data:</strong> Not using analytics to improve campaigns</li>
                <li>• <strong>Weak Call-to-Action:</strong> Unclear next steps for customers</li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/owner-admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}
