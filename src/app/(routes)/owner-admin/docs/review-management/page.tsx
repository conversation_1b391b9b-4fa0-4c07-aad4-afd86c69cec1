"use client";

import React from "react";
import Link from "next/link";
import { <PERSON>L<PERSON>t, MessageSquare, Star, Reply, Flag, TrendingUp, Users, AlertTriangle, CheckCircle } from "lucide-react";

export default function ReviewManagementPage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <Link href="/owner-admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>Review Management</span>
        </div>

        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Review Management
          <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
            Complete
          </span>
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Understand how to manage customer reviews, respond to feedback, and maintain your business reputation effectively.
        </p>
      </div>

      {/* Content */}
      <div className="text-gray-700 leading-relaxed">

        {/* Viewing Reviews Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <MessageSquare className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Viewing Reviews
          </h2>
          <p className="mb-6">
            To see a list of all reviews for your products, navigate to the "Review Management" tab in your business dashboard.
            The review table displays comprehensive information about each review:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-blue-500" />
                Review Details
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Review Content</strong> - Title and text content of the review</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Rating</strong> - Star rating given by the reviewer</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Product</strong> - Associated product or service being reviewed</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Reviewer</strong> - User who submitted the review</div>
                </li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                Status & Metrics
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Status</strong> - Published, pending, flagged, or rejected</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Submission Date</strong> - When the review was originally submitted</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Last Modified</strong> - Most recent update or moderation action</div>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div><strong>Engagement</strong> - Helpful votes and user interactions</div>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Responding to Reviews Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Reply className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Responding to Reviews
          </h2>
          <p className="mb-6">
            Engaging with customer reviews is crucial for maintaining your business reputation and building customer relationships:
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">Response Best Practices</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">For Positive Reviews</h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• Thank the customer for their feedback</li>
                  <li>• Acknowledge specific points they mentioned</li>
                  <li>• Invite them to return or try other products</li>
                  <li>• Keep responses genuine and personal</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">For Negative Reviews</h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• Respond professionally and empathetically</li>
                  <li>• Acknowledge their concerns</li>
                  <li>• Offer solutions or ways to improve</li>
                  <li>• Take conversations private when appropriate</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Response Templates
              </h3>
              <p className="text-gray-600 mb-4">Use these templates as starting points for your responses:</p>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Positive Review Template:</h4>
                <p className="text-sm text-gray-700 italic">
                  "Thank you so much for your wonderful review! We're thrilled to hear that you had a great experience with [specific aspect].
                  We look forward to serving you again soon!"
                </p>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                Handling Difficult Reviews
              </h3>
              <p className="text-gray-600 mb-4">When dealing with challenging feedback:</p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Stay calm and professional in your response</li>
                <li>Never argue or become defensive</li>
                <li>Focus on solutions rather than blame</li>
                <li>Offer to discuss the matter privately</li>
                <li>Follow up to ensure resolution</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Review Analytics Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Star className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Review Analytics
          </h2>
          <p className="mb-6">
            Monitor your review performance and customer sentiment with comprehensive analytics:
          </p>

          <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-200">
            <h3 className="text-xl font-bold mb-4 text-gray-900">Key Metrics to Track</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <Star className="w-5 h-5 text-yellow-600" />
                  <h4 className="font-semibold text-gray-900">Average Rating</h4>
                </div>
                <p className="text-sm text-gray-600">Overall star rating across all products</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-gray-900">Review Trends</h4>
                </div>
                <p className="text-sm text-gray-600">Rating changes over time</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <Users className="w-5 h-5 text-blue-600" />
                  <h4 className="font-semibold text-gray-900">Response Rate</h4>
                </div>
                <p className="text-sm text-gray-600">Percentage of reviews you've responded to</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <MessageSquare className="w-5 h-5 text-purple-600" />
                  <h4 className="font-semibold text-gray-900">Review Volume</h4>
                </div>
                <p className="text-sm text-gray-600">Number of reviews received per period</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <Flag className="w-5 h-5 text-red-600" />
                  <h4 className="font-semibold text-gray-900">Sentiment Analysis</h4>
                </div>
                <p className="text-sm text-gray-600">Positive vs. negative feedback ratio</p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <CheckCircle className="w-5 h-5 text-teal-600" />
                  <h4 className="font-semibold text-gray-900">Resolution Rate</h4>
                </div>
                <p className="text-sm text-gray-600">Success rate in addressing customer concerns</p>
              </div>
            </div>
          </div>
        </section>

        {/* Managing Flagged Reviews Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Flag className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Managing Flagged Reviews
          </h2>
          <p className="mb-6">
            Handle inappropriate or spam reviews through the review management system:
          </p>

          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-red-900 mb-3">When to Flag a Review</h3>
              <ul className="space-y-2 text-sm text-red-800">
                <li>• Contains offensive or inappropriate language</li>
                <li>• Appears to be spam or fake</li>
                <li>• Violates platform community guidelines</li>
                <li>• Contains personal information or attacks</li>
                <li>• Is completely unrelated to your business</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-yellow-900 mb-3">Review Moderation Process</h3>
              <p className="text-sm text-yellow-800 mb-3">
                When you flag a review, it goes through a moderation process:
              </p>
              <ol className="list-decimal list-inside text-sm text-yellow-800 space-y-1">
                <li>Review is temporarily hidden during investigation</li>
                <li>Platform moderators assess the flagged content</li>
                <li>Decision is made to keep, remove, or request revision</li>
                <li>You're notified of the final decision</li>
              </ol>
            </div>
          </div>
        </section>

        {/* Best Practices */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Review Management Best Practices
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-4">Success Strategies</h3>
              <ul className="space-y-2 text-sm text-green-800">
                <li>• <strong>Respond Promptly:</strong> Reply to reviews within 24-48 hours</li>
                <li>• <strong>Be Personal:</strong> Use the reviewer's name when possible</li>
                <li>• <strong>Stay Professional:</strong> Maintain a courteous tone always</li>
                <li>• <strong>Show Appreciation:</strong> Thank customers for their time</li>
                <li>• <strong>Learn and Improve:</strong> Use feedback to enhance your business</li>
              </ul>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-orange-900 mb-4">Common Mistakes</h3>
              <ul className="space-y-2 text-sm text-orange-800">
                <li>• <strong>Ignoring Reviews:</strong> Not responding damages your reputation</li>
                <li>• <strong>Generic Responses:</strong> Cookie-cutter replies feel impersonal</li>
                <li>• <strong>Defensive Reactions:</strong> Arguing with customers is counterproductive</li>
                <li>• <strong>Delayed Responses:</strong> Late replies reduce their effectiveness</li>
                <li>• <strong>Public Arguments:</strong> Keep disputes private when possible</li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/owner-admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}
