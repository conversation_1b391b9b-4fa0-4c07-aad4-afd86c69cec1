"use client";

import React from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Users,
  Eye,
  MousePointer,
  Heart,
  Search,
  Star,
  MessageSquare,
  Share2,
  ShoppingCart,
  Smartphone,
  Monitor,
  Tablet,
} from "lucide-react";

export default function UserExperiencePage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/owner-admin" className="hover:text-blue-600">
            Business Admin
          </Link>
          <span>/</span>
          <Link href="/owner-admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>User Experience</span>
        </div>

        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          User Experience Guide
          <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
            Complete
          </span>
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Learn about the customer-facing features and how users interact with
          your business on the platform. Understanding the user journey helps
          you optimize your business presence.
        </p>
      </div>

      {/* Content */}
      <div className="text-gray-700 leading-relaxed">
        {/* Customer Journey Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Users className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Customer Journey Overview
          </h2>
          <p className="mb-6">
            Understanding how customers discover, evaluate, and interact with
            your business helps you optimize every touchpoint:
          </p>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200 mb-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">
              Customer Journey Stages
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 shadow-sm border border-blue-100">
                <div className="flex items-center gap-3 mb-3">
                  <Search className="w-5 h-5 text-blue-600" />
                  <h4 className="font-semibold text-gray-900">Discovery</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Customers find your business through search, categories, or
                  recommendations
                </p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm border border-green-100">
                <div className="flex items-center gap-3 mb-3">
                  <Eye className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-gray-900">Evaluation</h4>
                </div>
                <p className="text-sm text-gray-600">
                  They browse your products, read reviews, and compare options
                </p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm border border-purple-100">
                <div className="flex items-center gap-3 mb-3">
                  <MousePointer className="w-5 h-5 text-purple-600" />
                  <h4 className="font-semibold text-gray-900">Engagement</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Customers interact with your content, leave reviews, or make
                  purchases
                </p>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm border border-orange-100">
                <div className="flex items-center gap-3 mb-3">
                  <Heart className="w-5 h-5 text-orange-600" />
                  <h4 className="font-semibold text-gray-900">Loyalty</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Satisfied customers return, recommend, and become brand
                  advocates
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Product Discovery Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Search className="inline-block w-8 h-8 mr-2 text-blue-600" />
            How Customers Find Your Products
          </h2>
          <p className="mb-6">
            Customers discover your business through multiple channels on the
            platform:
          </p>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Search className="w-5 h-5 text-blue-600" />
                Search Results
              </h3>
              <p className="text-gray-600 mb-4">
                The primary way customers find businesses and products:
              </p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Keyword-based search for products and services</li>
                <li>Location-based search for local businesses</li>
                <li>Category browsing and filtering</li>
                <li>Advanced search with multiple criteria</li>
                <li>Voice search and mobile-optimized results</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-600" />
                Featured Listings
              </h3>
              <p className="text-gray-600 mb-4">
                Premium placement opportunities:
              </p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Sponsored product placements</li>
                <li>Editor's choice and featured collections</li>
                <li>Trending and popular product sections</li>
                <li>Category-specific featured listings</li>
                <li>Seasonal and promotional highlights</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Share2 className="w-5 h-5 text-green-600" />
                Social Discovery
              </h3>
              <p className="text-gray-600 mb-4">
                Word-of-mouth and social recommendations:
              </p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Customer reviews and ratings</li>
                <li>Social media sharing and mentions</li>
                <li>Referral programs and recommendations</li>
                <li>User-generated content and testimonials</li>
                <li>Community discussions and forums</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Product Pages Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Eye className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Product Page Experience
          </h2>
          <p className="mb-6">
            Your product pages are crucial for converting visitors into
            customers. Here's what customers see:
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">
              Key Product Page Elements
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">
                  Visual Elements
                </h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• High-quality product images and gallery</li>
                  <li>• Business logo and branding</li>
                  <li>• Visual rating displays (stars)</li>
                  <li>• Category and tag badges</li>
                  <li>• Responsive image optimization</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">
                  Information Display
                </h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• Product name and description</li>
                  <li>• Business contact information</li>
                  <li>• Operating hours and location</li>
                  <li>• Website and social media links</li>
                  <li>• Customer reviews and ratings</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Customer Actions Available
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Leave Reviews
                  </h4>
                  <p className="text-sm text-gray-600">
                    Share experiences and rate products
                  </p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <Share2 className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Share & Recommend
                  </h4>
                  <p className="text-sm text-gray-600">
                    Share with friends and family
                  </p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Visit Business
                  </h4>
                  <p className="text-sm text-gray-600">
                    Click through to your website
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Device Experience Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Smartphone className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Multi-Device Experience
          </h2>
          <p className="mb-6">
            Customers access your business information across various devices.
            Here's how the experience adapts:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <Smartphone className="w-6 h-6 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Mobile Experience
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Touch-optimized navigation</li>
                <li>• Swipeable image galleries</li>
                <li>• One-tap calling and directions</li>
                <li>• Mobile-friendly review forms</li>
                <li>• Quick sharing options</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <Tablet className="w-6 h-6 text-green-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Tablet Experience
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Optimized for larger screens</li>
                <li>• Enhanced image viewing</li>
                <li>• Multi-column layouts</li>
                <li>• Improved readability</li>
                <li>• Better multi-tasking support</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <Monitor className="w-6 h-6 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Desktop Experience
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Full-featured interface</li>
                <li>• Detailed product galleries</li>
                <li>• Advanced filtering options</li>
                <li>• Comprehensive business info</li>
                <li>• Enhanced review management</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Review System Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Star className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Customer Review System
          </h2>
          <p className="mb-6">
            The review system is central to the customer experience and your
            business reputation:
          </p>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">
              How Customers Leave Reviews
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center text-yellow-800 font-bold text-sm">
                  1
                </div>
                <div>
                  <h4 className="font-semibold text-yellow-900">
                    Find Your Product
                  </h4>
                  <p className="text-sm text-yellow-800">
                    Customers navigate to your product page
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center text-yellow-800 font-bold text-sm">
                  2
                </div>
                <div>
                  <h4 className="font-semibold text-yellow-900">
                    Rate Experience
                  </h4>
                  <p className="text-sm text-yellow-800">
                    Select star rating (1-5 stars)
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center text-yellow-800 font-bold text-sm">
                  3
                </div>
                <div>
                  <h4 className="font-semibold text-yellow-900">
                    Write Review
                  </h4>
                  <p className="text-sm text-yellow-800">
                    Add detailed feedback and comments
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center text-yellow-800 font-bold text-sm">
                  4
                </div>
                <div>
                  <h4 className="font-semibold text-yellow-900">
                    Submit & Publish
                  </h4>
                  <p className="text-sm text-yellow-800">
                    Review appears on your product page
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Review Display Features
              </h3>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Overall rating summary with star display</li>
                <li>
                  Rating breakdown by star level (5-star: 60%, 4-star: 25%,
                  etc.)
                </li>
                <li>Most recent reviews shown first</li>
                <li>Helpful voting system for reviews</li>
                <li>Filter reviews by rating or date</li>
                <li>Business owner responses highlighted</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Review Quality Features
              </h3>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
                <li>Verified customer badges where applicable</li>
                <li>Spam and fake review detection</li>
                <li>Community moderation and reporting</li>
                <li>Review authenticity verification</li>
                <li>Guidelines for constructive feedback</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Optimization Tips Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Optimizing for Customer Experience
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-4">
                Enhancement Strategies
              </h3>
              <ul className="space-y-2 text-sm text-green-800">
                <li>
                  • <strong>Quality Images:</strong> Use high-resolution,
                  professional photos
                </li>
                <li>
                  • <strong>Complete Information:</strong> Fill out all business
                  details
                </li>
                <li>
                  • <strong>Regular Updates:</strong> Keep information current
                  and accurate
                </li>
                <li>
                  • <strong>Respond to Reviews:</strong> Engage with customer
                  feedback
                </li>
                <li>
                  • <strong>Optimize for Search:</strong> Use relevant keywords
                  and tags
                </li>
              </ul>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-orange-900 mb-4">
                Common Issues to Avoid
              </h3>
              <ul className="space-y-2 text-sm text-orange-800">
                <li>
                  • <strong>Incomplete Profiles:</strong> Missing key business
                  information
                </li>
                <li>
                  • <strong>Poor Image Quality:</strong> Blurry or
                  unprofessional photos
                </li>
                <li>
                  • <strong>Outdated Information:</strong> Incorrect hours or
                  contact details
                </li>
                <li>
                  • <strong>Ignoring Reviews:</strong> Not responding to
                  customer feedback
                </li>
                <li>
                  • <strong>Inconsistent Branding:</strong> Mismatched business
                  information
                </li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/owner-admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}
