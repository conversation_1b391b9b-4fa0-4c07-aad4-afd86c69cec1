"use client";

import { useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";
import { getUser } from "@/app/util/serverFunctions";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle, Save, Bell, Shield, Eye, Mail } from "lucide-react";

export default function SettingsPage() {
    const auth = useAuth();
    const [activeTab, setActiveTab] = useState("general");
    const [notificationsEnabled, setNotificationsEnabled] = useState(true);
    const [emailNotifications, setEmailNotifications] = useState(true);
    const [marketingEmails, setMarketingEmails] = useState(false);
    const [publicProfile, setPublicProfile] = useState(true);

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading user data</h3>
                <p className="text-muted-foreground">{error?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    const user = data?.data;
    if (!user) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No user data found</h3>
                <p className="text-muted-foreground">Please sign in to view settings</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
                <p className="text-muted-foreground">
                    Manage your account settings and preferences
                </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList>
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="notifications">Notifications</TabsTrigger>
                    <TabsTrigger value="privacy">Privacy</TabsTrigger>
                </TabsList>

                <TabsContent value="general" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Profile Information</CardTitle>
                            <CardDescription>
                                Update your profile information
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="firstName">First Name</Label>
                                    <Input id="firstName" defaultValue={user.firstName} />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input id="lastName" defaultValue={user.lastName} />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input id="email" type="email" defaultValue={user.email} disabled />
                                <p className="text-xs text-muted-foreground">
                                    Your email is managed by your authentication provider
                                </p>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="username">Username</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">@</span>
                                    <Input 
                                        id="username" 
                                        defaultValue={user.userName} 
                                        className="pl-7"
                                        placeholder="username"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    You can change your username every 30 days. Your profile will be accessible at @username
                                </p>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button>
                                <Save className="h-4 w-4 mr-2" />
                                Save Changes
                            </Button>
                        </CardFooter>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Business Settings</CardTitle>
                            <CardDescription>
                                Configure your business preferences
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="businessName">Default Business Name</Label>
                                <Input id="businessName" defaultValue={user.businesses?.[0]?.ownerName || ""} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="businessEmail">Business Email</Label>
                                <Input id="businessEmail" type="email" defaultValue={user.email} />
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button>
                                <Save className="h-4 w-4 mr-2" />
                                Save Changes
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="notifications" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Notification Preferences</CardTitle>
                            <CardDescription>
                                Manage how you receive notifications
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="notifications">Enable Notifications</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Receive notifications about your business
                                    </p>
                                </div>
                                <Switch
                                    id="notifications"
                                    checked={notificationsEnabled}
                                    onCheckedChange={setNotificationsEnabled}
                                />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="emailNotifications">Email Notifications</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Receive notifications via email
                                    </p>
                                </div>
                                <Switch
                                    id="emailNotifications"
                                    checked={emailNotifications}
                                    onCheckedChange={setEmailNotifications}
                                    disabled={!notificationsEnabled}
                                />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="marketingEmails">Marketing Emails</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Receive marketing and promotional emails
                                    </p>
                                </div>
                                <Switch
                                    id="marketingEmails"
                                    checked={marketingEmails}
                                    onCheckedChange={setMarketingEmails}
                                />
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button>
                                <Bell className="h-4 w-4 mr-2" />
                                Save Notification Settings
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="privacy" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Privacy Settings</CardTitle>
                            <CardDescription>
                                Manage your privacy preferences
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="publicProfile">Public Profile</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Make your profile visible to others
                                    </p>
                                </div>
                                <Switch
                                    id="publicProfile"
                                    checked={publicProfile}
                                    onCheckedChange={setPublicProfile}
                                />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label htmlFor="dataSharing">Data Sharing</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Allow sharing of anonymized data for analytics
                                    </p>
                                </div>
                                <Switch
                                    id="dataSharing"
                                    defaultChecked
                                />
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button>
                                <Shield className="h-4 w-4 mr-2" />
                                Save Privacy Settings
                            </Button>
                        </CardFooter>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}
