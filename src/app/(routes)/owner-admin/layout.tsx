"use client";

import React, { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { useUser } from "@/app/hooks/useUser";
import {
  LayoutGrid,
  Package,
  Megaphone,
  MessageSquare,
  CreditCard,
  BarChart3,
  Settings,
  BookOpen,
  Menu,
  ChevronLeft,
  Package2,
  Code,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { baseUrl } from "@/app/util/serverFunctions";

export default function OwnerAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { userId, isLoaded } = useAuth();
  const { user, isLoading: userIsLoading, isAllowed, securityReason } = useUser();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [hasBusinesses, setHasBusinesses] = useState<boolean | null>(null);

  // Check if user is authenticated and has proper status
  useEffect(() => {
    if (isLoaded && !userId) {
      // Let Clerk handle the redirect automatically instead of manual redirect
      // This prevents CORS issues with Account Portal
      return;
    }
    
    // Check if user is allowed (not banned, suspended, or deleted)
    if (user && !isAllowed) {
      console.warn(`User access restricted: ${securityReason}`);
      // The redirect will be handled by the useSecureUser hook
      return;
    }
  }, [isLoaded, userId, router, user, isAllowed, securityReason]);

  // Check if user has businesses
  useEffect(() => {
    const checkBusinesses = async () => {
      try {
        const response = await fetch(baseUrl + "/api/get/user", {
          credentials: "include",
        });
        const data = await response.json();
        setHasBusinesses(data.data?.businesses?.length > 0);
      } catch (error) {
        console.error("Error checking businesses:", error);
        setHasBusinesses(false);
      }
    };
    checkBusinesses();
  }, []);

  // Get the current page title based on the pathname
  const getPageTitle = () => {
    if (pathname === "/owner-admin") return "Dashboard";
    if (pathname === "/owner-admin/products") return "Products";
    if (pathname === "/owner-admin/promotions") return "Promotions";
    if (pathname === "/owner-admin/reviews") return "Reviews";
    if (pathname === "/owner-admin/widgets") return "Widgets";
    if (pathname === "/owner-admin/subscription") return "Subscription";
    if (pathname === "/owner-admin/analytics") return "Analytics";
    if (pathname === "/owner-admin/settings") return "Settings";
    if (pathname === "/owner-admin/docs") return "Documentation";
    return "Business Admin";
  };

  // Check if a nav item is active
  const isActive = (path: string) => {
    if (path === "/owner-admin" && pathname === "/owner-admin") return true;
    if (path !== "/owner-admin" && pathname.startsWith(path)) return true;
    return false;
  };

  // Navigation items
  const navItems = [
    {
      path: "/owner-admin",
      label: "Dashboard",
      icon: <LayoutGrid className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/products",
      label: "Products",
      icon: <Package className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/promotions",
      label: "Promotions",
      icon: <Megaphone className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/reviews",
      label: "Reviews",
      icon: <MessageSquare className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/widgets",
      label: "Widgets",
      icon: <Code className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/subscription",
      label: "Subscription",
      icon: <CreditCard className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/analytics",
      label: "Analytics",
      icon: <BarChart3 className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/settings",
      label: "Settings",
      icon: <Settings className="w-5 h-5" />,
    },
    {
      path: "/owner-admin/docs",
      label: "Documentation",
      icon: <BookOpen className="w-5 h-5" />,
    },
  ];

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (hasBusinesses === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-pulse text-xl font-medium text-blue-600">
          Checking Businesses...
        </div>
      </div>
    );
  }

  if (hasBusinesses === false) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-6 text-center">
        <div className="max-w-md space-y-4">
          <Package2 className="w-16 h-16 mx-auto text-gray-400" />
          <h1 className="text-2xl font-bold">No Businesses Found</h1>
          <p className="text-gray-500">
            You need to have at least one business to access this section.
          </p>
          <Button asChild className="mt-4">
            <Link href="/claim-product">Claim or Create a Business</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-10 flex items-center justify-between h-16 px-4 md:px-6 border-b bg-background">
        <div className="flex items-center gap-4">
          <Link
            href="/mybusinesses"
            className="flex items-center gap-2 text-sm"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Back to My Businesses</span>
          </Link>
          <div className="h-6 border-l mx-2"></div>
          <Link
            href="/owner-admin"
            className="flex items-center gap-2 font-semibold"
          >
            <Package2 className="w-5 h-5" />
            <span className="hidden sm:inline">Business Admin</span>
            <span className="sm:hidden">Admin</span>
          </Link>
        </div>

        {/* Mobile menu trigger */}
        <div className="md:hidden">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="flex flex-col h-full w-64 p-0 bg-white"
            >
              <div className="flex-shrink-0 p-4 border-b bg-gray-50">
                <Link
                  href="/owner-admin"
                  className="flex items-center gap-2 font-semibold"
                >
                  <Package2 className="w-5 h-5" />
                  <span>Business Admin</span>
                </Link>
              </div>
              <nav className="overflow-y-auto flex-1 p-4 space-y-2">
                {navItems.map((item) => {
                  const active = isActive(item.path);
                  return (
                    <Button
                      key={item.path}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start relative sidebar-nav-hover transition-all duration-200",
                        active ? "sidebar-nav-active" : "hover:bg-gray-50",
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                      asChild
                    >
                      <Link href={item.path}>
                        {React.cloneElement(item.icon, {
                          className: cn(
                            "w-5 h-5 mr-3",
                            active && "text-primary",
                          ),
                        })}
                        {item.label}
                        {active && (
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full shadow-sm" />
                        )}
                      </Link>
                    </Button>
                  );
                })}
              </nav>
              <div className="flex-shrink-0 p-4 border-t">
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/mybusinesses">Back to My Businesses</Link>
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside className="hidden w-64 p-4 border-r md:block bg-gray-50">
          <nav className="space-y-2">
            {navItems.map((item) => {
              const active = isActive(item.path);
              return (
                <Button
                  key={item.path}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start relative sidebar-nav-hover transition-all duration-200",
                    active ? "sidebar-nav-active" : "hover:bg-gray-50",
                  )}
                  asChild
                >
                  <Link href={item.path}>
                    {React.cloneElement(item.icon, {
                      className: cn("w-5 h-5 mr-3", active && "text-blue-600"),
                    })}
                    {item.label}
                    {active && (
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full shadow-sm" />
                    )}
                  </Link>
                </Button>
              );
            })}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 p-6 md:p-8 pb-6">
          <div className="flex flex-col gap-6">
            {/* Page header */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-2xl font-bold">{getPageTitle()}</h1>
                <p className="text-muted-foreground">
                  {getPageTitle() === "Dashboard" &&
                    "Overview of your business admin panel"}
                  {getPageTitle() === "Products" &&
                    "Manage your products and listings"}
                  {getPageTitle() === "Promotions" &&
                    "Manage your promotions and campaigns"}
                  {getPageTitle() === "Reviews" && "Manage customer reviews"}
                  {getPageTitle() === "Widgets" && "Create and manage embeddable widgets"}
                  {getPageTitle() === "Subscription" &&
                    "Manage your subscription and billing"}
                  {getPageTitle() === "Analytics" &&
                    "View your business analytics"}
                  {getPageTitle() === "Settings" &&
                    "Configure your business settings"}
                  {getPageTitle() === "Documentation" &&
                    "Access business owner guides and resources"}
                </p>
              </div>
            </div>

            {/* Page content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
