"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
    ChevronLeft,
    CalendarIcon,
    Edit,
    Trash,
    BarChart3,
    Eye,
    MousePointerClick,
    ShoppingCart,
    CheckCircle,
    AlertCircle,
    Megaphone,
    Info
} from "lucide-react";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { iPromotion, iProduct } from "@/app/util/Interfaces";
import EditPromotionModal from "@/app/components/EditPromotionModal";

// Function to fetch a single promotion
const fetchPromotion = async (promotionId: string): Promise<iPromotion> => {
    const response = await fetch(`/api/promotions/${promotionId}`);
    if (!response.ok) {
        throw new Error('Failed to fetch promotion');
    }
    const data = await response.json();
    return data.success ? data.data : null;
};

// Function to fetch promotion analytics
const fetchPromotionAnalytics = async (promotionId: string) => {
    const response = await fetch(`/api/promotions/${promotionId}/analytics`);
    if (!response.ok) {
        throw new Error('Failed to fetch promotion analytics');
    }
    const data = await response.json();
    return data.success ? data.data : null;
};

// Function to update a promotion
const updatePromotion = async ({ promotionId, updateData }: { promotionId: string; updateData: any }) => {
    const response = await fetch(`/api/promotions/${promotionId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update promotion');
    }

    return response.json();
};

// Function to delete a promotion
const deletePromotion = async (promotionId: string) => {
    const response = await fetch(`/api/promotions/${promotionId}`, {
        method: 'DELETE',
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete promotion');
    }

    return response.json();
};

// Function to fetch business products
const fetchBusinessProducts = async (businessId: string): Promise<iProduct[]> => {
    const response = await fetch(`/api/get/products/all?businessId=${businessId}`);
    if (!response.ok) {
        throw new Error('Failed to fetch products');
    }
    const data = await response.json();
    return data.success ? data.data : [];
};

interface StatCardProps {
    title: string;
    value: string | number;
    icon: React.ReactNode;
    description: string;
    change?: number;
}

const StatCard = ({ title, value, icon, description, change }: StatCardProps) => (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className="p-2 rounded-full bg-muted">{icon}</div>
        </CardHeader>
        <CardContent>
            <div className="text-2xl font-bold">{value}</div>
            {change !== undefined && (
                <div className="flex items-center mt-1">
                    <div className={`flex items-center text-xs ${change >= 0 ? "text-green-500" : "text-red-500"}`}>
                        {change >= 0 ? "+" : ""}{change}% from last period
                    </div>
                </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
        </CardContent>
    </Card>
);

export default function PromotionDetailPage({ params }: { params: { id: string } }) {
    const router = useRouter();
    const queryClient = useQueryClient();
    const [activeTab, setActiveTab] = useState("overview");
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

    // Fetch promotion data
    const { data: promotion, isLoading, error } = useQuery({
        queryKey: ['promotion', params.id],
        queryFn: () => fetchPromotion(params.id),
    });

    // Fetch analytics data
    const { data: analytics, isLoading: analyticsLoading } = useQuery({
        queryKey: ['promotion-analytics', params.id],
        queryFn: () => fetchPromotionAnalytics(params.id),
        enabled: !!promotion,
    });

    // Fetch business products for the edit modal
    const businessId = promotion?.businessId;
    const { data: products = [] } = useQuery({
        queryKey: ['business-products', businessId],
        queryFn: () => fetchBusinessProducts(businessId!),
        enabled: !!businessId,
    });

    // Update mutation
    const updateMutation = useMutation({
        mutationFn: updatePromotion,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['promotion', params.id] });
            queryClient.invalidateQueries({ queryKey: ['business-promotions'] });
            queryClient.invalidateQueries({ queryKey: ['promotions'] });
        },
        onError: (error) => {
            alert(`Failed to update promotion: ${error.message}`);
        },
    });

    // Delete mutation
    const deleteMutation = useMutation({
        mutationFn: deletePromotion,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['business-promotions'] });
            queryClient.invalidateQueries({ queryKey: ['promotions'] });
            router.push('/owner-admin/promotions');
        },
        onError: (error) => {
            alert(`Failed to delete promotion: ${error.message}`);
        },
    });

    // Handle delete
    const handleDelete = () => {
        deleteMutation.mutate(params.id);
        setIsDeleteDialogOpen(false);
    };

    // Loading state
    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    // Error state
    if (error || !promotion) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Promotion not found</h3>
                <p className="text-muted-foreground">
                    {error?.message || "The requested promotion could not be found"}
                </p>
                <Button asChild>
                    <Link href="/owner-admin/promotions">Back to Promotions</Link>
                </Button>
            </div>
        );
    }

    // Calculate if promotion is current, upcoming, or expired
    const now = new Date();
    const isUpcoming = now < new Date(promotion.startDate);
    const isExpired = now > new Date(promotion.endDate);
    const isCurrent = !isUpcoming && !isExpired;

    // Format dates
    const formatDate = (date: Date | string) => {
        return format(new Date(date), "PPP");
    };

    // Toggle promotion active status
    const toggleActiveStatus = () => {
        updateMutation.mutate({
            promotionId: params.id,
            updateData: { isActive: !promotion.isActive }
        });
    };

    // Get status badge
    const getStatusBadge = () => {
        if (!promotion.isActive) {
            return <Badge variant="outline" className="bg-gray-100">Inactive</Badge>;
        }
        if (isUpcoming) {
            return <Badge variant="outline" className="bg-blue-100 text-blue-800">Upcoming</Badge>;
        }
        if (isExpired) {
            return <Badge variant="outline" className="bg-red-100 text-red-800">Expired</Badge>;
        }
        return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>;
    };

    // Get discount text
    const getDiscountText = () => {
        if (promotion.discountPercentage) {
            return `${promotion.discountPercentage}% off`;
        }
        if (promotion.discountAmount) {
            return `$${promotion.discountAmount} off`;
        }
        return "Special offer";
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/owner-admin/promotions")}
                >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back to promotions
                </Button>
            </div>

            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                    <div className="flex items-center gap-2">
                        <h2 className="text-3xl font-bold tracking-tight">{promotion.title}</h2>
                        <div className="mt-1">
                            {getStatusBadge()}
                        </div>
                    </div>
                    <p className="text-muted-foreground mt-1">{promotion.description}</p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant={promotion.isActive ? "outline" : "default"}
                        onClick={toggleActiveStatus}
                    >
                        {promotion.isActive ? "Deactivate" : "Activate"}
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditDialogOpen(true)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                    <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                    </Button>
                </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList>
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Promotion Image */}
                        <Card className="md:row-span-2">
                            <CardHeader>
                                <CardTitle>Promotion Image</CardTitle>
                            </CardHeader>
                            <CardContent className="flex flex-col items-center">
                                <div className="aspect-video w-full relative rounded-md overflow-hidden bg-muted">
                                    {promotion.image ? (
                                        <div
                                            className="absolute inset-0 bg-cover bg-center"
                                            style={{ backgroundImage: `url(${promotion.image})` }}
                                        ></div>
                                    ) : (
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <Megaphone className="h-12 w-12 text-muted-foreground" />
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Promotion Details */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Promotion Details</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label className="text-muted-foreground">Start Date</Label>
                                        <p className="font-medium">{formatDate(promotion.startDate)}</p>
                                    </div>
                                    <div>
                                        <Label className="text-muted-foreground">End Date</Label>
                                        <p className="font-medium">{formatDate(promotion.endDate)}</p>
                                    </div>
                                    <div>
                                        <Label className="text-muted-foreground">Discount</Label>
                                        <p className="font-medium">{getDiscountText()}</p>
                                    </div>
                                    <div>
                                        <Label className="text-muted-foreground">Status</Label>
                                        <p className="font-medium">{promotion.isActive ? "Active" : "Inactive"}</p>
                                    </div>
                                    {promotion.promotionCode && (
                                        <div className="col-span-2">
                                            <Label className="text-muted-foreground">Promotion Code</Label>
                                            <p className="font-medium">{promotion.promotionCode}</p>
                                        </div>
                                    )}
                                    <div className="col-span-2">
                                        <Label className="text-muted-foreground">Created At</Label>
                                        <p className="font-medium">{formatDate(promotion.createdAt)}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Promotion Status */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Promotion Status</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label className="text-muted-foreground">Current Status</Label>
                                    <div className="flex items-center space-x-2">
                                        {isCurrent && promotion.isActive && (
                                            <Alert>
                                                <CheckCircle className="h-4 w-4 text-green-500" />
                                                <AlertTitle>Active Promotion</AlertTitle>
                                                <AlertDescription>
                                                    This promotion is currently active and visible to customers.
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                        {isUpcoming && promotion.isActive && (
                                            <Alert>
                                                <Info className="h-4 w-4 text-blue-500" />
                                                <AlertTitle>Upcoming Promotion</AlertTitle>
                                                <AlertDescription>
                                                    This promotion will become active on {formatDate(promotion.startDate)}.
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                        {isExpired && (
                                            <Alert>
                                                <AlertCircle className="h-4 w-4 text-amber-500" />
                                                <AlertTitle>Expired Promotion</AlertTitle>
                                                <AlertDescription>
                                                    This promotion has expired and is no longer active.
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                        {!promotion.isActive && (
                                            <Alert>
                                                <AlertCircle className="h-4 w-4 text-gray-500" />
                                                <AlertTitle>Inactive Promotion</AlertTitle>
                                                <AlertDescription>
                                                    This promotion is currently disabled and not visible to customers.
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                    </div>
                                </div>

                                <div className="pt-4">
                                    <Button
                                        className="w-full"
                                        variant={promotion.isActive ? "outline" : "default"}
                                        onClick={toggleActiveStatus}
                                    >
                                        {promotion.isActive ? "Deactivate Promotion" : "Activate Promotion"}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="analytics" className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <StatCard
                            title="Total Views"
                            value={Number(promotion.viewCount || 0)}
                            icon={<Eye className="h-4 w-4" />}
                            description="Number of times promotion was viewed"
                            change={12}
                        />
                        <StatCard
                            title="Total Clicks"
                            value={Number(promotion.clickCount || 0)}
                            icon={<MousePointerClick className="h-4 w-4" />}
                            description="Number of clicks on promotion"
                            change={5}
                        />
                        <StatCard
                            title="Conversions"
                            value={Number(promotion.conversionCount || 0)}
                            icon={<ShoppingCart className="h-4 w-4" />}
                            description="Number of purchases using promotion"
                            change={8}
                        />
                        <StatCard
                            title="Conversion Rate"
                            value={`${((promotion.viewCount || 0) > 0 ? ((promotion.conversionCount || 0) / (promotion.viewCount || 1)) * 100 : 0).toFixed(1)}%`}
                            icon={<BarChart3 className="h-4 w-4" />}
                            description="Percentage of views resulting in purchase"
                            change={2}
                        />
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Performance Over Time</CardTitle>
                            <CardDescription>Views, clicks, and conversions over the promotion period</CardDescription>
                        </CardHeader>
                        <CardContent className="h-[300px] flex items-center justify-center text-muted-foreground">
                            <div className="text-center">
                                <BarChart3 className="h-16 w-16 mx-auto mb-4 opacity-20" />
                                <p>Performance chart will appear here with real data</p>
                                <p className="text-sm mt-2">
                                    This would typically show a chart of performance metrics over time
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Traffic Sources</CardTitle>
                                <CardDescription>Where promotion views are coming from</CardDescription>
                            </CardHeader>
                            <CardContent className="h-[200px] flex items-center justify-center text-muted-foreground">
                                <div className="text-center">
                                    <p>Traffic sources chart will appear here</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>User Demographics</CardTitle>
                                <CardDescription>Age groups and devices of viewers</CardDescription>
                            </CardHeader>
                            <CardContent className="h-[200px] flex items-center justify-center text-muted-foreground">
                                <div className="text-center">
                                    <p>Demographics chart will appear here</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>

            {/* Edit Promotion Modal */}
            {promotion && (
                <EditPromotionModal
                    promotion={promotion}
                    products={products}
                    isOpen={isEditDialogOpen}
                    onClose={() => setIsEditDialogOpen(false)}
                    onSuccess={() => {
                        // Refresh promotion data
                        queryClient.invalidateQueries({ queryKey: ['promotion', params.id] });
                        queryClient.invalidateQueries({ queryKey: ['business-promotions'] });
                        queryClient.invalidateQueries({ queryKey: ['promotions'] });
                    }}
                />
            )}

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Delete Promotion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this promotion?
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <p className="text-center text-muted-foreground">
                            This action cannot be undone. This will permanently delete the promotion and remove its data from our servers.
                        </p>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDelete} disabled={deleteMutation.isPending}>
                            {deleteMutation.isPending ? "Deleting..." : "Delete"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}