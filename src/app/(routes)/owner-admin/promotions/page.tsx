"use client";

import { useState } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { getUser } from "@/app/util/serverFunctions";
import {
    PlusCircle,
    Search,
    BarChart3,
    ArrowUpDown,
    ChevronDown,
    Eye,
    MousePointerClick,
    MoreHorizontal,
    Pencil,
    Trash,
    Tag
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Dialog,
    DialogContent,
    DialogDes<PERSON>,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { iPromotion, iProduct } from "@/app/util/Interfaces";
import { useRouter } from "next/navigation";
import EditPromotionModal from "@/app/components/EditPromotionModal";

// Function to fetch business promotions
const fetchBusinessPromotions = async (businessId: string): Promise<iPromotion[]> => {
    const response = await fetch(`/api/promotions?businessId=${businessId}`);
    if (!response.ok) {
        throw new Error('Failed to fetch promotions');
    }
    const data = await response.json();
    return data.success ? data.data : [];
};

// Function to delete a promotion
const deletePromotion = async (promotionId: string): Promise<void> => {
    const response = await fetch(`/api/promotions/${promotionId}`, {
        method: 'DELETE',
    });
    if (!response.ok) {
        throw new Error('Failed to delete promotion');
    }
};

// Function to fetch business products
const fetchBusinessProducts = async (businessId: string): Promise<iProduct[]> => {
    const response = await fetch(`/api/get/products/all?businessId=${businessId}`);
    if (!response.ok) {
        throw new Error('Failed to fetch products');
    }
    const data = await response.json();
    return data.success ? data.data : [];
};

export default function PromotionsPage() {
    const router = useRouter();
    const { userId } = useAuth();
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [promotionToDelete, setPromotionToDelete] = useState<string | null>(null);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [promotionToEdit, setPromotionToEdit] = useState<iPromotion | null>(null);

    // Get user data to find their business
    const { data: user, isLoading: userLoading } = useQuery({
        queryKey: ['user', userId],
        queryFn: () => getUser(),
        enabled: !!userId,
    });

    // Get business promotions
    const businessId = user?.data?.businesses?.[0]?.id;
    const { data: promotions = [], isLoading: promotionsLoading, error, refetch } = useQuery({
        queryKey: ['business-promotions', businessId],
        queryFn: () => fetchBusinessPromotions(businessId!),
        enabled: !!businessId,
    });

    // Get business products for the edit modal
    const { data: products = [] } = useQuery({
        queryKey: ['business-products', businessId],
        queryFn: () => fetchBusinessProducts(businessId!),
        enabled: !!businessId,
    });

    const isLoading = userLoading || promotionsLoading;

    // Filter promotions based on search term and status filter
    const filteredPromotions = promotions.filter(promo => {
        const matchesSearch = promo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            promo.description.toLowerCase().includes(searchTerm.toLowerCase());

        if (statusFilter === "all") return matchesSearch;
        if (statusFilter === "active") return matchesSearch && promo.isActive;
        if (statusFilter === "inactive") return matchesSearch && !promo.isActive;

        // Calculate if promotion is current, upcoming, or expired
        const now = new Date();
        const isUpcoming = now < new Date(promo.startDate);
        const isExpired = now > new Date(promo.endDate);

        if (statusFilter === "upcoming") return matchesSearch && isUpcoming && promo.isActive;
        if (statusFilter === "current") return matchesSearch && !isUpcoming && !isExpired && promo.isActive;
        if (statusFilter === "expired") return matchesSearch && isExpired;

        return matchesSearch;
    });

    // Calculate performance stats
    const totalViews = promotions.reduce((sum, promo) => sum + (promo.viewCount || 0), 0);
    const totalClicks = promotions.reduce((sum, promo) => sum + (promo.clickCount || 0), 0);
    const clickRate = totalViews > 0 ? (totalClicks / totalViews) * 100 : 0;

    // Get status badge for a promotion
    const getStatusBadge = (promotion: iPromotion) => {
        if (!promotion.isActive) {
            return <Badge variant="outline" className="bg-gray-100">Inactive</Badge>;
        }

        const now = new Date();
        const isUpcoming = now < new Date(promotion.startDate);
        const isExpired = now > new Date(promotion.endDate);

        if (isUpcoming) {
            return <Badge variant="outline" className="bg-blue-100 text-blue-800">Upcoming</Badge>;
        }
        if (isExpired) {
            return <Badge variant="outline" className="bg-red-100 text-red-800">Expired</Badge>;
        }
        return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>;
    };

    // Format date
    const formatDate = (date: Date | string) => {
        return format(new Date(date), "MMM d, yyyy");
    };

    // Get discount text
    const getDiscountText = (promotion: iPromotion) => {
        if (promotion.discountPercentage) {
            return `${promotion.discountPercentage}% off`;
        }
        if (promotion.discountAmount) {
            return `$${promotion.discountAmount} off`;
        }
        return "Special offer";
    };

    // Handle delete
    const handleDelete = (id: string) => {
        setPromotionToDelete(id);
        setIsDeleteDialogOpen(true);
    };

    // Handle edit
    const handleEdit = (promotion: iPromotion) => {
        setPromotionToEdit(promotion);
        setIsEditModalOpen(true);
    };

    // Confirm delete
    const confirmDelete = async () => {
        if (!promotionToDelete) return;

        try {
            await deletePromotion(promotionToDelete);
            // Refetch promotions to update the list
            refetch();
            alert("Promotion deleted successfully!");
        } catch (error) {
            console.error("Failed to delete promotion:", error);
            alert("Failed to delete promotion. Please try again.");
        } finally {
            setIsDeleteDialogOpen(false);
            setPromotionToDelete(null);
        }
    };

    // Show loading state
    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Promotions</h2>
                        <p className="text-muted-foreground">
                            Manage your promotional campaigns and special offers
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/owner-admin/promotions/create">
                            <PlusCircle className="h-4 w-4 mr-2" />
                            New Promotion
                        </Link>
                    </Button>
                </div>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i}>
                            <CardHeader className="flex flex-row items-center justify-between pb-2">
                                <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                                <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                            </CardHeader>
                            <CardContent>
                                <div className="h-8 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
                                <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
                <div className="bg-white border rounded-lg p-8">
                    <div className="animate-pulse space-y-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    </div>
                </div>
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Promotions</h2>
                        <p className="text-muted-foreground">
                            Manage your promotional campaigns and special offers
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/owner-admin/promotions/create">
                            <PlusCircle className="h-4 w-4 mr-2" />
                            New Promotion
                        </Link>
                    </Button>
                </div>
                <Card>
                    <CardContent className="p-8 text-center">
                        <p className="text-red-600 mb-4">Failed to load promotions</p>
                        <Button onClick={() => refetch()}>Try Again</Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                    <h2 className="text-3xl font-bold tracking-tight">Promotions</h2>
                    <p className="text-muted-foreground">
                        Manage your promotional campaigns and special offers
                    </p>
                </div>
                <Button asChild>
                    <Link href="/owner-admin/promotions/create">
                        <PlusCircle className="h-4 w-4 mr-2" />
                        New Promotion
                    </Link>
                </Button>
            </div>

            {/* Overview cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">Total Promotions</CardTitle>
                        <Tag className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{promotions.length}</div>
                        <p className="text-xs text-muted-foreground">
                            {promotions.filter(p => p.isActive).length} active
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                        <Eye className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalViews.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            Across all promotions
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
                        <MousePointerClick className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{clickRate.toFixed(1)}%</div>
                        <p className="text-xs text-muted-foreground">
                            {totalClicks.toLocaleString()} total clicks
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search promotions..."
                        className="pl-8"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <Select
                    defaultValue="all"
                    onValueChange={(value) => setStatusFilter(value)}
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Promotions</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="upcoming">Upcoming</SelectItem>
                        <SelectItem value="current">Current</SelectItem>
                        <SelectItem value="expired">Expired</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block bg-white border rounded-lg">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[40%]">Promotion</TableHead>
                            <TableHead className="w-[15%]">Discount</TableHead>
                            <TableHead className="w-[15%]">Dates</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="w-[15%]">Performance</TableHead>
                            <TableHead className="w-[5%]"></TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredPromotions.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={6} className="text-center py-10 text-muted-foreground">
                                    No promotions found. Create a new promotion to get started.
                                </TableCell>
                            </TableRow>
                        ) : (
                            filteredPromotions.map((promotion) => (
                                <TableRow key={promotion.id} className="cursor-pointer hover:bg-muted/50" onClick={() => router.push(`/owner-admin/promotions/${promotion.id}`)}>
                                    <TableCell className="font-medium">
                                        <div className="flex items-center space-x-3">
                                            <div
                                                className="w-10 h-10 rounded-md bg-cover bg-center bg-muted"
                                                style={{
                                                    backgroundImage: promotion.image ? `url(${promotion.image})` : undefined,
                                                    backgroundColor: !promotion.image ? 'hsl(var(--muted))' : undefined
                                                }}
                                            ></div>
                                            <div>
                                                <div className="font-medium">{promotion.title}</div>
                                                <div className="text-xs text-muted-foreground truncate max-w-[250px]">
                                                    {promotion.description}
                                                </div>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>{getDiscountText(promotion)}</TableCell>
                                    <TableCell>
                                        <div className="text-xs">
                                            <div>Start: {formatDate(promotion.startDate)}</div>
                                            <div>End: {formatDate(promotion.endDate)}</div>
                                        </div>
                                    </TableCell>
                                    <TableCell>{getStatusBadge(promotion)}</TableCell>
                                    <TableCell>
                                        <div className="flex items-center space-x-1 text-xs">
                                            <Eye className="h-3 w-3 text-muted-foreground" />
                                            <span>{promotion.viewCount || 0}</span>
                                            <span className="text-muted-foreground mx-0.5">·</span>
                                            <MousePointerClick className="h-3 w-3 text-muted-foreground" />
                                            <span>{promotion.clickCount || 0}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <span className="sr-only">Open menu</span>
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                <DropdownMenuItem onClick={(e) => {
                                                    e.stopPropagation();
                                                    router.push(`/owner-admin/promotions/${promotion.id}`);
                                                }}>
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View details
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleEdit(promotion);
                                                }}>
                                                    <Pencil className="h-4 w-4 mr-2" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    className="text-red-600"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        if (promotion.id) {
                                                            handleDelete(promotion.id);
                                                        }
                                                    }}
                                                >
                                                    <Trash className="h-4 w-4 mr-2" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
                {filteredPromotions.length === 0 ? (
                    <Card>
                        <CardContent className="p-8 text-center">
                            <p className="text-muted-foreground">
                                No promotions found. Create a new promotion to get started.
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    filteredPromotions.map((promotion) => (
                        <Card 
                            key={promotion.id} 
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => router.push(`/owner-admin/promotions/${promotion.id}`)}
                        >
                            <CardContent className="p-4">
                                <div className="flex items-start space-x-3">
                                    {/* Promotion Image */}
                                    <div
                                        className="w-16 h-16 rounded-lg bg-cover bg-center bg-muted flex-shrink-0"
                                        style={{
                                            backgroundImage: promotion.image ? `url(${promotion.image})` : undefined,
                                            backgroundColor: !promotion.image ? 'hsl(var(--muted))' : undefined
                                        }}
                                    >
                                        {!promotion.image && (
                                            <div className="w-full h-full flex items-center justify-center">
                                                <Tag className="h-6 w-6 text-muted-foreground" />
                                            </div>
                                        )}
                                    </div>
                                    
                                    {/* Promotion Details */}
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-start justify-between mb-2">
                                            <h3 className="font-semibold text-sm leading-tight pr-2">{promotion.title}</h3>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                                    <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0">
                                                        <span className="sr-only">Open menu</span>
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                    <DropdownMenuItem onClick={(e) => {
                                                        e.stopPropagation();
                                                        router.push(`/owner-admin/promotions/${promotion.id}`);
                                                    }}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View details
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleEdit(promotion);
                                                    }}>
                                                        <Pencil className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        className="text-red-600"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            if (promotion.id) {
                                                                handleDelete(promotion.id);
                                                            }
                                                        }}
                                                    >
                                                        <Trash className="h-4 w-4 mr-2" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                        
                                        <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                                            {promotion.description}
                                        </p>
                                        
                                        {/* Status and Discount */}
                                        <div className="flex items-center justify-between mb-3">
                                            {getStatusBadge(promotion)}
                                            <Badge variant="secondary" className="text-xs">
                                                {getDiscountText(promotion)}
                                            </Badge>
                                        </div>
                                        
                                        {/* Dates */}
                                        <div className="text-xs text-muted-foreground mb-3">
                                            <div>Start: {formatDate(promotion.startDate)}</div>
                                            <div>End: {formatDate(promotion.endDate)}</div>
                                        </div>
                                        
                                        {/* Performance */}
                                        <div className="flex items-center space-x-3 text-xs">
                                            <div className="flex items-center space-x-1">
                                                <Eye className="h-3 w-3 text-muted-foreground" />
                                                <span>{promotion.viewCount || 0}</span>
                                                <span className="text-muted-foreground">views</span>
                                            </div>
                                            <div className="flex items-center space-x-1">
                                                <MousePointerClick className="h-3 w-3 text-muted-foreground" />
                                                <span>{promotion.clickCount || 0}</span>
                                                <span className="text-muted-foreground">clicks</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Delete Promotion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this promotion?
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <p className="text-center text-muted-foreground">
                            This action cannot be undone. This will permanently delete the promotion and remove its data from our servers.
                        </p>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={confirmDelete}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit Promotion Modal */}
            {promotionToEdit && (
                <EditPromotionModal
                    promotion={promotionToEdit}
                    products={products}
                    isOpen={isEditModalOpen}
                    onClose={() => {
                        setIsEditModalOpen(false);
                        setPromotionToEdit(null);
                    }}
                    onSuccess={() => {
                        refetch(); // Refresh the promotions list
                    }}
                />
            )}
        </div>
    );
}