import { useState, useEffect, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import {
  createCommentOnReview,
  getCommentsForReview,
} from "@/app/util/serverFunctions";
import {
  createOwnerResponse<PERSON>he<PERSON>,
  getOwnerResponseInfo,
} from "../utils/reviewUtils";
import type {
  OwnerResponseState,
  OwnerResponseInfo,
  iComment,
  iUser,
  iReview,
  iProduct,
  iBusiness,
} from "../utils/types";

interface UseOwnerResponseProps {
  userData: { data: iUser } | undefined;
  reviews: iReview[];
  allProducts: iProduct[];
}

export function useOwnerResponse({
  userData,
  reviews,
  allProducts,
}: UseOwnerResponseProps) {
  const auth = useAuth();
  const queryClient = useQueryClient();

  const [responseMode, setResponseMode] = useState<Record<string, boolean>>({});
  const [responses, setResponses] = useState<Record<string, string>>({});
  const [reviewComments, setReviewComments] = useState<
    Record<string, iComment[]>
  >({});

  const responseState: OwnerResponseState = {
    responseMode,
    responses,
    reviewComments,
  };

  // Create owner response checker function
  const hasOwnerResponse = useCallback(
    (reviewId: string): boolean => {
      if (!userData?.data?.businesses) return false;
      const checker = createOwnerResponseChecker(
        reviewComments,
        userData.data.businesses,
      );
      return checker(reviewId);
    },
    [reviewComments, userData?.data?.businesses],
  );

  // Function to check if review has owner response
  const checkOwnerResponse = useCallback(async (reviewId: string) => {
    try {
      const response = await getCommentsForReview(reviewId);
      if (response.success && response.data) {
        setReviewComments((prev) => ({
          ...prev,
          [reviewId]: response.data || [],
        }));
      }
    } catch (error) {
      console.error("Error fetching comments:", error);
    }
  }, []);

  // Load comments for all reviews when they change
  useEffect(() => {
    if (reviews.length > 0) {
      reviews.forEach((review) => {
        if (review.id) {
          checkOwnerResponse(review.id);
        }
      });
    }
  }, [reviews, checkOwnerResponse]);

  // Get owner response information for a specific review
  const getOwnerResponseInfoForReview = useCallback(
    (reviewId: string): OwnerResponseInfo | null => {
      return getOwnerResponseInfo(
        reviewId,
        reviewComments,
        reviews,
        allProducts,
      );
    },
    [reviewComments, reviews, allProducts],
  );

  // Toggle response mode for a specific review
  const toggleResponseMode = useCallback((reviewId: string) => {
    setResponseMode((prev) => ({
      ...prev,
      [reviewId]: !prev[reviewId],
    }));
  }, []);

  // Handle response text changes
  const handleResponseChange = useCallback((reviewId: string, text: string) => {
    setResponses((prev) => ({
      ...prev,
      [reviewId]: text,
    }));
  }, []);

  // Mutation for submitting owner responses
  const submitResponseMutation = useMutation({
    mutationFn: async ({
      reviewId,
      responseText,
    }: {
      reviewId: string;
      responseText: string;
    }) => {
      if (!auth.userId || !userData?.data) {
        throw new Error("User not authenticated");
      }

      const user: iUser = userData.data as iUser;
      const review = reviews.find((r) => r.id === reviewId);

      if (!review) {
        throw new Error("Review not found");
      }

      const comment: iComment = {
        body: responseText,
        reviewId: reviewId,
        userId: auth.userId,
        user: user,
        review: review,
        createdDate: new Date(),
        isDeleted: false,
        upvotes: 0,
        downvotes: 0,
      };

      const response = await createCommentOnReview(comment);
      if (!response.success) {
        throw new Error(response.error || "Failed to submit response");
      }
      return response;
    },
    onSuccess: (data, variables) => {
      // Clear response mode and text
      setResponseMode((prev) => ({
        ...prev,
        [variables.reviewId]: false,
      }));
      setResponses((prev) => ({
        ...prev,
        [variables.reviewId]: "",
      }));

      // Invalidate and refetch reviews to show the new response
      queryClient.invalidateQueries({ queryKey: ["reviews"] });

      // Refresh comments for this specific review
      if (variables.reviewId) {
        checkOwnerResponse(variables.reviewId);
      }

      toast.success("Response submitted successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to submit response");
      console.error("Submit response error:", error);
    },
  });

  // Submit response function
  const submitResponse = useCallback(
    (reviewId: string) => {
      const responseText = responses[reviewId];
      if (!responseText?.trim()) {
        toast.error("Please enter a response");
        return;
      }

      submitResponseMutation.mutate({ reviewId, responseText });
    },
    [responses, submitResponseMutation],
  );

  return {
    responseState,
    hasOwnerResponse,
    getOwnerResponseInfo: getOwnerResponseInfoForReview,
    toggleResponseMode,
    handleResponseChange,
    submitResponse,
    isSubmitting: submitResponseMutation.isPending,
    checkOwnerResponse,
  };
}
