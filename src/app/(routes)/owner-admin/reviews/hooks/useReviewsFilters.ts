import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import type { ReviewFilters } from "../utils/types";

export function useReviewsFilters() {
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [selectedResponseStatus, setSelectedResponseStatus] = useState<
    string | null
  >(null);

  // Handle URL parameters
  useEffect(() => {
    const productId = searchParams.get("product");
    if (productId) {
      setSelectedProduct(productId);
    }
  }, [searchParams]);

  const filters: ReviewFilters = {
    searchTerm,
    selectedProduct,
    selectedRating,
    selectedResponseStatus,
  };

  const setters = {
    setSearchTerm,
    setSelectedProduct,
    setSelectedRating,
    setSelectedResponseStatus,
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedProduct(null);
    setSelectedRating(null);
    setSelectedResponseStatus(null);
  };

  const hasActiveFilters =
    searchTerm !== "" ||
    selectedProduct !== null ||
    selectedRating !== null ||
    selectedResponseStatus !== null;

  return {
    filters,
    ...setters,
    clearFilters,
    hasActiveFilters,
  };
}
