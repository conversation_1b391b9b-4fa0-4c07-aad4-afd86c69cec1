import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { getUser } from "@/app/util/serverFunctions";
import { getAllProducts, getAllReviews } from "../utils/reviewUtils";
import type {
  ReviewFilters,
  iUser,
  iBusiness,
  iProduct,
  iReview,
} from "../utils/types";

interface UseReviewsDataProps {
  filters: ReviewFilters;
  hasOwnerResponse: (reviewId: string) => boolean;
}

export function useReviewsData({
  filters,
}: Omit<UseReviewsDataProps, "hasOwnerResponse"> & {
  hasOwnerResponse?: (reviewId: string) => boolean;
}) {
  const auth = useAuth();

  // Fetch user data and businesses
  const {
    data: userData,
    isLoading: userLoading,
    isError: userError,
    error: userErrorData,
  } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: async () => await getUser(),
    refetchOnWindowFocus: false,
  }) as any;

  // Extract businesses and products
  const businesses: iBusiness[] = useMemo(
    () => userData?.data?.businesses || [],
    [userData?.data?.businesses],
  );
  const allProducts: iProduct[] = useMemo(
    () => getAllProducts(businesses),
    [businesses],
  );

  // Get all reviews across products or filtered by selected product
  const allReviews: iReview[] = useMemo(() => {
    return getAllReviews(allProducts, filters.selectedProduct);
  }, [allProducts, filters.selectedProduct]);

  // Helper function to get product name by ID
  const getProductName = (productId: string): string => {
    const product = allProducts.find((p: iProduct) => p.id === productId);
    return product ? product.name : "Unknown Product";
  };

  return {
    // Data
    userData,
    user: userData?.data as iUser | undefined,
    businesses,
    allProducts,
    allReviews,
    reviews: allReviews,

    // Loading states
    isLoading: userLoading,
    isError: userError,
    error: userErrorData,

    // Helper functions
    getProductName,
  };
}
