import { useState, useEffect, useMemo } from "react";
import { calculatePagination } from "../utils/reviewUtils";
import type { PaginationState } from "../utils/types";

export function useReviewsPagination(totalItems: number) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Reset to page 1 when total items change (e.g., filters applied)
  useEffect(() => {
    setCurrentPage(1);
  }, [totalItems]);

  const paginationState: PaginationState = useMemo(() => {
    const totalPages = Math.ceil(totalItems / pageSize);
    return {
      currentPage,
      pageSize,
      totalItems,
      totalPages,
    };
  }, [currentPage, pageSize, totalItems]);

  const paginationInfo = useMemo(() => {
    return calculatePagination(totalItems, pageSize, currentPage);
  }, [totalItems, pageSize, currentPage]);

  const goToPage = (page: number) => {
    const { totalPages } = paginationInfo;
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (paginationInfo.hasNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (paginationInfo.hasPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  const changePageSize = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const getDisplayInfo = () => {
    const { startIndex, endIndex } = paginationInfo;
    return {
      start: totalItems > 0 ? startIndex + 1 : 0,
      end: Math.min(endIndex, totalItems),
      total: totalItems,
    };
  };

  return {
    paginationState,
    paginationInfo,
    currentPage,
    pageSize,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    changePageSize,
    getDisplayInfo,
    setCurrentPage,
    setPageSize,
  };
}
