"use client";

import { MessageSquare } from "lucide-react";

interface EmptyStateProps {
  hasFilters: boolean;
  searchTerm?: string;
  selectedRating?: number | null;
  selectedResponseStatus?: string | null;
}

export function EmptyState({
  hasFilters,
  searchTerm,
  selectedRating,
  selectedResponseStatus,
}: EmptyStateProps) {
  const getEmptyMessage = () => {
    if (hasFilters) {
      return "Try adjusting your filters to see more reviews";
    }
    return "Your products haven't received any reviews yet. As customers leave reviews, they'll appear here.";
  };

  const getEmptyTitle = () => {
    if (hasFilters) {
      return "No reviews match your filters";
    }
    return "No reviews found";
  };

  return (
    <div className="flex flex-col items-center justify-center bg-muted rounded-lg p-8 space-y-4">
      <div className="rounded-full bg-background p-3">
        <MessageSquare className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="text-xl font-semibold">{getEmptyTitle()}</h3>
      <p className="text-center text-muted-foreground max-w-md">
        {getEmptyMessage()}
      </p>
      {hasFilters && (
        <div className="text-sm text-muted-foreground mt-2">
          Active filters:
          {searchTerm && (
            <span className="ml-1 px-2 py-1 bg-background rounded-md">
              Search: "{searchTerm}"
            </span>
          )}
          {selectedRating && (
            <span className="ml-1 px-2 py-1 bg-background rounded-md">
              {selectedRating} star{selectedRating !== 1 ? "s" : ""}
            </span>
          )}
          {selectedResponseStatus && (
            <span className="ml-1 px-2 py-1 bg-background rounded-md">
              {selectedResponseStatus === "no_response"
                ? "Needs Response"
                : "Already Responded"}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
