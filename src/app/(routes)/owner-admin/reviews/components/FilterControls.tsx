"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  MessageSquare,
  ChevronDown,
} from "lucide-react";
import type { FilterControlsProps } from "../utils/types";

export function FilterControls({
  searchTerm,
  selectedRating,
  selectedResponseStatus,
  selectedProduct,
  allProducts,
  onSearchChange,
  onRatingChange,
  onResponseStatusChange,
  onProductChange,
  getProductName,
}: FilterControlsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-4">
      {/* Search and filters */}
      <div className="md:col-span-3 space-y-4">
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-grow">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search reviews..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>

          {/* Rating Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Rating
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onRatingChange(null)}>
                All Ratings
              </DropdownMenuItem>
              {[5, 4, 3, 2, 1].map((rating) => (
                <DropdownMenuItem
                  key={rating}
                  onClick={() => onRatingChange(rating)}
                >
                  {rating} {rating === 1 ? "Star" : "Stars"}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Response Status Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                {selectedResponseStatus === "no_response"
                  ? "Needs Response"
                  : selectedResponseStatus === "responded"
                    ? "Already Responded"
                    : "All Reviews"}
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onResponseStatusChange(null)}>
                All Reviews
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onResponseStatusChange("no_response")}
              >
                Needs Response
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onResponseStatusChange("responded")}
              >
                Already Responded
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Product selector */}
      <div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full">
              {selectedProduct
                ? getProductName(selectedProduct)
                : "All Products"}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            <DropdownMenuItem onClick={() => onProductChange(null)}>
              All Products
            </DropdownMenuItem>
            {allProducts.map((product) => (
              <DropdownMenuItem
                key={product.id}
                onClick={() => product.id && onProductChange(product.id)}
              >
                {product.name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
