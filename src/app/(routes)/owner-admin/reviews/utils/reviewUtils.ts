import { iReview, iProduct, iBusiness, iComment } from "@/app/util/Interfaces";
import { ReviewFilters, OwnerResponseInfo } from "./types";

/**
 * Filters reviews based on the provided filter criteria
 */
export function filterReviews(
  reviews: iReview[],
  filters: ReviewFilters,
  hasOwnerResponse: (reviewId: string) => boolean
): iReview[] {
  let filteredReviews = [...reviews];

  // Apply search filter
  if (filters.searchTerm) {
    const term = filters.searchTerm.toLowerCase();
    filteredReviews = filteredReviews.filter(
      (review) =>
        review.title.toLowerCase().includes(term) ||
        review.body.toLowerCase().includes(term)
    );
  }

  // Apply rating filter
  if (filters.selectedRating !== null) {
    filteredReviews = filteredReviews.filter(
      (review) => Math.round(review.rating) === filters.selectedRating
    );
  }

  // Apply response status filter
  if (filters.selectedResponseStatus !== null) {
    filteredReviews = filteredReviews.filter((review) => {
      const hasResponse = hasOwnerResponse(review.id || "");
      if (filters.selectedResponseStatus === "responded") {
        return hasResponse;
      } else if (filters.selectedResponseStatus === "no_response") {
        return !hasResponse;
      }
      return true;
    });
  }

  return filteredReviews;
}

/**
 * Sorts reviews by creation date (newest first)
 */
export function sortReviewsByDate(reviews: iReview[]): iReview[] {
  return reviews.sort(
    (a, b) =>
      new Date(b.createdDate || 0).getTime() -
      new Date(a.createdDate || 0).getTime()
  );
}

/**
 * Gets all products from businesses
 */
export function getAllProducts(businesses: iBusiness[]): iProduct[] {
  return businesses.flatMap((business) => business.products || []);
}

/**
 * Gets all reviews from products or a specific product
 */
export function getAllReviews(
  products: iProduct[],
  selectedProductId?: string | null
): iReview[] {
  if (selectedProductId) {
    const selectedProduct = products.find((p) => p.id === selectedProductId);
    return selectedProduct?.reviews || [];
  }

  return products.flatMap((product) => product.reviews || []);
}

/**
 * Gets product name by ID
 */
export function getProductName(products: iProduct[], productId: string): string {
  const product = products.find((p) => p.id === productId);
  return product ? product.name : "Unknown Product";
}

/**
 * Checks if a review has an owner response
 */
export function createOwnerResponseChecker(
  reviewComments: Record<string, iComment[]>,
  businesses: iBusiness[]
) {
  const allProducts = getAllProducts(businesses);

  return (reviewId: string): boolean => {
    const comments = reviewComments[reviewId] || [];

    return comments.some((comment) => {
      // Find the business owner ID for this product
      const product = allProducts.find((p) => {
        // Find the review first to get its productId
        const allReviews = allProducts.flatMap((p) => p.reviews || []);
        const review = allReviews.find((r) => r.id === reviewId);
        return review && p.id === review.productId;
      });
      const businessOwnerId = product?.business?.ownerId;

      // Check if any comment is from the business owner
      return comment.userId === businessOwnerId;
    });
  };
}

/**
 * Gets owner response information for a review
 */
export function getOwnerResponseInfo(
  reviewId: string,
  reviewComments: Record<string, iComment[]>,
  reviews: iReview[],
  allProducts: iProduct[]
): OwnerResponseInfo | null {
  const comments = reviewComments[reviewId] || [];
  const review = reviews.find((r) => r.id === reviewId);

  if (!review) return null;

  const product = allProducts.find((p) => p.id === review.productId);
  const businessOwnerId = product?.business?.ownerId;

  const ownerComments = comments.filter((comment) => comment.userId === businessOwnerId);

  if (ownerComments.length > 0) {
    // Get the most recent owner comment
    const latestOwnerComment = ownerComments.sort(
      (a, b) => new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime()
    )[0];

    return {
      hasResponse: true,
      responseDate: latestOwnerComment.createdDate,
      responseCount: ownerComments.length,
    };
  }

  return { hasResponse: false, responseDate: null, responseCount: 0 };
}

/**
 * Calculates pagination values
 */
export function calculatePagination(totalItems: number, pageSize: number, currentPage: number) {
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    totalPages,
    startIndex,
    endIndex,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

/**
 * Gets paginated items from an array
 */
export function paginateArray<T>(items: T[], currentPage: number, pageSize: number): T[] {
  const { startIndex, endIndex } = calculatePagination(items.length, pageSize, currentPage);
  return items.slice(startIndex, endIndex);
}
