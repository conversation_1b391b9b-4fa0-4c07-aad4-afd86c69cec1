import {
  iUser,
  iProduct,
  iBusiness,
  iReview,
  iComment,
} from "@/app/util/Interfaces";

export interface ReviewCardProps {
  review: iReview;
  productName: string;
  isResponseMode: boolean;
  responseText: string;
  onToggleResponse: () => void;
  onResponseChange: (text: string) => void;
  onSubmitResponse: () => void;
  isSubmitting?: boolean;
  ownerResponseInfo?: OwnerResponseInfo | null;
}

export interface OwnerResponseInfo {
  hasResponse: boolean;
  responseDate: Date | null;
  responseCount: number;
}

export interface ReviewFilters {
  searchTerm: string;
  selectedProduct: string | null;
  selectedRating: number | null;
  selectedResponseStatus: string | null;
}

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface OwnerResponseState {
  responseMode: Record<string, boolean>;
  responses: Record<string, string>;
  reviewComments: Record<string, iComment[]>;
}

export interface FilterControlsProps {
  searchTerm: string;
  selectedRating: number | null;
  selectedResponseStatus: string | null;
  selectedProduct: string | null;
  allProducts: iProduct[];
  onSearchChange: (term: string) => void;
  onRatingChange: (rating: number | null) => void;
  onResponseStatusChange: (status: string | null) => void;
  onProductChange: (productId: string | null) => void;
  getProductName: (productId: string) => string;
}

export interface PaginationControlsProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

// Re-export commonly used interfaces
export type { iUser, iProduct, iBusiness, iReview, iComment };
