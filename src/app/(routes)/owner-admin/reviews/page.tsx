"use client";

import Link from "next/link";
import { useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import ReviewCard from "@/app/components/ReviewCard";
import {
  paginateArray,
  filterReviews,
  sortReviewsByDate,
} from "./utils/reviewUtils";
import { FilterControls, PaginationControls, EmptyState } from "./components";
import {
  useReviewsFilters,
  useReviewsPagination,
  useOwnerResponse,
  useReviewsData,
} from "./hooks";

export default function ReviewsManagement() {
  // Custom hooks for state management
  const filtersState = useReviewsFilters();
  const { filters } = filtersState;

  // Initial data fetching (without owner response filtering)
  const reviewsData = useReviewsData({
    filters: {
      ...filters,
      selectedResponseStatus: null, // Don't filter by response status initially
    },
    hasOwnerResponse: () => false, // Dummy function for initial load
  });

  const {
    userData,
    user,
    allProducts,
    reviews: allReviews,
    isLoading,
    isError,
    error,
    getProductName,
  } = reviewsData;

  // Owner response functionality
  const ownerResponse = useOwnerResponse({
    userData,
    reviews: allReviews,
    allProducts,
  });

  const {
    responseState,
    hasOwnerResponse,
    getOwnerResponseInfo,
    toggleResponseMode,
    handleResponseChange,
    submitResponse,
    isSubmitting,
  } = ownerResponse;

  // Apply final filtering with proper hasOwnerResponse function
  const finalReviews = useMemo(() => {
    const filtered = filterReviews(allReviews, filters, hasOwnerResponse);
    return sortReviewsByDate(filtered);
  }, [allReviews, filters, hasOwnerResponse]);

  // Pagination
  const pagination = useReviewsPagination(finalReviews.length);
  const { currentPage, pageSize, paginationState, goToPage, changePageSize } =
    pagination;

  // Get paginated reviews
  const paginatedReviews = paginateArray(finalReviews, currentPage, pageSize);

  // Show loading state while data is being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Handle error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
        <AlertTriangle className="h-12 w-12 text-red-500" />
        <h3 className="text-xl font-semibold">Error loading reviews</h3>
        <p className="text-muted-foreground">
          {error?.toString() || "An unknown error occurred"}
        </p>
      </div>
    );
  }

  // Handle case where user data is not available
  if (!userData?.data) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
        <AlertTriangle className="h-12 w-12 text-yellow-500" />
        <h3 className="text-xl font-semibold">No user data available</h3>
        <p className="text-muted-foreground">
          We couldn&apos;t load your user profile
        </p>
      </div>
    );
  }

  const hasActiveFilters =
    filters.searchTerm !== "" ||
    filters.selectedProduct !== null ||
    filters.selectedRating !== null ||
    filters.selectedResponseStatus !== null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Reviews Management
          </h2>
          <p className="text-muted-foreground">
            View and respond to customer reviews
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button asChild variant="outline">
            <Link href="/owner-admin">Dashboard</Link>
          </Button>
          <Button asChild>
            <Link href="/owner-admin/products">Manage Products</Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <FilterControls
        searchTerm={filters.searchTerm}
        selectedRating={filters.selectedRating}
        selectedResponseStatus={filters.selectedResponseStatus}
        selectedProduct={filters.selectedProduct}
        allProducts={allProducts}
        onSearchChange={filtersState.setSearchTerm}
        onRatingChange={filtersState.setSelectedRating}
        onResponseStatusChange={filtersState.setSelectedResponseStatus}
        onProductChange={filtersState.setSelectedProduct}
        getProductName={getProductName}
      />

      {/* Reviews Content */}
      <div>
        {/* Pagination Controls (top) */}
        <PaginationControls
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={finalReviews.length}
          totalPages={paginationState.totalPages}
          onPageChange={goToPage}
          onPageSizeChange={changePageSize}
        />

        {/* Reviews List or Empty State */}
        {finalReviews.length === 0 ? (
          <div className="mt-4">
            <EmptyState
              hasFilters={hasActiveFilters}
              searchTerm={filters.searchTerm}
              selectedRating={filters.selectedRating}
              selectedResponseStatus={filters.selectedResponseStatus}
            />
          </div>
        ) : (
          <div className="mt-4 space-y-6">
            {paginatedReviews.map((review) => {
              // Removed debug console.log statement

              // Find the product for this review
              const product = allProducts.find(
                (p) => p.id === review.productId,
              );

              // Create enriched review with minimal fallbacks since API now provides user data
              const enrichedReview = {
                ...review,
                // Ensure user object has fallback avatar if needed
                user: review.user
                  ? {
                      ...review.user,
                      avatar: review.user.avatar || "/logo.png",
                    }
                  : undefined,
                // Ensure product object exists
                product: product || {
                  id: review.productId,
                  name: getProductName(review.productId),
                  display_image: "/logo.png",
                  description: "",
                  images: [],
                  videos: [],
                  links: [],
                  tags: [],
                  website: [],
                  rating: 0,
                  hasOwner: null,
                  ownerId: null,
                  createdById: "",
                  isDeleted: false,
                  createdDate: new Date(),
                },
                // Ensure required arrays exist
                images: review.images || [],
                videos: review.videos || [],
                links: review.links || [],
                likedBy: review.likedBy || [],
                comments: review.comments || [],
              };

              return (
                <div
                  key={review.id}
                  className="bg-white rounded-lg border border-gray-200 overflow-hidden"
                >
                  <ReviewCard review={enrichedReview} showFullContent={true} />

                  {/* Admin Response Panel */}
                  <div className="p-4 bg-gray-50 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">
                        Owner Response
                      </h4>
                      {getOwnerResponseInfo(review.id || "")?.hasResponse && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Responded
                        </span>
                      )}
                    </div>

                    {responseState.responseMode[review.id || ""] ? (
                      <div className="space-y-3">
                        <textarea
                          placeholder="Write your response to this review..."
                          value={responseState.responses[review.id || ""] || ""}
                          onChange={(e) =>
                            handleResponseChange(
                              review.id || "",
                              e.target.value,
                            )
                          }
                          className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        />
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            onClick={() => toggleResponseMode(review.id || "")}
                            disabled={isSubmitting}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={() => submitResponse(review.id || "")}
                            disabled={
                              !responseState.responses[
                                review.id || ""
                              ]?.trim() || isSubmitting
                            }
                          >
                            {isSubmitting ? "Submitting..." : "Submit Response"}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => toggleResponseMode(review.id || "")}
                        className="w-full"
                      >
                        {getOwnerResponseInfo(review.id || "")?.hasResponse
                          ? "Add Another Response"
                          : "Respond to Review"}
                      </Button>
                    )}

                    {getOwnerResponseInfo(review.id || "")?.hasResponse &&
                      getOwnerResponseInfo(review.id || "")?.responseDate && (
                        <p className="text-xs text-green-600 mt-2">
                          You responded on{" "}
                          {new Date(
                            getOwnerResponseInfo(
                              review.id || "",
                            )!.responseDate!,
                          ).toLocaleDateString()}
                          {getOwnerResponseInfo(review.id || "")!
                            .responseCount > 1 &&
                            ` (${getOwnerResponseInfo(review.id || "")!.responseCount} responses)`}
                        </p>
                      )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination Controls (bottom) */}
        {finalReviews.length > 0 && paginationState.totalPages > 1 && (
          <div className="mt-8">
            <PaginationControls
              currentPage={currentPage}
              pageSize={pageSize}
              totalItems={finalReviews.length}
              totalPages={paginationState.totalPages}
              onPageChange={goToPage}
              onPageSizeChange={changePageSize}
            />
          </div>
        )}
      </div>
    </div>
  );
}
