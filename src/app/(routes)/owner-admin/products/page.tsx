"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { useSearchParams } from "next/navigation";
import { getUser } from "@/app/util/serverFunctions";
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription
} from "@/components/ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    ChevronDown,
    Plus,
    Search,
    Edit2,
    Trash2,
    Star,
    <PERSON>S<PERSON>re,
    <PERSON>,
    Filter,
    AlertTriangle
} from "lucide-react";
import { iUser, iBusiness, iProduct } from "@/app/util/Interfaces";
import Link from "next/link";

export default function ProductsManagement() {
    const auth = useAuth();
    const searchParams = useSearchParams();
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedBusiness, setSelectedBusiness] = useState<string | null>(null);
    const [viewMode, setViewMode] = useState<"grid" | "table">("table");

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    // Handle URL parameters
    useEffect(() => {
        const businessId = searchParams.get("business");
        if (businessId) {
            setSelectedBusiness(businessId);
        }
    }, [searchParams]);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading products</h3>
                <p className="text-muted-foreground">{error?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    if (!data?.data) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No user data available</h3>
                <p className="text-muted-foreground">We couldn&apos;t load your user profile</p>
            </div>
        );
    }

    const user: iUser = data.data as iUser;
    const businesses = user.businesses || [];

    // Filter products based on selected business
    let filteredProducts: iProduct[] = [];
    if (selectedBusiness) {
        const business = businesses.find(b => b.id === selectedBusiness);
        filteredProducts = business?.products || [];
    } else {
        filteredProducts = businesses.flatMap(business => business.products || []);
    }

    // Apply search filter
    if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(term) ||
            product.description.toLowerCase().includes(term)
        );
    }

    // Sort products by view count (most popular first)
    filteredProducts.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));

    return (
        <div className="space-y-6">
            <div className="flex flex-col lg:flex-row justify-between gap-4">
                <div>
                    <h2 className="text-3xl font-bold tracking-tight">Products Management</h2>
                    <p className="text-muted-foreground">
                        Manage and track all your business products
                    </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                    <Button asChild>
                        <Link href="/submit">
                            <Plus className="mr-2 h-4 w-4" />
                            Add New Product
                        </Link>
                    </Button>
                </div>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
                {/* Search and filter section */}
                <div className="w-full md:w-2/3 lg:w-3/4 space-y-4">
                    <div className="flex flex-col sm:flex-row gap-2">
                        <div className="relative flex-grow">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="Search products..."
                                className="pl-8"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" className="flex-shrink-0">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Filter
                                    <ChevronDown className="ml-2 h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-[200px]">
                                <DropdownMenuItem onClick={() => setSelectedBusiness(null)}>
                                    All Businesses
                                </DropdownMenuItem>
                                {businesses.map((business) => (
                                    <DropdownMenuItem
                                        key={business.id}
                                        onClick={() => setSelectedBusiness(business.id)}
                                    >
                                        {business.ownerName || "Business " + business.id.substring(0, 8)}
                                    </DropdownMenuItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <div className="flex gap-1">
                            <Button
                                variant={viewMode === "table" ? "default" : "outline"}
                                size="icon"
                                onClick={() => setViewMode("table")}
                                className="h-10 w-10"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                                    <line x1="3" y1="9" x2="21" y2="9" />
                                    <line x1="3" y1="15" x2="21" y2="15" />
                                    <line x1="9" y1="3" x2="9" y2="21" />
                                    <line x1="15" y1="3" x2="15" y2="21" />
                                </svg>
                            </Button>
                            <Button
                                variant={viewMode === "grid" ? "default" : "outline"}
                                size="icon"
                                onClick={() => setViewMode("grid")}
                                className="h-10 w-10"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <rect x="3" y="3" width="7" height="7" />
                                    <rect x="14" y="3" width="7" height="7" />
                                    <rect x="14" y="14" width="7" height="7" />
                                    <rect x="3" y="14" width="7" height="7" />
                                </svg>
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Business selector for mobile */}
                <div className="w-full md:w-1/3 lg:w-1/4">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="w-full">
                                {selectedBusiness
                                    ? businesses.find(b => b.id === selectedBusiness)?.ownerName || "Selected Business"
                                    : "All Businesses"}
                                <ChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                            <DropdownMenuItem onClick={() => setSelectedBusiness(null)}>
                                All Businesses
                            </DropdownMenuItem>
                            {businesses.map((business) => (
                                <DropdownMenuItem
                                    key={business.id}
                                    onClick={() => setSelectedBusiness(business.id)}
                                >
                                    {business.ownerName || "Business " + business.id.substring(0, 8)}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Products list */}
            <div>
                {filteredProducts.length === 0 ? (
                    <div className="flex flex-col items-center justify-center bg-muted rounded-lg p-8 space-y-4">
                        <div className="rounded-full bg-background p-3">
                            <AlertTriangle className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-xl font-semibold">No products found</h3>
                        <p className="text-center text-muted-foreground max-w-md">
                            {searchTerm
                                ? "Try a different search term or clear your filters"
                                : "You don't have any products yet. Add your first product to get started."}
                        </p>
                        <Button asChild>
                            <Link href="/submit">Add New Product</Link>
                        </Button>
                    </div>
                ) : (
                    <>
                        {viewMode === "table" ? (
                            <>
                                {/* Desktop Table View */}
                                <div className="hidden md:block rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Name</TableHead>
                                                <TableHead>Business</TableHead>
                                                <TableHead>Rating</TableHead>
                                                <TableHead>Views</TableHead>
                                                <TableHead>Reviews</TableHead>
                                                <TableHead className="text-right">Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {filteredProducts.map((product) => {
                                                const business = businesses.find(b => b.id === product.businessId);
                                                return (
                                                    <TableRow key={product.id}>
                                                        <TableCell className="font-medium">
                                                            <Link
                                                                href={`/product/${product.id}`}
                                                                className="hover:underline"
                                                            >
                                                                {product.name}
                                                            </Link>
                                                        </TableCell>
                                                        <TableCell>
                                                            {business?.ownerName || "Unknown"}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center">
                                                                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                                                                {product.rating.toFixed(1)}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center">
                                                                <Eye className="h-4 w-4 text-muted-foreground mr-1" />
                                                                {product.viewCount || 0}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center">
                                                                <MessageSquare className="h-4 w-4 text-muted-foreground mr-1" />
                                                                {product._count?.reviews || 0}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            <div className="flex justify-end gap-2">
                                                                <Button asChild size="sm" variant="outline">
                                                                    <Link href={`/product/${product.id}`}>
                                                                        View
                                                                    </Link>
                                                                </Button>
                                                                <Link href={`/editproduct?pid=${product.id}`}>
                                                                    <Button size="icon" variant="ghost" className="h-8 w-8">
                                                                        <Edit2 className="h-4 w-4" />
                                                                    </Button>
                                                                </Link>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            })}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Mobile Card View */}
                                <div className="md:hidden space-y-4">
                                    {filteredProducts.map((product) => {
                                        const business = businesses.find(b => b.id === product.businessId);
                                        return (
                                            <Card key={product.id} className="cursor-pointer hover:shadow-md transition-shadow">
                                                <CardContent className="p-4">
                                                    <div className="flex items-start justify-between mb-3">
                                                        <div className="flex-1 min-w-0">
                                                            <h3 className="font-semibold text-lg leading-tight">
                                                                <Link
                                                                    href={`/product/${product.id}`}
                                                                    className="hover:underline"
                                                                >
                                                                    {product.name}
                                                                </Link>
                                                            </h3>
                                                            <p className="text-sm text-muted-foreground">
                                                                {business?.ownerName || "Unknown business"}
                                                            </p>
                                                        </div>
                                                        <DropdownMenu>
                                                            <DropdownMenuTrigger asChild>
                                                                <Button variant="ghost" size="icon" className="h-8 w-8 flex-shrink-0">
                                                                    <ChevronDown className="h-4 w-4" />
                                                                </Button>
                                                            </DropdownMenuTrigger>
                                                            <DropdownMenuContent align="end">
                                                                <DropdownMenuItem asChild>
                                                                    <Link href={`/product/${product.id}`}>
                                                                        View Product
                                                                    </Link>
                                                                </DropdownMenuItem>
                                                                <DropdownMenuItem asChild>
                                                                    <Link href={`/editproduct?pid=${product.id}`}>
                                                                        Edit Details
                                                                    </Link>
                                                                </DropdownMenuItem>
                                                                <DropdownMenuItem asChild>
                                                                    <Link href={`/owner-admin/promotions?product=${product.id}`}>
                                                                        Manage Promotions
                                                                    </Link>
                                                                </DropdownMenuItem>
                                                                <DropdownMenuItem asChild>
                                                                    <Link href={`/owner-admin/reviews?product=${product.id}`}>
                                                                        View Reviews
                                                                    </Link>
                                                                </DropdownMenuItem>
                                                            </DropdownMenuContent>
                                                        </DropdownMenu>
                                                    </div>
                                                    
                                                    {/* Stats */}
                                                    <div className="grid grid-cols-3 gap-3 mb-4">
                                                        <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
                                                            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                                                            <div>
                                                                <div className="text-sm font-medium">{product.rating.toFixed(1)}</div>
                                                                <div className="text-xs text-muted-foreground">Rating</div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
                                                            <Eye className="h-4 w-4 text-muted-foreground" />
                                                            <div>
                                                                <div className="text-sm font-medium">{product.viewCount || 0}</div>
                                                                <div className="text-xs text-muted-foreground">Views</div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
                                                            <MessageSquare className="h-4 w-4 text-muted-foreground" />
                                                            <div>
                                                                <div className="text-sm font-medium">{product._count?.reviews || 0}</div>
                                                                <div className="text-xs text-muted-foreground">Reviews</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Actions */}
                                                    <div className="flex gap-2">
                                                        <Button asChild variant="outline" size="sm" className="flex-1">
                                                            <Link href={`/product/${product.id}`}>
                                                                View Product
                                                            </Link>
                                                        </Button>
                                                        <Button asChild size="sm" className="flex-1">
                                                            <Link href={`/editproduct?pid=${product.id}`}>
                                                                Edit
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </div>
                            </>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {filteredProducts.map((product) => {
                                    const business = businesses.find(b => b.id === product.businessId);
                                    return (
                                        <Card key={product.id}>
                                            <CardHeader className="pb-2">
                                                <div className="flex justify-between items-start">
                                                    <div>
                                                        <CardTitle className="text-lg font-semibold">
                                                            <Link
                                                                href={`/product/${product.id}`}
                                                                className="hover:underline"
                                                            >
                                                                {product.name}
                                                            </Link>
                                                        </CardTitle>
                                                        <CardDescription>
                                                            {business?.ownerName || "Unknown business"}
                                                        </CardDescription>
                                                    </div>
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="icon" className="h-8 w-8">
                                                                <ChevronDown className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem asChild>
                                                                <Link href={`/product/${product.id}`}>
                                                                    View Product
                                                                </Link>
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem asChild>
                                                                <Link href={`/editproduct?pid=${product.id}`}>
                                                                    Edit Details
                                                                </Link>
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem asChild>
                                                                <Link href={`/owner-admin/promotions?product=${product.id}`}>
                                                                    Manage Promotions
                                                                </Link>
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem asChild>
                                                                <Link href={`/owner-admin/reviews?product=${product.id}`}>
                                                                    View Reviews
                                                                </Link>
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="grid grid-cols-3 gap-2 mb-4">
                                                    <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                                                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mb-1" />
                                                        <span className="text-sm font-medium">{product.rating.toFixed(1)}</span>
                                                        <span className="text-xs text-muted-foreground">Rating</span>
                                                    </div>
                                                    <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                                                        <Eye className="h-4 w-4 text-muted-foreground mb-1" />
                                                        <span className="text-sm font-medium">{product.viewCount || 0}</span>
                                                        <span className="text-xs text-muted-foreground">Views</span>
                                                    </div>
                                                    <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                                                        <MessageSquare className="h-4 w-4 text-muted-foreground mb-1" />
                                                        <span className="text-sm font-medium">{product._count?.reviews || 0}</span>
                                                        <span className="text-xs text-muted-foreground">Reviews</span>
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button asChild variant="outline" size="sm" className="flex-1">
                                                        <Link href={`/product/${product.id}`}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    <Button asChild size="sm" className="flex-1">
                                                        <Link href={`/product/${product.id}`}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
}