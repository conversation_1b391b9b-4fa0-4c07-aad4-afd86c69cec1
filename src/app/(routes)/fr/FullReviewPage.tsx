'use client'
import React from 'react'
import ExpandedReview from '@/app/components/ExpandedReview'
import GrandProductCard from '@/app/components/GrandProductCard'
import { iProduct } from '@/app/util/Interfaces'

interface FullReviewPageProps {
    searchParams: {
        id: string
        productid: string
        cid?: string
    }
    productData: iProduct | null
}

export default function FullReviewPage({ searchParams, productData }: FullReviewPageProps) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-myTheme-lightbg via-white to-myTheme-lightbg/50">
            <div className="max-w-5xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12">
                <div className="mb-6 sm:mb-8">
                    <a
                        href="/"
                        className="group inline-flex items-center text-sm font-medium text-gray-600 hover:text-myTheme-primary transition-all duration-300"
                    >
                        <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        Back to Home
                    </a>
                </div>

                <div className="space-y-8 sm:space-y-10 lg:space-y-12">
                    <GrandProductCard productId={searchParams.productid} productData={productData} />
                    <div className="ml-1 sm:ml-2 lg:ml-6 pl-2 sm:pl-3 lg:pl-4 border-l-2 sm:border-l-4 border-myTheme-primary/40">
                        <ExpandedReview
                            reviewId={searchParams.id}
                            productId={searchParams.productid}
                            cId={searchParams.cid || ""}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}