import React from 'react'
import { Metadata } from 'next'
import { getReview, getProduct } from '@/app/util/serverFunctions'
import FullReviewPage from './FullReviewPage'
import { stripSpecificHtmlTags } from '@/app/util/helpers'
import { notFound } from 'next/navigation'
import { iProduct } from '@/app/util/Interfaces'

export async function generateMetadata({ searchParams }: { searchParams: { id: string, productid: string } }): Promise<Metadata> {
  const reviewId = searchParams.id
  const productId = searchParams.productid
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy'

  try {
    // Fetch review and product data
    const review = await getReview(reviewId)
    const product = await getProduct(productId)

    if (!review.success || !product.success || !review.data || !product.data) {
      notFound();
    }

    // Clean and prepare the review body
    const cleanReviewBody = review.data.body.replace(/<[^>]*>/g, '')
    const reviewTitle = (review.data.title || 'Review').substring(0, 50)
    const productName = (product.data.name || 'Product').substring(0, 30)
    const reviewRating = review.data.rating || 0

    // Create canonical URL
    const canonicalUrl = `${baseUrl}/fr?id=${encodeURIComponent(reviewId)}&productid=${encodeURIComponent(productId)}`

    // Ensure we have a valid image URL
    let imageUrl = product.data.display_image
    if (!imageUrl || !imageUrl.startsWith('http')) {
      imageUrl = `${baseUrl}/api/og?title=${encodeURIComponent(reviewTitle)}&description=${encodeURIComponent(cleanReviewBody.substring(0, 100))}&forceGenerate=true`
    }

    // Ensure the image URL is absolute
    if (!imageUrl.startsWith('http')) {
      imageUrl = `${baseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`
    }

    // Create page title and description with length limits
    const pageTitle = `${reviewTitle} - Review of ${productName}`.substring(0, 70)
    const pageDescription = `${cleanReviewBody.substring(0, 130)}... | Rating: ${reviewRating}/5`.substring(0, 180)

    return {
      title: pageTitle,
      description: pageDescription,
      metadataBase: new URL(baseUrl),
      openGraph: {
        title: `${productName} Reviews | Review It`.substring(0, 70),
        description: `Check out this review of ${productName} on Review It. Rating: ${reviewRating}/5`.substring(0, 180),
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${productName} review image`,
            type: 'image/jpeg'
          }
        ],
        type: 'article',
        siteName: 'Review It',
        locale: 'en_US'
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description: pageDescription,
        images: [imageUrl],
        site: '@reviewitgy',
        creator: '@reviewitgy'
      },
      alternates: {
        canonical: canonicalUrl,
      },
    }
  } catch (error) {
    return {
      title: 'Review It - Review Not Found',
      description: 'The requested review could not be found.',
    }
  }
}

export default async function Page({ searchParams }: { searchParams: { id: string, productid: string, cid?: string } }) {
  const productResponse = await getProduct(searchParams.productid)
  const productData: iProduct | null = productResponse.success && productResponse.data ? productResponse.data : null

  return <FullReviewPage searchParams={searchParams} productData={productData} />
}
