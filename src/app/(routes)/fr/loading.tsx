"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  const skeletonItems = Array(5).fill(null);

  return (
    <div className="flex flex-col w-full min-h-screen p-4 space-y-6">
      {/* Header section */}
      <div className="flex flex-col space-y-4 w-full max-w-4xl mx-auto">
        <div className="flex justify-between items-center w-full">
          <Skeleton className="h-8 w-48" /> {/* Title */}
          <Skeleton className="h-10 w-32" /> {/* New FR button */}
        </div>
        <Skeleton className="h-12 w-full rounded-lg" /> {/* Search bar */}
      </div>

      {/* Feature requests list */}
      <div className="w-full max-w-4xl mx-auto space-y-4">
        {skeletonItems.map((_, index) => (
          <div key={index} className="bg-white/80 rounded-xl p-6 border border-gray-100 space-y-4">
            <div className="flex justify-between items-start">
              <div className="space-y-2 flex-1">
                <Skeleton className="h-6 w-3/4" /> {/* Title */}
                <Skeleton className="h-4 w-1/4" /> {/* Status */}
              </div>
              <Skeleton className="h-8 w-20" /> {/* Vote count */}
            </div>
            <Skeleton className="h-16 w-full" /> {/* Description */}
            <div className="flex gap-3">
              <Skeleton className="h-6 w-24" /> {/* Tag */}
              <Skeleton className="h-6 w-24" /> {/* Tag */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

