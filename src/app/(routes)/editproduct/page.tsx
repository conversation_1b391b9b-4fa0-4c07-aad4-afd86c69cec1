"use client";
import EditProductForm from "@/app/components/EditProductForm";
import { iProduct } from "@/app/util/Interfaces";
import { useSearchParams, useRouter } from "next/navigation";
import { getProduct } from "@/app/util/serverFunctions";
import { useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import { isProductOwner } from "@/app/util/clientFunctions";

const EditPage = () => {
  const user = useUser();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [product, setProduct] = useState<iProduct | null>(null);
  const productId = searchParams.get("pid");

  useEffect(() => {
    const fetchProduct = async () => {
      if (productId) {
        const response = await getProduct(productId);
        if (!response.success || !response.data) {
          return {
            title: 'Product Not Found | Review It',
            description: 'The requested product could not be found.',
          };
        }
        const product = response.data;

        // Check if user owns the product
        const isOwner = isProductOwner(user.user?.id, product);

        if (!isOwner) {
          router.push("/"); // Redirect unauthorized users using client-side navigation
          return;
        }

        setProduct(product);
      }
    };
    fetchProduct();
  }, [productId, user.user?.id, router]);

  return (
    <div className="flex flex-col w-full h-full p-2 sm:pt-8 bg-myTheme-lightbg  justify-start items-center">
      {product && <EditProductForm initialProduct={product} />}
    </div>
  );
};

export default EditPage;
