"use client";

import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { iProduct, iReview } from "@/app/util/Interfaces";
import Image from "next/legacy/image";
import SearchBoxAndListener from "@/app/components/SearchBoxAndListener";
import Link from "next/link";
import PaginatedProductCarousel from "../../components/PaginatedProductCarousel";
import Quote from "@/app/components/Quote";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "@/app/util/serverFunctions";
import { TopReviewsSkeleton } from "@/app/components/skeletons";

const WriteReviewPage = () => {
  const [products] = useAtom(allProductsStore);
  const [filteredProducts, setFilteredProducts] = useState<iProduct[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();

  // Fetch recent reviews
  const { data: recentReviews } = useQuery({
    queryKey: ["recentReviews"],
    queryFn: async () => {
      const response = await getLatestReviews("latest");
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch recent reviews");
      }
      return response.data || [];
    },
  });

  useEffect(() => {
    if (products) {
      setFilteredProducts(products);
    }
  }, [products]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (products) {
      const results = products.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        (product.tags && product.tags.some(tag =>
          tag.toLowerCase().includes(query.toLowerCase())
        ))
      );
      setFilteredProducts(results);
    }
  };

  const handleSelectProduct = (productId: string) => {
    router.push(`/cr?id=${productId}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section - Streamlined */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Write a Review
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Share your experience and help others make the right choice
        </p>
        
        {/* Recent Reviews Section - Integrated */}
        {recentReviews === undefined ? (
          <div className="mt-10 hidden md:block">
            <TopReviewsSkeleton />
          </div>
        ) : recentReviews && recentReviews.length > 0 && (
          <div className="mt-10 hidden md:block max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentReviews.slice(0, 3).map((review: iReview) => (
               <Quote
                 key={review.id}
                 userName={review.user?.userName || "Anonymous"}
                 userImage={review.user?.avatar || undefined}
                 quoteText={review.body}
               />
             ))}
            </div>
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
              <p className="text-gray-700">
                Your turn to share! Join others in helping people make informed decisions.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Search Section - Simplified */}
      <div className="mb-10">
        <SearchBoxAndListener onSearch={handleSearch} />
      </div>

      {/* Products Section */}
      {filteredProducts && filteredProducts.length > 0 ? (
        <PaginatedProductCarousel
          allProducts={filteredProducts}
          miniCardOptions={{
            size: "md",
            showWriteReview: true,
          }}
          itemsPerPage={3}
          title={searchQuery ? `Results for "${searchQuery}"` : "Browse Products"}
        />
      ) : (
        searchQuery && (
          <div className="text-center py-8">
            <p className="text-gray-600">No products found matching "{searchQuery}"</p>
          </div>
        )
      )}
      
      {/* Call to Action - Simplified */}
      <div className="mt-16 bg-gradient-to-r from-gray-50 to-blue-50 py-10 rounded-xl border border-gray-200">
        <div className="text-center max-w-md mx-auto">
          <p className="text-gray-600 mb-4">
            Can&apos;t find a company or product? Add it and be the first to review!
          </p>
          <Link 
            href="/submit" 
            className="inline-block bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium px-5 py-2.5 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors duration-200"
          >
            Add New Product
          </Link>
        </div>
      </div>
    </div>
  );
};

export default WriteReviewPage;
