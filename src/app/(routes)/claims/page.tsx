import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import ClaimsHistoryComponent from "@/app/components/ClaimsHistoryComponent";

export const metadata = {
    title: "My Claims | Review It",
    description: "View and manage your product claims",
};

export default async function ClaimsPage() {
    const { userId } = await auth();

    if (!userId) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center px-4">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Authentication Required</h1>
                    <p className="text-gray-600">Please sign in to view your claims.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 py-10 px-4 sm:px-6 lg:px-8">
            <div className="max-w-5xl mx-auto">
                <div className="mb-8 text-center sm:text-left">
                    <h1 className="text-3xl font-bold text-myTheme-primary">My Claims</h1>
                    <p className="text-gray-600 mt-2">View and track all your product claims</p>
                </div>
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <ClaimsHistoryComponent />
                </div>
            </div>
        </div>
    );
}