import React from 'react';

export const metadata = {
    title: 'Terms of Service | Review It',
    description: 'Terms of Service for Review It - Understanding our terms and conditions.',
};

export default function TermsOfService() {
    return (
        <div className="container mx-auto px-4 py-8 max-w-4xl">
            <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>

            <div className="prose prose-lg">
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Agreement to Terms</h2>
                    <p>
                        By accessing or using Review It, you agree to be bound by these Terms of Service and all
                        applicable laws and regulations. If you do not agree with any of these terms, you are
                        prohibited from using or accessing this site.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Use License</h2>
                    <p>
                        Permission is granted to temporarily access the materials on Review It&apos;s website for
                        personal, non-commercial use. This is the grant of a license, not a transfer of title, and
                        under this license you may not:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Modify or copy the materials</li>
                        <li>Use the materials for any commercial purpose</li>
                        <li>Attempt to reverse engineer any software contained on the site</li>
                        <li>Remove any copyright or other proprietary notations</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">User Responsibilities</h2>
                    <p>
                        As a user of Review It, you agree to:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Provide accurate and truthful information</li>
                        <li>Maintain the security of your account</li>
                        <li>Not engage in any fraudulent or harmful activities</li>
                        <li>Respect the rights of other users</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Disclaimer</h2>
                    <p>
                        The materials on Review It&apos;s website are provided on an &apos;as is&apos; basis. Review It makes no
                        warranties, expressed or implied, and hereby disclaims and negates all other warranties
                        including, without limitation, implied warranties or conditions of merchantability, fitness
                        for a particular purpose, or non-infringement of intellectual property or other violation
                        of rights.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Contact Information</h2>
                    <p>
                        If you have any questions about these Terms of Service, please contact us at:
                        <EMAIL>
                    </p>
                </section>
            </div>
        </div>
    );
} 