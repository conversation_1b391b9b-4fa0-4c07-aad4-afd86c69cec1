"use client";

import {
  CheckCircleIcon,
  TagIcon,
  ClockIcon,
  PhoneIcon,
  GlobeIcon,
  MailIcon,
  ArrowLeftIcon,
  ShareIcon,
  ImageIcon,
} from "lucide-react";
import {
  Card,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import Image from "next/image";
import { useSearchParams, useRouter } from "next/navigation";
import { iProduct } from "@/app/util/Interfaces";
import { useCallback, useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useShare } from '@/app/hooks/useShare';
import OpeningHours from '@/app/components/OpeningHours';

export default function ProductSuccess() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [product, setProduct] = useState<iProduct | null>(null);
  const { share, isSharing } = useShare();

  const checkImageAccess = (url: string): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
      const img = document.createElement('img');
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  const parseAndSetProduct = useCallback(() => {
    const productParam = searchParams.get("product");
    if (productParam) {
      try {
        const data = JSON.parse(decodeURIComponent(productParam));
        setProduct(data);
      } catch (error) {
        console.error("Error parsing product data:", error);
        setProduct(null);
      }
    } else {
      setProduct(null);
    }
  }, [searchParams]);

  useEffect(() => {
    parseAndSetProduct();
  }, [parseAndSetProduct]);

  if (!product) {
    return (
      <div className="container mx-auto p-6 min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md text-center">
          <CardContent className="pt-6">
            <p className="text-gray-600">No product data available</p>
            <Link
              href="/mybusinesses"
              className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800"
            >
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Return to Dashboard
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleShare = async () => {
    await share({
      title: product.name,
      text: product.description,
      url: `${window.location.origin}/reviews?id=${product.id}`,
      imageUrl: product.display_image
    });
  };

  return (
    <div className="container mx-auto p-6 bg-gray-50 min-h-screen flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-3xl"
      >
        <Card className="shadow-lg">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircleIcon className="w-16 h-16 mx-auto text-green-500" />
            </motion.div>
            <CardTitle className="text-3xl font-bold text-green-700">
              Success!
            </CardTitle>
            <p className="text-xl text-gray-600">
              Your product has been successfully {product.id ? "updated" : "added"}.
            </p>
          </CardHeader>
          <CardContent>
            <div className="bg-white p-6 rounded-lg shadow-sm space-y-6">
              <div className="flex justify-between items-start gap-4">
                <div className="flex-grow">
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">
                    {product.name}
                  </h2>
                  <p className="text-gray-600">{product.description}</p>
                </div>
                {product.display_image && (
                  <div className="relative shrink-0 w-32 h-32">
                    <Image
                      src={product.display_image}
                      alt={product.name}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover rounded-lg shadow-md hover:scale-105 transition-transform duration-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTJlOGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzY0NzQ4YiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
                        target.onerror = null;
                      }}
                    />
                    <Badge className="absolute -top-2 -right-2 bg-blue-500">
                      <ImageIcon className="w-4 h-4" />
                    </Badge>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2">
                {product.tags?.map((tag: string, index: number) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="bg-blue-50 text-blue-700 border border-blue-200"
                  >
                    <TagIcon className="w-3 h-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {product.openingHrs && product.closingHrs && product.openingDays && product.openingDays.length > 0 && (
                  <div className="flex items-center text-gray-700">
                    <ClockIcon className="w-4 h-4 mr-2 text-gray-500" />
                    <OpeningHours product={product} />
                  </div>
                )}
                {product.telephone && (
                  <div className="flex items-center text-gray-700">
                    <PhoneIcon className="w-4 h-4 mr-2 text-gray-500" />
                    <a
                      href={`tel:${product.telephone}`}
                      className="hover:text-blue-600"
                    >
                      {product.telephone}
                    </a>
                  </div>
                )}
                {product.website && product.website.length > 0 && (
                  <div className="flex items-center text-gray-700">
                    <GlobeIcon className="w-4 h-4 mr-2 text-gray-500" />
                    <a
                      href={product.website[0]}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-blue-600 truncate"
                    >
                      {product.website[0]}
                    </a>
                  </div>
                )}
                {product.email && (
                  <div className="flex items-center text-gray-700">
                    <MailIcon className="w-4 h-4 mr-2 text-gray-500" />
                    <a
                      href={`mailto:${product.email}`}
                      className="hover:text-blue-600"
                    >
                      {product.email}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-wrap justify-center gap-3 p-6">
            <Link
              href={`/reviews?id=${product.id}`}
              className="bg-blue-600 text-white hover:bg-blue-700 py-2 px-6 rounded-lg transition-colors duration-300 flex items-center"
            >
              View Product
            </Link>
            <Link
              href={`/cr?id=${product.id}`}
              className="bg-green-600 text-white hover:bg-green-700 py-2 px-6 rounded-lg transition-colors duration-300 flex items-center"
            >
              Review Now
            </Link>
            <Link
              href={`/editproduct?pid=${product.id}`}
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 py-2 px-6 rounded-lg transition-colors duration-300 flex items-center"
            >
              Edit Details
            </Link>
            <button
              onClick={handleShare}
              disabled={isSharing}
              className="bg-green-50 text-green-700 hover:bg-green-100 py-2 px-6 rounded-lg transition-colors duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ShareIcon className={`w-4 h-4 mr-2 ${isSharing ? "animate-pulse" : ""}`} />
              {isSharing ? "Sharing..." : "Share"}
            </button>
            <Link
              href="/mybusinesses"
              className="bg-gray-50 text-gray-600 hover:bg-gray-100 py-2 px-6 rounded-lg transition-colors duration-300 flex items-center"
            >
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Link>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
