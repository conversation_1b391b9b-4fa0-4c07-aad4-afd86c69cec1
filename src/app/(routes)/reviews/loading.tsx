"use client";

import ReviewBoxSkeleton from "@/app/components/skeletons/ReviewBoxSkeleton";
import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  const skeletonItems = Array(6).fill(null);

  return (
    <div className="flex flex-col w-full min-h-screen p-4 space-y-6">
      {/* Header section */}
      <div className="flex flex-col items-start space-y-4 w-full max-w-7xl mx-auto">
        <Skeleton className="h-8 w-48" /> {/* Title */}
        <div className="flex gap-4">
          <Skeleton className="h-10 w-28 rounded-md" /> {/* Filter */}
          <Skeleton className="h-10 w-32 rounded-md" /> {/* Filter */}
        </div>
      </div>

      {/* Reviews grid */}
      <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {skeletonItems.map((_, index) => (
          <ReviewBoxSkeleton key={index} />
        ))}
      </div>
    </div>
  );
}
