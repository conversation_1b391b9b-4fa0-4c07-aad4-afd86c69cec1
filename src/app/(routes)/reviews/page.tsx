'use client'
import React from 'react'
import Reviews from '@/app/components/Reviews'
import { useSearchParams } from 'next/navigation'

export default function ReviewsPage() {
    const searchParams = useSearchParams()
    const productId = searchParams.get('id')
    return (
      <div className="min-h-screen bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
        <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
          <div className="mb-8">
            <a
              href="/"
              className="group inline-flex items-center text-sm font-medium text-gray-600 hover:text-myTheme-primary transition-all duration-300"
            >
              <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Home
            </a>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            <div className="px-6 py-10 sm:px-8 bg-gradient-to-r from-white/50 to-blue-50/30">
              <h1 className="text-4xl font-bold text-gray-900 mb-3">Product Reviews</h1>
              <p className="text-gray-600 text-lg">See what people are saying about this product</p>
            </div>
            <div className="px-6 pb-8 sm:px-8">
              <Reviews productId={productId || ''} />
            </div>
          </div>
        </div>
      </div>
    )
}
