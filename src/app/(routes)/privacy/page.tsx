import React from 'react';

export const metadata = {
    title: 'Privacy Policy | Review It',
    description: 'Privacy Policy for Review It - Learn about how we collect, use, and protect your data.',
};

export default function PrivacyPolicy() {
    return (
        <div className="container mx-auto px-4 py-8 max-w-4xl">
            <h1 className="text-3xl font-bold mb-8">Privacy Policy</h1>

            <div className="prose prose-lg">
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Introduction</h2>
                    <p>
                        At Review It, we take your privacy seriously. This Privacy Policy explains how we collect,
                        use, disclose, and safeguard your information when you use our service.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Information We Collect</h2>
                    <p>
                        We collect information that you provide directly to us when you:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Create an account</li>
                        <li>Submit reviews</li>
                        <li>Contact our support team</li>
                        <li>Subscribe to our newsletters</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">How We Use Your Information</h2>
                    <p>
                        We use the information we collect to:
                    </p>
                    <ul className="list-disc pl-6 mt-2">
                        <li>Provide and maintain our service</li>
                        <li>Improve user experience</li>
                        <li>Send you updates and marketing communications</li>
                        <li>Respond to your inquiries</li>
                        <li>Detect and prevent fraud</li>
                    </ul>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Data Security</h2>
                    <p>
                        We implement appropriate technical and organizational security measures to protect your
                        personal information. However, no method of transmission over the Internet is 100% secure.
                    </p>
                </section>

                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
                    <p>
                        If you have any questions about this Privacy Policy, please contact us at:
                        <EMAIL>
                    </p>
                </section>
            </div>
        </div>
    );
} 