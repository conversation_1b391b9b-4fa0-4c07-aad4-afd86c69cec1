"use client";

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "@/app/util/serverFunctions";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import BusinessPageHero from "@/app/components/BusinessPageHero";
import BusinessFeatures from "@/app/components/BusinessFeatures";
import BusinessBenefits from "@/app/components/BusinessBenefits";
import BusinessTestimonials from "@/app/components/BusinessTestimonials";
import BusinessCTA from "@/app/components/BusinessCTA";

// Note: metadata should be in a server component (layout.tsx) since this is a client component

export default function BusinessPage() {
  const [allProducts, setAllProducts] = useAtom(allProductsStore);

  // Background fetch with atom as initial data
  const { data } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - consider data fresh
    initialData:
      allProducts?.length > 0
        ? { success: true, data: allProducts }
        : undefined,
  }) as any;

  // Update the atom when fresh data arrives
  React.useEffect(() => {
    if (data && data.success && data.data && data.data !== allProducts) {
      setAllProducts(data.data);
    }
  }, [data, allProducts, setAllProducts]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section with Real Data */}
      <BusinessPageHero />

      {/* Features Section */}
      <BusinessFeatures />

      {/* Benefits Section */}
      <BusinessBenefits />

      {/* Testimonials Section with Real Data */}
      <BusinessTestimonials />

      {/* CTA Section */}
      <BusinessCTA />
    </div>
  );
}
