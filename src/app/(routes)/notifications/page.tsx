"use client"
import AllNotifications from '@/app/components/notification-components/AllNotifications'
import React from 'react'
import { useAtom } from "jotai";
import { ownerNotificationsAtom } from "@/app/store/store";
import { userNotificationsAtom } from "@/app/store/store";
import { Bell, Sparkles } from 'lucide-react';
import { useSearchParams } from 'next/navigation';

const NotificationsPage = () => {
  const [ONA] = useAtom(ownerNotificationsAtom);
  const [UNA] = useAtom(userNotificationsAtom);
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 animate-fadeIn">
      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 sm:py-10 lg:px-8">
        {/* Enhanced Header Section with gradient and animation */}
        <div className="text-center mb-6 sm:mb-10">
          <div className="relative inline-block mb-3 sm:mb-4">
            <div className="absolute inset-0 bg-blue-100 rounded-full blur-xl opacity-30 animate-pulse-subtle"></div>
            <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 sm:p-4 rounded-full shadow-lg">
              <Bell className="w-6 h-6 sm:w-8 sm:h-8" aria-hidden="true" />
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2 sm:mb-3">
            Notifications
          </h1>
          <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-4 leading-relaxed">
            Stay updated with the latest activity on your reviews and products
          </p>
        </div>

        {/* Notifications Content with enhanced card styling */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 sm:p-6 hover:shadow-lg transition-all duration-300">
            <AllNotifications productId={productId} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotificationsPage
