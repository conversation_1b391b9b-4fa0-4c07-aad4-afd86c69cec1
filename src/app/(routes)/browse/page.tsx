"use client";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useSearchParams, useRouter } from "next/navigation";
import { iProduct } from "@/app/util/Interfaces";
import { getProducts } from "@/app/util/serverFunctions";
import ArrangeByPanel from "@/app/components/ArrangeByPanel";
import BrowseSkeleton from "@/app/components/skeletons/BrowseSkeleton";
import ProductCard from "@/app/components/ProductCard";
import { allProductsStore } from "@/app/store/store";
import { iCalculatedRating } from "@/app/util/Interfaces";
import { calculateAverageReviewRatingSync } from "@/app/util/calculateAverageReviewRating";
import { WeightedRatingResult } from "@/app/util/calculateWeightedRating";
import { useAuth } from "@clerk/nextjs";
import { Search, Plus } from "lucide-react";
import Link from "next/link";

const Page = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  // Get products from atom first for instant UI
  const [allProducts, setAllProducts] = useAtom(allProductsStore);
  
  // Background fetch with atom as initial data
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - consider data fresh
    initialData: allProducts?.length > 0 ? { success: true, data: allProducts } : undefined,
  }) as any;
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");

  const { userId } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const updateQueryParams = useCallback(() => {
    const params = new URLSearchParams();
    if (selectedRating) params.set("rating", selectedRating.toString());
    if (selectedTags.length > 0) params.set("tags", selectedTags.join(","));

    router.push(`${window.location.pathname}?${params.toString()}`, {
      scroll: false,
    });
  }, [selectedRating, selectedTags, router]);

  useEffect(() => {
    const ratingParam = searchParams.get("rating");
    const tagsParam = searchParams.get("tags");

    setSelectedRating(ratingParam ? Number(ratingParam) : null);
    setSelectedTags(tagsParam ? tagsParam.split(",") : []);
  }, [searchParams]);

  useEffect(() => {
    updateQueryParams();
  }, [selectedRating, selectedTags, updateQueryParams]);

  // Update the atom when fresh data arrives
  useEffect(() => {
    if (data && data.success && data.data && data.data !== allProducts) {
      setAllProducts(data.data);
    }
  }, [data, allProducts, setAllProducts]);

  useEffect(() => {
    if (selectedRating !== null) {
      // Reset selected tags when rating changes
      setSelectedTags([]);
    }
  }, [selectedRating]);

  const getFilteredTags = useMemo(() => {
    if (!allProducts || !Array.isArray(allProducts) || allProducts.length === 0) {
      return [];
    }

    const productsArray: iProduct[] = allProducts;

    const filteredProductsForTags = productsArray.filter((product) => {
      try {
        if (!product || !product.reviews) return false;

        const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object';
        const rating = hasWeightedRating
          ? product.weightedRating
          : calculateAverageReviewRatingSync(product.reviews, true) as iCalculatedRating;

        const matchesRating = !selectedRating || (rating && rating.roundedRating === selectedRating);
        
        const matchesTags = selectedTags.length === 0 ||
          (product.tags &&
            Array.isArray(product.tags) &&
            selectedTags.every((tag) => product.tags.includes(tag)));

        const matchesSearch = !searchTerm ||
          (product.name &&
            product.name.toLowerCase().includes(searchTerm.toLowerCase()));

        return matchesRating && matchesTags && matchesSearch;
      } catch (error) {
        console.error("Error filtering product for tags:", error);
        return false;
      }
    });

    const tagSet = new Set<string>();
    filteredProductsForTags.forEach((product) => {
      if (product.tags && Array.isArray(product.tags)) {
        product.tags.forEach((tag) => {
          if (tag) tagSet.add(tag);
        });
      }
    });

    return Array.from(tagSet);
  }, [allProducts, selectedRating, selectedTags, searchTerm]);

  const handleRatingChange = (rating: number | null) => {
    setSelectedRating(rating);
    setCurrentPage(1);
  };

  const handleTagChange = (tags: string[]) => {
    setSelectedTags(tags);
    setCurrentPage(1);
  };

  // Show loading only if no products in atom and still loading
  if (isLoading && (!allProducts || allProducts.length === 0)) {
    return (
      <div className="flex h-full items-center justify-center">
        <BrowseSkeleton />
      </div>
    );
  }

  // Handle error state only if no products in atom
  if (isError && (!allProducts || allProducts.length === 0)) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <div className="text-red-500 mb-4 text-xl">
          {error?.toString() || "Failed to load products"}
        </div>
        <p className="text-gray-600 mb-4">
          We are having trouble connecting to our database. Please try again
          later.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-myTheme-primary text-white rounded-lg hover:bg-myTheme-primary/90"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  // Use products from atom for instant UI
  const products: iProduct[] = allProducts || [];

  // Safely filter products with null checks
  const filteredProducts = products.filter((product) => {
    try {
      if (!product || !product.reviews) return false;

      // Check if product has weighted rating, otherwise calculate sync
      const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object';
      const rating = hasWeightedRating 
        ? product.weightedRating
        : calculateAverageReviewRatingSync(product.reviews, true) as iCalculatedRating;

      const matchesRating =
        !selectedRating || (rating && rating.roundedRating === selectedRating);
      const matchesTags =
        selectedTags.length === 0 ||
        (product.tags &&
          Array.isArray(product.tags) &&
          selectedTags.every((tag) => product.tags.includes(tag)));
      const matchesSearch =
        !searchTerm ||
        (product.name &&
          product.name.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesRating && matchesTags && matchesSearch;
    } catch (error) {
      console.error("Error filtering product:", error);
      return false; // Skip this product on error
    }
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProducts.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center gap-4 mb-6">
            {/* <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center shadow-md">
              <Search className="w-8 h-8 text-white" />
            </div> */}
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
              Browse Products & Services
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-10 leading-relaxed">
            Discover and explore products and services with authentic reviews
            from our community
          </p>
          
          {/* Add Your Business Button */}
          <div className="flex justify-center">
            <Link
              href="/submit"
              className="group inline-flex items-center gap-3 px-8 py-4 bg-blue-500 text-white rounded-2xl font-semibold shadow-md hover:shadow-lg transition-shadow duration-300"
            >
              <Plus className="w-5 h-5" />
              Add Your Business
            </Link>
          </div>
        </div>

        <div className="flex flex-col xl:flex-row gap-8">
          {/* Filters Panel */}
          <div className="w-full xl:w-80 xl:flex-shrink-0">
            <div className="sticky top-8">
              <div className="bg-white rounded-2xl shadow-md border border-gray-100 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  Filters
                </h3>
                <ArrangeByPanel
                  products={products}
                  setSelectedRating={handleRatingChange}
                  selectedRating={selectedRating}
                  selectedTags={selectedTags}
                  setSelectedTags={handleTagChange}
                  filteredProductsLength={filteredProducts.length}
                  availableTags={getFilteredTags}
                />
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1 min-w-0 space-y-6">
            <div className="bg-white rounded-2xl shadow-md border border-gray-100 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="text-sm font-medium text-gray-600">
                  <span className="text-blue-500 font-bold">{currentItems.length}</span> of{" "}
                  <span className="text-blue-500 font-bold">{filteredProducts.length}</span> results
                </div>
                <div className="h-1 flex-1 mx-4 bg-blue-100 rounded-full"></div>
              </div>
              
              {currentItems.length > 0 ? (
                <div className="grid gap-6">
                  {currentItems.map((product: iProduct, index: number) => (
                    <div
                      key={product.id}
                      className="hover:shadow-md transition-shadow duration-300"
                    >
                      <ProductCard
                        product={product}
                        options={{
                          showLatestReview: false,
                          size: "small",
                          showWriteReview: true,
                          showClaimThisProduct: true
                        }}
                        currentUserId={userId ? userId : null}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="w-10 h-10 text-gray-400" />
                  </div>
                  <p className="text-gray-500 text-lg font-medium">
                    No products match your filters
                  </p>
                  <p className="text-gray-400 text-sm mt-2">
                    Try adjusting your search criteria
                  </p>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex flex-wrap justify-center gap-2 mt-8 pt-8 border-t border-gray-100/50">
                  {Array.from({ length: totalPages }, (_, index) => (
                    <button
                      key={index}
                      onClick={() => handlePageChange(index + 1)}
                      className={`px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 transform hover:scale-105 ${currentPage === index + 1
                        ? "bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white shadow-xl"
                        : "bg-white/80 backdrop-blur-sm text-gray-600 hover:bg-white border border-gray-200/50"
                        }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
