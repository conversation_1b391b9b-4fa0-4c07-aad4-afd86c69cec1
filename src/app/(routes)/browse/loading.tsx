"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Title and Description */}
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-5 w-96" />
            </div>
            
            {/* Add Product Button */}
            <Skeleton className="h-10 w-40 rounded-lg" />
          </div>
          
          {/* Search Bar */}
          <div className="mt-6">
            <div className="relative max-w-2xl">
              <Skeleton className="h-12 w-full rounded-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          
          {/* Sidebar - ArrangeByPanel */}
          <div className="lg:w-80 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden sticky top-4">
              {/* Sidebar Header */}
              <div className="p-5 bg-gray-50 border-b border-gray-200">
                <Skeleton className="h-6 w-32 mb-5" />
                
                {/* Rating Filter Section */}
                <div className="mb-6">
                  <Skeleton className="h-5 w-16 mb-3" />
                  <div className="grid grid-cols-4 gap-2">
                    {[1, 2, 3, 4].map((i) => (
                      <Skeleton key={i} className="h-8 rounded-full" />
                    ))}
                  </div>
                </div>
                
                {/* Search Tags Section */}
                <div className="mb-6">
                  <Skeleton className="h-5 w-24 mb-3" />
                  <Skeleton className="h-12 w-full rounded-full" />
                </div>
                
                {/* Clear Filters Button */}
                <Skeleton className="h-8 w-28 rounded-full" />
              </div>
              
              {/* Available Tags Section */}
              <div className="p-5 bg-white">
                <div className="flex items-center justify-between mb-4">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-6 w-8 rounded-full" />
                </div>
                
                {/* Tag Grid */}
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((i) => (
                    <Skeleton key={i} className="h-8 w-20 rounded-full" />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <div className="flex items-center gap-4">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-8 w-24 rounded-lg" />
                <Skeleton className="h-8 w-24 rounded-lg" />
              </div>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  {/* Product Image */}
                  <div className="relative">
                    <Skeleton className="w-full h-48" />
                    {/* Claim Badge */}
                    <div className="absolute top-3 right-3">
                      <Skeleton className="h-6 w-16 rounded-full" />
                    </div>
                  </div>
                  
                  {/* Product Info */}
                  <div className="p-4 space-y-3">
                    {/* Product Name */}
                    <Skeleton className="h-6 w-3/4" />
                    
                    {/* Address */}
                    <div className="flex items-start gap-2">
                      <Skeleton className="h-4 w-4 rounded mt-0.5" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                    
                    {/* Rating */}
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Skeleton key={star} className="h-4 w-4 rounded" />
                        ))}
                      </div>
                      <Skeleton className="h-4 w-16" />
                    </div>
                    
                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      <Skeleton className="h-5 w-16 rounded-full" />
                      <Skeleton className="h-5 w-20 rounded-full" />
                      <Skeleton className="h-5 w-14 rounded-full" />
                    </div>
                    
                    {/* Contact Info */}
                    <div className="space-y-2 pt-2 border-t border-gray-100">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-3">
                      <Skeleton className="h-8 flex-1 rounded-lg" />
                      <Skeleton className="h-8 w-20 rounded-lg" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="flex justify-center mt-12">
              <div className="flex items-center gap-2">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-10 w-10 rounded-lg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
