
"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  const skeletonItems = Array(5).fill(null);

  return (
    <div className="w-full max-w-4xl mx-auto p-4 md:p-6 space-y-6">
      {/* Search Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
      </div>

      {/* Search Results Skeleton */}
      <div className="space-y-4">
        {skeletonItems.map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 rounded-lg border">
            <Skeleton className="h-16 w-16 rounded-md" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
