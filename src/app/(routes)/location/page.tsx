"use client";
import { useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { getProduct } from '@/app/util/serverFunctions';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import LoadingSpinner from '@/app/components/LoadingSpinner';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { ShareButtonWrapper } from '@/app/components/ShareButtonWrapper';
import { generateShareMetadata } from '@/app/lib/shareUtils';

const LocationPage = () => {
    const searchParams = useSearchParams();
    const productId = searchParams.get('id');

    const { data: product, isLoading, isError } = useQuery({
        queryKey: ['product', productId],
        queryFn: () => getProduct(productId!),
        enabled: !!productId,
    });

    if (isLoading) return <LoadingSpinner />;
    if (isError) return <div>Error loading product</div>;
    if (!product) return <div>Product not found</div>;
    if (!product.success || !product.data || !product.data.latitude || !product.data.longitude) {
        return (
            <div className="container mx-auto p-6">
                <div className="bg-white rounded-lg shadow p-6 text-center">
                    <h1 className="text-xl font-bold mb-4">Location Not Available</h1>
                    <p className="mb-4">This product does not have location information.</p>
                    <Link href={`/product/${productId}`} className="inline-flex items-center text-blue-600 hover:text-blue-800">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Product
                    </Link>
                </div>
            </div>
        );
    }

    const location = {
        lat: product.data.latitude,
        lng: product.data.longitude,
    };

    // Generate location-specific share metadata
    const shareMetadata = generateShareMetadata({
        title: `${product.data.name} - Location`,
        description: product.data.address
            ? `Find ${product.data.name} at ${product.data.address}. View on map and get directions.`
            : `Find ${product.data.name} on the map. View location and get directions.`,
        url: `${window.location.origin}/location?id=${productId}`,
        imageUrl: product.data.display_image,
        rating: product.data.rating,
        reviewCount: product.data._count?.reviews || 0,
    });

    return (
        <div className="container mx-auto p-6">
            <div className="mb-4 flex justify-between items-center">
                <Link href={`/product/${productId}`} className="inline-flex items-center text-blue-600 hover:text-blue-800">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Product
                </Link>
                <ShareButtonWrapper metadata={shareMetadata} className="bg-blue-600 text-white hover:bg-blue-700" />
            </div>
            <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                    <h1 className="text-xl font-bold">{product.data.name}</h1>
                    {product.data.address && (
                        <p className="text-gray-600 mt-1">{product.data.address}</p>
                    )}
                </div>
                <div className="h-[500px]">
                    <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
                        <GoogleMap
                            mapContainerStyle={{ width: '100%', height: '100%' }}
                            center={location}
                            zoom={15}
                        >
                            <Marker position={location} />
                        </GoogleMap>
                    </LoadScript>
                </div>
            </div>
        </div>
    );
};

export default LocationPage; 