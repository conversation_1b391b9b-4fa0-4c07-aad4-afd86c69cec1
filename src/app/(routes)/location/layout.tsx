import { Metadata } from 'next';
import { getProduct } from '@/app/util/serverFunctions';

export async function generateMetadata(props: any): Promise<Metadata> {
    const productId = props.searchParams?.id as string | undefined;

    if (!productId) {
        return {
            title: 'Location Not Found | Review It',
            description: 'The requested product location could not be found.',
        };
    }

    try {
        const product = await getProduct(productId);

        if (!product.success || !product.data || !product.data.latitude || !product.data.longitude) {
            return {
                title: 'Location Not Available | Review It',
                description: 'This product does not have location information.',
            };
        }

        return {
            title: `${product.data.name} - Location | Review It`,
            description: product.data.address
                ? `Find ${product.data.name} at ${product.data.address}. View on map and get directions.`
                : `Find ${product.data.name} on the map. View location and get directions.`,
            openGraph: {
                title: `${product.data.name} - Location`,
                description: product.data.address
                    ? `Find ${product.data.name} at ${product.data.address}. View on map and get directions.`
                    : `Find ${product.data.name} on the map. View location and get directions.`,
                url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy'}/location?id=${productId}`,
                type: 'website',
                siteName: 'Review It',
                images: [
                    {
                        url: product.data.display_image,
                        width: 1200,
                        height: 630,
                        alt: product.data.name,
                    },
                ],
            },
            twitter: {
                card: 'summary_large_image',
                title: `${product.data.name} - Location`,
                description: product.data.address
                    ? `Find ${product.data.name} at ${product.data.address}. View on map and get directions.`
                    : `Find ${product.data.name} on the map. View location and get directions.`,
                images: [product.data.display_image],
                site: '@reviewitgy',
                creator: '@reviewitgy',
            },
        };
    } catch (error) {
        return {
            title: 'Error Loading Location | Review It',
            description: 'There was an error loading the product location.',
        };
    }
}

export default function LocationLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return <>{children}</>;
} 