"use client";
import React, { useEffect } from "react";
import { Metadata } from "next";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { getProducts } from "@/app/util/serverFunctions";
import { allProductsStore } from "@/app/store/store";
import EnhancedTopReviews from "@/app/components/EnhancedTopReviews";
import ReviewsExploreHero from "@/app/components/ReviewsExploreHero";

// Note: metadata moved to layout or handled differently for client components
// export const metadata: Metadata = {
//   title: "Explore Reviews | ReviewIt Guyana",
//   description:
//     "Discover and explore reviews from across Guyana. Advanced search and filtering to find exactly what you're looking for.",
//   keywords: "reviews, Guyana, search reviews, filter reviews, business reviews",
// };

export default function ReviewsExplorePage() {
  const [allProducts, setAllProducts] = useAtom(allProductsStore);

  // Background fetch with atom as initial data
  const { data } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - consider data fresh
    initialData:
      allProducts?.length > 0
        ? { success: true, data: allProducts }
        : undefined,
  }) as any;

  // Update the atom when fresh data arrives
  useEffect(() => {
    if (data && data.success && data.data && data.data !== allProducts) {
      setAllProducts(data.data);
    }
  }, [data, allProducts, setAllProducts]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section with Real Data */}
      <ReviewsExploreHero />

      {/* Enhanced Reviews Component */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <EnhancedTopReviews />
        </div>
      </section>

      {/* Features Info Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Powerful Search & Discovery
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Our advanced filtering system helps you find the most relevant
            reviews for your needs
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Smart Search
              </h3>
              <p className="text-gray-600 text-sm">
                Search across reviews, businesses, and reviewers with
                intelligent matching
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Advanced Filters
              </h3>
              <p className="text-gray-600 text-sm">
                Filter by time period, category, location, and review quality
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Real-time Results
              </h3>
              <p className="text-gray-600 text-sm">
                See instant results as you type and filter with live updates
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
