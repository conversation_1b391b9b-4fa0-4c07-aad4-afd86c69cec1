'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Package, Star, FileCheck, Flag } from 'lucide-react';
import { RecentUsers } from '@/components/admin/RecentUsers';
import { ActiveUsers } from '@/components/admin/ActiveUsers';
import { TopProducts } from '@/components/admin/TopProducts';
import { TopAttentionProducts } from '@/components/admin/TopAttentionProducts';
import { ReviewDistribution } from '@/components/admin/ReviewDistribution';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface DashboardMetrics {
    topAttention?: any[];
    totalUsers: number;
    totalProducts: number;
    totalReviews: number;
    totalReports: number;
    recentUsers: Array<{
        id: string;
        userName: string;
        firstName: string;
        lastName: string;
        createdDate: string;
        avatar: string | null;
    }>;
    activeUsers: Array<{
        id: string;
        userName: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        _count: {
            reviews: number;
            comments: number;
        };
    }>;
    topProducts: Array<{
        id: string;
        name: string;
        description: string;
        rating: number;
        display_image: string;
        _count: {
            reviews: number;
        };
    }>;
    reviewDistribution: Array<{
        rating: number;
        count: number;
        percentage: number;
    }>;
    reportDistribution: Array<{
        status: string;
        _count: {
            status: number;
        };
    }>;
    recentReports: Array<{
        id: string;
        reason: string;
        status: string;
        createdAt: string;
        review: {
            id: string;
            title: string;
            rating: number;
        };
        user: {
            id: string;
            userName: string;
            firstName: string;
            lastName: string;
        };
    }>;
}

export default function AdminDashboard() {
    const router = useRouter();
    const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchMetrics = async () => {
            try {
                const response = await fetch('/api/admin/dashboard/metrics');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch dashboard metrics');
                }

                setMetrics(data.metrics);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'An error occurred');
            } finally {
                setLoading(false);
            }
        };

        fetchMetrics();

                        // Fetch top attention products separately
        const fetchAttention = async () => {
            try {
                const attRes = await fetch('/api/analytics/top-attention?limit=5');
                const attData = await attRes.json();
                if (attRes.ok && attData?.data) {
                    setMetrics(prev => (prev ? { ...prev, topAttention: attData.data } : prev));
                }
            } catch (e) {
                console.error('Failed to load top attention products');
            }
        };

        fetchAttention();
    }, []);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-red-500 text-center">
                    <h2 className="text-2xl font-bold mb-4">Error</h2>
                    <p>{error}</p>
                </div>
            </div>
        );
    }

    if (!metrics) {
        return null;
    }

    return (
        <div className="container py-10">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
                    <p className="text-muted-foreground mt-1">
                        Overview of your performance
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button asChild variant="outline">
                        <Link href="/admin/claims" className="flex items-center gap-2">
                            <FileCheck className="h-4 w-4" />
                            Manage Claims
                        </Link>
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <Card className="h-[400px] overflow-auto">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{metrics.totalUsers}</div>
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                        <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{metrics.totalProducts}</div>
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
                        <Star className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{metrics.totalReviews}</div>
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
                        <Flag className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{metrics.totalReports}</div>
                    </CardContent>
                </Card>
            </div>

            {/* Charts and Lists */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Review Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ReviewDistribution data={metrics.reviewDistribution} />
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Report Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {metrics.reportDistribution.map((dist) => (
                                <div key={dist.status} className="flex items-center justify-between">
                                    <span className="text-sm font-medium capitalize">{dist.status.toLowerCase()}</span>
                                    <span className="text-sm text-muted-foreground">{dist._count.status}</span>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Recent Reports</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {metrics.recentReports.map((report) => (
                                <div key={report.id} className="flex items-center justify-between">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">{report.review.title}</p>
                                        <p className="text-xs text-muted-foreground">
                                            Reported by {report.user.firstName} {report.user.lastName}
                                        </p>
                                    </div>
                                    <span className={`text-xs px-2 py-1 rounded-full ${report.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                            report.status === 'REVIEWED' ? 'bg-blue-100 text-blue-800' :
                                                'bg-green-100 text-green-800'
                                          }`}>
                                        {report.status}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Recent Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <RecentUsers users={metrics.recentUsers} />
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Most Active Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ActiveUsers users={metrics.activeUsers} />
                    </CardContent>
                </Card>

                <Card className="h-[400px] overflow-auto">
                    <CardHeader>
                        <CardTitle>Top Rated Products</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <TopProducts products={metrics.topProducts} />
                    {metrics.topAttention && (
                        <Card className="mt-6 h-[400px] overflow-auto">
                            <CardHeader>
                                <CardTitle>Most Attention (Depth × Time)</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <TopAttentionProducts products={metrics.topAttention} />
                            </CardContent>
                        </Card>
                    )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}