'use client';

import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import ClaimReviewPanel from '@/app/components/admin/ClaimReviewPanel';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

export default function AdminClaimsPage() {
    const [claims, setClaims] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        fetchClaims();
    }, []);

    const fetchClaims = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/admin/claims');
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message);
            }

            setClaims(data.claims);
        } catch (error) {
            console.error('Error fetching claims:', error);
            setError('Failed to fetch claims');
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="container mx-auto py-8">
                <div className="space-y-4">
                    <Skeleton className="h-8 w-1/4" />
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-32 w-full" />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full px-4 py-8">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
                    <p className="text-gray-600">{error}</p>
                    <Button
                        onClick={fetchClaims}
                        className="mt-4"
                        variant="outline"
                    >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full px-4 py-8">
            <div className="flex justify-between items-center mb-8">
                <h1 className="text-3xl font-bold">Manage Claims</h1>
                <Button
                    onClick={fetchClaims}
                    variant="outline"
                >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                </Button>
            </div>

            <ClaimReviewPanel claims={claims} onRefresh={fetchClaims} />
        </div>
    );
}