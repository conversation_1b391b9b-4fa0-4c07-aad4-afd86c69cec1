"use client";

import {
  Box,
  <PERSON>,
  Folder,
  Zap,
  Rocket,
  FileText,
  ArrowRight,
} from "lucide-react";

interface ConceptCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  status: "complete" | "in-progress" | "placeholder";
  features: string[];
}

function ConceptCard({
  title,
  description,
  icon,
  href,
  status,
  features,
}: ConceptCardProps) {
  const statusColors = {
    complete: "bg-green-100 text-green-800 border-green-200",
    "in-progress": "bg-yellow-100 text-yellow-800 border-yellow-200",
    placeholder: "bg-gray-100 text-gray-600 border-gray-200",
  };

  const statusLabels = {
    complete: "Complete",
    "in-progress": "In Progress",
    placeholder: "Coming Soon",
  };

  return (
    <div className="docs-card group">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <span
              className={`inline-block px-2 py-1 text-xs rounded border ${statusColors[status]}`}
            >
              {statusLabels[status]}
            </span>
          </div>
        </div>
      </div>

      <p className="text-gray-600 mb-4">{description}</p>

      <div className="space-y-2 mb-4">
        <h4 className="text-sm font-medium text-gray-900">Key Topics:</h4>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li
              key={index}
              className="text-sm text-gray-600 flex items-center gap-2"
            >
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
              {feature}
            </li>
          ))}
        </ul>
      </div>

      <a
        href={href}
        className={`inline-flex items-center gap-2 text-sm font-medium transition-colors ${
          status === "placeholder"
            ? "text-gray-400 cursor-not-allowed"
            : "text-blue-600 hover:text-blue-700 group-hover:gap-3"
        }`}
      >
        {status === "placeholder" ? "Coming Soon" : "Learn More"}
        {status !== "placeholder" && (
          <ArrowRight className="h-4 w-4 transition-all" />
        )}
      </a>
    </div>
  );
}

export default function CoreConceptsPage() {
  const concepts = [
    {
      title: "Component Library",
      description:
        "Reusable UI components built with React, TypeScript, and Tailwind CSS. Includes form components, modals, navigation elements, and more.",
      icon: <Box className="h-6 w-6" />,
      href: "/admin/docs/core-concepts/components",
      status: "complete" as const,
      features: [
        "UI Component Documentation",
        "Form Components & Validation",
        "Navigation & Layout Components",
        "Modal & Dialog Components",
        "Data Display Components",
      ],
    },
    {
      title: "Database Schema",
      description:
        "Complete database schema documentation including relationships, indexes, and data models used throughout the application.",
      icon: <Database className="h-6 w-6" />,
      href: "/admin/docs/core-concepts/database",
      status: "complete" as const,
      features: [
        "Entity Relationship Diagrams",
        "Table Schemas & Relationships",
        "Data Migration Strategies",
        "Database Optimization",
        "Backup & Recovery",
      ],
    },
    {
      title: "Project Structure",
      description:
        "Detailed breakdown of the application architecture, file organization, and development patterns used in the codebase.",
      icon: <Folder className="h-6 w-6" />,
      href: "/admin/docs/core-concepts/project-structure",
      status: "complete" as const,
      features: [
        "Directory Structure",
        "File Naming Conventions",
        "Code Organization Patterns",
        "Environment Configuration",
        "Build & Deployment Structure",
      ],
    },
    {
      title: "Caching Strategy",
      description:
        "Redis-based caching implementation for improved performance, including cache invalidation patterns and optimization strategies.",
      icon: <Zap className="h-6 w-6" />,
      href: "/admin/docs/core-concepts/caching",
      status: "in-progress" as const,
      features: [
        "Redis Configuration",
        "Cache Key Patterns",
        "Invalidation Strategies",
        "Performance Optimization",
        "Monitoring & Debugging",
      ],
    },
    {
      title: "Deployment",
      description:
        "Production deployment guidelines, environment setup, CI/CD pipelines, and monitoring configurations.",
      icon: <Rocket className="h-6 w-6" />,
      href: "/admin/docs/core-concepts/deployment",
      status: "in-progress" as const,
      features: [
        "Production Environment Setup",
        "CI/CD Pipeline Configuration",
        "Environment Variables",
        "Monitoring & Logging",
        "Performance Optimization",
      ],
    },
  ];

  return (
    <div className="docs-container">
      <div className="docs-content">
        <div className="prose max-w-none">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Core Concepts
            </h1>
            <p className="text-lg text-gray-600">
              Essential concepts and architectural patterns that power the
              ReviewIt platform.
            </p>
          </div>

          <div className="intro-section mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-start gap-4">
                <FileText className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h2 className="text-xl font-semibold text-blue-900 mb-2">
                    Understanding the Architecture
                  </h2>
                  <p className="text-blue-800 mb-4">
                    ReviewIt is built using modern web development practices
                    with a focus on scalability, maintainability, and developer
                    experience. This section covers the fundamental concepts you
                    need to understand to work effectively with the codebase.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🏗️ Architecture
                      </div>
                      <div className="text-blue-700">
                        Next.js App Router with server-side rendering and API
                        routes
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        📊 Data Layer
                      </div>
                      <div className="text-blue-700">
                        Prisma ORM with PostgreSQL and Redis caching
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🎨 Frontend
                      </div>
                      <div className="text-blue-700">
                        React 18 with TypeScript and Tailwind CSS
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="docs-grid">
            {concepts.map((concept, index) => (
              <ConceptCard key={index} {...concept} />
            ))}
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-3">
                🎯 Getting Started
              </h3>
              <p className="text-green-800 text-sm mb-4">
                New to the project? Start with these core concepts to build a
                solid foundation.
              </p>
              <div className="space-y-2">
                <a
                  href="/admin/docs/core-concepts/project-structure"
                  className="block text-sm text-green-700 hover:text-green-900 hover:underline"
                >
                  1. Project Structure & Organization
                </a>
                <a
                  href="/admin/docs/core-concepts/database"
                  className="block text-sm text-green-700 hover:text-green-900 hover:underline"
                >
                  2. Database Schema & Models
                </a>
                <a
                  href="/admin/docs/core-concepts/components"
                  className="block text-sm text-green-700 hover:text-green-900 hover:underline"
                >
                  3. Component Library & Patterns
                </a>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-orange-900 mb-3">
                ⚡ Advanced Topics
              </h3>
              <p className="text-orange-800 text-sm mb-4">
                Ready to dive deeper? Explore performance optimization and
                deployment strategies.
              </p>
              <div className="space-y-2">
                <a
                  href="/admin/docs/core-concepts/caching"
                  className="block text-sm text-orange-700 hover:text-orange-900 hover:underline"
                >
                  1. Caching & Performance
                </a>
                <a
                  href="/admin/docs/core-concepts/deployment"
                  className="block text-sm text-orange-700 hover:text-orange-900 hover:underline"
                >
                  2. Deployment & DevOps
                </a>
                <a
                  href="/admin/docs/api-routes"
                  className="block text-sm text-orange-700 hover:text-orange-900 hover:underline"
                >
                  3. API Architecture
                </a>
              </div>
            </div>
          </div>

          <div className="mt-8 p-6 bg-gray-50 rounded-lg border">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              📚 Additional Resources
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Developer Resources
                </h4>
                <ul className="space-y-1 text-gray-600">
                  <li>
                    •{" "}
                    <a
                      href="/admin/docs/api-routes"
                      className="hover:text-blue-600 hover:underline"
                    >
                      API Routes Documentation
                    </a>
                  </li>
                  <li>
                    •{" "}
                    <a
                      href="/admin/docs/user-management"
                      className="hover:text-blue-600 hover:underline"
                    >
                      User Management Guide
                    </a>
                  </li>
                  <li>
                    •{" "}
                    <a
                      href="/admin/docs/product-management"
                      className="hover:text-blue-600 hover:underline"
                    >
                      Product Management
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  External Documentation
                </h4>
                <ul className="space-y-1 text-gray-600">
                  <li>
                    •{" "}
                    <a
                      href="https://nextjs.org/docs"
                      target="_blank"
                      className="hover:text-blue-600 hover:underline"
                    >
                      Next.js Documentation
                    </a>
                  </li>
                  <li>
                    •{" "}
                    <a
                      href="https://www.prisma.io/docs"
                      target="_blank"
                      className="hover:text-blue-600 hover:underline"
                    >
                      Prisma ORM Documentation
                    </a>
                  </li>
                  <li>
                    •{" "}
                    <a
                      href="https://tailwindcss.com/docs"
                      target="_blank"
                      className="hover:text-blue-600 hover:underline"
                    >
                      Tailwind CSS Documentation
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <a
              href="/admin/docs"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              ← Back to Documentation Home
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
