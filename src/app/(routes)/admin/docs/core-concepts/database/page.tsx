"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s<PERSON>ontaine<PERSON>,
  TableOfContents,
  CollapsibleSection,
  CodeBlock,
  InfoBox,
  StatusBadge,
  BackToDocsButton,
} from "@/components/docs";
import {
  Database,
  Users,
  ShoppingCart,
  Star,
  MessageSquare,
  Building,
  Flag,
  BarChart3,
  Key,
  Link,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  FileText,
} from "lucide-react";

interface EntitySectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  tables: {
    name: string;
    description: string;
    fields: {
      name: string;
      type: string;
      description: string;
      constraints?: string[];
    }[];
    relationships?: {
      field: string;
      references: string;
      type: "one-to-one" | "one-to-many" | "many-to-many";
    }[];
    indexes?: string[];
  }[];
}

function EntitySection({
  title,
  description,
  icon,
  tables,
}: EntitySectionProps) {
  return (
    <CollapsibleSection title={title} defaultOpen={true}>
      <div className="mb-4 flex items-center gap-3">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="space-y-6">
        {tables.map((table, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg p-6 bg-white"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-xl font-bold text-gray-900">{table.name}</h4>
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-mono">
                {table.fields.length} fields
              </span>
            </div>

            <p className="text-gray-600 mb-4">{table.description}</p>

            {/* Fields Table */}
            <div className="mb-6">
              <h5 className="text-lg font-semibold text-gray-900 mb-3">
                Fields
              </h5>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-50 border-b border-gray-200">
                      <th className="text-left p-3 font-medium text-gray-900">
                        Field
                      </th>
                      <th className="text-left p-3 font-medium text-gray-900">
                        Type
                      </th>
                      <th className="text-left p-3 font-medium text-gray-900">
                        Description
                      </th>
                      <th className="text-left p-3 font-medium text-gray-900">
                        Constraints
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {table.fields.map((field, fieldIndex) => (
                      <tr
                        key={fieldIndex}
                        className="border-b border-gray-100 hover:bg-gray-50"
                      >
                        <td className="p-3 font-mono text-blue-600">
                          {field.name}
                        </td>
                        <td className="p-3 font-mono text-green-600">
                          {field.type}
                        </td>
                        <td className="p-3 text-gray-700">
                          {field.description}
                        </td>
                        <td className="p-3">
                          {field.constraints && field.constraints.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {field.constraints.map((constraint, i) => (
                                <span
                                  key={i}
                                  className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded"
                                >
                                  {constraint}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">None</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Relationships */}
            {table.relationships && table.relationships.length > 0 && (
              <div className="mb-6">
                <h5 className="text-lg font-semibold text-gray-900 mb-3">
                  Relationships
                </h5>
                <div className="space-y-2">
                  {table.relationships.map((rel, relIndex) => (
                    <div
                      key={relIndex}
                      className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg"
                    >
                      <Link className="h-4 w-4 text-blue-600" />
                      <span className="font-mono text-blue-600">
                        {rel.field}
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400" />
                      <span className="font-mono text-green-600">
                        {rel.references}
                      </span>
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                        {rel.type}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Indexes */}
            {table.indexes && table.indexes.length > 0 && (
              <div>
                <h5 className="text-lg font-semibold text-gray-900 mb-3">
                  Indexes
                </h5>
                <div className="flex flex-wrap gap-2">
                  {table.indexes.map((index, indexIndex) => (
                    <span
                      key={indexIndex}
                      className="bg-purple-100 text-purple-800 text-sm px-3 py-1 rounded font-mono"
                    >
                      {index}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </CollapsibleSection>
  );
}

export default function DatabaseSchemaPage() {
  const tocItems = [
    { id: "overview", title: "Overview", level: 1 },
    { id: "core-entities", title: "Core Entities", level: 1 },
    { id: "user-management", title: "User Management", level: 1 },
    { id: "content-system", title: "Content System", level: 1 },
    { id: "business-analytics", title: "Business & Analytics", level: 1 },
    { id: "moderation-system", title: "Moderation System", level: 1 },
    { id: "relationships", title: "Entity Relationships", level: 1 },
    { id: "performance", title: "Performance Optimization", level: 1 },
    { id: "migrations", title: "Migrations & Maintenance", level: 1 },
  ];

  const coreEntities = [
    {
      name: "Product",
      description:
        "Central entity representing businesses, services, or items that can be reviewed",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        {
          name: "name",
          type: "String",
          description: "Product name",
          constraints: ["@unique"],
        },
        {
          name: "description",
          type: "String",
          description: "Detailed product description",
        },
        {
          name: "display_image",
          type: "String",
          description: "Main product image URL",
        },
        {
          name: "images",
          type: "String[]",
          description: "Array of additional image URLs",
        },
        {
          name: "videos",
          type: "String[]",
          description: "Array of video URLs",
        },
        {
          name: "links",
          type: "String[]",
          description: "Array of external links",
        },
        {
          name: "tags",
          type: "String[]",
          description: "Product categorization tags",
        },
        {
          name: "rating",
          type: "Int",
          description: "Overall rating (1-5)",
          constraints: ["@default(3)"],
        },
        {
          name: "address",
          type: "String?",
          description: "Physical address (optional)",
        },
        {
          name: "telephone",
          type: "String?",
          description: "Contact phone number",
        },
        { name: "website", type: "String[]", description: "Website URLs" },
        { name: "email", type: "String?", description: "Contact email" },
        { name: "openingHrs", type: "String?", description: "Opening hours" },
        { name: "closingHrs", type: "String?", description: "Closing hours" },
        {
          name: "openingDays",
          type: "String[]",
          description: "Days of operation",
          constraints: ["@default([])"],
        },
        {
          name: "latitude",
          type: "Float?",
          description: "GPS latitude coordinate",
        },
        {
          name: "longitude",
          type: "Float?",
          description: "GPS longitude coordinate",
        },
        {
          name: "hasOwner",
          type: "Boolean?",
          description: "Whether product has a claimed owner",
        },
        { name: "ownerId", type: "String?", description: "Owner user ID" },
        {
          name: "businessId",
          type: "String?",
          description: "Associated business ID",
        },
        {
          name: "createdById",
          type: "String",
          description: "User who created the product",
        },
        {
          name: "featuredPosition",
          type: "Int?",
          description: "Featured listing position",
        },
        {
          name: "viewCount",
          type: "Int",
          description: "Total view count",
          constraints: ["@default(0)"],
        },
        {
          name: "rating1Star",
          type: "Int",
          description: "1-star rating count",
          constraints: ["@default(0)"],
        },
        {
          name: "rating2Stars",
          type: "Int",
          description: "2-star rating count",
          constraints: ["@default(0)"],
        },
        {
          name: "rating3Stars",
          type: "Int",
          description: "3-star rating count",
          constraints: ["@default(0)"],
        },
        {
          name: "rating4Stars",
          type: "Int",
          description: "4-star rating count",
          constraints: ["@default(0)"],
        },
        {
          name: "rating5Stars",
          type: "Int",
          description: "5-star rating count",
          constraints: ["@default(0)"],
        },
        {
          name: "isDeleted",
          type: "Boolean",
          description: "Soft delete flag",
          constraints: ["@default(false)"],
        },
        {
          name: "createdDate",
          type: "DateTime",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
        {
          name: "updatedAt",
          type: "DateTime?",
          description: "Last update timestamp",
        },
        {
          name: "descriptionLastUpdatedAt",
          type: "DateTime?",
          description: "Last description update",
        },
        {
          name: "imagesLastUpdatedAt",
          type: "DateTime?",
          description: "Last image update",
        },
      ],
      relationships: [
        {
          field: "businessId",
          references: "Business.id",
          type: "one-to-many" as const,
        },
        {
          field: "createdById",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "analytics",
          references: "ProductAnalytics.productId",
          type: "one-to-one" as const,
        },
        {
          field: "claims",
          references: "ProductClaim.productId",
          type: "one-to-one" as const,
        },
        {
          field: "reviews",
          references: "Review.productId",
          type: "one-to-many" as const,
        },
        {
          field: "viewEvents",
          references: "ProductViewEvent.productId",
          type: "one-to-many" as const,
        },
        {
          field: "userInteractions",
          references: "UserProductInteraction.productId",
          type: "one-to-many" as const,
        },
        {
          field: "promotions",
          references: "Promotion.productId",
          type: "one-to-many" as const,
        },
      ],
      indexes: [
        "isDeleted + rating",
        "viewCount",
        "featuredPosition",
        "businessId",
      ],
    },
    {
      name: "Review",
      description:
        "User reviews and ratings for products with moderation and interaction features",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "title", type: "String", description: "Review title" },
        { name: "body", type: "String", description: "Review content" },
        { name: "rating", type: "Int", description: "Star rating (1-5)" },
        {
          name: "productId",
          type: "String",
          description: "Associated product ID",
        },
        { name: "userId", type: "String", description: "Review author ID" },
        {
          name: "helpfulVotes",
          type: "Int",
          description: "Helpful vote count",
          constraints: ["@default(0)"],
        },
        {
          name: "unhelpfulVotes",
          type: "Int",
          description: "Unhelpful vote count",
          constraints: ["@default(0)"],
        },
        { name: "images", type: "String[]", description: "Review image URLs" },
        { name: "videos", type: "String[]", description: "Review video URLs" },
        {
          name: "links",
          type: "String[]",
          description: "External links in review",
        },
        {
          name: "isVerified",
          type: "Boolean?",
          description: "Verification status",
        },
        {
          name: "verifiedBy",
          type: "String?",
          description: "Verifier user ID",
        },
        {
          name: "verifiedAt",
          type: "DateTime?",
          description: "Verification timestamp",
        },
        {
          name: "isPublic",
          type: "Boolean",
          description: "Public visibility",
          constraints: ["@default(true)"],
        },
        {
          name: "isDeleted",
          type: "Boolean?",
          description: "Soft delete flag",
          constraints: ["@default(false)"],
        },
        {
          name: "ownerRespondedAt",
          type: "DateTime?",
          description: "Owner response timestamp",
        },
        {
          name: "createdBy",
          type: "String?",
          description: "Creator ID (if different from user)",
        },
        {
          name: "createdDate",
          type: "DateTime",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "productId",
          references: "Product.id",
          type: "one-to-many" as const,
        },
        {
          field: "userId",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "voteCount",
          references: "VoteCount.reviewId",
          type: "one-to-one" as const,
        },
        {
          field: "comments",
          references: "Comment.reviewId",
          type: "one-to-many" as const,
        },
        {
          field: "likedBy",
          references: "User.id",
          type: "many-to-many" as const,
        },
        {
          field: "reports",
          references: "ReviewReport.reviewId",
          type: "one-to-many" as const,
        },
        {
          field: "moderationHistory",
          references: "ModerationEvent.reviewId",
          type: "one-to-many" as const,
        },
      ],
      indexes: [
        "isDeleted + isVerified",
        "rating",
        "createdDate",
        "productId",
        "userId",
      ],
    },
  ];

  const userManagement = [
    {
      name: "User",
      description:
        "User accounts with authentication, profile information, and role management",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        {
          name: "userName",
          type: "String",
          description: "Unique username",
          constraints: ["@unique"],
        },
        {
          name: "email",
          type: "String",
          description: "Email address",
          constraints: ["@unique"],
        },
        { name: "firstName", type: "String", description: "First name" },
        { name: "lastName", type: "String", description: "Last name" },
        { name: "avatar", type: "String?", description: "Profile picture URL" },
        { name: "bio", type: "String?", description: "User biography" },
        {
          name: "clerkUserId",
          type: "String",
          description: "Clerk authentication ID",
          constraints: ["@unique"],
        },
        {
          name: "role",
          type: "String",
          description: "User role",
          constraints: ["@default('USER')"],
        },
        {
          name: "status",
          type: "String",
          description: "Account status",
          constraints: ["@default('ACTIVE')"],
        },
        {
          name: "isDeleted",
          type: "Boolean?",
          description: "Soft delete flag",
          constraints: ["@default(false)"],
        },
        {
          name: "lastLoginAt",
          type: "DateTime?",
          description: "Last login timestamp",
        },
        {
          name: "loginCount",
          type: "Int",
          description: "Total login count",
          constraints: ["@default(0)"],
        },
        {
          name: "suspendedReason",
          type: "String?",
          description: "Suspension reason",
        },
        {
          name: "suspendedUntil",
          type: "DateTime?",
          description: "Suspension end date",
        },
        {
          name: "createdDate",
          type: "DateTime",
          description: "Account creation date",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "product",
          references: "Product.createdById",
          type: "one-to-many" as const,
        },
        {
          field: "reviews",
          references: "Review.userId",
          type: "one-to-many" as const,
        },
        {
          field: "comments",
          references: "Comment.userId",
          type: "one-to-many" as const,
        },
        {
          field: "commentVotes",
          references: "CommentVote.userId",
          type: "one-to-many" as const,
        },
        {
          field: "businesses",
          references: "Business.ownerId",
          type: "one-to-many" as const,
        },
        {
          field: "productClaims",
          references: "ProductClaim.userId",
          type: "one-to-many" as const,
        },
        {
          field: "likedReviews",
          references: "Review.id",
          type: "many-to-many" as const,
        },
        {
          field: "adminActions",
          references: "AdminAction.adminId",
          type: "one-to-many" as const,
        },
        {
          field: "bugReports",
          references: "BugReport.reporterId",
          type: "one-to-many" as const,
        },
      ],
      indexes: ["role + status", "isDeleted + status", "lastLoginAt"],
    },
    {
      name: "Business",
      description:
        "Business accounts for product owners with subscription and verification status",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (CUID)",
          constraints: ["@id", "@unique", "@default(cuid())"],
        },
        {
          name: "ownerId",
          type: "String",
          description: "Business owner user ID",
        },
        {
          name: "ownerName",
          type: "String?",
          description: "Business owner name",
        },
        {
          name: "subscriptionStatus",
          type: "String",
          description: "Subscription status",
        },
        {
          name: "subscriptionExpiry",
          type: "DateTime?",
          description: "Subscription expiration date",
        },
        {
          name: "isVerified",
          type: "Boolean?",
          description: "Verification status",
          constraints: ["@default(false)"],
        },
        {
          name: "createdDate",
          type: "DateTime?",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "ownerId",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "products",
          references: "Product.businessId",
          type: "one-to-many" as const,
        },
        {
          field: "promotions",
          references: "Promotion.businessId",
          type: "one-to-many" as const,
        },
        {
          field: "analytics",
          references: "BusinessAnalytics.businessId",
          type: "one-to-one" as const,
        },
      ],
      indexes: [],
    },
  ];

  const contentSystem = [
    {
      name: "Comment",
      description:
        "Threaded comment system for reviews with voting and moderation",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "body", type: "String", description: "Comment content" },
        {
          name: "reviewId",
          type: "String",
          description: "Associated review ID",
        },
        { name: "userId", type: "String", description: "Comment author ID" },
        {
          name: "parentId",
          type: "String?",
          description: "Parent comment ID for threading",
        },
        {
          name: "upvotes",
          type: "Int",
          description: "Upvote count",
          constraints: ["@default(0)"],
        },
        {
          name: "downvotes",
          type: "Int",
          description: "Downvote count",
          constraints: ["@default(0)"],
        },
        {
          name: "isDeleted",
          type: "Boolean?",
          description: "Soft delete flag",
          constraints: ["@default(false)"],
        },
        {
          name: "createdDate",
          type: "DateTime",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "reviewId",
          references: "Review.id",
          type: "one-to-many" as const,
        },
        {
          field: "userId",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "parentId",
          references: "Comment.id",
          type: "one-to-many" as const,
        },
        {
          field: "votes",
          references: "CommentVote.commentId",
          type: "one-to-many" as const,
        },
      ],
      indexes: [],
    },
    {
      name: "CommentVote",
      description: "User votes on comments with duplicate prevention",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "commentId", type: "String", description: "Target comment ID" },
        { name: "userId", type: "String", description: "Voting user ID" },
        {
          name: "clerkUserId",
          type: "String",
          description: "Clerk user ID for duplicate prevention",
        },
        {
          name: "voteType",
          type: "String",
          description: "Vote type (UP/DOWN)",
          constraints: ["@default('UP')"],
        },
        {
          name: "createdAt",
          type: "DateTime",
          description: "Vote timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "commentId",
          references: "Comment.id",
          type: "one-to-many" as const,
        },
        {
          field: "userId",
          references: "User.id",
          type: "one-to-many" as const,
        },
      ],
      indexes: [
        "commentId + clerkUserId (unique)",
        "commentId",
        "userId",
        "clerkUserId",
      ],
    },
  ];

  const businessAnalytics = [
    {
      name: "ProductAnalytics",
      description:
        "Detailed analytics for product performance and user engagement",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        {
          name: "productId",
          type: "String",
          description: "Associated product ID",
          constraints: ["@unique"],
        },
        {
          name: "totalViews",
          type: "Int",
          description: "Total view count",
          constraints: ["@default(0)"],
        },
        {
          name: "uniqueVisitors",
          type: "Int",
          description: "Unique visitor count",
          constraints: ["@default(0)"],
        },
        {
          name: "averageViewDuration",
          type: "Float",
          description: "Average time spent viewing",
          constraints: ["@default(0)"],
        },
        {
          name: "peakHours",
          type: "Json",
          description: "Peak viewing hours data",
        },
        {
          name: "weekdayStats",
          type: "Json",
          description: "Weekday statistics",
        },
        {
          name: "lastUpdated",
          type: "DateTime",
          description: "Last analytics update",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "productId",
          references: "Product.id",
          type: "one-to-one" as const,
        },
      ],
      indexes: ["productId", "totalViews"],
    },
    {
      name: "BusinessAnalytics",
      description: "Business-level analytics and performance metrics",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        {
          name: "businessId",
          type: "String",
          description: "Associated business ID",
          constraints: ["@unique"],
        },
        {
          name: "averageReviewResponseTime",
          type: "Float?",
          description: "Average response time in hours",
        },
        {
          name: "negativeReviewResponseRate",
          type: "Float?",
          description: "Response rate for negative reviews (%)",
        },
        {
          name: "productContentFreshnessScore",
          type: "Float?",
          description: "Content freshness score (0-100)",
        },
        {
          name: "lastCalculated",
          type: "DateTime",
          description: "Last calculation timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "businessId",
          references: "Business.id",
          type: "one-to-one" as const,
        },
      ],
      indexes: ["businessId"],
    },
    {
      name: "Promotion",
      description: "Business promotions and marketing campaigns with analytics",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "title", type: "String", description: "Promotion title" },
        {
          name: "description",
          type: "String",
          description: "Promotion description",
        },
        {
          name: "startDate",
          type: "DateTime",
          description: "Campaign start date",
        },
        { name: "endDate", type: "DateTime", description: "Campaign end date" },
        {
          name: "discountPercentage",
          type: "Float?",
          description: "Discount percentage",
        },
        {
          name: "discountAmount",
          type: "Float?",
          description: "Fixed discount amount",
        },
        { name: "promotionCode", type: "String?", description: "Promo code" },
        {
          name: "isActive",
          type: "Boolean",
          description: "Active status",
          constraints: ["@default(true)"],
        },
        { name: "image", type: "String?", description: "Promotion image URL" },
        {
          name: "imagePublicId",
          type: "String?",
          description: "Cloudinary image ID",
        },
        {
          name: "productId",
          type: "String",
          description: "Associated product ID",
        },
        {
          name: "businessId",
          type: "String",
          description: "Associated business ID",
        },
        { name: "createdById", type: "String", description: "Creator user ID" },
        {
          name: "viewCount",
          type: "Int",
          description: "View count",
          constraints: ["@default(0)"],
        },
        {
          name: "clickCount",
          type: "Int",
          description: "Click count",
          constraints: ["@default(0)"],
        },
        {
          name: "conversionCount",
          type: "Int",
          description: "Conversion count",
          constraints: ["@default(0)"],
        },
        {
          name: "createdAt",
          type: "DateTime",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
        {
          name: "updatedAt",
          type: "DateTime",
          description: "Update timestamp",
          constraints: ["@updatedAt"],
        },
      ],
      relationships: [
        {
          field: "productId",
          references: "Product.id",
          type: "one-to-many" as const,
        },
        {
          field: "businessId",
          references: "Business.id",
          type: "one-to-many" as const,
        },
        {
          field: "createdById",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "analytics",
          references: "PromotionAnalytics.promotionId",
          type: "one-to-one" as const,
        },
      ],
      indexes: [
        "productId",
        "businessId",
        "isActive",
        "startDate + endDate",
        "createdAt",
        "createdById",
      ],
    },
  ];

  const moderationSystem = [
    {
      name: "ModerationEvent",
      description: "Audit log for moderation actions on reviews and content",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "reviewId", type: "String", description: "Target review ID" },
        { name: "adminId", type: "String", description: "Moderator user ID" },
        {
          name: "action",
          type: "String",
          description: "Moderation action taken",
        },
        { name: "reason", type: "String?", description: "Reason for action" },
        {
          name: "createdAt",
          type: "DateTime",
          description: "Action timestamp",
          constraints: ["@default(now())"],
        },
      ],
      relationships: [
        {
          field: "reviewId",
          references: "Review.id",
          type: "one-to-many" as const,
        },
        {
          field: "adminId",
          references: "User.id",
          type: "one-to-many" as const,
        },
      ],
      indexes: ["reviewId", "adminId", "createdAt", "action"],
    },
    {
      name: "ReviewReport",
      description: "User reports for inappropriate or problematic reviews",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "reviewId", type: "String", description: "Reported review ID" },
        { name: "userId", type: "String", description: "Reporter user ID" },
        { name: "reason", type: "String", description: "Report reason" },
        {
          name: "status",
          type: "String",
          description: "Report status",
          constraints: ["@default('PENDING')"],
        },
        { name: "notes", type: "String?", description: "Admin notes" },
        {
          name: "resolvedBy",
          type: "String?",
          description: "Resolver admin ID",
        },
        {
          name: "resolvedAt",
          type: "DateTime?",
          description: "Resolution timestamp",
        },
        {
          name: "createdAt",
          type: "DateTime",
          description: "Report timestamp",
          constraints: ["@default(now())"],
        },
        {
          name: "updatedAt",
          type: "DateTime",
          description: "Update timestamp",
          constraints: ["@updatedAt"],
        },
      ],
      relationships: [
        {
          field: "reviewId",
          references: "Review.id",
          type: "one-to-many" as const,
        },
        {
          field: "userId",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "resolvedBy",
          references: "User.id",
          type: "one-to-many" as const,
        },
      ],
      indexes: ["reviewId", "userId", "status"],
    },
    {
      name: "BugReport",
      description:
        "User-submitted bug reports with status tracking and resolution",
      fields: [
        {
          name: "id",
          type: "String",
          description: "Unique identifier (UUID)",
          constraints: ["@id", "@default(uuid())"],
        },
        { name: "title", type: "String", description: "Bug report title" },
        { name: "description", type: "String", description: "Bug description" },
        {
          name: "browser",
          type: "String?",
          description: "Browser information",
        },
        { name: "device", type: "String?", description: "Device information" },
        {
          name: "mobile_os",
          type: "String?",
          description: "Mobile OS information",
        },
        {
          name: "status",
          type: "BugReportStatus",
          description: "Bug status",
          constraints: ["@default(OPEN)"],
        },
        {
          name: "reporterId",
          type: "String?",
          description: "Reporter user ID",
        },
        {
          name: "resolved_by",
          type: "String?",
          description: "Resolver admin ID",
        },
        {
          name: "resolved_at",
          type: "DateTime?",
          description: "Resolution timestamp",
        },
        {
          name: "resolution_notes",
          type: "String?",
          description: "Resolution notes",
        },
        {
          name: "created_at",
          type: "DateTime",
          description: "Creation timestamp",
          constraints: ["@default(now())"],
        },
        {
          name: "updated_at",
          type: "DateTime",
          description: "Update timestamp",
          constraints: ["@updatedAt"],
        },
      ],
      relationships: [
        {
          field: "reporterId",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "resolved_by",
          references: "User.id",
          type: "one-to-many" as const,
        },
        {
          field: "adminActions",
          references: "AdminAction.bugReportId",
          type: "one-to-many" as const,
        },
      ],
      indexes: ["status", "reporterId", "resolved_by", "created_at"],
    },
  ];

  return (
    <DocsContainer>
      <DocsHeader
        title="Database Schema"
        description="Complete database schema documentation including relationships, indexes, and data models"
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Core Concepts", href: "/admin/docs/core-concepts" },
          {
            label: "Database Schema",
            href: "/admin/docs/core-concepts/database",
          },
        ]}
      />

      <TableOfContents items={tocItems} />

      <section id="overview">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>

        <InfoBox type="info" title="Database Technology Stack">
          <p>
            ReviewIt uses PostgreSQL as the primary database with Prisma ORM for
            type-safe database access. The schema is designed for scalability,
            data integrity, and performance optimization with comprehensive
            indexing and relationship management.
          </p>
        </InfoBox>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <Database className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="font-semibold text-blue-900">PostgreSQL</div>
            <div className="text-sm text-blue-700">Primary Database</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <Key className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="font-semibold text-green-900">Prisma ORM</div>
            <div className="text-sm text-green-700">Type-Safe Access</div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="font-semibold text-purple-900">24 Tables</div>
            <div className="text-sm text-purple-700">Total Entities</div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
            <CheckCircle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="font-semibold text-orange-900">45+ Indexes</div>
            <div className="text-sm text-orange-700">Performance Optimized</div>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Schema Statistics
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-900">Core Entities</div>
              <div className="text-gray-600">
                Product, Review, User, Business
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Analytics Tables</div>
              <div className="text-gray-600">
                6 specialized analytics entities
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Moderation System</div>
              <div className="text-gray-600">
                5 tables for content moderation
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Business Features</div>
              <div className="text-gray-600">
                Promotions, claims, subscriptions
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="core-entities">
        <EntitySection
          title="Core Entities"
          description="Primary business entities that form the foundation of the ReviewIt platform"
          icon={<ShoppingCart className="h-6 w-6" />}
          tables={coreEntities}
        />
      </section>

      <section id="user-management">
        <EntitySection
          title="User Management"
          description="User accounts, authentication, and business ownership with role-based access control"
          icon={<Users className="h-6 w-6" />}
          tables={userManagement}
        />
      </section>

      <section id="content-system">
        <EntitySection
          title="Content System"
          description="Comment threading system with voting mechanisms and user interactions"
          icon={<MessageSquare className="h-6 w-6" />}
          tables={contentSystem}
        />
      </section>

      <section id="business-analytics">
        <EntitySection
          title="Business & Analytics"
          description="Analytics tracking, promotions, and business intelligence data collection"
          icon={<BarChart3 className="h-6 w-6" />}
          tables={businessAnalytics}
        />
      </section>

      <section id="moderation-system">
        <EntitySection
          title="Moderation System"
          description="Content moderation, reporting system, and administrative actions tracking"
          icon={<Flag className="h-6 w-6" />}
          tables={moderationSystem}
        />
      </section>

      <section id="relationships">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Entity Relationships
        </h2>

        <div className="mb-6">
          <InfoBox type="info" title="Relationship Patterns">
            <p>
              The database uses a combination of one-to-one, one-to-many, and
              many-to-many relationships to maintain data integrity and support
              complex business logic. Foreign key constraints ensure referential
              integrity across all related entities.
            </p>
          </InfoBox>
        </div>

        <div className="space-y-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Core Entity Relationships
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <span className="font-mono text-blue-600">User</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-green-600">Product</span>
                <span className="text-sm text-gray-600">
                  (One user can create many products)
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <span className="font-mono text-blue-600">Product</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-green-600">Review</span>
                <span className="text-sm text-gray-600">
                  (One product can have many reviews)
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <span className="font-mono text-blue-600">Review</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-green-600">Comment</span>
                <span className="text-sm text-gray-600">
                  (One review can have many comments)
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <span className="font-mono text-blue-600">Business</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-green-600">Product</span>
                <span className="text-sm text-gray-600">
                  (One business can own many products)
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Analytics Relationships
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <span className="font-mono text-green-600">Product</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-purple-600">
                  ProductAnalytics
                </span>
                <span className="text-sm text-gray-600">
                  (One-to-one analytics tracking)
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <span className="font-mono text-green-600">Business</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-purple-600">
                  BusinessAnalytics
                </span>
                <span className="text-sm text-gray-600">
                  (One-to-one business metrics)
                </span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <span className="font-mono text-green-600">Promotion</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-purple-600">
                  PromotionAnalytics
                </span>
                <span className="text-sm text-gray-600">
                  (One-to-one promotion tracking)
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Many-to-Many Relationships
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                <span className="font-mono text-yellow-600">User</span>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-orange-600">Review</span>
                <span className="text-sm text-gray-600">
                  (Users can like multiple reviews)
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="performance">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Performance Optimization
        </h2>

        <div className="space-y-6">
          <InfoBox type="success" title="Indexing Strategy">
            <p>
              The database uses strategic indexing to optimize query performance
              for common operations like searching products, filtering reviews,
              and tracking analytics. Composite indexes are used for
              multi-column queries.
            </p>
          </InfoBox>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Critical Indexes
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    Product(isDeleted, rating)
                  </code>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    Review(productId)
                  </code>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    User(role, status)
                  </code>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    CommentVote(commentId, clerkUserId)
                  </code>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Query Optimization
              </h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Composite indexes for multi-field queries</li>
                <li>• Partial indexes for filtered data</li>
                <li>• Unique constraints prevent duplicates</li>
                <li>• Foreign key indexes for join operations</li>
                <li>• Date-based indexes for time series queries</li>
              </ul>
            </div>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-orange-900 mb-3">
              Performance Considerations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-orange-800">
              <div>
                <h4 className="font-medium mb-2">Read Optimization</h4>
                <ul className="space-y-1">
                  <li>• Product search with rating filters</li>
                  <li>• Review pagination by creation date</li>
                  <li>• User activity tracking queries</li>
                  <li>• Analytics aggregation queries</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Write Optimization</h4>
                <ul className="space-y-1">
                  <li>• Batch analytics updates</li>
                  <li>• Efficient vote counting mechanisms</li>
                  <li>• Soft delete patterns</li>
                  <li>• Audit trail maintenance</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="migrations">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Migrations & Maintenance
        </h2>

        <div className="space-y-6">
          <InfoBox type="warning" title="Migration Best Practices">
            <p>
              Database migrations should be carefully planned and tested in
              staging environments. Always backup production data before
              applying migrations and consider downtime requirements for
              structural changes.
            </p>
          </InfoBox>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Schema Evolution
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-2">
                  <FileText className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <div className="font-medium">Version Control</div>
                    <div className="text-gray-600">
                      All schema changes tracked in Prisma migrations
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div>
                    <div className="font-medium">Breaking Changes</div>
                    <div className="text-gray-600">
                      Require careful planning and data migration scripts
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">Backward Compatibility</div>
                    <div className="text-gray-600">
                      New fields added as optional when possible
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Maintenance Tasks
              </h3>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-blue-50 rounded">
                  <div className="font-medium text-blue-900">
                    Analytics Aggregation
                  </div>
                  <div className="text-blue-700">
                    Daily batch processing for analytics tables
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded">
                  <div className="font-medium text-green-900">Data Cleanup</div>
                  <div className="text-green-700">
                    Soft-deleted record archival and purging
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded">
                  <div className="font-medium text-purple-900">
                    Index Maintenance
                  </div>
                  <div className="text-purple-700">
                    Regular index optimization and statistics updates
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Common Migration Commands
            </h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Generate Migration
                </h4>
                <CodeBlock
                  code="npx prisma migrate dev --name add-new-feature"
                  language="bash"
                />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Apply Production Migration
                </h4>
                <CodeBlock code="npx prisma migrate deploy" language="bash" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Reset Development Database
                </h4>
                <CodeBlock code="npx prisma migrate reset" language="bash" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <BackToDocsButton />
    </DocsContainer>
  );
}
