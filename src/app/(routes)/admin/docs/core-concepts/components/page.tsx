"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s<PERSON><PERSON><PERSON>,
  TableOfContents,
  CollapsibleSection,
  CodeBlock,
  InfoBox,
  StatusBadge,
  BackToDocsButton,
} from "@/components/docs";
import {
  Box,
  FileText,
  User,
  Image,
  Layout,
  MessageSquare,
  Star,
  ShoppingCart,
  AlertCircle,
  CheckCircle,
  Search,
  Filter,
  Upload,
  Eye,
  Settings,
} from "lucide-react";

interface ComponentSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  components: {
    name: string;
    description: string;
    location: string;
    props?: string[];
    example?: string;
    status: "stable" | "beta" | "deprecated";
  }[];
}

function ComponentSection({
  title,
  description,
  icon,
  components,
}: ComponentSectionProps) {
  return (
    <CollapsibleSection title={title} defaultOpen={true}>
      <div className="mb-4 flex items-center gap-3">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="space-y-4">
        {components.map((component, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg p-4 bg-gray-50"
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-gray-900">{component.name}</h4>
              <StatusBadge
                status={
                  component.status === "stable"
                    ? "complete"
                    : component.status === "beta"
                      ? "in-progress"
                      : "placeholder"
                }
                size="sm"
              />
            </div>

            <p className="text-sm text-gray-600 mb-2">
              {component.description}
            </p>

            <div className="text-xs text-gray-500 mb-3">
              <strong>Location:</strong>{" "}
              <code className="bg-gray-100 px-1 rounded">
                {component.location}
              </code>
            </div>

            {component.props && (
              <div className="mb-3">
                <h5 className="text-sm font-medium text-gray-700 mb-1">
                  Key Props:
                </h5>
                <div className="flex flex-wrap gap-1">
                  {component.props.map((prop, i) => (
                    <span
                      key={i}
                      className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                    >
                      {prop}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {component.example && (
              <details className="mt-3">
                <summary className="text-sm font-medium text-blue-600 cursor-pointer hover:text-blue-800">
                  View Example Usage
                </summary>
                <div className="mt-2">
                  <CodeBlock code={component.example} language="tsx" />
                </div>
              </details>
            )}
          </div>
        ))}
      </div>
    </CollapsibleSection>
  );
}

export default function ComponentLibraryPage() {
  const tocItems = [
    { id: "overview", title: "Overview", level: 1 },
    { id: "ui-components", title: "UI Components", level: 1 },
    { id: "form-components", title: "Form Components", level: 1 },
    { id: "layout-components", title: "Layout Components", level: 1 },
    { id: "product-components", title: "Product Components", level: 1 },
    { id: "review-components", title: "Review Components", level: 1 },
    { id: "admin-components", title: "Admin Components", level: 1 },
    { id: "utility-components", title: "Utility Components", level: 1 },
    { id: "best-practices", title: "Best Practices", level: 1 },
  ];

  const uiComponents = [
    {
      name: "LoadingSpinner",
      description:
        "Customizable loading spinner with multiple variants and sizes",
      location: "src/app/components/LoadingSpinner.tsx",
      props: ["size", "variant", "className"],
      status: "stable" as const,
      example: `import { LoadingSpinner } from '@/components/LoadingSpinner';

<LoadingSpinner size="md" variant="primary" />`,
    },
    {
      name: "DeleteConfirmationDialog",
      description:
        "Modal dialog for confirming delete actions with customizable content",
      location: "src/app/components/DeleteConfirmationDialog.tsx",
      props: ["isOpen", "onClose", "onConfirm", "title", "message"],
      status: "stable" as const,
      example: `import { DeleteConfirmationDialog } from '@/components/DeleteConfirmationDialog';

<DeleteConfirmationDialog
  isOpen={showDialog}
  onClose={() => setShowDialog(false)}
  onConfirm={handleDelete}
  title="Delete Product"
  message="Are you sure you want to delete this product?"
/>`,
    },
    {
      name: "Breadcrumb",
      description:
        "Navigation breadcrumb component with customizable separator",
      location: "src/app/components/Breadcrumb.tsx",
      props: ["items", "separator", "className"],
      status: "stable" as const,
      example: `import { Breadcrumb } from '@/components/Breadcrumb';

<Breadcrumb
  items={[
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: 'Product Name' }
  ]}
/>`,
    },
    {
      name: "DisplayError",
      description:
        "Standardized error display component with retry functionality",
      location: "src/app/components/DisplayError.tsx",
      props: ["error", "onRetry", "title", "showRetry"],
      status: "stable" as const,
    },
  ];

  const formComponents = [
    {
      name: "ImageUpload",
      description: "Drag-and-drop image upload with preview and validation",
      location: "src/app/components/ImageUpload.tsx",
      props: ["onUpload", "maxFiles", "acceptedTypes", "maxSize"],
      status: "stable" as const,
      example: `import { ImageUpload } from '@/components/ImageUpload';

<ImageUpload
  onUpload={handleImageUpload}
  maxFiles={5}
  acceptedTypes={['image/jpeg', 'image/png']}
  maxSize={5242880} // 5MB
/>`,
    },
    {
      name: "LocationPicker",
      description:
        "Interactive map component for selecting geographical locations",
      location: "src/app/components/LocationPicker.tsx",
      props: ["onLocationSelect", "defaultLocation", "zoom"],
      status: "stable" as const,
    },
    {
      name: "OpeningHours",
      description:
        "Business hours input component with day-by-day configuration",
      location: "src/app/components/OpeningHours.tsx",
      props: ["value", "onChange", "format"],
      status: "stable" as const,
    },
    {
      name: "Editor",
      description: "Rich text editor with markdown support and toolbar",
      location: "src/app/components/Editor.tsx",
      props: ["content", "onChange", "placeholder", "toolbar"],
      status: "beta" as const,
    },
  ];

  const layoutComponents = [
    {
      name: "Navbar",
      description:
        "Main navigation component with responsive design and user menu",
      location: "src/app/components/Navbar.tsx",
      props: ["user", "onLogout", "showSearch"],
      status: "stable" as const,
    },
    {
      name: "Footer",
      description:
        "Site footer with links, social media, and company information",
      location: "src/app/components/Footer.tsx",
      props: ["links", "socialMedia", "showNewsletter"],
      status: "stable" as const,
    },
    {
      name: "HeroSection",
      description: "Landing page hero section with call-to-action buttons",
      location: "src/app/components/HeroSection.tsx",
      props: ["title", "subtitle", "ctaButtons", "backgroundImage"],
      status: "stable" as const,
    },
    {
      name: "QuickTabs",
      description: "Tabbed interface component for organizing content sections",
      location: "src/app/components/QuickTabs.tsx",
      props: ["tabs", "activeTab", "onTabChange", "variant"],
      status: "stable" as const,
    },
  ];

  const productComponents = [
    {
      name: "ProductCard",
      description:
        "Standard product display card with image, rating, and actions",
      location: "src/app/components/ProductCard.tsx",
      props: ["product", "showActions", "variant", "onFavorite"],
      status: "stable" as const,
      example: `import { ProductCard } from '@/components/ProductCard';

<ProductCard
  product={productData}
  showActions={true}
  variant="default"
  onFavorite={handleFavorite}
/>`,
    },
    {
      name: "GrandProductCard",
      description:
        "Enhanced product card with detailed information and larger layout",
      location: "src/app/components/GrandProductCard.tsx",
      props: ["product", "showReviews", "showPromotions"],
      status: "stable" as const,
    },
    {
      name: "MiniProductCard",
      description: "Compact product card for lists and grid layouts",
      location: "src/app/components/MiniProductCard.tsx",
      props: ["product", "orientation", "showRating"],
      status: "stable" as const,
    },
    {
      name: "ProductShowcase",
      description: "Featured products carousel with navigation controls",
      location: "src/app/components/ProductShowcase.tsx",
      props: ["products", "autoPlay", "showDots", "slidesToShow"],
      status: "stable" as const,
    },
    {
      name: "FeaturedProducts",
      description:
        "Grid layout for displaying featured products with filtering",
      location: "src/app/components/FeaturedProducts.tsx",
      props: ["products", "filters", "onFilterChange"],
      status: "stable" as const,
    },
  ];

  const reviewComponents = [
    {
      name: "EnhancedTopReviews",
      description:
        "Advanced review display with sorting, filtering, and pagination",
      location: "src/app/components/EnhancedTopReviews.tsx",
      props: ["reviews", "sortBy", "filters", "onSort", "onFilter"],
      status: "stable" as const,
    },
    {
      name: "ExpandedReview",
      description:
        "Detailed review view with full content and interaction options",
      location: "src/app/components/ExpandedReview.tsx",
      props: ["review", "showActions", "onVote", "onReport"],
      status: "stable" as const,
    },
    {
      name: "CommentList",
      description: "Threaded comment system with nested replies and moderation",
      location: "src/app/components/CommentList.tsx",
      props: ["comments", "allowReplies", "maxDepth", "onModerate"],
      status: "stable" as const,
    },
    {
      name: "CommentForm",
      description: "Form component for adding new comments with validation",
      location: "src/app/components/CommentForm.tsx",
      props: ["onSubmit", "placeholder", "maxLength", "showCharCount"],
      status: "stable" as const,
    },
  ];

  const adminComponents = [
    {
      name: "BugReportList",
      description:
        "Admin interface for managing bug reports with status tracking",
      location: "src/app/components/BugReportList.tsx",
      props: ["reports", "onStatusChange", "onAssign", "filters"],
      status: "stable" as const,
    },
    {
      name: "ClaimStatus",
      description: "Product claim management interface with approval workflow",
      location: "src/app/components/ClaimStatus.tsx",
      props: ["claim", "onStatusChange", "onApprove", "onReject"],
      status: "stable" as const,
    },
    {
      name: "EditProductForm",
      description: "Comprehensive product editing form with media upload",
      location: "src/app/components/EditProductForm.tsx",
      props: ["product", "onSave", "onCancel", "sections"],
      status: "stable" as const,
    },
    {
      name: "ManageYourBusiness",
      description: "Business management dashboard with analytics and controls",
      location: "src/app/components/ManageYourBusiness.tsx",
      props: ["business", "analytics", "onUpdate"],
      status: "beta" as const,
    },
  ];

  const utilityComponents = [
    {
      name: "ImageModal",
      description: "Modal for viewing images in full size with navigation",
      location: "src/app/components/ImageModal.tsx",
      props: ["images", "currentIndex", "onClose", "onNext", "onPrevious"],
      status: "stable" as const,
    },
    {
      name: "ImageGallery",
      description: "Responsive image gallery with thumbnail navigation",
      location: "src/app/components/ImageGallery.tsx",
      props: ["images", "layout", "onImageClick", "showThumbnails"],
      status: "stable" as const,
    },
    {
      name: "ArrangeByPanel",
      description: "Sorting and filtering controls for content organization",
      location: "src/app/components/ArrangeByPanel.tsx",
      props: ["options", "currentSort", "onSortChange", "showFilters"],
      status: "stable" as const,
    },
    {
      name: "InstallPwa",
      description: "Progressive Web App installation prompt component",
      location: "src/app/components/InstallPwa.tsx",
      props: ["showPrompt", "onInstall", "onDismiss"],
      status: "beta" as const,
    },
  ];

  return (
    <DocsContainer>
      <DocsHeader
        title="Component Library"
        description="Comprehensive documentation of reusable UI components built with React, TypeScript, and Tailwind CSS"
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Core Concepts", href: "/admin/docs/core-concepts" },
          { label: "Components", href: "/admin/docs/core-concepts/components" },
        ]}
      />

      <TableOfContents items={tocItems} />

      <section id="overview">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>

        <InfoBox type="info" title="Component Architecture">
          <p>
            ReviewIt uses a modular component architecture built on React 18
            with TypeScript for type safety and Tailwind CSS for styling. All
            components follow consistent design patterns and include
            comprehensive prop interfaces for flexibility and reusability.
          </p>
        </InfoBox>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-900">Type Safety</h3>
            </div>
            <p className="text-sm text-blue-800">
              All components include TypeScript interfaces with comprehensive
              prop definitions and JSDoc comments.
            </p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Box className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-900">Reusability</h3>
            </div>
            <p className="text-sm text-green-800">
              Components are designed to be highly reusable with flexible props
              and consistent styling patterns.
            </p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Settings className="h-5 w-5 text-purple-600" />
              <h3 className="font-semibold text-purple-900">Customizable</h3>
            </div>
            <p className="text-sm text-purple-800">
              Tailwind CSS classes enable easy customization while maintaining
              design consistency.
            </p>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Component Categories
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Layout className="h-4 w-4 text-gray-600" />
              <span>UI Components (4)</span>
            </div>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-600" />
              <span>Form Components (4)</span>
            </div>
            <div className="flex items-center gap-2">
              <Box className="h-4 w-4 text-gray-600" />
              <span>Layout Components (4)</span>
            </div>
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4 text-gray-600" />
              <span>Product Components (5)</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-gray-600" />
              <span>Review Components (4)</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-600" />
              <span>Admin Components (4)</span>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-gray-600" />
              <span>Utility Components (4)</span>
            </div>
          </div>
        </div>
      </section>

      <section id="ui-components">
        <ComponentSection
          title="UI Components"
          description="Core user interface components including loaders, dialogs, navigation, and error handling"
          icon={<Layout className="h-6 w-6" />}
          components={uiComponents}
        />
      </section>

      <section id="form-components">
        <ComponentSection
          title="Form Components"
          description="Interactive form elements with validation, file uploads, and specialized input types"
          icon={<FileText className="h-6 w-6" />}
          components={formComponents}
        />
      </section>

      <section id="layout-components">
        <ComponentSection
          title="Layout Components"
          description="Structural components for page layout, navigation, and content organization"
          icon={<Box className="h-6 w-6" />}
          components={layoutComponents}
        />
      </section>

      <section id="product-components">
        <ComponentSection
          title="Product Components"
          description="Components specifically designed for displaying and managing product information"
          icon={<ShoppingCart className="h-6 w-6" />}
          components={productComponents}
        />
      </section>

      <section id="review-components">
        <ComponentSection
          title="Review Components"
          description="Components for displaying reviews, comments, and user-generated content"
          icon={<Star className="h-6 w-6" />}
          components={reviewComponents}
        />
      </section>

      <section id="admin-components">
        <ComponentSection
          title="Admin Components"
          description="Administrative interface components for content management and moderation"
          icon={<User className="h-6 w-6" />}
          components={adminComponents}
        />
      </section>

      <section id="utility-components">
        <ComponentSection
          title="Utility Components"
          description="Helper components for common functionality like image handling and PWA features"
          icon={<Eye className="h-6 w-6" />}
          components={utilityComponents}
        />
      </section>

      <section id="best-practices">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Best Practices
        </h2>

        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-3">
              ✅ Do's
            </h3>
            <ul className="space-y-2 text-green-800">
              <li>• Use TypeScript interfaces for all component props</li>
              <li>
                • Follow the established naming conventions (PascalCase for
                components)
              </li>
              <li>
                • Include comprehensive JSDoc comments for complex components
              </li>
              <li>• Use Tailwind CSS classes for styling consistency</li>
              <li>• Implement proper error boundaries and loading states</li>
              <li>• Test components with different prop combinations</li>
            </ul>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-900 mb-3">
              ❌ Don'ts
            </h3>
            <ul className="space-y-2 text-red-800">
              <li>• Don't use inline styles - prefer Tailwind classes</li>
              <li>• Don't create components without proper TypeScript types</li>
              <li>• Don't duplicate existing component functionality</li>
              <li>• Don't hardcode values that should be configurable props</li>
              <li>• Don't forget to handle edge cases and error states</li>
              <li>
                • Don't create overly complex components - prefer composition
              </li>
            </ul>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              🔧 Development Guidelines
            </h3>
            <div className="space-y-4 text-blue-800">
              <div>
                <h4 className="font-medium mb-2">Component Structure</h4>
                <CodeBlock
                  code={`// Component file structure
interface ComponentProps {
  // Prop definitions with JSDoc
  /** Description of the prop */
  propName: string;
  optionalProp?: boolean;
}

export function Component({ propName, optionalProp = false }: ComponentProps) {
  // Component logic
  return (
    <div className="tailwind-classes">
      {/* Component JSX */}
    </div>
  );
}`}
                  language="tsx"
                />
              </div>

              <div>
                <h4 className="font-medium mb-2">Prop Naming</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Use descriptive names that clearly indicate purpose</li>
                  <li>
                    Boolean props should use "is", "has", "show", or "allow"
                    prefixes
                  </li>
                  <li>
                    Event handlers should use "on" prefix (onClick, onSubmit)
                  </li>
                  <li>Data props should be nouns (user, product, items)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <BackToDocsButton />
    </DocsContainer>
  );
}
