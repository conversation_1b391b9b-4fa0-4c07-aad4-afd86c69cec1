"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s<PERSON><PERSON><PERSON>,
  TableOfContents,
  CollapsibleSection,
  CodeBlock,
  InfoBox,
  StatusBadge,
  BackToDocsButton,
} from "@/components/docs";
import {
  Folder,
  FileText,
  Settings,
  Code,
  Package,
  Globe,
  Database,
  Lock,
  Layers,
  FolderOpen,
  File,
  GitBranch,
  CheckCircle,
  AlertCircle,
  Info,
} from "lucide-react";

interface DirectorySectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  structure: {
    name: string;
    type: "folder" | "file";
    description: string;
    children?: {
      name: string;
      type: "folder" | "file";
      description: string;
      important?: boolean;
    }[];
    important?: boolean;
  }[];
}

function DirectorySection({
  title,
  description,
  icon,
  structure,
}: DirectorySectionProps) {
  const renderStructureItem = (item: any, level: number = 0) => {
    const indent = level * 20;
    const ItemIcon = item.type === "folder" ? FolderOpen : File;

    return (
      <div key={item.name} className="mb-2">
        <div
          className={`flex items-center gap-2 p-2 rounded ${
            item.important
              ? "bg-blue-50 border border-blue-200"
              : "hover:bg-gray-50"
          }`}
          style={{ marginLeft: `${indent}px` }}
        >
          <ItemIcon
            className={`h-4 w-4 ${
              item.type === "folder" ? "text-blue-600" : "text-gray-600"
            }`}
          />
          <span
            className={`font-mono text-sm ${
              item.important ? "font-semibold text-blue-900" : "text-gray-800"
            }`}
          >
            {item.name}
          </span>
          {item.important && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
              Key
            </span>
          )}
        </div>
        <div
          className="text-sm text-gray-600 mb-2"
          style={{ marginLeft: `${indent + 24}px` }}
        >
          {item.description}
        </div>
        {item.children &&
          item.children.map((child: any) =>
            renderStructureItem(child, level + 1),
          )}
      </div>
    );
  };

  return (
    <CollapsibleSection title={title} defaultOpen={true}>
      <div className="mb-4 flex items-center gap-3">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        {structure.map((item) => renderStructureItem(item))}
      </div>
    </CollapsibleSection>
  );
}

export default function ProjectStructurePage() {
  const tocItems = [
    { id: "overview", title: "Overview", level: 1 },
    { id: "root-structure", title: "Root Directory Structure", level: 1 },
    { id: "src-directory", title: "Source Directory (src/)", level: 1 },
    { id: "app-directory", title: "App Directory Structure", level: 1 },
    { id: "components", title: "Components Organization", level: 1 },
    { id: "configuration", title: "Configuration Files", level: 1 },
    { id: "conventions", title: "Naming Conventions", level: 1 },
    { id: "patterns", title: "Code Organization Patterns", level: 1 },
    { id: "best-practices", title: "Best Practices", level: 1 },
  ];

  const rootStructure = [
    {
      name: ".next/",
      type: "folder" as const,
      description: "Next.js build output and cache directory (auto-generated)",
    },
    {
      name: "node_modules/",
      type: "folder" as const,
      description: "NPM package dependencies",
    },
    {
      name: "prisma/",
      type: "folder" as const,
      description: "Database schema and migration files",
      important: true,
      children: [
        {
          name: "schema.prisma",
          type: "file" as const,
          description: "Database schema definition",
          important: true,
        },
        {
          name: "migrations/",
          type: "folder" as const,
          description: "Database migration files",
        },
      ],
    },
    {
      name: "public/",
      type: "folder" as const,
      description: "Static assets served directly by the web server",
      important: true,
      children: [
        {
          name: "images/",
          type: "folder" as const,
          description: "Static image assets",
        },
        {
          name: "icons/",
          type: "folder" as const,
          description: "Icon files and favicons",
        },
        {
          name: "manifest.json",
          type: "file" as const,
          description: "PWA manifest file",
        },
      ],
    },
    {
      name: "src/",
      type: "folder" as const,
      description: "Main source code directory",
      important: true,
    },
    {
      name: "components/",
      type: "folder" as const,
      description: "Reusable UI components library",
      important: true,
    },
    {
      name: "package.json",
      type: "file" as const,
      description: "Project dependencies and scripts",
      important: true,
    },
    {
      name: "next.config.js",
      type: "file" as const,
      description: "Next.js configuration",
      important: true,
    },
    {
      name: "tailwind.config.js",
      type: "file" as const,
      description: "Tailwind CSS configuration",
      important: true,
    },
    {
      name: "tsconfig.json",
      type: "file" as const,
      description: "TypeScript configuration",
      important: true,
    },
  ];

  const srcStructure = [
    {
      name: "app/",
      type: "folder" as const,
      description: "Next.js 13+ App Router directory structure",
      important: true,
      children: [
        {
          name: "(routes)/",
          type: "folder" as const,
          description:
            "Route groups for organization without affecting URL structure",
        },
        {
          name: "admin/",
          type: "folder" as const,
          description: "Admin panel routes and pages",
          important: true,
        },
        {
          name: "api/",
          type: "folder" as const,
          description: "API routes for backend functionality",
          important: true,
        },
        {
          name: "components/",
          type: "folder" as const,
          description: "Page-specific and feature components",
          important: true,
        },
        {
          name: "globals.css",
          type: "file" as const,
          description: "Global CSS styles and Tailwind imports",
        },
        {
          name: "layout.tsx",
          type: "file" as const,
          description: "Root layout component",
          important: true,
        },
        {
          name: "page.tsx",
          type: "file" as const,
          description: "Home page component",
          important: true,
        },
      ],
    },
    {
      name: "components/",
      type: "folder" as const,
      description: "Shared component library and documentation components",
      important: true,
      children: [
        {
          name: "docs/",
          type: "folder" as const,
          description: "Documentation-specific reusable components",
          important: true,
        },
        {
          name: "ui/",
          type: "folder" as const,
          description: "Base UI components from shadcn/ui",
        },
      ],
    },
    {
      name: "lib/",
      type: "folder" as const,
      description: "Utility functions, database connections, and shared logic",
      important: true,
      children: [
        {
          name: "prisma.ts",
          type: "file" as const,
          description: "Prisma client configuration",
          important: true,
        },
        {
          name: "utils.ts",
          type: "file" as const,
          description: "Utility functions and helpers",
        },
        {
          name: "auth.ts",
          type: "file" as const,
          description: "Authentication configuration",
        },
      ],
    },
    {
      name: "types/",
      type: "folder" as const,
      description: "TypeScript type definitions and interfaces",
      important: true,
    },
  ];

  const appDirectoryStructure = [
    {
      name: "(routes)/",
      type: "folder" as const,
      description: "Route groups for public-facing pages",
      children: [
        {
          name: "admin/",
          type: "folder" as const,
          description: "Administrative interface routes",
          children: [
            {
              name: "docs/",
              type: "folder" as const,
              description: "Documentation system",
              important: true,
            },
            {
              name: "dashboard/",
              type: "folder" as const,
              description: "Admin dashboard pages",
            },
          ],
        },
        {
          name: "products/",
          type: "folder" as const,
          description: "Product listing and detail pages",
        },
        {
          name: "reviews/",
          type: "folder" as const,
          description: "Review-related pages",
        },
      ],
    },
    {
      name: "api/",
      type: "folder" as const,
      description: "API route handlers",
      important: true,
      children: [
        {
          name: "products/",
          type: "folder" as const,
          description: "Product-related API endpoints",
        },
        {
          name: "reviews/",
          type: "folder" as const,
          description: "Review management API endpoints",
        },
        {
          name: "users/",
          type: "folder" as const,
          description: "User management API endpoints",
        },
        {
          name: "auth/",
          type: "folder" as const,
          description: "Authentication API endpoints",
        },
      ],
    },
    {
      name: "components/",
      type: "folder" as const,
      description: "Feature-specific components",
      important: true,
      children: [
        {
          name: "ProductCard.tsx",
          type: "file" as const,
          description: "Product display components",
        },
        {
          name: "ReviewSystem.tsx",
          type: "file" as const,
          description: "Review and rating components",
        },
        {
          name: "Navigation.tsx",
          type: "file" as const,
          description: "Navigation components",
        },
      ],
    },
  ];

  const configurationFiles = [
    {
      name: "next.config.js",
      type: "file" as const,
      description:
        "Next.js framework configuration including image domains, redirects, and build settings",
      important: true,
    },
    {
      name: "tailwind.config.js",
      type: "file" as const,
      description:
        "Tailwind CSS configuration with custom colors, fonts, and component settings",
      important: true,
    },
    {
      name: "tsconfig.json",
      type: "file" as const,
      description:
        "TypeScript compiler configuration with path aliases and strict mode settings",
      important: true,
    },
    {
      name: "components.json",
      type: "file" as const,
      description: "shadcn/ui component library configuration",
    },
    {
      name: ".eslintrc.json",
      type: "file" as const,
      description:
        "ESLint configuration for code quality and style enforcement",
    },
    {
      name: "postcss.config.js",
      type: "file" as const,
      description: "PostCSS configuration for CSS processing",
    },
    {
      name: ".env.local",
      type: "file" as const,
      description:
        "Environment variables for local development (not committed to git)",
      important: true,
    },
    {
      name: ".gitignore",
      type: "file" as const,
      description: "Git ignore patterns to exclude files from version control",
    },
  ];

  return (
    <DocsContainer>
      <DocsHeader
        title="Project Structure"
        description="Detailed breakdown of the application architecture, file organization, and development patterns"
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Core Concepts", href: "/admin/docs/core-concepts" },
          {
            label: "Project Structure",
            href: "/admin/docs/core-concepts/project-structure",
          },
        ]}
      />

      <TableOfContents items={tocItems} />

      <section id="overview">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>

        <InfoBox type="info" title="Architecture Overview">
          <p>
            ReviewIt follows Next.js 13+ App Router conventions with a
            well-organized directory structure that separates concerns, promotes
            reusability, and maintains scalability. The project uses TypeScript
            throughout and follows modern React patterns.
          </p>
        </InfoBox>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <Layers className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="font-semibold text-blue-900">Next.js 13+</div>
            <div className="text-sm text-blue-700">App Router</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <Code className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="font-semibold text-green-900">TypeScript</div>
            <div className="text-sm text-green-700">Strict Mode</div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <Package className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="font-semibold text-purple-900">
              Component Library
            </div>
            <div className="text-sm text-purple-700">Reusable UI</div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
            <Database className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="font-semibold text-orange-900">Prisma ORM</div>
            <div className="text-sm text-orange-700">Type-Safe DB</div>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Key Architectural Principles
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                🏗️ Separation of Concerns
              </h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Pages handle routing and layout</li>
                <li>• Components focus on UI rendering</li>
                <li>• API routes manage business logic</li>
                <li>• Utils contain shared functionality</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                🔄 Code Reusability
              </h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Shared component library</li>
                <li>• Common utility functions</li>
                <li>• Consistent type definitions</li>
                <li>• Reusable custom hooks</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">📈 Scalability</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Modular folder structure</li>
                <li>• Feature-based organization</li>
                <li>• Clear naming conventions</li>
                <li>• Minimal coupling between modules</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">🛡️ Type Safety</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• TypeScript throughout the codebase</li>
                <li>• Prisma-generated types</li>
                <li>• Strict compiler settings</li>
                <li>• Interface definitions for props</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section id="root-structure">
        <DirectorySection
          title="Root Directory Structure"
          description="Top-level directories and configuration files that define the project foundation"
          icon={<Folder className="h-6 w-6" />}
          structure={rootStructure}
        />
      </section>

      <section id="src-directory">
        <DirectorySection
          title="Source Directory (src/)"
          description="Main application source code organization with clear separation of concerns"
          icon={<Code className="h-6 w-6" />}
          structure={srcStructure}
        />
      </section>

      <section id="app-directory">
        <DirectorySection
          title="App Directory Structure"
          description="Next.js App Router file-based routing system with route groups and API endpoints"
          icon={<Globe className="h-6 w-6" />}
          structure={appDirectoryStructure}
        />
      </section>

      <section id="components">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Components Organization
        </h2>

        <div className="space-y-6">
          <InfoBox type="success" title="Component Architecture">
            <p>
              Components are organized into multiple directories based on their
              scope and reusability. This approach promotes code reuse while
              maintaining clear boundaries between different types of
              components.
            </p>
          </InfoBox>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Global Components
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Package className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-gray-900">
                      components/ui/
                    </div>
                    <div className="text-sm text-gray-600">
                      Base UI components from shadcn/ui library
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-gray-900">
                      components/docs/
                    </div>
                    <div className="text-sm text-gray-600">
                      Documentation-specific reusable components
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Feature Components
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Layers className="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-gray-900">
                      src/app/components/
                    </div>
                    <div className="text-sm text-gray-600">
                      Feature-specific components and business logic
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <FolderOpen className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-gray-900">
                      Page Components
                    </div>
                    <div className="text-sm text-gray-600">
                      Components specific to individual pages or routes
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              Component Naming Patterns
            </h3>
            <div className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-900 mb-2">
                    File Naming
                  </h4>
                  <ul className="space-y-1 text-blue-800">
                    <li>• PascalCase for component files</li>
                    <li>• Descriptive names (ProductCard.tsx)</li>
                    <li>• Feature prefix when needed</li>
                    <li>• Index files for directories</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-2">
                    Component Structure
                  </h4>
                  <ul className="space-y-1 text-blue-800">
                    <li>• Interface definitions first</li>
                    <li>• Default export for main component</li>
                    <li>• Named exports for sub-components</li>
                    <li>• Props documentation with JSDoc</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="configuration">
        <DirectorySection
          title="Configuration Files"
          description="Project configuration files that control build processes, development tools, and deployment settings"
          icon={<Settings className="h-6 w-6" />}
          structure={configurationFiles}
        />
      </section>

      <section id="conventions">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Naming Conventions
        </h2>

        <div className="space-y-6">
          <InfoBox type="info" title="Consistent Naming">
            <p>
              Consistent naming conventions improve code readability,
              maintainability, and developer experience. These conventions are
              enforced through ESLint rules and code review processes.
            </p>
          </InfoBox>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                File & Directory Names
              </h3>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-green-50 rounded">
                  <div className="font-medium text-green-900">✅ Correct</div>
                  <div className="font-mono text-green-700">
                    ProductCard.tsx
                  </div>
                  <div className="font-mono text-green-700">
                    user-management/
                  </div>
                  <div className="font-mono text-green-700">api/products/</div>
                </div>
                <div className="p-3 bg-red-50 rounded">
                  <div className="font-medium text-red-900">❌ Incorrect</div>
                  <div className="font-mono text-red-700">productcard.tsx</div>
                  <div className="font-mono text-red-700">UserManagement/</div>
                  <div className="font-mono text-red-700">API/Products/</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Variable & Function Names
              </h3>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-green-50 rounded">
                  <div className="font-medium text-green-900">✅ Correct</div>
                  <div className="font-mono text-green-700">
                    const userProfile = ...
                  </div>
                  <div className="font-mono text-green-700">
                    function handleSubmit() ...
                  </div>
                  <div className="font-mono text-green-700">
                    const API_BASE_URL = ...
                  </div>
                </div>
                <div className="p-3 bg-red-50 rounded">
                  <div className="font-medium text-red-900">❌ Incorrect</div>
                  <div className="font-mono text-red-700">
                    const UserProfile = ...
                  </div>
                  <div className="font-mono text-red-700">
                    function HandleSubmit() ...
                  </div>
                  <div className="font-mono text-red-700">
                    const api_base_url = ...
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Naming Reference Guide
            </h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left p-3 font-medium text-gray-900">
                      Type
                    </th>
                    <th className="text-left p-3 font-medium text-gray-900">
                      Convention
                    </th>
                    <th className="text-left p-3 font-medium text-gray-900">
                      Example
                    </th>
                    <th className="text-left p-3 font-medium text-gray-900">
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Components</td>
                    <td className="p-3 font-mono">PascalCase</td>
                    <td className="p-3 font-mono text-blue-600">ProductCard</td>
                    <td className="p-3 text-gray-600">
                      React components and classes
                    </td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Functions</td>
                    <td className="p-3 font-mono">camelCase</td>
                    <td className="p-3 font-mono text-green-600">
                      handleSubmit
                    </td>
                    <td className="p-3 text-gray-600">Functions and methods</td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Variables</td>
                    <td className="p-3 font-mono">camelCase</td>
                    <td className="p-3 font-mono text-purple-600">
                      userProfile
                    </td>
                    <td className="p-3 text-gray-600">
                      Variables and properties
                    </td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Constants</td>
                    <td className="p-3 font-mono">UPPER_SNAKE_CASE</td>
                    <td className="p-3 font-mono text-orange-600">
                      API_BASE_URL
                    </td>
                    <td className="p-3 text-gray-600">Global constants</td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Files</td>
                    <td className="p-3 font-mono">PascalCase</td>
                    <td className="p-3 font-mono text-red-600">
                      ProductCard.tsx
                    </td>
                    <td className="p-3 text-gray-600">Component files</td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="p-3 font-medium">Directories</td>
                    <td className="p-3 font-mono">kebab-case</td>
                    <td className="p-3 font-mono text-indigo-600">
                      user-management
                    </td>
                    <td className="p-3 text-gray-600">Folder names</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      <section id="patterns">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Code Organization Patterns
        </h2>

        <div className="space-y-6">
          <InfoBox type="success" title="Proven Patterns">
            <p>
              The project follows established patterns for code organization
              that promote maintainability, testability, and developer
              productivity. These patterns are consistently applied throughout
              the codebase.
            </p>
          </InfoBox>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Feature-Based Organization
              </h3>
              <p className="text-gray-600 mb-4">
                Related functionality is grouped together by feature rather than
                by technical layer.
              </p>
              <CodeBlock
                code={`src/app/
├── products/
│   ├── page.tsx              # Product listing page
│   ├── [id]/
│   │   └── page.tsx          # Product detail page
│   └── components/
│       ├── ProductGrid.tsx   # Product-specific components
│       └── ProductFilter.tsx
├── reviews/
│   ├── page.tsx
│   └── components/
│       ├── ReviewForm.tsx
│       └── ReviewList.tsx
└── api/
    ├── products/
    │   └── route.ts
    └── reviews/
        └── route.ts`}
                language="text"
              />
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Component Co-location
              </h3>
              <p className="text-gray-600 mb-4">
                Components are placed close to where they are used, with shared
                components extracted to common directories.
              </p>
              <CodeBlock
                code={`// Page-specific components
src/app/products/components/
├── ProductGrid.tsx
├── ProductFilter.tsx
└── ProductSearch.tsx

// Shared components
src/components/
├── ui/
│   ├── Button.tsx
│   ├── Modal.tsx
│   └── Input.tsx
└── docs/
    ├── DocsHeader.tsx
    └── CodeBlock.tsx`}
                language="text"
              />
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                API Route Organization
              </h3>
              <p className="text-gray-600 mb-4">
                API routes follow RESTful conventions with nested resources and
                proper HTTP methods.
              </p>
              <CodeBlock
                code={`src/app/api/
├── products/
│   ├── route.ts              # GET /api/products, POST /api/products
│   ├── [id]/
│   │   ├── route.ts          # GET /api/products/[id], PUT /api/products/[id]
│   │   └── reviews/
│   │       └── route.ts      # GET /api/products/[id]/reviews
│   └── search/
│       └── route.ts          # GET /api/products/search
├── reviews/
│   ├── route.ts              # GET /api/reviews, POST /api/reviews
│   └── [id]/
│       └── route.ts          # GET /api/reviews/[id], PUT /api/reviews/[id]
└── users/
    ├── route.ts              # GET /api/users
    └── [id]/
        └── route.ts          # GET /api/users/[id]`}
                language="text"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="best-practices">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Best Practices
        </h2>

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-3">
                ✅ Do's
              </h3>
              <ul className="space-y-2 text-green-800 text-sm">
                <li>• Use TypeScript interfaces for all component props</li>
                <li>• Follow the established directory structure</li>
                <li>• Keep components focused and single-purpose</li>
                <li>• Use descriptive names for files and functions</li>
                <li>• Co-locate related files when possible</li>
                <li>• Export components from index files</li>
                <li>• Use absolute imports with path aliases</li>
                <li>• Document complex components with JSDoc</li>
              </ul>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-red-900 mb-3">
                ❌ Don'ts
              </h3>
              <ul className="space-y-2 text-red-800 text-sm">
                <li>• Don't create deeply nested directory structures</li>
                <li>• Don't use relative imports for distant files</li>
                <li>• Don't mix different naming conventions</li>
                <li>• Don't create overly generic component names</li>
                <li>• Don't place all components in a single directory</li>
                <li>• Don't ignore TypeScript errors</li>
                <li>• Don't create circular dependencies</li>
                <li>• Don't skip proper error boundaries</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              🔧 Development Guidelines
            </h3>
            <div className="space-y-4 text-blue-800">
              <div>
                <h4 className="font-medium mb-2">File Organization</h4>
                <ul className="text-sm space-y-1">
                  <li>• Keep related files together in feature directories</li>
                  <li>• Use index files to create clean import paths</li>
                  <li>• Separate concerns: components, utilities, types</li>
                  <li>• Follow the principle of least surprise</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Import Organization</h4>
                <CodeBlock
                  code={`// 1. External library imports
import React from 'react';
import { NextPage } from 'next';

// 2. Internal imports (absolute paths)
import { Button } from '@/components/ui/button';
import { ProductCard } from '@/components/ProductCard';

// 3. Relative imports (same directory)
import './styles.css';
import { ProductType } from './types';`}
                  language="tsx"
                />
              </div>

              <div>
                <h4 className="font-medium mb-2">Component Structure</h4>
                <CodeBlock
                  code={`// Component file structure
interface ComponentProps {
  /** Description of the prop */
  title: string;
  /** Optional prop with default */
  showActions?: boolean;
}

export function Component({ title, showActions = true }: ComponentProps) {
  // Component logic here

  return (
    <div>
      <h1>{title}</h1>
      {showActions && (
        <div>
          {/* Action buttons */}
        </div>
      )}
    </div>
  );
}

export default Component;`}
                  language="tsx"
                />
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">
              ⚠️ Common Pitfalls
            </h3>
            <div className="space-y-3 text-yellow-800 text-sm">
              <div>
                <h4 className="font-medium">Deep Nesting</h4>
                <p>
                  Avoid creating deeply nested directory structures that make
                  imports difficult.
                </p>
              </div>
              <div>
                <h4 className="font-medium">Barrel Exports</h4>
                <p>
                  Use index files judiciously - too many can create circular
                  dependencies.
                </p>
              </div>
              <div>
                <h4 className="font-medium">Monolithic Components</h4>
                <p>Break down large components into smaller, focused pieces.</p>
              </div>
              <div>
                <h4 className="font-medium">Inconsistent Naming</h4>
                <p>
                  Stick to established naming conventions throughout the
                  project.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              📋 Project Structure Checklist
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Before Adding New Features
                </h4>
                <ul className="space-y-1 text-gray-700">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Plan the directory structure</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Identify reusable components</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Define TypeScript interfaces</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Consider API route organization</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Code Review Checklist
                </h4>
                <ul className="space-y-1 text-gray-700">
                  <li className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span>Consistent naming conventions</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span>Proper file organization</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span>TypeScript compliance</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span>Component reusability</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <BackToDocsButton />
    </DocsContainer>
  );
}
