"use client";

import React from "react";
import {
  Home,
  User,
  Search,
  Star,
  ShoppingBag,
  MessageCircle,
  FileText,
  Shield,
  Info,
  MapPin,
  Bell,
  Edit3,
  Eye,
  UserCheck,
  ArrowRight,
  Globe,
  Heart,
  TrendingUp,
  Filter,
  Share2,
  Bookmark,
} from "lucide-react";

interface RouteCardProps {
  title: string;
  path: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  userTypes: string[];
  authRequired: boolean;
}

function RouteCard({
  title,
  path,
  description,
  icon,
  features,
  userTypes,
  authRequired,
}: RouteCardProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <span className="text-sm text-gray-500 font-mono">{path}</span>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          {authRequired && (
            <span className="inline-block px-2 py-1 text-xs bg-amber-100 text-amber-800 rounded border border-amber-200">
              Auth Required
            </span>
          )}
        </div>
      </div>

      <p className="text-gray-600 mb-4">{description}</p>

      <div className="space-y-3">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Key Features:
          </h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center gap-2"
              >
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Available to:
          </h4>
          <div className="flex flex-wrap gap-1">
            {userTypes.map((type, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded"
              >
                {type}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

interface FeatureSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  routes: RouteCardProps[];
}

function FeatureSection({
  title,
  description,
  icon,
  routes,
}: FeatureSectionProps) {
  return (
    <section className="mb-12">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {routes.map((route, index) => (
          <RouteCard key={index} {...route} />
        ))}
      </div>
    </section>
  );
}

export default function UserFacingPage() {
  const coreRoutes: RouteCardProps[] = [
    {
      title: "Home Page",
      path: "/",
      description:
        "The main landing page featuring hero section, recent reviews, top reviewers, and company categories.",
      icon: <Home className="h-5 w-5" />,
      features: [
        "Hero section with search functionality",
        "Recent reviews showcase",
        "Top reviewers leaderboard",
        "Company categories navigation",
        "Quick access to write reviews",
        "Value proposition display",
        "Featured content sections",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Browse & Discovery",
      path: "/browse",
      description:
        "Comprehensive browsing interface for discovering products and businesses across different categories.",
      icon: <Globe className="h-5 w-5" />,
      features: [
        "Category-based filtering",
        "Advanced search options",
        "Sorting by ratings, popularity, date",
        "Grid and list view options",
        "Pagination and infinite scroll",
        "Location-based filtering",
        "Popular products highlighting",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Search",
      path: "/search",
      description:
        "Advanced search functionality with filters, sorting options, and comprehensive results display.",
      icon: <Search className="h-5 w-5" />,
      features: [
        "Real-time search suggestions",
        "Multi-criteria filtering",
        "Location-based search",
        "Category filtering",
        "Rating range filters",
        "Sort by relevance, rating, date",
        "Search history (authenticated users)",
        "Advanced boolean search operators",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Product Details",
      path: "/product/[id]",
      description:
        "Detailed product pages with comprehensive information, reviews, ratings, and interaction options.",
      icon: <ShoppingBag className="h-5 w-5" />,
      features: [
        "Complete product information",
        "Review and rating display",
        "Photo galleries and media",
        "Business owner information",
        "Related products suggestions",
        "Share functionality",
        "Report inappropriate content",
        "Bookmark/save for later",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
  ];

  const reviewRoutes: RouteCardProps[] = [
    {
      title: "Write Review",
      path: "/write-review",
      description:
        "Comprehensive review submission form with rating system, photo uploads, and detailed feedback options.",
      icon: <Edit3 className="h-5 w-5" />,
      features: [
        "Multi-step review form",
        "5-star rating system",
        "Photo and media uploads",
        "Detailed review categories",
        "Anonymous review option",
        "Review preview before submission",
        "Tag and categorize reviews",
        "Auto-save draft functionality",
      ],
      userTypes: ["Authenticated Users"],
      authRequired: true,
    },
    {
      title: "Reviews Explorer",
      path: "/reviews-explore",
      description:
        "Explore and discover reviews across the platform with advanced filtering and sorting capabilities.",
      icon: <Eye className="h-5 w-5" />,
      features: [
        "Filter by rating, date, category",
        "Sort by helpfulness, date, rating",
        "Search within reviews",
        "View review photos and media",
        "Like and share reviews",
        "Follow reviewers",
        "Report inappropriate reviews",
        "Review analytics and trends",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "My Reviews",
      path: "/reviews",
      description:
        "Personal dashboard for managing submitted reviews, tracking engagement, and viewing review analytics.",
      icon: <Star className="h-5 w-5" />,
      features: [
        "View all submitted reviews",
        "Edit and update reviews",
        "Track review engagement",
        "View review statistics",
        "Manage review visibility",
        "Delete reviews",
        "Export review data",
        "Review performance metrics",
      ],
      userTypes: ["Authenticated Users"],
      authRequired: true,
    },
  ];

  const profileRoutes: RouteCardProps[] = [
    {
      title: "User Profile",
      path: "/userprofile",
      description:
        "Personal profile management with account settings, review history, and activity tracking.",
      icon: <User className="h-5 w-5" />,
      features: [
        "Profile information management",
        "Review history and statistics",
        "Account settings and preferences",
        "Privacy controls",
        "Activity timeline",
        "Achievement badges",
        "Profile customization",
        "Social connections",
      ],
      userTypes: ["Authenticated Users"],
      authRequired: true,
    },
    {
      title: "Public Profile",
      path: "/userprofile/[id]",
      description:
        "Public-facing user profiles showcasing reviews, ratings, and public activity.",
      icon: <UserCheck className="h-5 w-5" />,
      features: [
        "Public review showcase",
        "User statistics and metrics",
        "Review credibility indicators",
        "Follow/unfollow functionality",
        "Share profile functionality",
        "Report user option",
        "View user's favorite categories",
        "Review interaction history",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Notifications",
      path: "/notifications",
      description:
        "Notification center for review responses, likes, follows, and system updates.",
      icon: <Bell className="h-5 w-5" />,
      features: [
        "Review response notifications",
        "Like and engagement alerts",
        "Follow notifications",
        "System announcements",
        "Promotional notifications",
        "Notification preferences",
        "Mark as read/unread",
        "Bulk notification actions",
      ],
      userTypes: ["Authenticated Users"],
      authRequired: true,
    },
  ];

  const businessRoutes: RouteCardProps[] = [
    {
      title: "Business Listing",
      path: "/business",
      description:
        "Business directory and discovery page for finding local businesses and services.",
      icon: <ShoppingBag className="h-5 w-5" />,
      features: [
        "Business directory browsing",
        "Location-based search",
        "Category filtering",
        "Business ratings and reviews",
        "Contact information display",
        "Business hours and details",
        "Map integration",
        "Save favorite businesses",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Claim Product/Business",
      path: "/claim-product",
      description:
        "Interface for business owners to claim their products and businesses on the platform.",
      icon: <Shield className="h-5 w-5" />,
      features: [
        "Business verification process",
        "Ownership documentation upload",
        "Claim status tracking",
        "Business information management",
        "Contact verification",
        "Legal documentation handling",
        "Multi-step claim process",
        "Admin review workflow",
      ],
      userTypes: ["Business Owners", "Authenticated Users"],
      authRequired: true,
    },
    {
      title: "My Businesses",
      path: "/mybusinesses",
      description:
        "Dashboard for managing owned businesses, tracking performance, and handling customer interactions.",
      icon: <TrendingUp className="h-5 w-5" />,
      features: [
        "Business performance dashboard",
        "Review management tools",
        "Customer interaction tracking",
        "Business information updates",
        "Analytics and insights",
        "Response to reviews",
        "Business promotion tools",
        "Multi-business management",
      ],
      userTypes: ["Business Owners"],
      authRequired: true,
    },
    {
      title: "Edit Product",
      path: "/editproduct",
      description:
        "Product information management interface for business owners to update their listings.",
      icon: <Edit3 className="h-5 w-5" />,
      features: [
        "Product information editing",
        "Photo and media management",
        "Category and tag updates",
        "Pricing information updates",
        "Product description editing",
        "Availability status management",
        "SEO optimization tools",
        "Change history tracking",
      ],
      userTypes: ["Business Owners", "Product Managers"],
      authRequired: true,
    },
  ];

  const utilityRoutes: RouteCardProps[] = [
    {
      title: "Submit Content",
      path: "/submit",
      description:
        "General content submission interface for products, businesses, and user-generated content.",
      icon: <FileText className="h-5 w-5" />,
      features: [
        "Multi-type content submission",
        "Form validation and guidance",
        "Media upload capabilities",
        "Draft saving functionality",
        "Submission status tracking",
        "Content guidelines display",
        "Preview before submission",
        "Batch submission options",
      ],
      userTypes: ["Authenticated Users"],
      authRequired: true,
    },
    {
      title: "Bug Reports",
      path: "/bug-reports/new",
      description:
        "User-friendly bug reporting system for platform issues and feature requests.",
      icon: <MessageCircle className="h-5 w-5" />,
      features: [
        "Detailed bug reporting form",
        "Screenshot and media uploads",
        "Steps to reproduce tracking",
        "Priority level assignment",
        "Category selection",
        "Status tracking",
        "Admin response system",
        "Duplicate detection",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Location Services",
      path: "/location",
      description:
        "Location-based services and geographic content discovery interface.",
      icon: <MapPin className="h-5 w-5" />,
      features: [
        "Location detection and selection",
        "Geographic content filtering",
        "Map-based business discovery",
        "Distance-based sorting",
        "Regional content preferences",
        "Location history",
        "Nearby recommendations",
        "Geographic analytics",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
  ];

  const staticRoutes: RouteCardProps[] = [
    {
      title: "About Us",
      path: "/about",
      description:
        "Information about the platform, mission, team, and company values.",
      icon: <Info className="h-5 w-5" />,
      features: [
        "Company mission and vision",
        "Team member profiles",
        "Platform statistics",
        "History and milestones",
        "Contact information",
        "Press and media resources",
        "Career opportunities",
        "Partnership information",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Verification Process",
      path: "/about/verification",
      description:
        "Detailed information about the platform's verification processes for users and businesses.",
      icon: <Shield className="h-5 w-5" />,
      features: [
        "Verification process explanation",
        "Requirements and criteria",
        "Benefits of verification",
        "Step-by-step guides",
        "FAQ and troubleshooting",
        "Contact support options",
        "Verification badges explanation",
        "Business vs. user verification",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Privacy Policy",
      path: "/privacy",
      description:
        "Comprehensive privacy policy detailing data collection, usage, and user rights.",
      icon: <Shield className="h-5 w-5" />,
      features: [
        "Data collection policies",
        "Usage and sharing practices",
        "User rights and controls",
        "Cookie and tracking policies",
        "Third-party integrations",
        "Data retention policies",
        "Contact for privacy concerns",
        "Policy update notifications",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
    {
      title: "Terms of Service",
      path: "/tos",
      description:
        "Platform terms of service, user agreements, and usage guidelines.",
      icon: <FileText className="h-5 w-5" />,
      features: [
        "User agreement terms",
        "Platform usage guidelines",
        "Prohibited activities",
        "Account responsibilities",
        "Content ownership rights",
        "Dispute resolution process",
        "Service availability terms",
        "Terms update notifications",
      ],
      userTypes: ["All Users", "Guests"],
      authRequired: false,
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* Header Section */}
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          User-Facing Routes Documentation
        </h1>
        <p className="text-lg text-gray-600 max-w-4xl mx-auto">
          Comprehensive documentation for all public-facing routes and user
          interfaces in the ReviewIt platform. This guide covers functionality,
          features, and user experience for each route.
        </p>
      </div>

      {/* Quick Navigation */}
      <div className="mb-12 bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Navigation
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 text-sm">
          <a
            href="#core-routes"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Home className="h-4 w-4" />
            Core Routes
          </a>
          <a
            href="#review-system"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Star className="h-4 w-4" />
            Review System
          </a>
          <a
            href="#user-profiles"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <User className="h-4 w-4" />
            User Profiles
          </a>
          <a
            href="#business-routes"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <ShoppingBag className="h-4 w-4" />
            Business Routes
          </a>
          <a
            href="#utility-routes"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <FileText className="h-4 w-4" />
            Utility Routes
          </a>
          <a
            href="#static-pages"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Info className="h-4 w-4" />
            Static Pages
          </a>
        </div>
      </div>

      {/* Overview Section */}
      <div className="mb-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-blue-900 mb-4">
          Platform Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Globe className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-900">Public Access</h3>
            </div>
            <p className="text-sm text-blue-800">
              Most routes are accessible to all users without authentication,
              promoting open discovery and community engagement.
            </p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-900">Authentication</h3>
            </div>
            <p className="text-sm text-green-800">
              User-specific features require authentication, providing
              personalized experiences and account management.
            </p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <h3 className="font-semibold text-purple-900">Business Tools</h3>
            </div>
            <p className="text-sm text-purple-800">
              Dedicated business owner interfaces for managing products,
              reviews, and customer interactions.
            </p>
          </div>
        </div>
      </div>

      {/* Core Routes Section */}
      <div id="core-routes">
        <FeatureSection
          title="Core Routes"
          description="Essential platform routes providing primary user functionality and content discovery."
          icon={<Home className="h-6 w-6" />}
          routes={coreRoutes}
        />
      </div>

      {/* Review System Section */}
      <div id="review-system">
        <FeatureSection
          title="Review System"
          description="Comprehensive review management system for writing, exploring, and managing user reviews."
          icon={<Star className="h-6 w-6" />}
          routes={reviewRoutes}
        />
      </div>

      {/* User Profile Section */}
      <div id="user-profiles">
        <FeatureSection
          title="User Profiles & Social Features"
          description="User profile management, social interactions, and community engagement features."
          icon={<User className="h-6 w-6" />}
          routes={profileRoutes}
        />
      </div>

      {/* Business Routes Section */}
      <div id="business-routes">
        <FeatureSection
          title="Business & Commerce Routes"
          description="Business discovery, management, and owner-specific functionality for product and service providers."
          icon={<ShoppingBag className="h-6 w-6" />}
          routes={businessRoutes}
        />
      </div>

      {/* Utility Routes Section */}
      <div id="utility-routes">
        <FeatureSection
          title="Utility & Support Routes"
          description="Supporting functionality including content submission, bug reporting, and location services."
          icon={<FileText className="h-6 w-6" />}
          routes={utilityRoutes}
        />
      </div>

      {/* Static Pages Section */}
      <div id="static-pages">
        <FeatureSection
          title="Information & Legal Pages"
          description="Static informational pages providing platform details, policies, and legal information."
          icon={<Info className="h-6 w-6" />}
          routes={staticRoutes}
        />
      </div>

      {/* User Experience Guidelines */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          User Experience Guidelines
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              Design Principles
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                • <strong>User-First Design:</strong> Intuitive navigation and
                clear information hierarchy
              </li>
              <li>
                • <strong>Accessibility:</strong> WCAG compliant with keyboard
                navigation and screen reader support
              </li>
              <li>
                • <strong>Mobile Responsive:</strong> Optimized for all device
                sizes and touch interfaces
              </li>
              <li>
                • <strong>Performance:</strong> Fast loading times with
                optimized images and caching
              </li>
              <li>
                • <strong>Consistency:</strong> Unified design language across
                all routes
              </li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-500" />
              Security & Privacy
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                • <strong>Data Protection:</strong> GDPR compliant with user
                data controls
              </li>
              <li>
                • <strong>Authentication:</strong> Secure login with Clerk
                authentication service
              </li>
              <li>
                • <strong>Content Moderation:</strong> Automated and manual
                review systems
              </li>
              <li>
                • <strong>Privacy Controls:</strong> Granular privacy settings
                for user content
              </li>
              <li>
                • <strong>Secure Communications:</strong> HTTPS encryption for
                all data transmission
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Technical Implementation */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Technical Implementation
        </h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Frontend Technologies
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Next.js 14 with App Router</li>
                <li>• React 18 with Server Components</li>
                <li>• TypeScript for type safety</li>
                <li>• Tailwind CSS for styling</li>
                <li>• Lucide React for icons</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Authentication & Security
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Clerk for authentication</li>
                <li>• JWT tokens for sessions</li>
                <li>• Role-based access control</li>
                <li>• CSRF protection</li>
                <li>• Rate limiting</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Performance Features
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Server-side rendering (SSR)</li>
                <li>• Static site generation (SSG)</li>
                <li>• Image optimization</li>
                <li>• Code splitting</li>
                <li>• Redis caching</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <a
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          ← Back to Documentation Home
        </a>
      </div>
    </div>
  );
}
