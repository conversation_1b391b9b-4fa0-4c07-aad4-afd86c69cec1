"use client";

import React from "react";

import Link from "next/link";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function AttentionAnalyticsDoc() {
  const aggregationQuerySnippet = `SELECT
  productId,
  AVG(depthPct)   AS avgScrollDepth,
  AVG(durationMs) AS avgDuration,
  AVG(depthPct) * AVG(durationMs) AS attentionScore
FROM ProductAttention
GROUP BY productId
ORDER BY attentionScore DESC
LIMIT $1;`;

  const hookSnippet = `const { data } = useQuery({
  queryKey: ["adminTopAttention", limit],
  queryFn: () => fetch(\`/api/analytics/top-attention?limit=\${limit}\`).then(r => r.json()),
});`;

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-8 py-10 space-y-10">
      <header className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Attention Analytics (Admin)</h1>
        <p className="text-gray-600">
          Technical overview of how <strong>scroll-depth</strong> and <strong>engagement duration</strong> events are
          collected, processed, cached, and surfaced in the admin dashboard.
        </p>
      </header>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">Event Collection</h2>
        <ul className="list-disc list-inside text-gray-700 space-y-1">
          <li>Client JS listens to <code>scroll</code> and <code>visibilitychange</code> events.</li>
          <li>Payload: <code>{'{ productId, depthPct, durationMs }'}</code> batched every 5&nbsp;s.</li>
          <li>Endpoint: <code className="text-blue-600">POST /api/events/attention</code>.</li>
        </ul>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">Aggregation Query</h2>
        <pre className="overflow-auto rounded-md bg-gray-100 p-4 text-sm text-gray-800">
{aggregationQuerySnippet}
        </pre>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">Caching</h2>
        <p className="text-gray-700">
          Results are cached in Redis for 5 minutes using the helper
          <code className="px-1 py-0.5 bg-slate-200 rounded">getTopAttentionProductsFromCache</code> with key:
        </p>
        <pre className="bg-gray-100 p-3 rounded-md text-sm text-gray-800">attention:top:{"{limit}"}</pre>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">API Usage</h2>
        <Card>
          <CardHeader>
            <CardTitle>GET /api/analytics/top-attention</CardTitle>
            <CardDescription>Admin-only endpoint</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p><strong>Query params</strong>: <code>?limit=5</code> (default 5)</p>
            <p><strong>Response</strong>:</p>
            <pre className="bg-gray-50 p-3 rounded-md overflow-auto">
{`[
  {
    "productId": "abc123",
    "name": "Premium Headphones",
    "avgScrollDepth": 0.82,
    "avgDuration": 15400,
    "attentionScore": 12628
  },
  ...
]`}
            </pre>
          </CardContent>
        </Card>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">Frontend Hook</h2>
        <pre className="bg-gray-100 p-3 rounded-md text-sm text-gray-800 overflow-auto">
{hookSnippet}
        </pre>
      </section>

      <Button asChild>
        <Link href="/admin/docs">← Back to Docs</Link>
      </Button>
    </div>
  );
}
