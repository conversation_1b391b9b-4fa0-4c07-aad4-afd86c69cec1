"use client";

import { Shield, Alert<PERSON>riangle, FileText, Code, Database, CheckCircle, XCircle, Clock } from "lucide-react";
import Link from "next/link";

export default function UserStatusEnforcementDocumentation() {
  return (
    <div className="docs-container">
      <div className="docs-content">
        <div className="prose max-w-none">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              User Status Enforcement System
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive documentation for enforcing user status restrictions across the ReviewIt platform.
            </p>
          </div>

          <div className="intro-section mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-start gap-4">
                <Shield className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h2 className="text-xl font-semibold text-blue-900 mb-2">
                    Overview
                  </h2>
                  <p className="text-blue-800 mb-4">
                    The User Status Enforcement System provides a comprehensive approach to restricting access for banned, suspended, and deleted users across the ReviewIt platform. 
                    It ensures consistent enforcement of user status restrictions at multiple levels of the application.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🛡️ Security Focus
                      </div>
                      <div className="text-blue-700">
                        Enforce user status restrictions and prevent unauthorized access
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🔄 Multiple Layers
                      </div>
                      <div className="text-blue-700">
                        Enforcement at middleware, layout, and component levels
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        📊 Audit Trail
                      </div>
                      <div className="text-blue-700">
                        Integration with security logs for access attempt tracking
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Code className="mr-2 h-6 w-6 text-blue-600" />
              Core Components
            </h2>
            
            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                checkUserSecureAccess
              </h3>
              <p className="text-gray-700 mb-4">
                The foundation of the user status enforcement system. This synchronous utility function checks if a user is allowed to access protected content based on their status.
              </p>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <h4 className="font-semibold text-gray-800 mb-2">Function Signature:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`function checkUserSecureAccess(
  user: iUser | null | undefined,
  redirectOnBanned: boolean = true,
  redirectOnSuspended: boolean = true,
  redirectOnDeleted: boolean = true
): { isAllowed: boolean; reason?: string }`}
                </pre>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Parameters:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">user</span>: The user object to check</li>
                    <li><span className="font-mono text-blue-600">redirectOnBanned</span>: Whether to redirect banned users</li>
                    <li><span className="font-mono text-blue-600">redirectOnSuspended</span>: Whether to redirect suspended users</li>
                    <li><span className="font-mono text-blue-600">redirectOnDeleted</span>: Whether to redirect deleted users</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Returns:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">isAllowed</span>: Boolean indicating if access is allowed</li>
                    <li><span className="font-mono text-blue-600">reason</span>: Optional string explaining why access was denied</li>
                  </ul>
                </div>
              </div>
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      This function will automatically redirect users based on their status when the redirect parameters are set to true.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                useSecureUser
              </h3>
              <p className="text-gray-700 mb-4">
                A React hook that wraps the checkUserSecureAccess function for use in client components. It provides a React-friendly interface with loading state information.
              </p>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <h4 className="font-semibold text-gray-800 mb-2">Hook Signature:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`function useSecureUser(
  user: iUser | null | undefined,
  options: {
    redirectOnBanned?: boolean;
    redirectOnSuspended?: boolean;
    redirectOnDeleted?: boolean;
  } = {}
): { 
  isAllowed: boolean; 
  reason?: string; 
  isLoading: boolean;
  user: iUser | null | undefined;
}`}
                </pre>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Parameters:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">user</span>: The user object to check</li>
                    <li><span className="font-mono text-blue-600">options</span>: Object with redirect preferences</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Returns:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">isAllowed</span>: Boolean indicating if access is allowed</li>
                    <li><span className="font-mono text-blue-600">reason</span>: Optional string explaining why access was denied</li>
                    <li><span className="font-mono text-blue-600">isLoading</span>: Boolean indicating if the check is still loading</li>
                    <li><span className="font-mono text-blue-600">user</span>: The original user object</li>
                  </ul>
                </div>
              </div>
              <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-5 w-5 text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      This hook is designed for React components and follows React's patterns for state management and side effects.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                checkUserSecureAccessServer
              </h3>
              <p className="text-gray-700 mb-4">
                An asynchronous server-side function that performs the same security checks as checkUserSecureAccess but adds logging capabilities for security events.
              </p>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <h4 className="font-semibold text-gray-800 mb-2">Function Signature:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`async function checkUserSecureAccessServer(
  user: iUser | null | undefined,
  requestInfo?: {
    ip?: string;
    path?: string;
    userAgent?: string;
  }
): Promise<{ isAllowed: boolean; reason?: string }>`}
                </pre>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Parameters:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">user</span>: The user object to check</li>
                    <li><span className="font-mono text-blue-600">requestInfo</span>: Optional object with request metadata for logging</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Returns:</h4>
                  <ul className="list-disc pl-5 text-gray-700">
                    <li><span className="font-mono text-blue-600">isAllowed</span>: Boolean indicating if access is allowed</li>
                    <li><span className="font-mono text-blue-600">reason</span>: Optional string explaining why access was denied</li>
                  </ul>
                </div>
              </div>
              <div className="bg-purple-50 border-l-4 border-purple-400 p-4 rounded">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Database className="h-5 w-5 text-purple-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-purple-700">
                      This function integrates with the security logs system to create audit trails of banned and suspended user access attempts.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <FileText className="mr-2 h-6 w-6 text-blue-600" />
              Usage Examples
            </h2>
            
            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Using with Custom useUser Hook
              </h3>
              <p className="text-gray-700 mb-4">
                The secure user check is integrated into the custom useUser hook for consistent enforcement across components.
              </p>
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-semibold text-gray-800 mb-2">Example:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`// In a React component
import { useUser } from "@/app/hooks/useUser";

function ProtectedComponent() {
  const { user, isAllowed, securityReason } = useUser();
  
  if (!isAllowed) {
    return <div>Access denied: {securityReason}</div>;
  }
  
  return <div>Protected content for {user.userName}</div>;
}`}
                </pre>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Server-Side Usage
              </h3>
              <p className="text-gray-700 mb-4">
                For server components and API routes, use the server-side function to check user status and log access attempts.
              </p>
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-semibold text-gray-800 mb-2">Example:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`// In a server component or API route
import { checkUserSecureAccessServer } from "@/app/util/secureUserCheck";

export async function POST(request: Request) {
  const user = await getUserFromDb();
  const { ip, userAgent } = getRequestMetadata(request);
  
  const { isAllowed, reason } = await checkUserSecureAccessServer(user, {
    ip,
    path: request.url,
    userAgent
  });
  
  if (!isAllowed) {
    return new Response(JSON.stringify({ error: reason }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }
  
  // Continue with protected operation
}`}
                </pre>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Direct Usage in Components
              </h3>
              <p className="text-gray-700 mb-4">
                For components that need more control over the security check behavior, use the useSecureUser hook directly.
              </p>
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-semibold text-gray-800 mb-2">Example:</h4>
                <pre className="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto">
{`// In a React component
import { useSecureUser } from "@/app/util/secureUserCheck";
import { useAtom } from "jotai";
import { currentUserAtom } from "@/app/store/store";

function CustomSecurityComponent() {
  const [user] = useAtom(currentUserAtom);
  const { isAllowed, reason, isLoading } = useSecureUser(user, {
    redirectOnBanned: false,  // Handle banned users manually
    redirectOnSuspended: true,
    redirectOnDeleted: true
  });
  
  if (isLoading) {
    return <div>Loading security status...</div>;
  }
  
  if (!isAllowed && reason === 'User is banned') {
    return <CustomBannedUserUI />;
  }
  
  return <div>Custom protected content</div>;
}`}
                </pre>
              </div>
            </div>
          </div>

          <div className="mb-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <AlertTriangle className="mr-2 h-6 w-6 text-blue-600" />
              Important Considerations
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <XCircle className="mr-2 h-5 w-5 text-red-500" />
                  Middleware Limitations
                </h3>
                <p className="text-gray-700">
                  While middleware-based enforcement is implemented as a backup, it has proven unreliable in production. 
                  Always use the secure user check in layouts and components for consistent enforcement.
                </p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Clock className="mr-2 h-5 w-5 text-yellow-500" />
                  Suspension Handling
                </h3>
                <p className="text-gray-700">
                  For suspended users, the system redirects to a dedicated page that displays the suspension duration and reason.
                  Make sure to include these details when suspending a user.
                </p>
              </div>
            </div>
          </div>

          <div className="mb-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Database className="mr-2 h-6 w-6 text-blue-600" />
              Related Documentation
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Link href="/admin/docs/security-logs" className="block">
                <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
                  <h3 className="text-lg font-semibold text-blue-600 mb-2">Security Logs System</h3>
                  <p className="text-gray-700">
                    Learn about the security logging system that records banned and suspended user access attempts.
                  </p>
                </div>
              </Link>
              
              <Link href="/admin/docs/user-management/test-user-status" className="block">
                <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
                  <h3 className="text-lg font-semibold text-blue-600 mb-2">Test User Status Utility</h3>
                  <p className="text-gray-700">
                    Documentation for the admin utility to test user status changes and enforcement.
                  </p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
