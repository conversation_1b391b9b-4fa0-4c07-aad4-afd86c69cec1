"use client";

import { <PERSON>, Al<PERSON>Triangle, FileText, Code, Database, BarChart } from "lucide-react";
import Link from "next/link";

export default function SecurityLogsDocumentation() {
  return (
    <div className="docs-container">
      <div className="docs-content">
        <div className="prose max-w-none">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Security Logs System
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive documentation for monitoring, tracking, and integrating security events across the ReviewIt platform.
            </p>
          </div>

          <div className="intro-section mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-start gap-4">
                <Shield className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h2 className="text-xl font-semibold text-blue-900 mb-2">
                    Overview
                  </h2>
                  <p className="text-blue-800 mb-4">
                    The Security Logs System is designed to track and monitor security-related events across the ReviewIt platform. 
                    It provides a standardized way to log, store, and analyze security events such as access attempts by suspended or banned users, 
                    authentication failures, and other security-relevant activities.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🛡️ Security Focus
                      </div>
                      <div className="text-blue-700">
                        Track user access attempts, authentication events, and security violations
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        📊 Data Storage
                      </div>
                      <div className="text-blue-700">
                        Structured database storage with Prisma ORM integration
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🔍 Monitoring
                      </div>
                      <div className="text-blue-700">
                        Admin dashboard for viewing and analyzing security events
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Database Schema Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Database className="h-6 w-6 text-gray-700" />
              Database Schema
            </h2>
            <div className="bg-white rounded-lg border p-6 mb-6">
              <h3 className="text-lg font-semibold mb-3">SecurityLog Model</h3>
              <p className="mb-4">
                The security logs are stored in the <code>SecurityLog</code> model in the Prisma schema:
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                <pre className="text-sm">
                  {`model SecurityLog {
  id          String   @id @default(uuid())
  eventType   String   // e.g., "SUSPENDED_ACCESS_ATTEMPT", "BANNED_ACCESS_ATTEMPT"
  userId      String   // The ID of the user involved in the event
  ipAddress   String   // IP address where the event originated
  userAgent   String   // User agent information
  requestPath String   // The path that was requested
  details     String?  // JSON string with additional event details
  severity    String   // "LOW", "MEDIUM", "HIGH", "CRITICAL"
  timestamp   DateTime @default(now())

  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
}`}
                </pre>
              </div>
            </div>

            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-3">Event Types</h3>
              <p className="mb-4">
                The system currently supports the following event types, but you can extend this list for your specific needs:
              </p>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default Severity</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SUSPENDED_ACCESS_ATTEMPT</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">When a suspended user attempts to access a protected route</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MEDIUM</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">BANNED_ACCESS_ATTEMPT</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">When a banned user attempts to access a protected route</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">HIGH</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">AUTHENTICATION_FAILURE</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Failed login attempts</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MEDIUM</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ADMIN_ACCESS_ATTEMPT</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">When a non-admin user attempts to access admin routes</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">HIGH</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">PERMISSION_VIOLATION</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">When a user attempts to access resources they don't have permission for</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">HIGH</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">RATE_LIMIT_EXCEEDED</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">When a user exceeds rate limits</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MEDIUM</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SUSPICIOUS_ACTIVITY</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Any suspicious behavior that might indicate security issues</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">HIGH</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </section>

          {/* Using the Security Logger Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Code className="h-6 w-6 text-gray-700" />
              Using the Security Logger
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-3">Basic Usage</h3>
              <p className="mb-4">
                The security logger is implemented in <code>src/app/util/securityLogger.ts</code> and provides several functions for logging different types of security events.
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto mb-6">
                <pre className="text-sm">
                  {`import { 
  logSecurityEvent, 
  logSuspendedAccessAttempt, 
  logBannedAccessAttempt 
} from '@/app/util/securityLogger';

// Example: Log a suspended user access attempt
await logSuspendedAccessAttempt(
  userId,         // The ID of the suspended user
  request,        // The NextRequest object
  suspendedUntil  // Optional: Date when suspension ends
);

// Example: Log a banned user access attempt
await logBannedAccessAttempt(
  userId,         // The ID of the banned user
  request         // The NextRequest object
);

// Example: Log a custom security event
await logSecurityEvent({
  eventType: 'AUTHENTICATION_FAILURE',
  userId: user.id,
  ipAddress: request.ip || 'unknown',
  userAgent: request.headers.get('user-agent') || 'unknown',
  requestPath: request.nextUrl.pathname,
  details: { reason: 'Invalid credentials', attempts: 5 },
  severity: 'MEDIUM'
});`}
                </pre>
              </div>

              <h3 className="text-lg font-semibold mb-3">Integration Examples</h3>
              <p className="mb-4">
                Here are some examples of how to integrate security logging in different parts of your application:
              </p>

              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 mb-2">1. Middleware Integration</h4>
                <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm">
                    {`// In your middleware.ts file
import { NextResponse } from 'next/server';
import { logSecurityEvent } from '@/app/util/securityLogger';

export async function middleware(request) {
  // Check if user is trying to access admin routes
  if (request.nextUrl.pathname.startsWith('/admin')) {
    const user = await getUser(request);
    
    if (!user) {
      // Log unauthenticated admin access attempt
      await logSecurityEvent({
        eventType: 'ADMIN_ACCESS_ATTEMPT',
        userId: 'anonymous',
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        requestPath: request.nextUrl.pathname,
        details: { message: 'Unauthenticated user attempted to access admin route' },
        severity: 'HIGH'
      });
      
      return NextResponse.redirect(new URL('/login', request.url));
    }
    
    if (user.role !== 'ADMIN') {
      // Log unauthorized admin access attempt
      await logSecurityEvent({
        eventType: 'ADMIN_ACCESS_ATTEMPT',
        userId: user.id,
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        requestPath: request.nextUrl.pathname,
        details: { userRole: user.role },
        severity: 'HIGH'
      });
      
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }
  
  return NextResponse.next();
}`}
                  </pre>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 mb-2">2. API Route Integration</h4>
                <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm">
                    {`// In an API route
import { NextRequest, NextResponse } from 'next/server';
import { logSecurityEvent } from '@/app/util/securityLogger';

export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();
    
    // Check login credentials
    const user = await authenticateUser(email, password);
    
    if (!user) {
      // Log failed login attempt
      await logSecurityEvent({
        eventType: 'AUTHENTICATION_FAILURE',
        userId: 'unknown', // We don't know the user ID yet
        ipAddress: req.ip || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
        requestPath: req.nextUrl.pathname,
        details: { email, reason: 'Invalid credentials' },
        severity: 'MEDIUM'
      });
      
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }
    
    // Successful login...
    return NextResponse.json({ success: true });
  } catch (error) {
    // Log unexpected errors
    await logSecurityEvent({
      eventType: 'UNEXPECTED_ERROR',
      userId: 'unknown',
      ipAddress: req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      requestPath: req.nextUrl.pathname,
      details: { error: error.message },
      severity: 'HIGH'
    });
    
    return NextResponse.json({ error: 'An error occurred' }, { status: 500 });
  }
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 mb-2">3. Server Component Integration</h4>
                <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm">
                    {`// In a server component or server action
import { cookies, headers } from 'next/headers';
import { logSecurityEvent } from '@/app/util/securityLogger';

export async function deleteUserData(userId: string, requesterId: string) {
  // Check if requester has permission
  const requester = await getUser(requesterId);
  
  if (requester.role !== 'ADMIN' && requesterId !== userId) {
    // Log unauthorized deletion attempt
    const headersList = headers();
    
    await logSecurityEvent({
      eventType: 'PERMISSION_VIOLATION',
      userId: requesterId,
      ipAddress: headersList.get('x-forwarded-for') || 'unknown',
      userAgent: headersList.get('user-agent') || 'unknown',
      requestPath: '/api/users/delete',
      details: { 
        targetUserId: userId,
        requesterRole: requester.role,
        action: 'delete_user_data'
      },
      severity: 'CRITICAL'
    });
    
    throw new Error('Unauthorized action');
  }
  
  // Proceed with deletion...
}`}
                  </pre>
                </div>
              </div>
            </div>
          </section>

          {/* Viewing Security Logs Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <BarChart className="h-6 w-6 text-gray-700" />
              Viewing Security Logs
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-3">Admin Dashboard</h3>
              <p className="mb-4">
                Security logs can be viewed in the admin dashboard at <Link href="/admin/security-logs" className="text-blue-600 hover:underline">/admin/security-logs</Link>.
                The dashboard provides filtering by:
              </p>
              <ul className="list-disc pl-6 mb-6 space-y-2">
                <li>Event type (e.g., suspended access attempts, banned access attempts)</li>
                <li>Severity level (LOW, MEDIUM, HIGH, CRITICAL)</li>
                <li>User ID</li>
                <li>Time period</li>
              </ul>

              <h3 className="text-lg font-semibold mb-3">Programmatic Access</h3>
              <p className="mb-4">
                You can also access security logs programmatically through the admin API:
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                <pre className="text-sm">
                  {`// Example: Fetch security logs for a specific user
const response = await fetch('/api/admin/security-logs?userId=user_123&limit=50');
const data = await response.json();

// Example: Fetch high severity security events
const response = await fetch('/api/admin/security-logs?severity=HIGH');
const data = await response.json();`}
                </pre>
              </div>
            </div>
          </section>

          {/* Best Practices Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <AlertTriangle className="h-6 w-6 text-gray-700" />
              Best Practices
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">When to Log Security Events</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Authentication failures (after multiple attempts)</li>
                    <li>Access attempts by suspended or banned users</li>
                    <li>Unauthorized access attempts to restricted resources</li>
                    <li>Unusual user behavior (multiple rapid requests, etc.)</li>
                    <li>Critical data modifications</li>
                    <li>Permission changes</li>
                    <li>Rate limit violations</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Security Log Guidelines</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Include enough context to understand what happened</li>
                    <li>Don't log sensitive data (passwords, tokens, etc.)</li>
                    <li>Use appropriate severity levels</li>
                    <li>Be consistent with event types</li>
                    <li>Include user identifiers when available</li>
                    <li>Include IP addresses for traceability</li>
                    <li>Make logs searchable with relevant indexes</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Advanced Usage Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="h-6 w-6 text-gray-700" />
              Advanced Usage
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-3">Creating Custom Event Types</h3>
              <p className="mb-4">
                You can extend the security logging system with your own custom event types:
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto mb-6">
                <pre className="text-sm">
                  {`// Example: Log a custom payment fraud attempt
await logSecurityEvent({
  eventType: 'PAYMENT_FRAUD_ATTEMPT',
  userId: user.id,
  ipAddress: req.ip || 'unknown',
  userAgent: req.headers.get('user-agent') || 'unknown',
  requestPath: req.nextUrl.pathname,
  details: { 
    paymentMethod: 'credit_card',
    cardCountry: 'US',
    ipCountry: 'RU',
    amount: 999.99,
    productId: 'premium_subscription'
  },
  severity: 'CRITICAL'
});`}
                </pre>
              </div>

              <h3 className="text-lg font-semibold mb-3">Setting Up Alerts</h3>
              <p className="mb-4">
                You can implement real-time alerts for critical security events:
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                <pre className="text-sm">
                  {`// Example: Create a webhook endpoint for security alerts
import { prisma } from '@/app/util/prismaClient';
import { sendSlackAlert, sendEmailAlert } from '@/app/util/notifications';

// Set up a cron job or scheduled task to check for critical security events
export async function checkCriticalSecurityEvents() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  
  // Find critical security events in the last 5 minutes
  const criticalEvents = await prisma.securityLog.findMany({
    where: {
      severity: 'CRITICAL',
      timestamp: {
        gte: fiveMinutesAgo
      }
    }
  });
  
  if (criticalEvents.length > 0) {
    // Send alerts to Slack and email
    await sendSlackAlert({
      channel: '#security-alerts',
      message: \`🚨 \${criticalEvents.length} critical security events detected!\`,
      events: criticalEvents
    });
    
    await sendEmailAlert({
      to: '<EMAIL>',
      subject: \`[ALERT] \${criticalEvents.length} Critical Security Events Detected\`,
      events: criticalEvents
    });
  }
}`}
                </pre>
              </div>
            </div>
          </section>

          {/* Additional Resources Section */}
          <section className="mb-8">
            <div className="mt-8 p-6 bg-gray-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                📚 Related Resources
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Internal Documentation
                  </h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>
                      • <Link href="/admin/docs/user-management" className="hover:text-blue-600 hover:underline">
                        User Management Guide
                      </Link>
                    </li>
                    <li>
                      • <Link href="/admin/docs/api-routes" className="hover:text-blue-600 hover:underline">
                        API Routes Documentation
                      </Link>
                    </li>
                    <li>
                      • <Link href="/admin/docs/core-concepts/database" className="hover:text-blue-600 hover:underline">
                        Database Schema & Models
                      </Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    External Resources
                  </h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>
                      • <a
                        href="https://cheatsheetseries.owasp.org/cheatsheets/Logging_Cheat_Sheet.html"
                        target="_blank"
                        className="hover:text-blue-600 hover:underline"
                      >
                        OWASP Logging Cheat Sheet
                      </a>
                    </li>
                    <li>
                      • <a
                        href="https://www.prisma.io/docs/reference/api-reference/prisma-client-reference"
                        target="_blank"
                        className="hover:text-blue-600 hover:underline"
                      >
                        Prisma Client Reference
                      </a>
                    </li>
                    <li>
                      • <a
                        href="https://nextjs.org/docs/app/building-your-application/routing/middleware"
                        target="_blank"
                        className="hover:text-blue-600 hover:underline"
                      >
                        Next.js Middleware Documentation
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <div className="mt-6 text-center">
            <Link
              href="/admin/docs"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              ← Back to Documentation Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
