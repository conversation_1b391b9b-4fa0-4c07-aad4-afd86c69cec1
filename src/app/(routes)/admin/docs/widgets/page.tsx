"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft, Shield, Globe, Key, AlertTriangle, CheckCircle, Database, Code, Settings, Users, BarChart3, Lock, Unlock } from "lucide-react";

export default function AdminWidgetDocsPage() {
  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/admin" className="hover:text-blue-600">
            Admin Dashboard
          </Link>
          <span>/</span>
          <Link href="/admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>Widget System</span>
        </div>

        <div className="text-center mb-12 p-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl">
          <h1 className="text-4xl font-bold mb-6 text-gray-900">
            Widget System Administration
            <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
              Complete
            </span>
          </h1>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto">
            Comprehensive administrative guide for the ReviewIt widget system, covering security implementation, 
            monitoring, troubleshooting, and platform management.
          </p>
        </div>
      </div>

      <div className="text-gray-700 leading-relaxed">
        {/* System Overview */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Settings className="inline-block w-8 h-8 mr-2 text-blue-600" />
            System Overview
          </h2>
          <p className="mb-6">
            The ReviewIt widget system implements a two-tier security architecture that balances ease of use with enterprise-grade security. 
            This system allows business owners to embed their reviews and ratings on external websites while maintaining control over where their widgets can be used.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-center mb-3">
                <Unlock className="w-6 h-6 text-green-600 mr-2" />
                <h3 className="text-xl font-semibold text-green-800">Simple Widgets</h3>
              </div>
              <ul className="text-green-700 space-y-2">
                <li>• Direct iframe embedding</li>
                <li>• No domain restrictions</li>
                <li>• Easy implementation</li>
                <li>• Public use cases</li>
                <li>• Can be copied anywhere</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center mb-3">
                <Lock className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-xl font-semibold text-blue-800">Secure Widgets</h3>
              </div>
              <ul className="text-blue-700 space-y-2">
                <li>• Domain verification required</li>
                <li>• JWT token authentication</li>
                <li>• Real-time validation</li>
                <li>• Enterprise security</li>
                <li>• Cannot be copied</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Security Architecture */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Shield className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Security Architecture
          </h2>

          <div className="mb-8">
            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Domain Verification System</h3>
            <p className="mb-4">
              Secure widgets require domain verification through one of three methods:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-2">HTML File Upload</h4>
                <p className="text-sm text-gray-600 mb-2">Upload verification file to domain root</p>
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  reviewit-verification-[CODE].html
                </code>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-2">DNS TXT Record</h4>
                <p className="text-sm text-gray-600 mb-2">Add TXT record to domain DNS</p>
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  reviewit-verification=[CODE]
                </code>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-2">Meta Tag</h4>
                <p className="text-sm text-gray-600 mb-2">Add meta tag to website head</p>
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  &lt;meta name="reviewit-verification" content="[CODE]" /&gt;
                </code>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Token Authentication</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <ul className="space-y-2">
                <li><strong>JWT-based:</strong> Signed with widget's API key</li>
                <li><strong>Domain-bound:</strong> Each token tied to specific domain</li>
                <li><strong>Time-limited:</strong> Configurable expiration (1 hour to 30 days)</li>
                <li><strong>Revocable:</strong> Tokens can be revoked instantly</li>
                <li><strong>Rate-limited:</strong> Per-token request limits enforced</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Database Schema */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Database className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Database Schema
          </h2>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Widget Model Updates</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`model Widget {
  // ... existing fields
  securityLevel     String   @default("SIMPLE") // "SIMPLE" | "SECURE"
  apiKey           String?  // For secure widgets
  tokenExpiry      Int      @default(3600)     // Token expiry in seconds
  maxRequestsPerHour Int    @default(1000)     // Rate limiting
  blockedCount     Int      @default(0)        // Security metrics
  allowedDomains   String[] // Array of allowed domains
}`}</pre>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">WidgetToken Table</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`model WidgetToken {
  id           String    @id @default(cuid())
  widgetId     String
  domain       String
  token        String
  expiresAt    DateTime
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  lastUsed     DateTime?
  requestCount Int       @default(0)
  widget       Widget    @relation(fields: [widgetId], references: [id], onDelete: Cascade)
}`}</pre>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">DomainVerification Table</h3>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
{`model DomainVerification {
  id               String    @id @default(cuid())
  widgetId         String
  domain           String
  verificationCode String
  isVerified       Boolean   @default(false)
  verifiedAt       DateTime?
  createdAt        DateTime  @default(now())
  expiresAt        DateTime
  widget           Widget    @relation(fields: [widgetId], references: [id], onDelete: Cascade)
  
  @@unique([widgetId, domain])
}`}</pre>
            </div>
          </div>
        </section>

        {/* API Endpoints */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Code className="inline-block w-8 h-8 mr-2 text-blue-600" />
            API Endpoints
          </h2>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Security Configuration</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-mono">PUT</span>
                  <code className="text-sm">/api/widgets/[widgetId]/security</code>
                </div>
                <div className="flex items-center gap-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono">GET</span>
                  <code className="text-sm">/api/widgets/[widgetId]/security</code>
                </div>
                <p className="text-sm text-gray-600">Manage widget security settings including security level, allowed domains, and rate limits.</p>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Domain Verification</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-mono">POST</span>
                  <code className="text-sm">/api/widgets/[widgetId]/verify-domain</code>
                </div>
                <div className="flex items-center gap-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono">GET</span>
                  <code className="text-sm">/api/widgets/[widgetId]/verify-domain</code>
                </div>
                <div className="flex items-center gap-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-mono">POST</span>
                  <code className="text-sm">/api/widgets/[widgetId]/verify/[domain]</code>
                </div>
                <p className="text-sm text-gray-600">Initiate and verify domain ownership for secure widgets.</p>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Token Management</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-mono">POST</span>
                  <code className="text-sm">/api/widgets/[widgetId]/generate-token</code>
                </div>
                <div className="flex items-center gap-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono">GET</span>
                  <code className="text-sm">/api/public/widgets/secure/[widgetId]</code>
                </div>
                <p className="text-sm text-gray-600">Generate secure tokens and serve widget data for authenticated requests.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Monitoring & Analytics */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <BarChart3 className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Monitoring & Analytics
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Security Metrics</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Failed authentication attempts</li>
                <li>• Blocked domain requests</li>
                <li>• Token usage patterns</li>
                <li>• Verification success rates</li>
                <li>• Rate limit violations</li>
                <li>• Suspicious activity detection</li>
              </ul>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Performance Metrics</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Widget load times</li>
                <li>• API response times</li>
                <li>• Token generation speed</li>
                <li>• Verification completion rates</li>
                <li>• Database query performance</li>
                <li>• CDN cache hit rates</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-center mb-3">
              <AlertTriangle className="w-6 h-6 text-yellow-600 mr-2" />
              <h3 className="text-lg font-semibold text-yellow-800">Security Alerts</h3>
            </div>
            <p className="text-yellow-700 mb-3">
              Monitor these metrics for potential security issues:
            </p>
            <ul className="text-yellow-700 space-y-1">
              <li>• Sudden spike in failed authentication attempts</li>
              <li>• Multiple verification attempts from same IP</li>
              <li>• Unusual token usage patterns</li>
              <li>• High rate of blocked requests</li>
            </ul>
          </div>
        </section>

        {/* Administration Tasks */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Users className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Administration Tasks
          </h2>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">User Support</h3>
              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Domain Verification Issues</h4>
                  <p className="text-sm text-gray-600">Help users with failed domain verification by checking DNS records, file uploads, or meta tags.</p>
                </div>
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Token Problems</h4>
                  <p className="text-sm text-gray-600">Regenerate tokens for users experiencing authentication issues or expired tokens.</p>
                </div>
                <div className="border-l-4 border-yellow-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Embed Code Issues</h4>
                  <p className="text-sm text-gray-600">Verify correct embed code implementation and troubleshoot CORS or loading issues.</p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Security Management</h3>
              <div className="space-y-3">
                <div className="border-l-4 border-red-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Revoke Compromised Tokens</h4>
                  <p className="text-sm text-gray-600">Immediately revoke tokens if security breach is suspected.</p>
                </div>
                <div className="border-l-4 border-orange-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Block Malicious Domains</h4>
                  <p className="text-sm text-gray-600">Add domains to blocklist if abuse is detected.</p>
                </div>
                <div className="border-l-4 border-purple-500 pl-4">
                  <h4 className="font-semibold text-gray-800">Rate Limit Adjustments</h4>
                  <p className="text-sm text-gray-600">Modify rate limits for high-traffic legitimate users or reduce limits for suspicious activity.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Troubleshooting */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <AlertTriangle className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Troubleshooting Guide
          </h2>

          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-red-800">Common Issues</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-red-700">Widget Not Loading</h4>
                  <ul className="text-red-600 text-sm mt-1 space-y-1">
                    <li>• Check if domain is in allowed list</li>
                    <li>• Verify token is valid and not expired</li>
                    <li>• Check CORS configuration</li>
                    <li>• Ensure embed script is loaded correctly</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-red-700">Authentication Failures</h4>
                  <ul className="text-red-600 text-sm mt-1 space-y-1">
                    <li>• Regenerate token for the domain</li>
                    <li>• Verify domain verification status</li>
                    <li>• Check token expiration settings</li>
                    <li>• Ensure correct widget ID in embed code</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-red-700">Domain Verification Failing</h4>
                  <ul className="text-red-600 text-sm mt-1 space-y-1">
                    <li>• Check DNS propagation for TXT records</li>
                    <li>• Verify HTML file is accessible at correct URL</li>
                    <li>• Ensure meta tag is in document head</li>
                    <li>• Check for typos in verification code</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-blue-800">Debug Tools</h3>
              <div className="space-y-3">
                <div className="bg-white border border-blue-200 rounded p-3">
                  <h4 className="font-semibold text-blue-700">Browser Console</h4>
                  <p className="text-blue-600 text-sm">Check for JavaScript errors and network requests in browser developer tools.</p>
                </div>
                <div className="bg-white border border-blue-200 rounded p-3">
                  <h4 className="font-semibold text-blue-700">API Response Logs</h4>
                  <p className="text-blue-600 text-sm">Monitor API responses for authentication and validation errors.</p>
                </div>
                <div className="bg-white border border-blue-200 rounded p-3">
                  <h4 className="font-semibold text-blue-700">Database Queries</h4>
                  <p className="text-blue-600 text-sm">Check widget, token, and verification records in database.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Best Practices */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <CheckCircle className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Best Practices
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-green-800">Security Best Practices</h3>
              <ul className="space-y-2 text-green-700">
                <li>• Regularly rotate JWT secrets</li>
                <li>• Monitor for suspicious activity patterns</li>
                <li>• Keep token expiry times reasonable (24 hours max)</li>
                <li>• Implement proper rate limiting</li>
                <li>• Use HTTPS for all widget communications</li>
                <li>• Regularly audit domain verification records</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-blue-800">Performance Best Practices</h3>
              <ul className="space-y-2 text-blue-700">
                <li>• Cache widget data appropriately</li>
                <li>• Optimize database queries with proper indexing</li>
                <li>• Use CDN for static widget assets</li>
                <li>• Implement proper error handling</li>
                <li>• Monitor API response times</li>
                <li>• Use connection pooling for database</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Migration Notes */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900 flex items-center">
            <Database className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Migration & Deployment
          </h2>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Database Migration</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg text-sm overflow-x-auto mb-4">
{`# Production deployment
npx prisma migrate deploy

# Development environment
npx prisma migrate dev`}</pre>
            
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Environment Variables</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg text-sm overflow-x-auto mb-4">
{`# Required for secure widgets
WIDGET_JWT_SECRET=your-jwt-secret-here
NEXT_PUBLIC_BASE_URL=https://reviewit.gy

# Optional security settings
WIDGET_TOKEN_EXPIRY=86400  # 24 hours
WIDGET_RATE_LIMIT=1000     # requests per hour`}</pre>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
              <div className="flex items-center mb-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                <h4 className="font-semibold text-yellow-800">Migration Notes</h4>
              </div>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• All existing widgets default to "SIMPLE" security level</li>
                <li>• No breaking changes to existing embed codes</li>
                <li>• Users can upgrade to "SECURE" at any time</li>
                <li>• Gradual migration with user education recommended</li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Admin Documentation
        </Link>
      </div>
    </div>
  );
}