"use client";

import React from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Code,
  FileText,
  Share2,
  Layers,
  CheckCircle,
  AlertTriangle,
  Info,
  Copy,
  Folder,
  Component,
  Users,
  Shield,
  Zap,
} from "lucide-react";

export default function ExtendingDocumentationPage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <Link href="/admin/docs" className="hover:text-blue-600">
            Documentation
          </Link>
          <span>/</span>
          <span>Extending Documentation</span>
        </div>

        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Extending the Documentation System
          <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">
            Developer Guide
          </span>
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Learn how to extend the documentation system using shared components,
          create new documentation sections, and maintain consistency across
          different user roles.
        </p>
      </div>

      {/* Content */}
      <div className="text-gray-700 leading-relaxed">
        {/* Overview Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Info className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Documentation Architecture Overview
          </h2>
          <p className="mb-6">
            The documentation system is built using a shared component
            architecture that allows content to be reused across different user
            roles while maintaining security boundaries and avoiding code
            duplication.
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              <Layers className="w-6 h-6 text-blue-600" />
              Architecture Benefits
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">
                  Security & Access Control
                </h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• Role-based documentation access</li>
                  <li>• No cross-contamination between user types</li>
                  <li>• Protected admin routes remain secure</li>
                  <li>• Business owners get curated content</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">
                  Maintainability
                </h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• Single source of truth for content</li>
                  <li>• Automatic updates across sections</li>
                  <li>• Consistent styling and formatting</li>
                  <li>• Easy to add new documentation</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Shared Components Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Component className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Creating Shared Documentation Components
          </h2>
          <p className="mb-6">
            Shared components are the foundation of the documentation system.
            They contain reusable content that can be imported and used across
            different documentation sections.
          </p>

          {/* File Structure */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Folder className="w-5 h-5 text-gray-600" />
              File Structure
            </h3>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-sm overflow-x-auto">
              {`src/components/docs/
├── business-owner/
│   ├── BusinessGuideContent.tsx
│   ├── ProductManagementContent.tsx
│   └── ReviewManagementContent.tsx
├── shared/
│   ├── ApiDocumentation.tsx
│   └── TechnicalGuides.tsx
└── admin/
    ├── SystemAdminGuides.tsx
    └── AdvancedFeatures.tsx`}
            </div>
          </div>

          {/* Creating a Shared Component */}
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Code className="w-5 h-5 text-green-600" />
                Step 1: Create the Shared Component
              </h3>
              <p className="text-gray-600 mb-4">
                Create your shared component in the appropriate directory:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`// src/components/docs/business-owner/NewFeatureContent.tsx
"use client";

import React from "react";
import { Icon1, Icon2 } from "lucide-react";

export default function NewFeatureContent() {
  return (
    <div className="text-gray-700 leading-relaxed">
      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 text-gray-900">
          <Icon1 className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Your Feature Title
        </h2>
        <p className="mb-6">
          Detailed explanation of your feature...
        </p>

        {/* Your content here */}
      </section>
    </div>
  );
}`}
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FileText className="w-5 h-5 text-purple-600" />
                Step 2: Create Documentation Pages
              </h3>
              <p className="text-gray-600 mb-4">
                Create pages that use your shared component:
              </p>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    For Business Owners:
                  </h4>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                    {`// src/app/(routes)/owner-admin/docs/new-feature/page.tsx
"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import NewFeatureContent from "@/components/docs/business-owner/NewFeatureContent";

export default function NewFeaturePage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
        <Link href="/owner-admin/docs">Documentation</Link>
        <span>/</span>
        <span>New Feature</span>
      </div>

      <h1 className="text-4xl font-bold mb-6 text-gray-900">
        New Feature Guide
      </h1>

      {/* Shared Content */}
      <NewFeatureContent />

      {/* Navigation */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link href="/owner-admin/docs" className="...">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}`}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    For Admin Users:
                  </h4>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                    {`// src/app/(routes)/admin/docs/new-feature/page.tsx
import NewFeatureContent from "@/components/docs/business-owner/NewFeatureContent";

export default function AdminNewFeaturePage() {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Admin-specific header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900">
          New Feature - Admin View
        </h1>
        <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mt-4">
          <p className="text-yellow-800">
            <strong>Admin Note:</strong> This feature is also available
            to business owners. See implementation details below.
          </p>
        </div>
      </div>

      {/* Same shared content */}
      <NewFeatureContent />
    </div>
  );
}`}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Adding to Navigation */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Share2 className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Adding Documentation to Navigation
          </h2>
          <p className="mb-6">
            Once you've created your documentation pages, you need to add them
            to the appropriate navigation menus.
          </p>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Owner-Admin Documentation Hub
              </h3>
              <p className="text-gray-600 mb-4">
                Add your documentation to the business owner documentation hub:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`// src/app/(routes)/owner-admin/docs/page.tsx
const businessOwnerDocs = [
  // ... existing docs
  {
    title: "New Feature Guide",
    description: "Learn how to use the new feature effectively...",
    icon: <YourIcon className="w-5 h-5 text-blue-600" />,
    href: "/owner-admin/docs/new-feature",
    isExternal: false,
    status: "complete" as const,
  },
];`}
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Admin Documentation Hub
              </h3>
              <p className="text-gray-600 mb-4">
                Add your documentation to the admin documentation index:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`// src/app/(routes)/admin/docs/page.tsx
{/* Add to appropriate section */}
<div className="bg-white border border-gray-200 rounded-lg p-6...">
  <h3 className="text-xl font-semibold mb-2 text-gray-900">
    New Feature Documentation
    <span className="...">Complete</span>
  </h3>
  <p className="text-gray-600 text-sm mb-4">
    Comprehensive guide for the new feature...
  </p>
  <a href="/admin/docs/new-feature" className="...">
    View Documentation →
  </a>
</div>`}
              </div>
            </div>
          </div>
        </section>

        {/* Security Considerations */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Shield className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Security Considerations
          </h2>
          <p className="mb-6">
            When extending documentation, it's crucial to maintain proper
            security boundaries between user roles.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Best Practices
              </h3>
              <ul className="space-y-2 text-sm text-green-800">
                <li>
                  • <strong>Use Shared Components:</strong> Avoid duplicating
                  content
                </li>
                <li>
                  • <strong>Role-Based Access:</strong> Keep admin routes
                  protected
                </li>
                <li>
                  • <strong>Content Curation:</strong> Only show relevant docs
                  to business owners
                </li>
                <li>
                  • <strong>Internal Links:</strong> Use relative paths within
                  same section
                </li>
                <li>
                  • <strong>Clear Permissions:</strong> Document who can access
                  what
                </li>
              </ul>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Security Pitfalls
              </h3>
              <ul className="space-y-2 text-sm text-orange-800">
                <li>
                  • <strong>Cross-linking:</strong> Don't link owner-admin to
                  /admin routes
                </li>
                <li>
                  • <strong>Sensitive Info:</strong> Avoid exposing admin-only
                  information
                </li>
                <li>
                  • <strong>External Dependencies:</strong> Don't rely on
                  protected resources
                </li>
                <li>
                  • <strong>Access Assumptions:</strong> Always verify user
                  permissions
                </li>
                <li>
                  • <strong>Data Leakage:</strong> Review shared components for
                  sensitive data
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Advanced Patterns */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            <Zap className="inline-block w-8 h-8 mr-2 text-blue-600" />
            Advanced Patterns
          </h2>
          <p className="mb-6">
            Advanced techniques for building flexible and maintainable
            documentation systems.
          </p>

          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Conditional Content Rendering
              </h3>
              <p className="text-gray-600 mb-4">
                Render different content based on user role within the same
                component:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`interface ContentProps {
  userRole?: 'admin' | 'business-owner' | 'user';
  showAdvancedFeatures?: boolean;
}

export default function FlexibleContent({
  userRole = 'business-owner',
  showAdvancedFeatures = false
}: ContentProps) {
  return (
    <div>
      {/* Common content for all users */}
      <section className="mb-8">
        <h2>Basic Feature Overview</h2>
        {/* ... */}
      </section>

      {/* Admin-only content */}
      {userRole === 'admin' && (
        <section className="mb-8">
          <h2>Administrative Controls</h2>
          {/* ... */}
        </section>
      )}

      {/* Advanced features */}
      {showAdvancedFeatures && (
        <section className="mb-8">
          <h2>Advanced Configuration</h2>
          {/* ... */}
        </section>
      )}
    </div>
  );
}`}
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Documentation Composition
              </h3>
              <p className="text-gray-600 mb-4">
                Combine multiple shared components to create comprehensive
                guides:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`import BasicSetup from '@/components/docs/shared/BasicSetup';
import AdvancedConfig from '@/components/docs/shared/AdvancedConfig';
import Troubleshooting from '@/components/docs/shared/Troubleshooting';

export default function ComprehensiveGuide() {
  return (
    <div>
      <BasicSetup />
      <AdvancedConfig />
      <Troubleshooting />

      {/* Page-specific content */}
      <section className="mb-8">
        <h2>Additional Resources</h2>
        {/* ... */}
      </section>
    </div>
  );
}`}
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Dynamic Documentation Loading
              </h3>
              <p className="text-gray-600 mb-4">
                Load documentation components dynamically based on feature flags
                or user permissions:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                {`import { lazy, Suspense } from 'react';

// Lazy load documentation components
const AdvancedFeatures = lazy(() =>
  import('@/components/docs/advanced/AdvancedFeatures')
);

export default function DynamicDocumentation({ hasAdvancedAccess }) {
  return (
    <div>
      {/* Always available content */}
      <BasicContent />

      {/* Conditionally loaded advanced content */}
      {hasAdvancedAccess && (
        <Suspense fallback={<div>Loading advanced features...</div>}>
          <AdvancedFeatures />
        </Suspense>
      )}
    </div>
  );
}`}
              </div>
            </div>
          </div>
        </section>

        {/* Testing Documentation */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
            Testing Documentation Components
          </h2>
          <p className="mb-6">
            Ensure your documentation components work correctly and remain
            accessible.
          </p>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">
              Testing Checklist
            </h3>
            <ul className="space-y-2 text-sm text-yellow-800">
              <li>✓ Components render without errors</li>
              <li>✓ Links work correctly in both contexts</li>
              <li>✓ Responsive design on all screen sizes</li>
              <li>
                ✓ Accessibility compliance (screen readers, keyboard navigation)
              </li>
              <li>✓ Content is appropriate for target audience</li>
              <li>✓ No broken images or missing assets</li>
              <li>✓ Search functionality works if implemented</li>
              <li>✓ Print-friendly formatting</li>
            </ul>
          </div>

          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
            {`// Example test for documentation component
import { render, screen } from '@testing-library/react';
import NewFeatureContent from '@/components/docs/business-owner/NewFeatureContent';

describe('NewFeatureContent', () => {
  it('renders main heading', () => {
    render(<NewFeatureContent />);
    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
  });

  it('contains no broken links', () => {
    render(<NewFeatureContent />);
    const links = screen.getAllByRole('link');
    links.forEach(link => {
      expect(link).toHaveAttribute('href');
    });
  });
});`}
          </div>
        </section>

        {/* Maintenance and Updates */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Maintenance and Updates
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                Regular Maintenance
              </h3>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>
                  • <strong>Content Review:</strong> Regularly review and update
                  documentation
                </li>
                <li>
                  • <strong>Link Validation:</strong> Check for broken internal
                  and external links
                </li>
                <li>
                  • <strong>Screenshot Updates:</strong> Keep UI screenshots
                  current
                </li>
                <li>
                  • <strong>Feature Parity:</strong> Ensure docs match current
                  feature set
                </li>
                <li>
                  • <strong>User Feedback:</strong> Incorporate user suggestions
                  and corrections
                </li>
              </ul>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-purple-900 mb-4">
                Version Control
              </h3>
              <ul className="space-y-2 text-sm text-purple-800">
                <li>
                  • <strong>Documentation Branches:</strong> Use feature
                  branches for major updates
                </li>
                <li>
                  • <strong>Change Logs:</strong> Document what changed in each
                  version
                </li>
                <li>
                  • <strong>Review Process:</strong> Have technical writers
                  review changes
                </li>
                <li>
                  • <strong>Automated Checks:</strong> Use linting and
                  validation tools
                </li>
                <li>
                  • <strong>Rollback Plan:</strong> Keep previous versions
                  accessible
                </li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Quick Reference Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Quick Reference
        </h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Essential Commands & Paths
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">File Structure</h4>
              <div className="bg-black text-green-400 p-3 rounded font-mono text-xs">
                {`# Shared Components
src/components/docs/business-owner/
src/components/docs/shared/
src/components/docs/admin/

# Business Owner Docs
src/app/(routes)/owner-admin/docs/

# Admin Docs
src/app/(routes)/admin/docs/`}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Quick Start</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span>
                    Create component in <code>/components/docs/</code>
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span>Add page that imports component</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                  <span>Update navigation menus</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                  <span>Test across all user roles</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h4 className="font-medium text-blue-900 mb-2">Remember</h4>
            <p className="text-sm text-blue-800">
              Never link from <code>/owner-admin/docs</code> to{" "}
              <code>/admin</code> routes. Always use shared components to avoid
              content duplication.
            </p>
          </div>
        </div>
      </section>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <Link
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Documentation
        </Link>
      </div>
    </div>
  );
}
