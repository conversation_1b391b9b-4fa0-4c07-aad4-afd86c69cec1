"use client";

import React from "react";
import {
  Tag,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON>,
  <PERSON>,
  <PERSON>ting<PERSON>,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  PlusCircle,
  Edit,
  Trash2,
  BarChart3,
  DollarSign,
} from "lucide-react";

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  color: string;
}

function FeatureCard({
  title,
  description,
  icon,
  features,
  color,
}: FeatureCardProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200">
      <div className="flex items-center gap-3 mb-4">
        <div className={`p-2 rounded-lg ${color}`}>{icon}</div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center gap-2"
              >
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

interface StepProps {
  step: number;
  title: string;
  description: string;
  actions: string[];
  icon: React.ReactNode;
}

function Step({ step, title, description, actions, icon }: StepProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start gap-3 mb-4">
        <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
          {step}
        </div>
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <div>
          <h3 className="font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
      <ul className="space-y-2">
        {actions.map((action, index) => (
          <li
            key={index}
            className="text-sm text-gray-600 flex items-center gap-2"
          >
            <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
            {action}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default function PromotionsPage() {
  const features = [
    {
      title: "Product Discounts",
      description: "Create percentage or fixed amount discounts",
      icon: <Percent className="h-5 w-5" />,
      features: [
        "Percentage-based discounts (e.g., 20% off)",
        "Fixed amount discounts (e.g., $10 off)",
        "Flexible discount options",
        "Easy setup and management",
      ],
      color: "bg-green-100 text-green-600",
    },
    {
      title: "Promotion Codes",
      description: "Optional promo codes for exclusive offers",
      icon: <Tag className="h-5 w-5" />,
      features: [
        "Custom promotion codes",
        "Code-based discounts",
        "Track code usage",
        "Optional feature",
      ],
      color: "bg-purple-100 text-purple-600",
    },
    {
      title: "Scheduling",
      description: "Set start and end dates for promotions",
      icon: <Calendar className="h-5 w-5" />,
      features: [
        "Flexible start and end dates",
        "Automatic activation/deactivation",
        "Schedule future promotions",
        "Time-limited offers",
      ],
      color: "bg-blue-100 text-blue-600",
    },
    {
      title: "Basic Analytics",
      description: "Track promotion performance",
      icon: <BarChart3 className="h-5 w-5" />,
      features: [
        "View count tracking",
        "Click count tracking",
        "Basic performance metrics",
        "Simple reporting",
      ],
      color: "bg-orange-100 text-orange-600",
    },
  ];

  const steps = [
    {
      step: 1,
      title: "Create Promotion",
      description: "Set up your discount promotion",
      icon: <PlusCircle className="h-5 w-5" />,
      actions: [
        "Choose a product to promote",
        "Set promotion title and description",
        "Configure discount type and amount",
        "Add optional promotion code",
        "Upload promotional image (optional)",
      ],
    },
    {
      step: 2,
      title: "Schedule Dates",
      description: "Set when your promotion will run",
      icon: <Calendar className="h-5 w-5" />,
      actions: [
        "Set promotion start date",
        "Set promotion end date",
        "Choose to activate immediately or later",
        "Promotion will auto-activate/deactivate",
      ],
    },
    {
      step: 3,
      title: "Monitor Performance",
      description: "Track how your promotion is performing",
      icon: <Eye className="h-5 w-5" />,
      actions: [
        "View promotion analytics",
        "Track view and click counts",
        "Monitor promotion status",
        "See active vs expired promotions",
      ],
    },
    {
      step: 4,
      title: "Manage Promotions",
      description: "Edit or delete promotions as needed",
      icon: <Settings className="h-5 w-5" />,
      actions: [
        "Edit promotion details anytime",
        "Activate or deactivate promotions",
        "Delete promotions when no longer needed",
        "Manage all promotions from one dashboard",
      ],
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* Header Section */}
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-blue-50 to-purple-100 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Promotions Documentation
        </h1>
        <p className="text-lg text-gray-600 max-w-4xl mx-auto">
          Create and manage discount promotions for your products. Simple,
          effective promotional tools to boost sales and attract customers.
        </p>
      </div>

      {/* Quick Navigation */}
      <div className="mb-12 bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Navigation
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <a
            href="#overview"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Info className="h-4 w-4" />
            System Overview
          </a>
          <a
            href="#features"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Gift className="h-4 w-4" />
            Features
          </a>
          <a
            href="#workflow"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Settings className="h-4 w-4" />
            How It Works
          </a>
          <a
            href="#best-practices"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <CheckCircle className="h-4 w-4" />
            Best Practices
          </a>
        </div>
      </div>

      {/* System Overview */}
      <section id="overview" className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          System Overview
        </h2>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            What is the Promotions System?
          </h3>
          <p className="text-blue-800 mb-4">
            The promotions system allows business owners to create discount
            offers for their products. It's a simple, straightforward tool
            focused on helping businesses attract customers with special
            pricing.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Gift className="h-5 w-5 text-blue-600" />
                <h4 className="font-medium text-blue-900">Product-Focused</h4>
              </div>
              <p className="text-sm text-blue-700">
                Each promotion is tied to a specific product in your catalog
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Percent className="h-5 w-5 text-blue-600" />
                <h4 className="font-medium text-blue-900">Discount-Based</h4>
              </div>
              <p className="text-sm text-blue-700">
                Offer percentage discounts or fixed dollar amounts off
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <h4 className="font-medium text-blue-900">Time-Limited</h4>
              </div>
              <p className="text-sm text-blue-700">
                Set start and end dates for your promotional campaigns
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section id="features" className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Features & Capabilities
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>
      </section>

      {/* How It Works */}
      <section id="workflow" className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              <Step {...step} />
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                  <ArrowRight className="h-5 w-5 text-blue-400" />
                </div>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* Promotion Fields */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Promotion Fields & Options
        </h2>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Required Fields
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Title</strong>
                    <p className="text-sm text-gray-600">
                      Promotion name (5-50 characters)
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Description</strong>
                    <p className="text-sm text-gray-600">
                      Promotion details (10-200 characters)
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Product</strong>
                    <p className="text-sm text-gray-600">
                      Select which product to promote
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Start & End Dates</strong>
                    <p className="text-sm text-gray-600">
                      When the promotion will be active
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Discount Type</strong>
                    <p className="text-sm text-gray-600">
                      Percentage, fixed amount, or none
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Optional Fields
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Promotion Code</strong>
                    <p className="text-sm text-gray-600">
                      Custom code for the discount (max 20 characters)
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Promotional Image</strong>
                    <p className="text-sm text-gray-600">
                      Upload an image to represent your promotion
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-gray-900">Active Status</strong>
                    <p className="text-sm text-gray-600">
                      Enable or disable the promotion manually
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Analytics */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Analytics & Tracking
        </h2>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-500" />
                Available Metrics
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">View Count</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    Times promotion was viewed
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <MousePointer className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Click Count</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    Times promotion was clicked
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Click Rate</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    Calculated from views/clicks
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                How to Access Analytics
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-bold">
                    1
                  </div>
                  <p className="text-sm text-gray-600">
                    Go to your promotions dashboard
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-bold">
                    2
                  </div>
                  <p className="text-sm text-gray-600">
                    View basic metrics in the promotions list
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-bold">
                    3
                  </div>
                  <p className="text-sm text-gray-600">
                    Click on individual promotions for detailed view
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section id="best-practices" className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Best Practices
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Do's
            </h3>
            <ul className="space-y-2 text-sm text-green-800">
              <li>
                • <strong>Clear Titles:</strong> Use descriptive, compelling
                promotion titles
              </li>
              <li>
                • <strong>Compelling Descriptions:</strong> Explain the value
                and benefits clearly
              </li>
              <li>
                • <strong>Reasonable Discounts:</strong> Offer meaningful but
                sustainable discounts
              </li>
              <li>
                • <strong>Appropriate Timing:</strong> Set realistic start and
                end dates
              </li>
              <li>
                • <strong>High-Quality Images:</strong> Use attractive
                promotional images
              </li>
              <li>
                • <strong>Monitor Performance:</strong> Check analytics
                regularly
              </li>
            </ul>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-900 mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Don'ts
            </h3>
            <ul className="space-y-2 text-sm text-red-800">
              <li>
                • <strong>Vague Descriptions:</strong> Avoid unclear or generic
                descriptions
              </li>
              <li>
                • <strong>Unrealistic Discounts:</strong> Don't offer
                unsustainable discounts
              </li>
              <li>
                • <strong>Overlapping Dates:</strong> Avoid conflicting
                promotion periods
              </li>
              <li>
                • <strong>Poor Image Quality:</strong> Don't use low-resolution
                or irrelevant images
              </li>
              <li>
                • <strong>Set and Forget:</strong> Don't ignore promotion
                performance
              </li>
              <li>
                • <strong>Complex Codes:</strong> Avoid overly complicated
                promotion codes
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Getting Started
        </h2>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                📋 Quick Setup Guide
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Choose Your Product
                    </strong>
                    <p className="text-sm text-blue-700">
                      Select which product you want to promote
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">Set Your Discount</strong>
                    <p className="text-sm text-blue-700">
                      Choose percentage or fixed amount discount
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Schedule Your Promotion
                    </strong>
                    <p className="text-sm text-blue-700">
                      Set start and end dates for your offer
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Activate & Monitor
                    </strong>
                    <p className="text-sm text-blue-700">
                      Launch your promotion and track its performance
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                🚀 Quick Actions
              </h3>
              <div className="space-y-2">
                <a
                  href="/owner-admin/promotions/create"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Create New Promotion
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    Set up a new discount promotion
                  </p>
                </a>
                <a
                  href="/owner-admin/promotions"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Manage Promotions
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    View and edit existing promotions
                  </p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* API Reference */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">API Reference</h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Promotion API Endpoints
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">
                Basic Operations
              </h4>
              <div className="space-y-2 text-sm font-mono">
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                    GET
                  </span>
                  <span className="text-gray-600">/api/promotions</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                    POST
                  </span>
                  <span className="text-gray-600">/api/promotions</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                    PUT
                  </span>
                  <span className="text-gray-600">/api/promotions/[id]</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                    DELETE
                  </span>
                  <span className="text-gray-600">/api/promotions/[id]</span>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">
                Analytics & Tracking
              </h4>
              <div className="space-y-2 text-sm font-mono">
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                    GET
                  </span>
                  <span className="text-gray-600">
                    /api/promotions/[id]/analytics
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                    POST
                  </span>
                  <span className="text-gray-600">
                    /api/promotions/[id]/track
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Query Parameters</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>
                • <code>businessId</code> - Filter promotions by business
              </li>
              <li>
                • <code>productId</code> - Get promotions for specific product
              </li>
              <li>
                • <code>activeOnly</code> - Return only active promotions
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <a
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          ← Back to Documentation Home
        </a>
      </div>
    </div>
  );
}
