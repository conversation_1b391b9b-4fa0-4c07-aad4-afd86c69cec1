"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, BackToDocsButton, TableOfContents } from "@/components/docs";

const sections: { id: string; title: string; level: number }[] = [
  { id: "overview", title: "Overview", level: 1 },
  { id: "feature-matrix", title: "Feature Matrix", level: 1 },
  { id: "data-model", title: "Data-Model Changes", level: 1 },
  { id: "tier-utilities", title: "Tier Utilities", level: 1 },
  { id: "enabling-limits", title: "Enabling Limits", level: 1 },
  { id: "payment-flow", title: "Stripe Payment Flow", level: 1 },
  { id: "testing", title: "Testing Checklist", level: 1 },
];

export default function SubscriptionTiersDoc() {
  return (
    <DocsContainer>
      <BackToDocsButton />
      <DocsHeader
        title="Subscription Tiers & Payments"
        description="Starter, Pro, Enterprise & billing architecture"
        status="in-progress"
      />

      <TableOfContents items={sections} />

      {/* Overview */}
      <section id="overview" className="doc-section">
        <h2>Overview</h2>
        <p>
          Review-It supports three subscription levels: <strong>Starter</strong> (free),
          <strong>Pro</strong>, and <strong>Enterprise</strong>. Tier checks are enforced both in the
          API layer and the UI. Billing is handled via Stripe with prices in GYD and USD.
        </p>
      </section>

      {/* Feature matrix */}
      <section id="feature-matrix" className="doc-section">
        <h2>Feature Matrix (v0.1)</h2>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-2 text-left">Capability</th>
                <th className="p-2 text-center">Starter</th>
                <th className="p-2 text-center">Pro</th>
                <th className="p-2 text-center">Enterprise</th>
              </tr>
            </thead>
            <tbody>
              {[
                ["Record & display reviews", "✔", "✔", "✔"],
                ["Reply as business owner", "—", "✔", "✔"],
                ["Basic analytics", "—", "✔", "✔"],
                ["Advanced analytics", "—", "—", "✔"],
                ["Monthly review quota", "200", "2 000", "∞"],
                ["Team members", "1", "5", "∞ + roles"],
                ["Integrations", "—", "✔", "✔"],
                ["SSO / SCIM", "—", "—", "✔"],
                ["Priority support", "—", "—", "✔"],
              ].map((row) => (
                <tr key={row[0]} className="border-t">
                  {row.map((cell, i) => (
                    <td key={i} className="p-2 text-center first:text-left">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>

      {/* Data model */}
      <section id="data-model" className="doc-section">
        <h2>Data-Model Changes</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>
            Added <code>enum Tier {'{'} starter pro enterprise {'}'}</code> in <code>schema.prisma</code>.
          </li>
          <li>
            <code>Business</code> model now includes <code>tier</code> (defaults to
            <code>starter</code>) and optional <code>tierExpiresAt</code>.
          </li>
          <li>
            Run <code>npx prisma migrate dev --name add-tier</code> after pulling latest
            migrations.
          </li>
        </ul>
      </section>

      {/* Tier utilities */}
      <section id="tier-utilities" className="doc-section">
        <h2>Tier Utilities</h2>
        <p>Located at <code>src/app/util/tier.ts</code>.</p>
        <ul className="list-disc list-inside space-y-2">
          <li>
            <code>hasRequiredTier()</code> – simple comparison logic.
          </li>
          <li>
            <code>assertTier()</code> – throws <code>402</code> if current tier is insufficient.
          </li>
          <li>
            <code>maybeAssertTier()</code> – gated by <code>ENABLE_TIER_LIMITS</code> env flag.
          </li>
          <li>
            React context: <code>TierProvider</code> &amp; <code>useTier()</code> for client code.
          </li>
        </ul>
      </section>

      {/* Enabling limits */}
      <section id="enabling-limits" className="doc-section">
        <h2>Enabling Limits</h2>
        <ol className="list-decimal list-inside space-y-2">
          <li>
            Set <code>ENABLE_TIER_LIMITS=true</code> in <code>.env</code> (or flag store).
          </li>
          <li>
            Seed at least one Starter &amp; Pro business for QA.
          </li>
          <li>
            Verify API returns 402 for disallowed actions (e.g., owner reply on Starter).
          </li>
        </ol>
      </section>

      {/* Payment flow */}
      <section id="payment-flow" className="doc-section">
        <h2>Stripe Payment Flow</h2>
        <ol className="list-decimal list-inside space-y-2">
          <li>
            Create Stripe Prices (<strong>GYD</strong> &amp; <strong>USD</strong>) for Pro monthly.
          </li>
          <li>
            <code>/api/billing/checkout</code> – creates Checkout Session and redirects user.
          </li>
          <li>
            <code>/api/billing/webhook</code> – on <code>checkout.session.completed</code> set
            <code>Business.tier = 'pro'</code> &amp; <code>tierExpiresAt</code>.
          </li>
          <li>
            Enterprise upgrades handled manually via admin dashboard.
          </li>
        </ol>
      </section>

      {/* Testing */}
      <section id="testing" className="doc-section">
        <h2>Testing Checklist</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>Starter owner cannot reply to reviews when limits enabled.</li>
          <li>Pro owner can reply successfully.</li>
          <li>Analytics tab visibility toggles with tier.</li>
          <li>Checkout upgrades tier and enables features immediately.</li>
        </ul>
      </section>
    </DocsContainer>
  );
}
