import React from "react";
import { ArrowLeft } from "lucide-react";

const SystemNotificationsDocsPage = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Back link */}
      <div className="mb-6">
        <a href="/admin/docs/notifications" className="text-blue-600 hover:underline flex items-center gap-1">
          <ArrowLeft className="w-4 h-4" />
          Notification Docs
        </a>
      </div>

      {/* Header */}
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">System Notifications</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Admin & platform-initiated messages delivered to users in real-time via the ReviewIt notification pipeline.
        </p>
      </div>

      {/* Overview */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Endpoint Summary</h2>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li>
            <code className="font-mono">POST /notifications/system</code> – create broadcast or targeted system
            notification
          </li>
          <li>
            <code className="font-mono">PUT /notifications/{"{id}"}/read?type=system</code> – mark as read
          </li>
          <li>
            <code className="font-mono">GET /notifications/stream</code> – SSE (already in use)
          </li>
        </ul>
      </section>

      {/* Payload */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Payload</h2>
        <p className="text-gray-700 leading-relaxed mb-4">
          Send JSON with the following keys. <strong>Omit</strong> or pass an empty array for <code>target_user_ids</code>
          to broadcast to all users.
        </p>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm">
{`{
  "target_user_ids": ["user_abc"],     // optional – broadcast when empty / missing
  "title": "Maintenance tonight",
  "message": "Service will be unavailable at 02:00 UTC.",
  "cta_url": "/status",               // optional deep-link
  "icon": "warning"                    // info | success | warning | error
}`}
        </pre>
      </section>

      {/* Examples */}
      <section className="mb-12 space-y-8">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Examples</h2>

        {/* Broadcast */}
        <div>
          <h3 className="text-xl font-semibold text-gray-800">Broadcast Announcement</h3>
          <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm">
{`POST /notifications/system
{
  "title": "New bug-fix deployed",
  "message": "Liking reviews now works reliably.",
  "icon": "success"
}`}
          </pre>
        </div>

        {/* Targeted */}
        <div>
          <h3 className="text-xl font-semibold text-gray-800">Targeted Claim Approval</h3>
          <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm">
{`POST /notifications/system
{
  "target_user_ids": ["user_2znup3vKqoP3CPAk3ZrWQxieB1y"],
  "title": "Product claim approved!",
  "message": "Your claim for 'Awesome Widget' is now live.",
  "cta_url": "/dashboard/claims/awesome-widget",
  "icon": "success"
}`}
          </pre>
        </div>
      </section>

      {/* Dev integration */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Integration in Code</h2>
        <p className="text-gray-700 leading-relaxed mb-4">
          Any backend service can trigger a system notification by calling the endpoint. Example using <code>fetch</code> inside a server action:
        </p>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm">
{`await fetch(process.env.NOTIF_URL + '/notifications/system', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    target_user_ids: [userId],
    title: 'Review approved!',
    message: 'Your review is now public.',
    icon: 'success'
  })
});`}
        </pre>
      </section>

      {/* SSE Note */}
      <section className="mb-24">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Real-time Delivery</h2>
        <p className="text-gray-700 leading-relaxed">
          System notifications flow through the same SSE connection as other notifications with <code>type: "system"</code>.
          The frontend already displays a toast and lists it under the bell dropdown.
        </p>
      </section>
    </div>
  );
};

export default SystemNotificationsDocsPage;
