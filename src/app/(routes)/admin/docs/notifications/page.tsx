import React from "react";
import { ArrowLeft } from "lucide-react";

const NotificationsDocsPage = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      <div className="mb-6">
        <a href="/admin/docs" className="text-blue-600 hover:underline flex items-center gap-1">
          <ArrowLeft className="w-4 h-4" />
          Documentation
        </a>
      </div>
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">Notification System</h1>
        <p className="text-lg text-gray-600">
          Deep-dive into Review-it’s real-time notification pipeline, data
          contracts, and UX guidelines.
        </p>
      </div>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Overview</h2>
        <p className="text-gray-700 leading-relaxed">
          Review-it delivers notifications for user mentions, comment/reply likes
          (up-votes), review helpful votes, moderation actions, and system
          alerts.
        </p>
        <ul className="list-disc ml-6 mt-4 text-gray-700 space-y-1">
          <li>Stored in <code>notification_*</code> tables (PostgreSQL).</li>
          <li>Broadcast in real-time via Server-Sent Events (SSE).</li>
          <li>Fetched & cached with React Query on the frontend.</li>
        </ul>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Like Notifications</h2>
        <p className="text-gray-700 leading-relaxed">
          All like-style events share a single endpoint: <code>/notifications/like</code>.
          The payload:
        </p>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm mt-4">
{`{
  target_type: "comment" | "review",
  target_id:   string, // commentId or reviewId
  from_id:     string, // clerkUserId of sender
  from_name:   string,
  product_id:  string,
  read:        boolean
}`}
        </pre>
        <p className="text-gray-700 leading-relaxed mt-4">
          The backend validates, stores, and immediately broadcasts an SSE event
          with <code>{`type: "like"`}</code>. The frontend merges these into the existing
          notifications list so admins see them alongside other events.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Design Choice: No Down-vote Alerts</h2>
        <p className="text-gray-700 leading-relaxed">
          We intentionally skip notifications for <strong>down-votes / dislikes</strong>
          on comments and replies. Rationale:
        </p>
        <ul className="list-disc ml-6 mt-4 text-gray-700 space-y-1">
          <li>Reduces notification noise and potential harassment.</li>
          <li>Aligns with industry practice (e.g., YouTube, Reddit don’t alert on dislikes).</li>
          <li>Encourages constructive engagement by emphasising positive feedback.</li>
        </ul>
        <p className="text-gray-700 leading-relaxed mt-4">
          This can be revisited; simply remove the <code>isUpVote</code> guard in
          <code>/api/vote/comment</code> to enable alerts for both directions.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Developer Notes</h2>
        <ul className="list-disc ml-6 mt-4 text-gray-700 space-y-1">
          <li>Like notifications reuse the same SSE hub as all other events.</li>
          <li>Cache invalidation: React Query queries <code>["notifications"]</code> are
            invalidated on new events to keep the list fresh.</li>
          <li>Ensure Clerk IDs are used as canonical user identifiers.</li>
        </ul>
      </section>

      {/* User creation safety note */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">User Creation Flow &amp; Safety Check</h2>
        <p className="text-gray-700 leading-relaxed">
          A Clerk webhook (<code>/api/webhook</code>) automatically inserts every new user into both our
          primary database and the notification service&rsquo;s <code>users</code> table as soon as the account
          is created.
        </p>
        <ol className="list-decimal ml-6 mt-4 text-gray-700 space-y-1">
          <li>Upsert to <code>prisma.user</code>.</li>
          <li>POST to <code>/users</code> on the notification service (via <code>createUser()</code>).</li>
        </ol>
        <p className="text-gray-700 leading-relaxed mt-4">
          Comment / reply / like helpers call <code>createUser()</code> again just before sending a
          notification. The call is idempotent: a <code>409&nbsp;Conflict</code> (user already exists)
          is treated as success, ensuring no notification is dropped if a user hasn&rsquo;t propagated yet.
        </p>
      </section>
    </div>
  );
};

export default NotificationsDocsPage;
