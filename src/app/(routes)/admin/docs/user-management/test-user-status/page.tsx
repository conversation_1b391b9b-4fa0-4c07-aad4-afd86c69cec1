"use client";

import { <PERSON>, User<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Check<PERSON>ir<PERSON> } from "lucide-react";
import Link from "next/link";

export default function TestUserStatusDocumentation() {
  return (
    <div className="docs-container">
      <div className="docs-content">
        <div className="prose max-w-none">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Test User Status Utility
            </h1>
            <p className="text-lg text-gray-600">
              Tools for administrators to test user status restrictions (ACTIVE, SUSPENDED, BANNED) without modifying the database directly.
            </p>
          </div>

          <div className="intro-section mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-start gap-4">
                <UserCog className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h2 className="text-xl font-semibold text-blue-900 mb-2">
                    Overview
                  </h2>
                  <p className="text-blue-800 mb-4">
                    The Test User Status utility allows administrators to temporarily change a user's status for testing purposes.
                    This is particularly useful for verifying that the middleware correctly redirects suspended or banned users to the appropriate status pages,
                    and that the security logging system properly records access attempts.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🔒 Admin Only
                      </div>
                      <div className="text-blue-700">
                        Protected endpoints and functions for administrators
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🧪 Testing Focus
                      </div>
                      <div className="text-blue-700">
                        Designed for testing user restrictions and security logs
                      </div>
                    </div>
                    <div className="bg-white bg-opacity-60 rounded p-3">
                      <div className="font-semibold text-blue-900 mb-1">
                        🔄 Temporary Changes
                      </div>
                      <div className="text-blue-700">
                        Non-destructive testing without permanent database changes
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Available Functions Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Code className="h-6 w-6 text-gray-700" />
              Available Functions
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <p className="mb-6">
                The test user status utility provides three main functions:
              </p>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">1. Suspend a User</h3>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`import { suspendUser } from '@/app/util/test-status';

// Suspend a user for a specified number of minutes
await suspendUser(
  userId,           // The ID of the user to suspend
  durationMinutes,  // Duration of suspension in minutes
  reason            // Optional reason for suspension
);

// Example
await suspendUser('user_123', 30, 'Testing suspension functionality');`}
                    </pre>
                  </div>
                  <p className="mt-3 text-gray-600">
                    This function temporarily sets the user's status to "SUSPENDED" and sets a <code>suspendedUntil</code> timestamp
                    based on the specified duration. The user will be automatically redirected to the suspended page if they try to access restricted content.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">2. Ban a User</h3>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`import { banUser } from '@/app/util/test-status';

// Permanently ban a user
await banUser(
  userId,  // The ID of the user to ban
  reason   // Optional reason for the ban
);

// Example
await banUser('user_123', 'Testing ban functionality');`}
                    </pre>
                  </div>
                  <p className="mt-3 text-gray-600">
                    This function sets the user's status to "BANNED". Banned users will be redirected to the banned page
                    if they try to access any restricted content.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">3. Reactivate a User</h3>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`import { reactivateUser } from '@/app/util/test-status';

// Reactivate a previously suspended or banned user
await reactivateUser(
  userId  // The ID of the user to reactivate
);

// Example
await reactivateUser('user_123');`}
                    </pre>
                  </div>
                  <p className="mt-3 text-gray-600">
                    This function resets the user's status back to "ACTIVE", removing any suspension or ban.
                    Use this after testing to restore normal access for the user.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Admin API Route Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Code className="h-6 w-6 text-gray-700" />
              Admin API Route
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <p className="mb-4">
                For easier testing, we've created an admin-only API route that allows you to change user status via HTTP requests:
              </p>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto mb-6">
                <pre className="text-sm">
                  {`POST /api/admin/test-user-status

// Request body format:
{
  "userId": "user_123",        // Required: The ID of the user
  "action": "suspend",         // Required: One of "suspend", "ban", or "reactivate"
  "duration": 30,              // Optional: Duration in minutes (for suspend action only)
  "reason": "Testing purpose"  // Optional: Reason for the status change
}`}
                </pre>
              </div>

              <h3 className="text-lg font-semibold mb-3">Example API Calls</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Suspend a User</h4>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`// Using fetch API
const response = await fetch('/api/admin/test-user-status', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userId: 'user_123',
    action: 'suspend',
    duration: 30,
    reason: 'Testing suspension'
  })
});

// Using curl
curl -X POST http://localhost:3000/api/admin/test-user-status \\
  -H "Content-Type: application/json" \\
  -d '{"userId": "user_123", "action": "suspend", "duration": 30, "reason": "Testing suspension"}'`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Ban a User</h4>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`// Using fetch API
const response = await fetch('/api/admin/test-user-status', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userId: 'user_123',
    action: 'ban',
    reason: 'Testing ban functionality'
  })
});

// Using curl
curl -X POST http://localhost:3000/api/admin/test-user-status \\
  -H "Content-Type: application/json" \\
  -d '{"userId": "user_123", "action": "ban", "reason": "Testing ban functionality"}'`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Reactivate a User</h4>
                  <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {`// Using fetch API
const response = await fetch('/api/admin/test-user-status', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userId: 'user_123',
    action: 'reactivate'
  })
});

// Using curl
curl -X POST http://localhost:3000/api/admin/test-user-status \\
  -H "Content-Type: application/json" \\
  -d '{"userId": "user_123", "action": "reactivate"}'`}
                    </pre>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-yellow-800">Security Note</h4>
                    <p className="text-yellow-700 text-sm">
                      This API route is protected and only accessible to users with the ADMIN role. All requests are logged for security purposes.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Testing Workflow Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-gray-700" />
              Testing Workflow
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <p className="mb-4">
                Here's a recommended workflow for testing user status restrictions:
              </p>
              <ol className="space-y-4 list-decimal pl-6">
                <li className="pl-2">
                  <span className="font-semibold">Log in as an administrator</span>
                  <p className="text-gray-600 mt-1">
                    You need administrator privileges to use the test user status API.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Identify a test user</span>
                  <p className="text-gray-600 mt-1">
                    Choose a test user account that you can use for testing. Make sure it's not a production user account.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Use the API to change the user's status</span>
                  <p className="text-gray-600 mt-1">
                    Use the admin API to suspend or ban the test user as shown in the examples above.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Test in a separate browser session</span>
                  <p className="text-gray-600 mt-1">
                    In another browser or incognito window, log in as the test user and attempt to access protected content.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Verify redirection</span>
                  <p className="text-gray-600 mt-1">
                    Confirm that the user is redirected to the appropriate status page (/suspended or /banned).
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Check security logs</span>
                  <p className="text-gray-600 mt-1">
                    Visit the <Link href="/admin/security-logs" className="text-blue-600 hover:underline">security logs dashboard</Link> to verify that the access attempt was properly logged.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Reactivate the user</span>
                  <p className="text-gray-600 mt-1">
                    After testing, use the reactivate API to restore the user's access.
                  </p>
                </li>
                <li className="pl-2">
                  <span className="font-semibold">Verify restored access</span>
                  <p className="text-gray-600 mt-1">
                    Confirm that the user can now access protected content again.
                  </p>
                </li>
              </ol>
            </div>
          </section>

          {/* Integration with Security Logs Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Code className="h-6 w-6 text-gray-700" />
              Integration with Security Logs
            </h2>
            <div className="bg-white rounded-lg border p-6">
              <p className="mb-4">
                The test user status functionality is integrated with the security logging system. When a suspended or banned user attempts to access protected content:
              </p>
              <ol className="space-y-3 list-decimal pl-6">
                <li className="pl-2">
                  The middleware detects their restricted status
                </li>
                <li className="pl-2">
                  The access attempt is logged using the security logging system
                </li>
                <li className="pl-2">
                  The user is redirected to the appropriate status page
                </li>
              </ol>
              <div className="mt-4">
                <p>
                  You can view these access attempts in the{" "}
                  <Link href="/admin/security-logs" className="text-blue-600 hover:underline">
                    security logs dashboard
                  </Link>. For more information about the security logging system, see the{" "}
                  <Link href="/admin/docs/security-logs" className="text-blue-600 hover:underline">
                    security logs documentation
                  </Link>.
                </p>
              </div>
            </div>
          </section>

          {/* Additional Resources Section */}
          <section className="mb-8">
            <div className="mt-8 p-6 bg-gray-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                📚 Related Resources
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Internal Documentation
                  </h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>
                      • <Link href="/admin/docs/security-logs" className="hover:text-blue-600 hover:underline">
                        Security Logs System
                      </Link>
                    </li>
                    <li>
                      • <Link href="/admin/docs/user-management" className="hover:text-blue-600 hover:underline">
                        User Management Guide
                      </Link>
                    </li>
                    <li>
                      • <Link href="/admin/docs/api-routes" className="hover:text-blue-600 hover:underline">
                        API Routes Documentation
                      </Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Related Pages
                  </h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>
                      • <Link href="/admin/security-logs" className="hover:text-blue-600 hover:underline">
                        Security Logs Dashboard
                      </Link>
                    </li>
                    <li>
                      • <Link href="/admin/users" className="hover:text-blue-600 hover:underline">
                        User Management
                      </Link>
                    </li>
                    <li>
                      • <Link href="/suspended" className="hover:text-blue-600 hover:underline">
                        Suspended User Page
                      </Link>
                    </li>
                    <li>
                      • <Link href="/banned" className="hover:text-blue-600 hover:underline">
                        Banned User Page
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <div className="mt-6 text-center">
            <Link
              href="/admin/docs/user-management"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              ← Back to User Management Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
