import {
  <PERSON>s<PERSON><PERSON><PERSON>,
  <PERSON>sHeader,
  CollapsibleSec<PERSON>,
  BackToDocsButton,
  CodeBlock,
} from "@/components/docs";

export default function ProjectOverviewPage() {
  return (
    <DocsContainer>
      <DocsHeader
        title="Project Overview"
        description="A comprehensive guide to ReviewIt - a Next.js-based platform for product reviews and business management."
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Project Overview", href: "/admin/docs/project-overview" },
        ]}
      />

      <div className="intro-section">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">
          What is ReviewIt?
        </h2>
        <p className="text-gray-700 mb-4">
          ReviewIt is a modern web application built with Next.js that enables
          businesses to manage their online presence through customer reviews,
          product listings, and comprehensive analytics. The platform serves
          both business owners and customers, providing tools for review
          management, product claiming, and business insights.
        </p>
      </div>

      <CollapsibleSection
        title="🏗️ Tech Stack & Architecture"
        defaultOpen={true}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Frontend</h4>
            <ul className="space-y-1 text-gray-700">
              <li>• Next.js 14 (App Router)</li>
              <li>• React 18 with TypeScript</li>
              <li>• Tailwind CSS for styling</li>
              <li>• Shadcn/ui components</li>
              <li>• Framer Motion for animations</li>
              <li>• React Hook Form for forms</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">
              Backend & Database
            </h4>
            <ul className="space-y-1 text-gray-700">
              <li>• Prisma ORM with PostgreSQL</li>
              <li>• NextAuth.js for authentication</li>
              <li>• Redis for caching</li>
              <li>• Cloudinary for image management</li>
              <li>• Resend for email services</li>
            </ul>
          </div>
        </div>
      </CollapsibleSection>

      <CollapsibleSection title="👥 User Types & Permissions">
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">🔧 Admin Users</h4>
            <p className="text-blue-800 text-sm mb-2">
              Full system access and management capabilities
            </p>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Complete user management</li>
              <li>• Product and business management</li>
              <li>• Review moderation and analytics</li>
              <li>• System configuration</li>
              <li>• Bug report management</li>
            </ul>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-900 mb-2">
              🏢 Business Owners
            </h4>
            <p className="text-green-800 text-sm mb-2">
              Manage their claimed businesses and respond to reviews
            </p>
            <ul className="text-green-700 text-sm space-y-1">
              <li>• Claim and verify business ownership</li>
              <li>• Respond to customer reviews</li>
              <li>• Update business information</li>
              <li>• View business analytics</li>
              <li>• Manage business hours and details</li>
            </ul>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2">
              👤 Regular Users
            </h4>
            <p className="text-purple-800 text-sm mb-2">
              Browse, search, and review products and businesses
            </p>
            <ul className="text-purple-700 text-sm space-y-1">
              <li>• Browse and search products</li>
              <li>• Submit and edit reviews</li>
              <li>• Vote on helpful reviews</li>
              <li>• Report inappropriate content</li>
              <li>• Manage personal profile</li>
            </ul>
          </div>
        </div>
      </CollapsibleSection>

      <CollapsibleSection title="🌟 Core Features">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Review System</h4>
            <ul className="space-y-2 text-gray-700">
              <li>
                • <strong>Star Ratings:</strong> 1-5 star rating system
              </li>
              <li>
                • <strong>Written Reviews:</strong> Detailed customer feedback
              </li>
              <li>
                • <strong>Review Voting:</strong> Helpful/unhelpful voting
              </li>
              <li>
                • <strong>Review Reporting:</strong> Flag inappropriate content
              </li>
              <li>
                • <strong>Owner Responses:</strong> Business owners can reply
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-3">
              Business Management
            </h4>
            <ul className="space-y-2 text-gray-700">
              <li>
                • <strong>Product Claiming:</strong> Businesses can claim
                products
              </li>
              <li>
                • <strong>Business Profiles:</strong> Detailed business
                information
              </li>
              <li>
                • <strong>Hours Management:</strong> Operating hours and
                holidays
              </li>
              <li>
                • <strong>Contact Information:</strong> Phone, email, website
              </li>
              <li>
                • <strong>Location Data:</strong> Address and map integration
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-3">
              Search & Discovery
            </h4>
            <ul className="space-y-2 text-gray-700">
              <li>
                • <strong>Advanced Search:</strong> Filter by category, rating,
                location
              </li>
              <li>
                • <strong>Sorting Options:</strong> By rating, date, helpfulness
              </li>
              <li>
                • <strong>Category Navigation:</strong> Organized product
                categories
              </li>
              <li>
                • <strong>Trending Products:</strong> Popular and highly-rated
                items
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-3">
              Analytics & Reporting
            </h4>
            <ul className="space-y-2 text-gray-700">
              <li>
                • <strong>Review Analytics:</strong> Rating trends and insights
              </li>
              <li>
                • <strong>User Engagement:</strong> User activity tracking
              </li>
              <li>
                • <strong>Business Metrics:</strong> Performance indicators
              </li>
              <li>
                • <strong>Top Reviewers:</strong> Most active users
              </li>
            </ul>
          </div>
        </div>
      </CollapsibleSection>

      <CollapsibleSection title="📊 Database Schema Overview">
        <div className="space-y-4">
          <p className="text-gray-700">
            The application uses a relational database design with the following
            main entities:
          </p>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">Core Entities</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Users:</strong> Authentication and profile data
                <br />
                <strong>Products:</strong> Items that can be reviewed
                <br />
                <strong>Reviews:</strong> User-generated content and ratings
                <br />
                <strong>Businesses:</strong> Company/organization information
              </div>
              <div>
                <strong>Categories:</strong> Product classification
                <br />
                <strong>Claims:</strong> Business ownership verification
                <br />
                <strong>BugReports:</strong> Issue tracking
                <br />
                <strong>Analytics:</strong> Metrics and tracking data
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      <CollapsibleSection title="🔗 Key Integrations">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">
                🔐 Authentication
              </h4>
              <ul className="text-orange-800 text-sm space-y-1">
                <li>• NextAuth.js with multiple providers</li>
                <li>• Google OAuth integration</li>
                <li>• Session management</li>
                <li>• Role-based access control</li>
              </ul>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-900 mb-2">
                🚀 Performance
              </h4>
              <ul className="text-red-800 text-sm space-y-1">
                <li>• Redis caching layer</li>
                <li>• Database query optimization</li>
                <li>• Image optimization with Next.js</li>
                <li>• Static generation where possible</li>
              </ul>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-semibold text-yellow-900 mb-2">
                📧 Communications
              </h4>
              <ul className="text-yellow-800 text-sm space-y-1">
                <li>• Resend for transactional emails</li>
                <li>• Email templates for notifications</li>
                <li>• Business claim verification emails</li>
              </ul>
            </div>

            <div className="bg-teal-50 p-4 rounded-lg">
              <h4 className="font-semibold text-teal-900 mb-2">
                🖼️ Media Management
              </h4>
              <ul className="text-teal-800 text-sm space-y-1">
                <li>• Cloudinary for image storage</li>
                <li>• Automatic image optimization</li>
                <li>• Multiple image formats support</li>
              </ul>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      <CollapsibleSection title="🗂️ Project Structure">
        <CodeBlock
          language="text"
          code={`review-it-nextjs-v7/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── admin/          # Admin panel routes
│   │   ├── api/            # API endpoints
│   │   ├── auth/           # Authentication pages
│   │   ├── products/       # Product pages
│   │   └── users/          # User profile pages
│   ├── components/         # Reusable UI components
│   ├── lib/               # Utility functions and configs
│   └── types/             # TypeScript type definitions
├── prisma/                # Database schema and migrations
├── public/                # Static assets
├── docs/                  # Documentation files
└── guides/               # Implementation guides`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="⚙️ Environment Setup">
        <div className="space-y-4">
          <p className="text-gray-700">
            Essential environment variables for local development:
          </p>

          <CodeBlock
            filename=".env.local"
            language="bash"
            code={`# Database
DATABASE_URL="postgresql://username:password@localhost:5432/reviewit"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Redis Cache
REDIS_URL="redis://localhost:6379"

# Email Service
RESEND_API_KEY="your-resend-api-key"

# Image Storage
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"`}
          />
        </div>
      </CollapsibleSection>

      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold text-blue-900 mb-2">
          🚀 Getting Started
        </h2>
        <p className="text-blue-800 mb-4">
          Ready to dive deeper? Here are the next steps to explore the platform:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-blue-900 mb-2">For Developers</h4>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>
                •{" "}
                <a href="/admin/docs/core-concepts" className="hover:underline">
                  Core Concepts
                </a>
              </li>
              <li>
                •{" "}
                <a href="/admin/docs/api-routes" className="hover:underline">
                  API Documentation
                </a>
              </li>
              <li>
                •{" "}
                <a
                  href="/admin/docs/core-concepts/components"
                  className="hover:underline"
                >
                  Component Library
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-blue-900 mb-2">
              For Administrators
            </h4>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>
                •{" "}
                <a
                  href="/admin/docs/user-management"
                  className="hover:underline"
                >
                  User Management
                </a>
              </li>
              <li>
                •{" "}
                <a
                  href="/admin/docs/product-management"
                  className="hover:underline"
                >
                  Product Management
                </a>
              </li>
              <li>
                •{" "}
                <a
                  href="/admin/docs/review-management"
                  className="hover:underline"
                >
                  Review Management
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <BackToDocsButton />
    </DocsContainer>
  );
}
