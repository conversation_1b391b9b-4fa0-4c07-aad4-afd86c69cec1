"use client";

import React from "react";
import {
  Building2,
  BarChart3,
  Package,
  Star,
  Settings,
  CreditCard,
  Users,
  TrendingUp,
  Shield,
  Bell,
  FileText,
  DollarSign,
  Target,
  Calendar,
  MessageSquare,
  Award,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  Zap,
} from "lucide-react";

interface BusinessRouteCardProps {
  title: string;
  path: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  accessLevel: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
}

function BusinessRouteCard({
  title,
  path,
  description,
  icon,
  features,
  accessLevel,
  difficulty,
}: BusinessRouteCardProps) {
  const difficultyColors = {
    Beginner: "bg-green-100 text-green-800 border-green-200",
    Intermediate: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Advanced: "bg-red-100 text-red-800 border-red-200",
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <span className="text-sm text-gray-500 font-mono">{path}</span>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <span
            className={`inline-block px-2 py-1 text-xs rounded border ${difficultyColors[difficulty]}`}
          >
            {difficulty}
          </span>
          <span className="inline-block px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded border border-purple-200">
            {accessLevel}
          </span>
        </div>
      </div>

      <p className="text-gray-600 mb-4">{description}</p>

      <div className="space-y-3">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Key Features:
          </h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center gap-2"
              >
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

interface FeatureSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  routes: BusinessRouteCardProps[];
}

function FeatureSection({
  title,
  description,
  icon,
  routes,
}: FeatureSectionProps) {
  return (
    <section className="mb-12">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {routes.map((route, index) => (
          <BusinessRouteCard key={index} {...route} />
        ))}
      </div>
    </section>
  );
}

export default function OwnerBusinessPage() {
  const dashboardRoutes: BusinessRouteCardProps[] = [
    {
      title: "Business Dashboard",
      path: "/owner-admin",
      description:
        "Central hub for business owners to manage their products, view analytics, and monitor performance across all owned businesses.",
      icon: <Building2 className="h-5 w-5" />,
      features: [
        "Multi-business overview dashboard",
        "Real-time performance metrics",
        "Quick access to key management tools",
        "Product statistics and summaries",
        "Recent activity timeline",
        "Business health indicators",
        "Subscription status monitoring",
        "Quick action buttons",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
    {
      title: "Business Analytics",
      path: "/owner-admin/analytics",
      description:
        "Comprehensive analytics dashboard providing insights into business performance, customer behavior, and growth metrics.",
      icon: <BarChart3 className="h-5 w-5" />,
      features: [
        "Traffic analytics and trends",
        "Customer behavior insights",
        "Product performance metrics",
        "Revenue and conversion tracking",
        "Geographic performance data",
        "Time-based trend analysis",
        "Custom date range filtering",
        "Exportable reports and data",
      ],
      accessLevel: "Business Owner",
      difficulty: "Intermediate",
    },
  ];

  const productRoutes: BusinessRouteCardProps[] = [
    {
      title: "Product Management",
      path: "/owner-admin/products",
      description:
        "Comprehensive product management interface for adding, editing, and organizing business products and services.",
      icon: <Package className="h-5 w-5" />,
      features: [
        "Product creation and editing",
        "Inventory management tools",
        "Category and tag organization",
        "Photo and media management",
        "Pricing and availability updates",
        "SEO optimization tools",
        "Bulk product operations",
        "Product performance tracking",
      ],
      accessLevel: "Business Owner",
      difficulty: "Intermediate",
    },
    {
      title: "Review Management",
      path: "/owner-admin/reviews",
      description:
        "Manage customer reviews, respond to feedback, and monitor review performance across all business products.",
      icon: <Star className="h-5 w-5" />,
      features: [
        "Review monitoring dashboard",
        "Response management system",
        "Review analytics and insights",
        "Flagged review handling",
        "Customer feedback trends",
        "Review verification tools",
        "Response templates",
        "Review export functionality",
      ],
      accessLevel: "Business Owner",
      difficulty: "Intermediate",
    },
  ];

  const marketingRoutes: BusinessRouteCardProps[] = [
    {
      title: "Promotions & Campaigns",
      path: "/owner-admin/promotions",
      description:
        "Create and manage promotional campaigns, discounts, and marketing initiatives to boost business visibility.",
      icon: <Target className="h-5 w-5" />,
      features: [
        "Campaign creation and management",
        "Discount code generation",
        "Promotional scheduling",
        "Target audience selection",
        "Campaign performance tracking",
        "A/B testing capabilities",
        "Budget and spend monitoring",
        "ROI analysis and reporting",
      ],
      accessLevel: "Business Owner",
      difficulty: "Advanced",
    },
    {
      title: "Subscription Management",
      path: "/owner-admin/subscription",
      description:
        "Manage business subscriptions, billing, and premium features for enhanced business capabilities.",
      icon: <CreditCard className="h-5 w-5" />,
      features: [
        "Subscription plan management",
        "Billing history and invoices",
        "Payment method updates",
        "Feature usage monitoring",
        "Upgrade and downgrade options",
        "Subscription analytics",
        "Auto-renewal settings",
        "Support ticket integration",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
  ];

  const configurationRoutes: BusinessRouteCardProps[] = [
    {
      title: "Business Settings",
      path: "/owner-admin/settings",
      description:
        "Configure business profile, contact information, preferences, and operational settings.",
      icon: <Settings className="h-5 w-5" />,
      features: [
        "Business profile management",
        "Contact information updates",
        "Operational hours configuration",
        "Notification preferences",
        "Privacy and visibility settings",
        "Integration configurations",
        "Team member management",
        "API access controls",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
  ];

  const workflowSteps = [
    {
      step: 1,
      title: "Business Registration",
      description: "Register your business on the platform",
      icon: <Building2 className="h-5 w-5" />,
      actions: [
        "Complete business verification",
        "Upload required documentation",
        "Set up business profile",
        "Configure basic settings",
      ],
    },
    {
      step: 2,
      title: "Product Setup",
      description: "Add and configure your products",
      icon: <Package className="h-5 w-5" />,
      actions: [
        "Add product listings",
        "Upload product photos",
        "Set pricing and availability",
        "Configure categories and tags",
      ],
    },
    {
      step: 3,
      title: "Analytics & Monitoring",
      description: "Track performance and customer engagement",
      icon: <BarChart3 className="h-5 w-5" />,
      actions: [
        "Monitor product views",
        "Track customer interactions",
        "Analyze review patterns",
        "Generate performance reports",
      ],
    },
    {
      step: 4,
      title: "Growth & Optimization",
      description: "Optimize and scale your business",
      icon: <TrendingUp className="h-5 w-5" />,
      actions: [
        "Run promotional campaigns",
        "Respond to customer reviews",
        "Optimize product listings",
        "Expand to new categories",
      ],
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* Header Section */}
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Owner/Business Section Documentation
        </h1>
        <p className="text-lg text-gray-600 max-w-4xl mx-auto">
          Comprehensive guide for business owners to manage their products,
          analyze performance, engage with customers, and grow their business
          presence on the ReviewIt platform.
        </p>
      </div>

      {/* Quick Navigation */}
      <div className="mb-12 bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Navigation
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <a
            href="#dashboard-overview"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Building2 className="h-4 w-4" />
            Dashboard Overview
          </a>
          <a
            href="#product-management"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Package className="h-4 w-4" />
            Product Management
          </a>
          <a
            href="#marketing-tools"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Target className="h-4 w-4" />
            Marketing Tools
          </a>
          <a
            href="#business-settings"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Settings className="h-4 w-4" />
            Business Settings
          </a>
        </div>
      </div>

      {/* Business Owner Workflow */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Business Owner Workflow
        </h2>
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {workflowSteps.map((step, index) => (
              <div key={index} className="relative">
                <div className="bg-white rounded-lg p-4 shadow-sm border border-white">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
                      {step.step}
                    </div>
                    <div className="p-1 bg-blue-100 rounded text-blue-600">
                      {step.icon}
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    {step.description}
                  </p>
                  <ul className="space-y-1">
                    {step.actions.map((action, actionIndex) => (
                      <li
                        key={actionIndex}
                        className="text-xs text-gray-500 flex items-center gap-1"
                      >
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
                {index < workflowSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <ArrowRight className="h-5 w-5 text-blue-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Dashboard Overview Section */}
      <div id="dashboard-overview">
        <FeatureSection
          title="Dashboard & Analytics"
          description="Central management hub and performance analytics for business owners."
          icon={<Building2 className="h-6 w-6" />}
          routes={dashboardRoutes}
        />
      </div>

      {/* Product Management Section */}
      <div id="product-management">
        <FeatureSection
          title="Product & Review Management"
          description="Comprehensive tools for managing products, inventory, and customer reviews."
          icon={<Package className="h-6 w-6" />}
          routes={productRoutes}
        />
      </div>

      {/* Marketing Tools Section */}
      <div id="marketing-tools">
        <FeatureSection
          title="Marketing & Growth Tools"
          description="Promotional campaigns, subscription management, and business growth features."
          icon={<Target className="h-6 w-6" />}
          routes={marketingRoutes}
        />
      </div>

      {/* Business Settings Section */}
      <div id="business-settings">
        <FeatureSection
          title="Configuration & Settings"
          description="Business profile, operational settings, and platform configuration."
          icon={<Settings className="h-6 w-6" />}
          routes={configurationRoutes}
        />
      </div>

      {/* Business Types & Access Levels */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Business Types & Access Levels
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Shield className="h-6 w-6 text-green-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Verified Business
              </h3>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Full dashboard access</li>
              <li>• Advanced analytics</li>
              <li>• Promotional campaigns</li>
              <li>• Priority support</li>
              <li>• Custom branding options</li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Users className="h-6 w-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Standard Business
              </h3>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Basic dashboard access</li>
              <li>• Standard analytics</li>
              <li>• Product management</li>
              <li>• Review management</li>
              <li>• Basic subscription features</li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Building2 className="h-6 w-6 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Enterprise Business
              </h3>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• All premium features</li>
              <li>• Advanced integrations</li>
              <li>• Custom analytics</li>
              <li>• Dedicated support</li>
              <li>• White-label options</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Key Features Overview */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Key Features Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Performance Tracking
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                • <strong>Real-time Analytics:</strong> Monitor views, clicks,
                and conversions as they happen
              </li>
              <li>
                • <strong>Customer Insights:</strong> Understand customer
                behavior and preferences
              </li>
              <li>
                • <strong>Trend Analysis:</strong> Identify patterns and
                opportunities for growth
              </li>
              <li>
                • <strong>Competitive Benchmarking:</strong> Compare performance
                against industry standards
              </li>
              <li>
                • <strong>Custom Reports:</strong> Generate detailed reports for
                specific time periods
              </li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              Customer Engagement
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                • <strong>Review Management:</strong> Respond to and manage
                customer reviews effectively
              </li>
              <li>
                • <strong>Feedback Analysis:</strong> Analyze customer sentiment
                and feedback trends
              </li>
              <li>
                • <strong>Communication Tools:</strong> Direct communication
                channels with customers
              </li>
              <li>
                • <strong>Reputation Monitoring:</strong> Track and improve
                online reputation
              </li>
              <li>
                • <strong>Customer Support:</strong> Integrated support ticket
                system
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Getting Started Guide */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Getting Started as a Business Owner
        </h2>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                📋 Setup Checklist
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Complete Business Verification
                    </strong>
                    <p className="text-sm text-blue-700">
                      Submit required documents and verify your business
                      identity
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Set Up Business Profile
                    </strong>
                    <p className="text-sm text-blue-700">
                      Add business information, contact details, and branding
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">Add Products</strong>
                    <p className="text-sm text-blue-700">
                      Create detailed product listings with photos and
                      descriptions
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Configure Settings
                    </strong>
                    <p className="text-sm text-blue-700">
                      Set up notifications, privacy, and operational preferences
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                🚀 Quick Start Actions
              </h3>
              <div className="space-y-2">
                <a
                  href="/owner-admin"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Visit Dashboard
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    Access your business dashboard
                  </p>
                </a>
                <a
                  href="/owner-admin/products"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Manage Products
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    Add and manage your products
                  </p>
                </a>
                <a
                  href="/owner-admin/analytics"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      View Analytics
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    Monitor your business performance
                  </p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Implementation */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Technical Implementation
        </h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Authentication & Security
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Clerk authentication integration</li>
                <li>• Role-based access control</li>
                <li>• Business verification system</li>
                <li>• Secure data transmission</li>
                <li>• API rate limiting</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Data Management
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Prisma ORM integration</li>
                <li>• Real-time data updates</li>
                <li>• Analytics data aggregation</li>
                <li>• File upload management</li>
                <li>• Database optimization</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                User Interface
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Responsive design patterns</li>
                <li>• React Query for state management</li>
                <li>• Tailwind CSS styling</li>
                <li>• Component-based architecture</li>
                <li>• Accessibility compliance</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Best Practices for Business Owners
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
              <Award className="h-5 w-5" />
              Success Strategies
            </h3>
            <ul className="space-y-2 text-sm text-green-800">
              <li>
                • <strong>Regular Updates:</strong> Keep product information
                current and accurate
              </li>
              <li>
                • <strong>Active Engagement:</strong> Respond promptly to
                customer reviews and feedback
              </li>
              <li>
                • <strong>Quality Photos:</strong> Use high-resolution images
                for better customer engagement
              </li>
              <li>
                • <strong>Monitor Analytics:</strong> Regular review of
                performance metrics for optimization
              </li>
              <li>
                • <strong>Customer Service:</strong> Maintain excellent customer
                service standards
              </li>
            </ul>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Common Pitfalls to Avoid
            </h3>
            <ul className="space-y-2 text-sm text-orange-800">
              <li>
                • <strong>Ignoring Reviews:</strong> Not responding to customer
                feedback can hurt reputation
              </li>
              <li>
                • <strong>Outdated Information:</strong> Keeping incorrect
                business hours or contact details
              </li>
              <li>
                • <strong>Poor Image Quality:</strong> Using low-resolution or
                unprofessional photos
              </li>
              <li>
                • <strong>Neglecting Analytics:</strong> Missing opportunities
                identified through data analysis
              </li>
              <li>
                • <strong>Inconsistent Branding:</strong> Maintaining consistent
                brand image across all listings
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Support & Resources */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Support & Resources
        </h2>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Documentation
              </h3>
              <p className="text-sm text-gray-600">
                Comprehensive guides and API documentation
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                <MessageSquare className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Support Chat</h3>
              <p className="text-sm text-gray-600">
                24/7 live chat support for business owners
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Community</h3>
              <p className="text-sm text-gray-600">
                Connect with other business owners and share experiences
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-3">
                <Bell className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Updates</h3>
              <p className="text-sm text-gray-600">
                Stay informed about new features and platform updates
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <a
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          ← Back to Documentation Home
        </a>
      </div>
    </div>
  );
}
