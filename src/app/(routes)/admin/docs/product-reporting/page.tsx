import React from 'react';

const ProductReportingPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Product Reporting</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Product Reporting
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>

                <div className="bg-gray-50 p-6 rounded-lg mb-8">
                    <h2 className="text-xl font-semibold mb-4 text-gray-900">Table of Contents</h2>
                    <ul className="space-y-2 text-sm">
                        <li><a href="#overview" className="text-blue-600 hover:underline">Overview</a></li>
                        <li><a href="#viewing-reports" className="text-blue-600 hover:underline">Viewing Product Reports</a></li>
                        <li><a href="#moderation-actions" className="text-blue-600 hover:underline">Moderation Actions</a></li>
                        <li><a href="#user-reporting-flow" className="text-blue-600 hover:underline">User Reporting Flow</a></li>
                        <li><a href="#technical-implementation" className="text-blue-600 hover:underline">Technical Implementation</a></li>
                        <li><a href="#best-practices" className="text-blue-600 hover:underline">Admin Best Practices</a></li>
                        <li><a href="#troubleshooting" className="text-blue-600 hover:underline">Troubleshooting</a></li>
                    </ul>
                </div>

                <h2 id="overview" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Overview</h2>
                <p className="mb-4">
                    The product reporting system allows users to flag inappropriate or problematic products/businesses.
                    Reports are managed through a unified admin interface alongside review reports, providing a comprehensive 
                    content moderation workflow.
                </p>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Key Features</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>User-Friendly Reporting</strong> - Simple modal interface with predefined report categories</li>
                    <li><strong>Unified Admin Dashboard</strong> - Product and review reports managed in one interface</li>
                    <li><strong>Comprehensive Tracking</strong> - Full audit trail with status updates and admin actions</li>
                    <li><strong>Duplicate Prevention</strong> - Users cannot report the same product multiple times</li>
                    <li><strong>Self-Report Protection</strong> - Product owners cannot report their own products</li>
                </ul>

                <h2 id="viewing-reports" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Viewing Product Reports</h2>
                <p className="mb-4">Product reports are accessible through <code>/admin/reports</code> and appear in a unified table alongside review reports. The interface includes:</p>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Report Table Columns</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Content</strong> - Product name and report type indicator</li>
                    <li><strong>Reporter</strong> - User who submitted the report with profile link</li>
                    <li><strong>Reason</strong> - Selected report category and additional details</li>
                    <li><strong>Status</strong> - PENDING, REVIEWED, or RESOLVED with color-coded badges</li>
                    <li><strong>Date</strong> - Submission timestamp with relative time</li>
                    <li><strong>Actions</strong> - View details and status update buttons</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Filtering Options</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Status Filter</strong> - All Status, Pending, Reviewed, Resolved</li>
                    <li><strong>Type Filter</strong> - All Types, Review Reports, Product Reports</li>
                    <li><strong>Sorting</strong> - By date, status, or reporter name</li>
                    <li><strong>Pagination</strong> - Navigate through large report lists</li>
                </ul>

                <h2 id="moderation-actions" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Moderation Actions</h2>
                <p className="mb-4">Clicking "View" on any product report opens a detailed modal with comprehensive information and moderation tools:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Report Details Modal</h3>
                <p className="mb-4">The unified report details modal provides three main tabs:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Report Tab</strong> - Status, submission date, reason, and admin notes</li>
                    <li><strong>Reported Product Tab</strong> - Product details, images, description, and creator info</li>
                    <li><strong>Users Tab</strong> - Reporter and product creator profiles with links</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Status Management</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>PENDING</strong> - Initial status when report is submitted</li>
                    <li><strong>REVIEWED</strong> - Admin has examined the report and is taking action</li>
                    <li><strong>RESOLVED</strong> - Report has been addressed and closed</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Admin Tools</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Status Updates</strong> - Change report status with dropdown selection</li>
                    <li><strong>Admin Notes</strong> - Add internal notes for tracking and communication</li>
                    <li><strong>Audit Trail</strong> - Automatic logging of all admin actions</li>
                    <li><strong>Quick Navigation</strong> - Direct links to view full product or user profiles</li>
                </ul>

                <h2 id="user-reporting-flow" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">User Reporting Flow</h2>
                <p className="mb-4">Users can report products through a streamlined interface with built-in protections:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Report Categories</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Inappropriate Content</strong> - Offensive or unsuitable material</li>
                    <li><strong>Spam or Fake Listing</strong> - Non-genuine business listings</li>
                    <li><strong>Copyright Violation</strong> - Unauthorized use of copyrighted content</li>
                    <li><strong>Misleading Information</strong> - False or deceptive business details</li>
                    <li><strong>Duplicate Listing</strong> - Same business listed multiple times</li>
                    <li><strong>Business is Closed/Doesn't Exist</strong> - Outdated or invalid listings</li>
                    <li><strong>Other</strong> - Custom reason with required explanation</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Validation & Protection</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Authentication Required</strong> - Users must be signed in to report</li>
                    <li><strong>Duplicate Prevention</strong> - One report per user per product</li>
                    <li><strong>Self-Report Block</strong> - Product owners cannot report their own products</li>
                    <li><strong>Input Sanitization</strong> - XSS protection and content validation</li>
                    <li><strong>Character Limits</strong> - Reason (10-500 chars), Notes (max 1000 chars)</li>
                </ul>

                <h2 id="technical-implementation" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Technical Implementation</h2>
                <p className="mb-4">The product reporting system integrates seamlessly with the existing review reporting infrastructure:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">API Endpoints</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>POST /api/products/[productId]/report</strong> - Submit new product report</li>
                    <li><strong>GET /api/admin/reports</strong> - Fetch all reports (unified endpoint)</li>
                    <li><strong>GET /api/admin/product-reports/[reportId]</strong> - Get specific product report details</li>
                    <li><strong>PUT /api/admin/product-reports/[reportId]</strong> - Update product report status</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Database Schema</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>ProductReport Model</strong> - Dedicated table with proper indexing</li>
                    <li><strong>AdminAction Logging</strong> - Automatic audit trail for all admin actions</li>
                    <li><strong>User Relations</strong> - Links to reporter, product creator, and resolver</li>
                    <li><strong>Status Tracking</strong> - Timestamps for creation, updates, and resolution</li>
                </ul>

                <h2 id="best-practices" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Admin Best Practices</h2>
                <p className="mb-4">Guidelines for effectively managing product reports:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Response Time Guidelines</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>High Priority</strong> - Inappropriate content, spam (within 24 hours)</li>
                    <li><strong>Medium Priority</strong> - Misleading info, duplicates (within 3 days)</li>
                    <li><strong>Low Priority</strong> - Closed businesses, minor issues (within 1 week)</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Investigation Process</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Review Report Details</strong> - Check reason and additional notes</li>
                    <li><strong>Examine Product Content</strong> - Visit product page and verify claims</li>
                    <li><strong>Check User History</strong> - Review reporter and product creator profiles</li>
                    <li><strong>Document Findings</strong> - Add comprehensive notes for future reference</li>
                    <li><strong>Take Appropriate Action</strong> - Update status and communicate if needed</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Communication Guidelines</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Professional Tone</strong> - Maintain neutral, helpful communication</li>
                    <li><strong>Clear Documentation</strong> - Use admin notes to explain decisions</li>
                    <li><strong>Consistent Standards</strong> - Apply moderation rules uniformly</li>
                    <li><strong>Follow-up</strong> - Monitor resolved reports for recurring issues</li>
                </ul>

                <h2 id="troubleshooting" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Troubleshooting</h2>
                <p className="mb-4">Common issues and solutions:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Report Not Loading</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Check Admin Permissions</strong> - Ensure user has ADMIN role</li>
                    <li><strong>Verify Report ID</strong> - Confirm the report exists in database</li>
                    <li><strong>Browser Console</strong> - Check for JavaScript errors</li>
                    <li><strong>API Response</strong> - Verify backend endpoints are responding</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Status Update Failures</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Network Issues</strong> - Check internet connection and retry</li>
                    <li><strong>Session Timeout</strong> - Re-authenticate and try again</li>
                    <li><strong>Validation Errors</strong> - Ensure all required fields are filled</li>
                    <li><strong>Database Conflicts</strong> - Check for concurrent modifications</li>
                </ul>

                <a
                    href="#top"
                    className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                >
                    ↑
                </a>
            </div>
        </div>
    );
};

export default ProductReportingPage;
