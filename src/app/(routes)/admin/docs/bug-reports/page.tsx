"use client";

import React from "react";
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  User,
  Settings,
  ArrowRight,
  AlertCircle,
  XCircle,
} from "lucide-react";

export default function BugReportsPage() {
  // Bug Report Statuses (as implemented)
  const bugStatuses = [
    {
      status: "OPEN",
      description: "Newly submitted and awaiting admin review",
      color: "bg-yellow-100 text-yellow-800",
      icon: <AlertTriangle className="h-5 w-5" />,
    },
    {
      status: "IN_PROGRESS",
      description: "Being investigated or worked on by admin",
      color: "bg-blue-100 text-blue-800",
      icon: <Clock className="h-5 w-5" />,
    },
    {
      status: "RESOLVED",
      description: "Fix has been implemented and marked as resolved",
      color: "bg-green-100 text-green-800",
      icon: <CheckCircle className="h-5 w-5" />,
    },
    {
      status: "CLOSED",
      description: "Closed by admin (completed, out of scope, or no further action)",
      color: "bg-gray-100 text-gray-800",
      icon: <XCircle className="h-5 w-5" />,
    },
    {
      status: "WONT_FIX",
      description: "<PERSON><PERSON> decided not to address this issue",
      color: "bg-red-100 text-red-800",
      icon: <AlertCircle className="h-5 w-5" />,
    },
  ];

  // Workflow Steps (as implemented)
  const workflowSteps = [
    {
      step: 1,
      title: "User Submission",
      description: "Authenticated user submits a bug report via the form",
      icon: <FileText className="h-5 w-5" />,
      actions: [
        "User must be signed in (Clerk authentication)",
        "POST /api/bugreports",
        "Required: title, description",
        "Optional: browser, device, mobile_os",
        "Initial status: 'OPEN'",
        "Success notification shown to user",
      ],
    },
    {
      step: 2,
      title: "Admin Review & Status Update",
      description: "Admin reviews the report and updates its status",
      icon: <Settings className="h-5 w-5" />,
      actions: [
        "Admin authentication required (role: ADMIN)",
        "PUT /api/admin/bugreports/[id]/status",
        "Allowed statuses: OPEN, IN_PROGRESS, RESOLVED, CLOSED, WONT_FIX",
        "Optional resolution notes can be added",
        "If status is RESOLVED: resolved_at and resolved_by are set",
        "All admin actions are logged in AdminAction table",
      ],
    },
  ];

  // Form Fields (as implemented)
  const formFields = [
    { field: "title", required: true, description: "Brief summary of the issue" },
    { field: "description", required: true, description: "Detailed explanation and reproduction steps" },
    { field: "browser", required: false, description: "Browser name & version (e.g., Chrome 91.0)" },
    { field: "device", required: false, description: "Device information (e.g., iPhone 12, Windows PC)" },
    { field: "mobile_os", required: false, description: "Mobile OS version (e.g., iOS 14.5, Android 11)" },
  ];

  // API Endpoints (as implemented)
  const apiEndpoints = [
    {
      method: "POST",
      endpoint: "/api/bugreports",
      description: "Create a new bug report (user)",
      auth: "User authentication required",
    },
    {
      method: "GET",
      endpoint: "/api/admin/bugreports",
      description: "Get all bug reports (admin)",
      auth: "Admin authentication required",
    },
    {
      method: "GET",
      endpoint: "/api/admin/bugreports/[id]",
      description: "Get specific bug report with details (admin)",
      auth: "Admin authentication required",
    },
    {
      method: "PUT",
      endpoint: "/api/admin/bugreports/[id]/status",
      description: "Update bug report status (admin)",
      auth: "Admin authentication required",
    },
    {
      method: "GET",
      endpoint: "/api/user/bugreports",
      description: "Get user's own reports + active bugs (user)",
      auth: "User authentication required",
    },
  ];

  return (
    <div className="max-w-4xl mx-auto p-8 space-y-12">
      {/* Header */}
      <header className="text-center">
        <h1 className="text-4xl font-bold mb-4">Bug Report System</h1>
        <p className="text-gray-600">
          Documentation for the actual bug report system implementation - how users submit reports and how admins manage them.
        </p>
      </header>

      {/* Status Overview */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Bug Report Statuses</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {bugStatuses.map((s) => (
            <div key={s.status} className="flex items-start gap-3 p-4 border rounded-lg bg-white">
              <div className={`p-2 rounded-lg ${s.color}`}>{s.icon}</div>
              <div>
                <h3 className="font-medium">{s.status}</h3>
                <p className="text-sm text-gray-600">{s.description}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Workflow */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Bug Report Workflow</h2>
        <div className="space-y-6">
          {workflowSteps.map((step) => (
            <div key={step.step} className="border-l-4 border-blue-500 pl-6">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
                  {step.step}
                </div>
                {step.icon}
                <span className="font-medium text-lg">Step {step.step}: {step.title}</span>
              </div>
              <p className="text-sm text-gray-700 mb-3">{step.description}</p>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                {step.actions.map((action, idx) => (
                  <li key={idx}>{action}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      {/* Form Fields */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Bug Report Form Fields</h2>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border border-gray-200">
            <thead>
              <tr className="bg-gray-50 text-left">
                <th className="p-3 border-b font-medium">Field</th>
                <th className="p-3 border-b font-medium">Required</th>
                <th className="p-3 border-b font-medium">Description</th>
              </tr>
            </thead>
            <tbody>
              {formFields.map((field) => (
                <tr key={field.field} className="bg-white border-b">
                  <td className="p-3 font-mono text-blue-600">{field.field}</td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      field.required 
                        ? "bg-red-100 text-red-800" 
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {field.required ? "Yes" : "No"}
                    </span>
                  </td>
                  <td className="p-3 text-gray-600">{field.description}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>

      {/* API Endpoints */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">API Endpoints</h2>
        <div className="space-y-4">
          {apiEndpoints.map((endpoint, idx) => (
            <div key={idx} className="border border-gray-200 rounded-lg p-4 bg-white">
              <div className="flex items-center gap-3 mb-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  endpoint.method === "GET" ? "bg-green-100 text-green-800" :
                  endpoint.method === "POST" ? "bg-blue-100 text-blue-800" :
                  endpoint.method === "PUT" ? "bg-yellow-100 text-yellow-800" :
                  "bg-gray-100 text-gray-800"
                }`}>
                  {endpoint.method}
                </span>
                <code className="text-sm bg-gray-100 px-2 py-1 rounded">{endpoint.endpoint}</code>
              </div>
              <p className="text-sm text-gray-600 mb-1">{endpoint.description}</p>
              <p className="text-xs text-gray-500">{endpoint.auth}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Database Schema */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Database Schema</h2>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="font-medium mb-3">BugReport Table</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Core Fields:</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• <code>id</code> - UUID primary key</li>
                <li>• <code>title</code> - String (required)</li>
                <li>• <code>description</code> - String (required)</li>
                <li>• <code>status</code> - BugReportStatus enum</li>
                <li>• <code>created_at</code> - DateTime</li>
                <li>• <code>updated_at</code> - DateTime</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Optional Fields:</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• <code>browser</code> - String (optional)</li>
                <li>• <code>device</code> - String (optional)</li>
                <li>• <code>mobile_os</code> - String (optional)</li>
                <li>• <code>resolution_notes</code> - String (optional)</li>
                <li>• <code>resolved_at</code> - DateTime (optional)</li>
                <li>• <code>resolved_by</code> - User ID (optional)</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Admin Features */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Admin Management Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border border-gray-200 rounded-lg p-6 bg-white">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <User className="h-5 w-5 text-blue-500" />
              Available Features
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• View all bug reports in admin panel</li>
              <li>• Filter reports by status (OPEN, IN_PROGRESS, etc.)</li>
              <li>• Update bug report status</li>
              <li>• Add resolution notes</li>
              <li>• View reporter and resolver information</li>
              <li>• Pagination support (10 items per page)</li>
              <li>• Audit trail via AdminAction logging</li>
            </ul>
          </div>
          <div className="border border-gray-200 rounded-lg p-6 bg-white">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Not Implemented
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Priority levels or SLA tracking</li>
              <li>• File attachments (screenshots/videos)</li>
              <li>• Advanced search and filtering</li>
              <li>• Internal comments or communication</li>
              <li>• Email notifications</li>
              <li>• Bulk operations</li>
              <li>• Analytics or reporting dashboard</li>
              <li>• Custom tags or categories</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Navigation Back */}
      <div className="text-center pt-8 border-t border-gray-200">
        <a
          href="/admin/docs"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          ← Back to Documentation Home
        </a>
      </div>
    </div>
  );
}

