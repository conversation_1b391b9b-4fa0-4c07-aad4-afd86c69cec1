"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

interface ActionConfig {
  label: string;
  action: string;
  confirm?: string; // Optional confirmation message
}

const ACTIONS: ActionConfig[] = [
  {
    label: "Invalidate All Products Cache",
    action: "invalidate-all-products",
    confirm: "Are you sure you want to invalidate ALL product caches?",
  },
  {
    label: "Invalidate Search Cache",
    action: "invalidate-search",
  },
  {
    label: "Invalidate Admin Cache",
    action: "invalidate-admin",
  },
  {
    label: "Invalidate Review Caches",
    action: "invalidate-reviews",
    confirm: "This will clear latest, popular, and trending review caches.",
  },
  {
    label: "Invalidate ALL Caches (Complete Redis Flush)",
    action: "invalidate-all",
    confirm:
      "This will completely clear the entire Redis cache database. This is very expensive and will impact performance significantly. Are you sure?",
  },
];

export default function AdminCachePage() {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  const runAction = async (config: ActionConfig) => {
    if (config.confirm && !window.confirm(config.confirm)) {
      return;
    }
    try {
      setLoadingAction(config.action);
      const res = await fetch("/api/admin/cache/invalidate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: config.action }),
      });
      const data = await res.json();
      if (res.ok && data.success) {
        toast.success(data.message ?? "Cache invalidated successfully.");
      } else {
        toast.error(data.message ?? "Failed to invalidate cache.");
      }
    } catch (error) {
      toast.error("Unexpected error invalidating cache.");
      console.error("Cache invalidation error", error);
    } finally {
      setLoadingAction(null);
    }
  };

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-semibold mb-4">Cache Management</h1>
      <p className="text-sm text-muted-foreground mb-6">
        Use the buttons below to invalidate specific cache groups. These
        operations impact performance; please use sparingly.
      </p>

      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {ACTIONS.map((cfg) => (
          <Button
            key={cfg.action}
            variant="secondary"
            disabled={loadingAction !== null}
            className="whitespace-normal text-left h-auto py-4"
            onClick={() => runAction(cfg)}
          >
            {loadingAction === cfg.action ? "Running..." : cfg.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
