'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ReviewModerationQueue, { ReviewQueueRefType } from '@/app/components/admin/ReviewModerationQueue';
import ReviewAnalytics from '@/app/components/admin/ReviewAnalytics';
import ReviewDetails from '@/app/components/admin/ReviewDetails';
import { iReview } from '@/app/util/Interfaces';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { toast } from 'sonner';

function ErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
    return (
        <div className="p-6 rounded-lg border border-red-200 bg-red-50 my-4">
            <h2 className="text-xl font-bold text-red-800 mb-2">Something went wrong:</h2>
            <p className="text-red-600 mb-4">{error.message}</p>
            <button
                onClick={resetErrorBoundary}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
                Try again
            </button>
        </div>
    );
}

export default function ReviewModerationPage() {
    const [selectedReview, setSelectedReview] = useState<iReview | null>(null);
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);
    const queueRef = useRef<ReviewQueueRefType>(null);

    // Prevent Cloudflare analytics from causing issues
    useEffect(() => {
        window.addEventListener('error', (event) => {
            if (event.filename?.includes('cloudflareinsights.com')) {
                event.preventDefault();
                event.stopPropagation();
                console.warn('Cloudflare insights error suppressed');
                return false;
            }
        }, true);
    }, []);

    const handleModerateReview = async (
        action: 'APPROVE' | 'REJECT' | 'FLAG',
        reason?: string
    ) => {
        if (!selectedReview) return;

        try {
            const response = await fetch('/api/admin/reviews/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reviewIds: [selectedReview.id],
                    action,
                    reason,
                }),
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to moderate review');
            }

            setIsDetailsOpen(false);
            setSelectedReview(null);
            toast.success(`Review ${action.toLowerCase()}ed successfully`);

            // Refresh the queue after moderation
            if (queueRef.current && queueRef.current.fetchReviews) {
                await queueRef.current.fetchReviews();
            } else {
                // Trigger a refresh if the direct method isn't available
                setRefreshTrigger(prev => prev + 1);
            }
        } catch (error) {
            toast.error(error instanceof Error ? error.message : 'An error occurred');
        }
    };

    return (
        <div className="w-full px-4 py-6">
            <h1 className="mb-6 text-3xl font-bold">Review Moderation</h1>

            <ErrorBoundary
                FallbackComponent={ErrorFallback}
                onReset={() => {
                    // Reset the component state here
                }}
            >
                <Tabs defaultValue="queue" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="queue">Moderation Queue</TabsTrigger>
                        <TabsTrigger value="analytics">Analytics</TabsTrigger>
                    </TabsList>

                    <TabsContent value="queue" className="space-y-4">
                        <ReviewModerationQueue
                            key={`queue-${refreshTrigger}`}
                            onReviewClick={(review) => {
                                setSelectedReview(review);
                                setIsDetailsOpen(true);
                            }}
                            ref={queueRef}
                        />
                    </TabsContent>

                    <TabsContent value="analytics">
                        <ReviewAnalytics />
                    </TabsContent>
                </Tabs>
            </ErrorBoundary>

            {selectedReview && (
                <ReviewDetails
                    review={selectedReview}
                    isOpen={isDetailsOpen}
                    onClose={() => {
                        setIsDetailsOpen(false);
                        setSelectedReview(null);
                    }}
                    onModerate={handleModerateReview}
                />
            )}
        </div>
    );
}