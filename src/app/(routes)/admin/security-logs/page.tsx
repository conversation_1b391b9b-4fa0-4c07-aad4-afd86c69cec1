'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import LoadingSpinner from '@/app/components/LoadingSpinner';

// Types for security logs
interface SecurityLog {
  id: string;
  eventType: string;
  userId: string;
  ipAddress: string;
  userAgent: string;
  requestPath: string;
  details: string | null;
  severity: string;
  timestamp: string;
}

interface SecurityLogsResponse {
  logs: SecurityLog[];
  total: number;
}

// Function to fetch security logs
async function getSecurityLogs(
  page: number = 1,
  limit: number = 10,
  eventType?: string,
  severity?: string,
  userId?: string
): Promise<SecurityLogsResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (eventType) params.append('eventType', eventType);
  if (severity) params.append('severity', severity);
  if (userId) params.append('userId', userId);

  const response = await fetch(`/api/admin/security-logs?${params.toString()}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch security logs');
  }
  
  return response.json();
}

export default function SecurityLogsPage() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [eventType, setEventType] = useState<string | undefined>(undefined);
  const [severity, setSeverity] = useState<string | undefined>(undefined);
  const [userId, setUserId] = useState<string>('');
  
  const {
    data,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['securityLogs', page, limit, eventType, severity, userId],
    queryFn: () => getSecurityLogs(page, limit, eventType, severity, userId || undefined),
  });
  
  // Reset to page 1 when filters change
  useEffect(() => {
    setPage(1);
  }, [eventType, severity, userId]);
  
  // Get severity badge color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW':
        return 'bg-blue-100 text-blue-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format event type for display
  const formatEventType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ');
  };
  
  // Parse and format details JSON
  const parseDetails = (details: string | null) => {
    if (!details) return null;
    
    try {
      const parsed = JSON.parse(details);
      return (
        <div className="space-y-1">
          {Object.entries(parsed).map(([key, value]) => (
            <div key={key} className="text-xs">
              <span className="font-medium">{key}: </span>
              <span>{String(value)}</span>
            </div>
          ))}
        </div>
      );
    } catch (e) {
      return <span className="text-xs">{details}</span>;
    }
  };
  
  if (isError) {
    return (
      <div className="p-4 text-red-500">
        <h2 className="text-xl font-bold">Error Loading Security Logs</h2>
        <p>There was an error loading the security logs. Please try again later.</p>
        <Button onClick={() => refetch()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6 p-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Security Logs</h1>
        <Button onClick={() => refetch()}>Refresh</Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Event Type</label>
              <Select
                value={eventType || 'all_events'}
                onValueChange={(value) => setEventType(value === 'all_events' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Events" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_events">All Events</SelectItem>
                  <SelectItem value="SUSPENDED_ACCESS_ATTEMPT">Suspended Access</SelectItem>
                  <SelectItem value="BANNED_ACCESS_ATTEMPT">Banned Access</SelectItem>
                  <SelectItem value="ADMIN_ACCESS_ATTEMPT">Admin Access</SelectItem>
                  <SelectItem value="AUTHENTICATION_FAILURE">Auth Failure</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">Severity</label>
              <Select
                value={severity || 'all_severities'}
                onValueChange={(value) => setSeverity(value === 'all_severities' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Severities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_severities">All Severities</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="CRITICAL">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">User ID</label>
              <Input
                placeholder="Filter by user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            
            <div className="flex items-end">
              <Button
                onClick={() => {
                  setEventType(undefined);
                  setSeverity(undefined);
                  setUserId('');
                }}
                variant="outline"
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Log Entries</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Event Type</TableHead>
                      <TableHead>User ID</TableHead>
                      <TableHead>Path</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data?.logs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6">
                          No security logs found
                        </TableCell>
                      </TableRow>
                    ) : (
                      data?.logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-medium whitespace-nowrap">
                            {format(new Date(log.timestamp), 'yyyy-MM-dd HH:mm:ss')}
                          </TableCell>
                          <TableCell>{formatEventType(log.eventType)}</TableCell>
                          <TableCell className="font-mono text-xs">{log.userId}</TableCell>
                          <TableCell className="max-w-[200px] truncate">{log.requestPath}</TableCell>
                          <TableCell>{log.ipAddress}</TableCell>
                          <TableCell>
                            <Badge className={getSeverityColor(log.severity)}>
                              {log.severity}
                            </Badge>
                          </TableCell>
                          <TableCell className="max-w-[200px]">
                            {parseDetails(log.details)}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Showing {data?.logs.length || 0} of {data?.total || 0} logs
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {page}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={!data || data.logs.length < limit}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
