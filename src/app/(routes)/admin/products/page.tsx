'use client';

import React from 'react';
import ProductsManagement from '@/components/products-management';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
  return (
    <div className="p-6 rounded-lg border border-red-200 bg-red-50 my-4">
      <h2 className="text-xl font-bold text-red-800 mb-2">Something went wrong:</h2>
      <p className="text-red-600 mb-4">{error.message}</p>
      <button
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        Try again
      </button>
    </div>
  );
}

export default function ProductsPage() {
  return (
    <div className="w-full px-4 py-6">
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <ProductsManagement />
      </ErrorBoundary>
    </div>
  );
}