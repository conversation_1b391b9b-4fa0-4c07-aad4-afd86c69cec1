/**
 * Centralized cache invalidation utilities
 * Handles both server-side Redis cache and client-side React Query cache
 */

export interface CacheInvalidationOptions {
  invalidateServer?: boolean;
  invalidateClient?: boolean;
  queryKeys?: string[][];
  serverActions?: string[];
}

/**
 * Comprehensive cache invalidation for products
 * Call this after any product creation, update, or deletion
 */
export async function invalidateProductCaches(options: CacheInvalidationOptions = {}) {
  const {
    invalidateServer = true,
    invalidateClient = true,
    queryKeys = [["products"]],
    serverActions = ["invalidate-all-products", "invalidate-search"]
  } = options;

  const results = {
    serverInvalidation: false,
    clientInvalidation: false,
    nextjsRevalidation: false,
    errors: [] as string[]
  };

  // 1. Invalidate server-side Redis cache
  if (invalidateServer) {
    try {
      for (const action of serverActions) {
        await fetch('/api/admin/cache/invalidate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action })
        });
      }
      results.serverInvalidation = true;
    } catch (error) {
      results.errors.push(`Server cache invalidation failed: ${error}`);
    }
  }

  // 2. Trigger Next.js revalidation
  try {
    await fetch('/api/revalidate?path=/browse', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    results.nextjsRevalidation = true;
  } catch (error) {
    results.errors.push(`Next.js revalidation failed: ${error}`);
  }

  // 3. Invalidate client-side React Query cache (if available)
  if (invalidateClient && typeof window !== 'undefined') {
    try {
      // Dispatch a custom event that components can listen to
      window.dispatchEvent(new CustomEvent('invalidate-product-cache', {
        detail: { queryKeys }
      }));
      results.clientInvalidation = true;
    } catch (error) {
      results.errors.push(`Client cache invalidation failed: ${error}`);
    }
  }

  return results;
}

/**
 * Hook to listen for cache invalidation events
 * Use this in components that need to respond to cache invalidation
 */
export function useCacheInvalidationListener(queryClient: any, queryKeys: string[][] = [["products"]]) {
  if (typeof window === 'undefined') return;

  const handleInvalidation = async (event: CustomEvent) => {
    const { queryKeys: eventQueryKeys } = event.detail;
    const keysToInvalidate = eventQueryKeys || queryKeys;
    
    for (const queryKey of keysToInvalidate) {
      await queryClient.invalidateQueries({ queryKey });
    }
  };

  window.addEventListener('invalidate-product-cache', handleInvalidation as unknown as EventListener);
  
  return () => {
    window.removeEventListener('invalidate-product-cache', handleInvalidation as unknown as EventListener);
  };
}