import { cookies } from 'next/headers';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from './prismaClient';
import { iProductViewEvent } from './Interfaces';

const SESSION_COOKIE_NAME = 'review_it_session';
const VIEW_COOKIE_PREFIX = 'product_view_';
const SESSION_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days
const VIEW_THROTTLE = 3; // Count a new view every N visits

interface ViewCookieData {
  visitCount: number;
  lastVisit: number;
  sessionId: string;
}

/**
 * Get or create a session ID for the current user
 */
export function getOrCreateSessionId(): string {
  const cookieStore = cookies();
  const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);

  if (sessionCookie?.value) {
    return sessionCookie.value;
  }

  const sessionId = uuidv4();
  cookieStore.set(SESSION_COOKIE_NAME, sessionId, {
    maxAge: SESSION_DURATION,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
  });

  return sessionId;
}

/**
 * Get the view cookie data for a specific product
 */
export function getProductViewCookie(productId: string): ViewCookieData | null {
  const cookieStore = cookies();
  const viewCookie = cookieStore.get(`${VIEW_COOKIE_PREFIX}${productId}`);

  if (!viewCookie?.value) {
    return null;
  }

  try {
    return JSON.parse(viewCookie.value);
  } catch {
    return null;
  }
}

/**
 * Update the view cookie data for a specific product
 */
export function updateProductViewCookie(productId: string, sessionId: string): ViewCookieData {
  const cookieStore = cookies();
  const existingData = getProductViewCookie(productId);

  const newData: ViewCookieData = {
    visitCount: (existingData?.visitCount || 0) + 1,
    lastVisit: Date.now(),
    sessionId,
  };

  cookieStore.set(`${VIEW_COOKIE_PREFIX}${productId}`, JSON.stringify(newData), {
    maxAge: SESSION_DURATION,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
  });

  return newData;
}

/**
 * Check if we should count this as a new view based on throttling rules
 */
export function shouldCountView(viewData: ViewCookieData | null): boolean {
  if (!viewData) {
    return true; // First visit
  }

  // First visit or every VIEW_THROTTLE visits after that
  return viewData.visitCount === 1 || viewData.visitCount % VIEW_THROTTLE === 0;
}

/**
 * Record a product view event
 */
export async function recordProductView(
  productId: string,
  userId?: string,
  source?: string,
  deviceType?: string
): Promise<void> {
  const sessionId = getOrCreateSessionId();
  const viewData = updateProductViewCookie(productId, sessionId);
  const shouldCount = shouldCountView(viewData);

  // Create the view event
  const viewEvent: Omit<iProductViewEvent, 'id'> = {
    productId,
    userId: userId || undefined,
    sessionId,
    timestamp: new Date(),
    source: source || undefined,
    deviceType: deviceType || undefined,
    isNewUser: !viewData,
    isThrottled: !shouldCount,
  };

  try {
    // Record the view event
    const viewEventQuery = `
      INSERT INTO "ProductViewEvent" (
        "id", "productId", "userId", "sessionId", "timestamp",
        "source", "deviceType", "isNewUser", "isThrottled"
      ) VALUES (
        gen_random_uuid(), '${productId}',
        ${userId ? `'${userId}'` : 'NULL'},
        '${sessionId}',
        NOW(),
        ${viewEvent.source ? `'${viewEvent.source}'` : 'NULL'},
        ${viewEvent.deviceType ? `'${viewEvent.deviceType}'` : 'NULL'},
        ${viewEvent.isNewUser},
        ${viewEvent.isThrottled}
      ) RETURNING *
    `;

    await prisma.$queryRawUnsafe(viewEventQuery);

    // Update analytics if this view should be counted
    if (shouldCount) {
      const analyticsQuery = `
        INSERT INTO "ProductAnalytics" (
          "id", "productId", "totalViews", "uniqueVisitors",
          "peakHours", "weekdayStats", "lastUpdated"
        ) VALUES (
          gen_random_uuid(), '${productId}', 1, 1,
          '${JSON.stringify({ [new Date().getHours()]: 1 })}',
          '${JSON.stringify({ [new Date().getDay()]: 1 })}',
          NOW()
        )
        ON CONFLICT ("productId") DO UPDATE
        SET
          "totalViews" = "ProductAnalytics"."totalViews" + 1,
          "lastUpdated" = NOW()
        RETURNING *
      `;

      await prisma.$queryRawUnsafe(analyticsQuery);
    }

    // Update user interaction if user is authenticated
    if (userId) {
      const interactionQuery = `
        INSERT INTO "UserProductInteraction" (
          "id", "userId", "productId", "lastViewed",
          "viewCount", "hasReviewed", "hasLiked", "averageTimeSpent"
        ) VALUES (
          gen_random_uuid(), '${userId}', '${productId}',
          NOW(), 1, false, false, 0
        )
        ON CONFLICT ("userId", "productId") DO UPDATE
        SET
          "lastViewed" = NOW(),
          "viewCount" = "UserProductInteraction"."viewCount" + 1
        RETURNING *
      `;

      await prisma.$queryRawUnsafe(interactionQuery);
    }
  } catch (error) {
    console.error('[Analytics] Error recording product view:', error);
  }
} 