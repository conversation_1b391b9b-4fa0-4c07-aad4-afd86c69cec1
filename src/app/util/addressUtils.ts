import { iProduct } from './Interfaces';

// Type for minimal product info that includes address fields
type ProductWithAddress = {
  address?: string | null;
  streetAddress?: string | null;
  city?: string | null;
};

/**
 * Format address for display with backward compatibility
 * Prioritizes new streetAddress + city format, falls back to legacy address
 */
export const formatProductAddress = (product: ProductWithAddress): string | null => {
  // If we have both new format fields, use them
  if (product.streetAddress && product.city) {
    return `${product.streetAddress}, ${product.city}`;
  }
  
  // If we have only street address, use it
  if (product.streetAddress) {
    return product.streetAddress;
  }
  
  // If we have only city, use it
  if (product.city) {
    return product.city;
  }
  
  // Fall back to legacy address field
  if (product.address) {
    return product.address;
  }
  
  // No address information available
  return null;
};

/**
 * Get short address for display in cards (truncated)
 */
export const formatShortAddress = (product: ProductWithAddress, maxLength: number = 30): string | null => {
  const fullAddress = formatProductAddress(product);
  
  if (!fullAddress) {
    return null;
  }
  
  if (fullAddress.length <= maxLength) {
    return fullAddress;
  }
  
  return `${fullAddress.substring(0, maxLength - 3)}...`;
};

/**
 * Get city name only for filtering/grouping purposes
 */
export const getProductCity = (product: ProductWithAddress): string | null => {
  // Prefer new city field
  if (product.city) {
    return product.city;
  }
  
  // Try to extract city from legacy address (simple heuristic)
  if (product.address) {
    const parts = product.address.split(',').map(p => p.trim());
    if (parts.length >= 2) {
      // Assume last part is city
      return parts[parts.length - 1];
    }
  }
  
  return null;
};

/**
 * Get street address only
 */
export const getProductStreetAddress = (product: ProductWithAddress): string | null => {
  // Prefer new street address field
  if (product.streetAddress) {
    return product.streetAddress;
  }
  
  // Try to extract street from legacy address (simple heuristic)
  if (product.address) {
    const parts = product.address.split(',').map(p => p.trim());
    if (parts.length >= 2) {
      // Assume everything except last part is street address
      return parts.slice(0, -1).join(', ');
    }
    // If no comma, assume whole thing is street address
    return product.address;
  }
  
  return null;
};

/**
 * Check if product has any address information
 */
export const hasAddressInfo = (product: ProductWithAddress): boolean => {
  return !!(product.streetAddress || product.city || product.address);
};

/**
 * Format address for Google Maps search
 */
export const formatAddressForMap = (product: ProductWithAddress): string | null => {
  const address = formatProductAddress(product);
  if (!address) return null;
  
  // Add country if not present (assuming Guyana for this project)
  if (!address.toLowerCase().includes('guyana')) {
    return `${address}, Guyana`;
  }
  
  return address;
};
