import { NextRequest, NextResponse } from 'next/server';

export interface CorsOptions {
  allowedOrigins?: string[] | '*';
  allowedMethods?: string[];
  allowedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
}

export function createCorsMiddleware(options: CorsOptions = {}) {
  const {
    allowedOrigins = '*',
    allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders = ['Content-Type', 'Authorization', 'X-Widget-ID', 'X-Referrer'],
    credentials = false,
    maxAge = 86400
  } = options;

  return function corsMiddleware(req: NextRequest, response?: NextResponse) {
    const origin = req.headers.get('origin');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      const preflightResponse = new NextResponse(null, { status: 200 });
      
      // Set CORS headers for preflight
      if (allowedOrigins === '*') {
        preflightResponse.headers.set('Access-Control-Allow-Origin', '*');
      } else if (Array.isArray(allowedOrigins) && origin && allowedOrigins.includes(origin)) {
        preflightResponse.headers.set('Access-Control-Allow-Origin', origin);
      }
      
      preflightResponse.headers.set('Access-Control-Allow-Methods', allowedMethods.join(', '));
      preflightResponse.headers.set('Access-Control-Allow-Headers', allowedHeaders.join(', '));
      preflightResponse.headers.set('Access-Control-Max-Age', maxAge.toString());
      
      if (credentials) {
        preflightResponse.headers.set('Access-Control-Allow-Credentials', 'true');
      }
      
      return preflightResponse;
    }

    // If no response provided, create headers object for manual application
    if (!response) {
      const headers: Record<string, string> = {};
      
      if (allowedOrigins === '*') {
        headers['Access-Control-Allow-Origin'] = '*';
      } else if (Array.isArray(allowedOrigins) && origin && allowedOrigins.includes(origin)) {
        headers['Access-Control-Allow-Origin'] = origin;
      }

      if (credentials) {
        headers['Access-Control-Allow-Credentials'] = 'true';
      }

      return headers;
    }

    // Set CORS headers on provided response
    if (allowedOrigins === '*') {
      response.headers.set('Access-Control-Allow-Origin', '*');
    } else if (Array.isArray(allowedOrigins) && origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }

    if (credentials) {
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }

    return response;
  };
}

// Widget-specific CORS middleware
export const widgetCorsMiddleware = createCorsMiddleware({
  allowedOrigins: '*', // Allow all origins for widgets
  allowedMethods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'X-Widget-ID', 'X-Referrer', 'User-Agent'],
  credentials: false, // No credentials needed for public widgets
  maxAge: 86400
});

// Restricted CORS for widget management APIs
export const restrictedCorsMiddleware = createCorsMiddleware({
  allowedOrigins: process.env.NODE_ENV === 'development' ? '*' : [
    'https://reviewit.gy',
    'https://www.reviewit.gy',
    'http://localhost:3000', // Development only
    'http://localhost:3001', // Alternative dev port
    'http://127.0.0.1:3000', // Alternative localhost
    'http://127.0.0.1:3001'  // Alternative localhost
  ],
  credentials: true, // Allow cookies for authentication
});