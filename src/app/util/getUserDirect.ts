import { addUserToDb } from "@/app/util/addUserToDb";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { clerkClient, auth } from "@clerk/nextjs/server";
import { iUser } from "@/app/util/Interfaces";

interface UserDATA {
  avatar?: string;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata: {
    id: string;
    userInDb: boolean;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Direct server-side user fetch that doesn't make HTTP requests
 * This is used when we're already on the server and don't need to make API calls
 */
export async function getUserDirect(): Promise<ApiResponse<iUser>> {
  try {
    const { sessionClaims } = await auth();

    // Check if sessionClaims exists and has the required data
    if (!sessionClaims) {
      return {
        success: false,
        error: "No active session found",
      };
    }

    const clerkClaimsData = sessionClaims as unknown as UserDATA;

    // Check if userId exists in the claims data
    if (!clerkClaimsData?.userId) {
      return {
        success: false,
        error: "User ID not found in session",
      };
    }

    let idFromPublicMetaData: string = "";
    let clerkUserData = null;

    if (!(await userInDb(clerkClaimsData.userId))) {
      // If the user doesn't exist, create them
      clerkUserData = await addUserToDb(clerkClaimsData);
      if (clerkUserData?.publicMetadata.id !== undefined) {
        idFromPublicMetaData = clerkUserData.publicMetadata.id as string;
      }
    } else {
      clerkUserData = await (
        await clerkClient()
      ).users.getUser(clerkClaimsData.userId);
      // then add publicMetaData.id to the reviewData object
      if (clerkUserData.publicMetadata.id !== undefined) {
        idFromPublicMetaData = clerkUserData.publicMetadata.id as string;
      } else {
        return {
          success: false,
          error: "publicMetadata.id not found",
        };
      }
    }

    const user = await prisma.user.findUnique({
      where: {
        id: idFromPublicMetaData,
      },
      include: {
        comments: {
          include: {
            review: true,
          },
        },
        reviews: {
          include: {
            product: true,
            user: true,
            voteCount: true,
            comments: {
              include: {
                user: true,
              },
            },
          },
        },
        likedReviews: true,
        _count: {
          select: {
            reviews: true,
            comments: true,
            likedReviews: true,
          },
        },
      },
    });

    if (!user) {
      return {
        success: false,
        error: "User not found in database",
      };
    }

    return {
      success: true,
      data: user as iUser,
    };
  } catch (error) {
    console.error("[getUserDirect] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}