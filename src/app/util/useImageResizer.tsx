import { useState, useCallback } from "react";

interface ResizeResultFile {
  file: File;
  dataUrl: string;
  resized: boolean;
}

interface ResizeResultBase64 {
  dataUrl: string;
  resized: boolean;
}

type ResizeResult = ResizeResultFile | ResizeResultBase64;

interface ResizeOptionsBase {
  maxFileSize?: number;
  quality?: number;
}

interface ResizeDimensionOptions extends ResizeOptionsBase {
  maxDimension: number;
  maxWidth?: undefined;
  maxHeight?: undefined;
}

interface ResizeWidthHeightOptions extends ResizeOptionsBase {
  maxDimension?: undefined;
  maxWidth: number;
  maxHeight: number;
}

type ResizeOptions = ResizeDimensionOptions | ResizeWidthHeightOptions;

const DEFAULT_MAX_FILE_SIZE = 300 * 1024;
const DEFAULT_QUALITY = 0.7;
const DEFAULT_MAX_DIMENSION = 800;

export const useImageResizer = () => {
  const [isResizing, setIsResizing] = useState(false);

  const calculateDimensions = useCallback(
    (width: number, height: number, options: ResizeOptions): {
      width: number;
      height: number;
      shouldResize: boolean;
    } => {
      let newWidth = width;
      let newHeight = height;
      let shouldResize = false;

      if ("maxDimension" in options && options.maxDimension) {
        const maxDimension = options.maxDimension;
        if (width > height && width > maxDimension) {
          newHeight = Math.round(height * (maxDimension / width));
          newWidth = maxDimension;
          shouldResize = true;
        } else if (height > maxDimension) {
          newWidth = Math.round(width * (maxDimension / height));
          newHeight = maxDimension;
          shouldResize = true;
        }
      } else if (options.maxWidth && options.maxHeight) {
        if (width > options.maxWidth) {
          newHeight = Math.round(height * (options.maxWidth / width));
          newWidth = options.maxWidth;
          shouldResize = true;
        }
        if (newHeight > options.maxHeight) {
          newWidth = Math.round(newWidth * (options.maxHeight / newHeight));
          newHeight = options.maxHeight;
          shouldResize = true;
        }
      }

      return {
        width: Math.round(newWidth),
        height: Math.round(newHeight),
        shouldResize,
      };
    },
    []
  );

  const createFullOptions = (
    partialOptions: Partial<ResizeOptions>
  ): ResizeOptions => {
    if (
      "maxWidth" in partialOptions &&
      partialOptions.maxWidth !== undefined &&
      "maxHeight" in partialOptions &&
      partialOptions.maxHeight !== undefined
    ) {
      return {
        maxWidth: partialOptions.maxWidth,
        maxHeight: partialOptions.maxHeight,
        maxFileSize: partialOptions.maxFileSize ?? DEFAULT_MAX_FILE_SIZE,
        quality: partialOptions.quality ?? DEFAULT_QUALITY,
      };
    }
    return {
      maxDimension: partialOptions.maxDimension ?? DEFAULT_MAX_DIMENSION,
      maxFileSize: partialOptions.maxFileSize ?? DEFAULT_MAX_FILE_SIZE,
      quality: partialOptions.quality ?? DEFAULT_QUALITY,
    };
  };

  const resizeFile = useCallback(
    async (file: File, options: ResizeOptions): Promise<ResizeResultFile> => {
      setIsResizing(true);

      try {
        return await new Promise<ResizeResultFile>((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement("canvas");
            const { width, height } = calculateDimensions(
              img.width,
              img.height,
              options
            );

            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext("2d");
            if (!ctx) {
              reject(new Error("Failed to get canvas context"));
              return;
            }

            ctx.drawImage(img, 0, 0, width, height);

            const quality = options.quality ?? DEFAULT_QUALITY;
            const dataUrl = canvas.toDataURL(file.type, quality);

            canvas.toBlob(
              (blob) => {
                if (!blob) {
                  resolve({
                    file,
                    dataUrl,
                    resized: false,
                  });
                  return;
                }

                const resizedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now(),
                });

                resolve({
                  file: resizedFile,
                  dataUrl,
                  resized: resizedFile.size !== file.size,
                });
              },
              file.type,
              quality
            );
          };

          img.onerror = () => reject(new Error(`Failed to load image: ${file.name}`));
          img.src = URL.createObjectURL(file);
        });
      } finally {
        setIsResizing(false);
      }
    },
    [calculateDimensions]
  );

  const resizeBase64 = useCallback(
    async (
      base64Str: string,
      options: ResizeOptions
    ): Promise<ResizeResultBase64> => {
      setIsResizing(true);

      try {
        return await new Promise<ResizeResultBase64>((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement("canvas");
            const { width, height, shouldResize } = calculateDimensions(
              img.width,
              img.height,
              options
            );

            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext("2d");
            if (!ctx) {
              reject(new Error("Failed to get canvas context"));
              return;
            }

            ctx.drawImage(img, 0, 0, width, height);

            const quality = options.quality ?? DEFAULT_QUALITY;
            const imageType = base64Str.startsWith("data:image/")
              ? base64Str.substring(5, base64Str.indexOf(";"))
              : "image/jpeg";

            const dataUrl = canvas.toDataURL(imageType, quality);

            resolve({
              dataUrl,
              resized: shouldResize,
            });
          };

          img.onerror = () => reject(new Error("Failed to load image from base64"));
          img.src = base64Str;
        });
      } finally {
        setIsResizing(false);
      }
    },
    [calculateDimensions]
  );

  const processFile = useCallback(
    async (
      file: File,
      options: Partial<ResizeOptions> = {}
    ): Promise<ResizeResultFile> => {
      if (!file.type.startsWith("image/")) {
        return {
          file,
          dataUrl: URL.createObjectURL(file),
          resized: false,
        };
      }

      const fullOptions = createFullOptions(options);
      let result = await resizeFile(file, fullOptions);

      const maxFileSize = fullOptions.maxFileSize ?? DEFAULT_MAX_FILE_SIZE;
      if (result.file.size > maxFileSize) {
        let quality = 0.8;
        while (result.file.size > maxFileSize && quality > 0.1) {
          quality -= 0.1;
          const qualityOptions = { ...fullOptions, quality };
          result = await resizeFile(file, qualityOptions);
        }
      }

      return result;
    },
    [resizeFile]
  );

  const processBase64 = useCallback(
    async (
      base64Str: string,
      options: Partial<ResizeOptions> = {}
    ): Promise<ResizeResultBase64> => {
      const fullOptions = createFullOptions(options);
      return resizeBase64(base64Str, fullOptions);
    },
    [resizeBase64]
  );

  const processImage = useCallback(
    async (
      input: File | string,
      options: Partial<ResizeOptions> = {}
    ): Promise<ResizeResult> => {
      try {
        if (typeof input === "string") {
          return await processBase64(input, options);
        }
        return await processFile(input, options);
      } finally {
        setIsResizing(false);
      }
    },
    [processFile, processBase64]
  );

  return {
    processImage,
    processFile,
    processBase64,
    isResizing,
  };
};
