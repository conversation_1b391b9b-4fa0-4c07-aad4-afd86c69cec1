import { iReview, iUser, iProduct } from "./Interfaces";
import { baseUrl } from "./serverFunctions";

export const getPendingReviews = async () => {
  try {
    const response = await fetch("/api/get/reviews/pending", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch pending reviews");
    }

    return {
      success: true,
      reviews: data.data as iReview[],
      count: data.dataLength,
    };
  } catch (error) {
    console.error("Error fetching pending reviews:", error);
    return {
      success: false,
      reviews: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const getApprovedReviews = async () => {
  try {
    const response = await fetch("/api/get/reviews/approved", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch approved reviews");
    }

    return {
      success: true,
      reviews: data.data as iReview[],
      count: data.dataLength,
    };
  } catch (error) {
    console.error("Error fetching approved reviews:", error);
    return {
      success: false,
      reviews: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const getRejectedReviews = async () => {
  try {
    const response = await fetch("/api/get/reviews/rejected", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch rejected reviews");
    }

    return {
      success: true,
      reviews: data.data as iReview[],
      count: data.dataLength,
    };
  } catch (error) {
    console.error("Error fetching rejected reviews:", error);
    return {
      success: false,
      reviews: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const approveReview = async (reviewId: string, isApproved: boolean) => {
  try {
    const response = await fetch("/api/update/review/approve", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        reviewId,
        isApproved,
      }),
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to update review");
    }

    return {
      success: true,
      review: data.data as iReview,
    };
  } catch (error) {
    console.error("Error approving/rejecting review:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const getAllUsers = async () => {
  try {
    const response = await fetch(baseUrl + "/api/get/users/all", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch users");
    }

    return {
      success: true,
      users: data.data as iUser[],
      count: data.dataLength,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      success: false,
      users: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const getAllProducts = async () => {
  try {
    const response = await fetch("/api/get/products/all", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch products");
    }

    return {
      success: true,
      products: data.data as iProduct[],
      count: data.dataLength,
    };
  } catch (error) {
    console.error("Error fetching products:", error);
    return {
      success: false,
      products: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

// Function to get recent reviews (last 24 hours)
export async function getRecentReviews() {
  try {
    const response = await fetch("/api/admin/recent-reviews", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error || "Failed to fetch recent reviews");
    }

    return {
      success: true,
      reviews: data.reviews || [],
      count: data.reviews?.length || 0,
    };
  } catch (error) {
    console.error("Error fetching recent reviews:", error);
    return {
      success: false,
      reviews: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Function to get recent notifications
export async function getRecentNotifications() {
  try {
    const response = await fetch("/api/admin/recent-notifications", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error || "Failed to fetch recent notifications");
    }

    return {
      success: true,
      notifications: data.notifications || [],
      count: data.notifications?.length || 0,
    };
  } catch (error) {
    console.error("Error fetching recent notifications:", error);
    return {
      success: false,
      notifications: [],
      count: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Function to get review statistics for the last 24 hours
export async function getReviewStats() {
  try {
    const response = await fetch("/api/admin/review-stats", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error || "Failed to fetch review statistics");
    }

    return {
      success: true,
      data: data.data || {
        last24Hours: 0,
        averagePerHour: 0,
      },
    };
  } catch (error) {
    console.error("Error fetching review statistics:", error);
    return {
      success: false,
      data: {
        last24Hours: 0,
        averagePerHour: 0,
      },
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export const updateUserStatus = async (
  userId: string,
  updates: Partial<iUser>
) => {
  try {
    const response = await fetch(`/api/admin/users/${userId}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updates),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error || "Failed to update user status");
    }

    return {
      success: true,
      data: data.data,
    };
  } catch (error) {
    console.error("Error updating user status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const updateUserRole = async (
  userId: string,
  role: "USER" | "ADMIN"
) => {
  try {
    const response = await fetch(`/api/admin/users/${userId}/role`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ role }),
    });

    if (!response.ok) {
      throw new Error("Failed to update user role");
    }

    return response.json();
  } catch (error) {
    console.error("Error updating user role:", error);
    throw error;
  }
};

export const getUserActivity = async (userId: string) => {
  try {
    const response = await fetch(`/api/admin/users/${userId}/activity`);

    if (!response.ok) {
      throw new Error("Failed to fetch user activity");
    }

    return response.json();
  } catch (error) {
    console.error("Error fetching user activity:", error);
    throw error;
  }
};

export const getProducts = async (params: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}) => {
  try {
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.append("page", params.page.toString());
    if (params.limit) searchParams.append("limit", params.limit.toString());
    if (params.search) searchParams.append("search", params.search);
    if (params.status) searchParams.append("status", params.status);
    if (params.sortBy) searchParams.append("sortBy", params.sortBy);
    if (params.sortOrder) searchParams.append("sortOrder", params.sortOrder);

    const response = await fetch(
      `/api/admin/products?${searchParams.toString()}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch products");
    }

    return response.json();
  } catch (error) {
    console.error("Error fetching products:", error);
    throw error;
  }
};

export const getProduct = async (id: string) => {
  try {
    const response = await fetch(`/api/admin/products/${id}`);

    if (!response.ok) {
      throw new Error("Failed to fetch product");
    }

    return response.json();
  } catch (error) {
    console.error("Error fetching product:", error);
    throw error;
  }
};

export const updateProduct = async (id: string, data: Partial<iProduct>) => {
  try {
    const response = await fetch(`/api/admin/products/${id}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update product");
    }

    return response.json();
  } catch (error) {
    console.error("Error updating product:", error);
    throw error;
  }
};

export const updateProductStatus = async (
  id: string,
  data: {
    isDeleted?: boolean;
    featuredPosition?: number | null;
    isPublic?: boolean;
  }
) => {
  try {
    const response = await fetch(`/api/admin/products/${id}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update product status");
    }

    return response.json();
  } catch (error) {
    console.error("Error updating product status:", error);
    throw error;
  }
};

export const deleteProduct = async (id: string) => {
  try {
    const response = await fetch(`/api/admin/products/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete product");
    }

    return response.json();
  } catch (error) {
    console.error("Error deleting product:", error);
    throw error;
  }
};
