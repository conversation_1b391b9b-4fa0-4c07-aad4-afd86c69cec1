/**
 * SSE Notification Service
 * Handles real-time notifications via Server-Sent Events
 * Integrates with existing Jotai state management
 */



export interface SSENotificationServiceConfig {
  userId: string;
  baseUrl?: string;
  debug?: boolean;
  onConnectionOpen?: () => void;
  onConnectionError?: (error: Event) => void;
  onMaxReconnectAttemptsReached?: () => void;
  onMessage?: (data: SSEMessage) => void;
}

export interface NotificationPayload {
  id: string;
  parent_id: string;
  parent_user_id: string;
  message: string;
  read: boolean;
  created_at: string;
}

export interface SSEMessage {
  userID: string;
  type: 'system' | 'user' | 'owner' | 'like';
  event: 'connected' | 'heartbeat' | 'existing_notification' | 'new_notification';
  notification: NotificationPayload | { message: string; time: string; };
}

export class SSENotificationService {
  private eventSource: EventSource | null = null;
  private userId: string;
  private baseUrl: string;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 3;
  private reconnectDelay: number = 2000;
  private debug: boolean = false;
  private connectionTimeout: number = 10000;
  private heartbeatInterval: number = 180000; // Increased to 3 minutes to minimize CPU usage while maintaining connection reliability
  private lastMessageTime: number = 0;
  private heartbeatTimer?: NodeJS.Timeout;
  private connectionTimeoutTimer?: NodeJS.Timeout;
  private connectionTimeoutId?: NodeJS.Timeout;

  // Callbacks
  private onConnectionOpen?: () => void;
  private onConnectionError?: (error: Event) => void;
  private onMaxReconnectAttemptsReached?: () => void;
  private onMessage?: (data: SSEMessage) => void;

  constructor({
    userId,
    baseUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || 'http://localhost:3001',
    debug = false,
    onConnectionOpen,
    onConnectionError,
    onMaxReconnectAttemptsReached,
    onMessage,
  }: {
    userId: string;
    baseUrl?: string;
    debug?: boolean;
    onConnectionOpen?: () => void;
    onConnectionError?: (error: Event) => void;
    onMaxReconnectAttemptsReached?: () => void;
    onMessage?: (data: SSEMessage) => void;
  }) {
    this.userId = userId;
    this.baseUrl = baseUrl;
    this.onConnectionOpen = onConnectionOpen;
    this.onConnectionError = onConnectionError;
    this.onMaxReconnectAttemptsReached = onMaxReconnectAttemptsReached;
    this.onMessage = onMessage;
    this.debug = debug;
  }



  /**
   * Connect to SSE stream
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      const now = Date.now();
      if (now - this.lastMessageTime > this.heartbeatInterval) {
        if (this.debug) {
          console.warn('SSE: No messages received for', this.heartbeatInterval, 'ms, reconnecting...');
        }
        this.reconnect();
      }
    }, this.heartbeatInterval / 2);
  }

  private async reconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (this.debug) {
        console.error('SSE: Max reconnect attempts reached');
      }
      this.onMaxReconnectAttemptsReached?.();
      return;
    }

    this.reconnectAttempts++;
    // Removed debug console.log statement
    await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
    this.connect();
  }

  disconnect(): void {
    this.clearTimers();
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
  }

  private clearTimers(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
    if (this.connectionTimeoutTimer) {
      clearTimeout(this.connectionTimeoutTimer);
      this.connectionTimeoutTimer = undefined;
    }
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId);
      this.connectionTimeoutId = undefined;
    }
  }

  async connect(): Promise<boolean> {
    if (this.eventSource) {
      return true;
    }
    const cleanUrl = this.baseUrl.endsWith('/') ? this.baseUrl.slice(0, -1) : this.baseUrl;
    const url = `${cleanUrl}/notifications/stream?user_id=${this.userId}`;
    return new Promise((resolve, reject) => {
      if (!this.userId) {
        console.error('SSE: User ID is required');
        resolve(false);
        return;
      }

      try {
        this.connectionTimeoutId = setTimeout(() => {
          if (!this.isConnected) {
            console.error('SSE Debug: Connection timeout after', this.connectionTimeout, 'ms');
            this.disconnect();
            this.onConnectionError?.(new Event('timeout'));
            resolve(false);
          }
        }, this.connectionTimeout);

        this.eventSource = new EventSource(url);

        this.eventSource.onopen = (event) => {
          this.onConnectionOpen?.();
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve(true);

          if (this.connectionTimeoutId) {
            clearTimeout(this.connectionTimeoutId);
            this.connectionTimeoutId = undefined;
          }
        };

        this.eventSource.onmessage = (event) => {
          this.lastMessageTime = Date.now();
          try {
            const data = typeof event.data === 'string' 
              ? JSON.parse(event.data) 
              : event.data;

            const message: SSEMessage = {
              userID: data.user_id,
              type: data.type,
              event: data.event,
              notification: data.notification
            };
            
            this.onMessage?.(message);
          } catch (e) {
            console.error('SSE: Failed to parse message:', e, '\nRaw data:', event.data);
          }
        };

        this.eventSource.onerror = (event: Event) => {
          console.error('SSE Debug: Connection error:', {
            event,
            timestamp: new Date().toISOString(),
            connectionStatus: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            url
          });
          
          const readyState = this.eventSource?.readyState;
          console.error('SSE Debug: Connection error:', {
            event,
            readyState,
            readyStateText: readyState === 0 ? 'CONNECTING' : readyState === 1 ? 'OPEN' : 'CLOSED',
            timestamp: new Date().toISOString(),
            url: url,
            connectionAttempt: this.reconnectAttempts + 1,
            maxAttempts: this.maxReconnectAttempts
          });
          
          this.isConnected = false;
          this.onConnectionError?.(event);
          this.reconnect();
        };

      } catch (error) {
        console.error('SSE: Failed to create EventSource:', error);
        resolve(false);
      }
    });
  }




}

export default SSENotificationService;