import DOMPurify from 'dompurify';

/**
 * Sanitizes HTML content to prevent XSS attacks while allowing safe formatting tags
 */
export const sanitizeHTML = (html: string): string => {
  // Only run on client side since DOMPurify needs DOM
  if (typeof window === 'undefined') {
    return html; // Return as-is on server side
  }

  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'strong', 'em', 'br', 'p', 'span', 'u'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });
};

/**
 * Prepares HTML content for safe rendering with dangerouslySetInnerHTML
 */
export const renderSafeHTML = (html: string) => ({
  __html: sanitizeHTML(html)
});

/**
 * Detects if content contains HTML tags
 */
export const isHTML = (str: string): boolean => {
  return /<[a-z][\s\S]*>/i.test(str);
};

/**
 * Renders review body content, handling both HTML and plain text
 */
export const renderReviewBody = (body: string) => {
  if (!body) return null;
  
  if (isHTML(body)) {
    return renderSafeHTML(body);
  }
  
  // For plain text, convert line breaks to <br> tags
  const textWithBreaks = body.replace(/\n/g, '<br>');
  return renderSafeHTML(textWithBreaks);
};