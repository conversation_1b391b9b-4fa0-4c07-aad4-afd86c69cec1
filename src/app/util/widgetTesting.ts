import { NextRequest } from 'next/server';

// CORS Testing Utility
export async function testWidgetCors(widgetId: string, origin?: string) {
  const testResults = {
    widgetData: false,
    widgetTracking: false,
    iframeEmbedding: false,
    errors: [] as string[]
  };

  try {
    // Test 1: Widget Data API
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    if (origin) {
      headers['Origin'] = origin;
    }

    const dataResponse = await fetch(`/api/public/widgets/${widgetId}`, {
      headers
    });
    
    testResults.widgetData = dataResponse.ok;
    if (!dataResponse.ok) {
      testResults.errors.push(`Widget Data API failed: ${dataResponse.status}`);
    }

    // Test 2: Widget Tracking API
    const trackResponse = await fetch(`/api/public/widgets/${widgetId}/track`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        event: 'test',
        referrer: origin || 'test',
        userAgent: 'test-agent'
      })
    });
    
    testResults.widgetTracking = trackResponse.ok;
    if (!trackResponse.ok) {
      testResults.errors.push(`Widget Tracking API failed: ${trackResponse.status}`);
    }

    // Test 3: Iframe embedding (basic check)
    testResults.iframeEmbedding = true; // Assume success if no errors

  } catch (error) {
    testResults.errors.push(`Test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return testResults;
}

// Rate Limit Testing
export function testRateLimit(req: NextRequest, type: 'PUBLIC_WIDGET_DATA' | 'WIDGET_TRACKING' | 'WIDGET_MANAGEMENT') {
  const limits = {
    PUBLIC_WIDGET_DATA: { limit: 200, windowMs: 60000 },
    WIDGET_TRACKING: { limit: 500, windowMs: 60000 },
    WIDGET_MANAGEMENT: { limit: 30, windowMs: 60000 }
  };

  const config = limits[type];
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'test-ip';
  
  return {
    type,
    limit: config.limit,
    windowMs: config.windowMs,
    ip,
    timestamp: new Date().toISOString()
  };
}

// Security Headers Validation
export function validateSecurityHeaders(headers: Headers) {
  const requiredHeaders = [
    'Access-Control-Allow-Origin',
    'Access-Control-Allow-Methods',
    'Access-Control-Allow-Headers'
  ];

  const results = {
    valid: true,
    missing: [] as string[],
    present: [] as string[]
  };

  requiredHeaders.forEach(header => {
    if (headers.has(header)) {
      results.present.push(header);
    } else {
      results.missing.push(header);
      results.valid = false;
    }
  });

  return results;
}

// Widget Configuration Validator
export function validateWidgetConfig(config: any) {
  const errors = [];
  const warnings = [];

  // Required fields
  if (!config.widgetId) {
    errors.push('Widget ID is required');
  }

  if (!config.businessId) {
    errors.push('Business ID is required');
  }

  if (!config.name || config.name.trim().length === 0) {
    errors.push('Widget name is required');
  }

  if (!config.type) {
    errors.push('Widget type is required');
  }

  // Validate widget type
  const validTypes = [
    'REVIEW_CAROUSEL', 'REVIEW_GRID', 'RATING_SUMMARY', 
    'MINI_REVIEW', 'BUSINESS_CARD', 'TRUST_BADGE', 'REVIEW_POPUP'
  ];
  
  if (config.type && !validTypes.includes(config.type)) {
    errors.push(`Invalid widget type: ${config.type}`);
  }

  // Validate maxReviews
  if (config.maxReviews && (config.maxReviews < 1 || config.maxReviews > 20)) {
    warnings.push('maxReviews should be between 1 and 20');
  }

  // Validate colors
  if (config.primaryColor && !/^#[0-9A-F]{6}$/i.test(config.primaryColor)) {
    warnings.push('Primary color should be a valid hex color');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}