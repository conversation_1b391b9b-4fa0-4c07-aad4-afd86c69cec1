import { z } from 'zod';

// Schema for validating TikTok URLs
const tiktokUrlSchema = z.string().url().refine(
    (url) => url.includes('tiktok.com'),
    'URL must be a TikTok link'
);

/**
 * Resolves a TikTok mobile link (vm.tiktok.com) to its expanded desktop format
 * by calling our backend API route.
 * @param url The TikTok URL to resolve
 * @returns Promise resolving to the expanded URL
 * @throws Error if resolution fails
 */
export async function resolveTikTokMobileLink(url: string): Promise<string> {
    try {
        // Basic validation on the client side can still be useful
        tiktokUrlSchema.parse(url);

        // If it's already a desktop URL, return as is (no need to call the backend)
        if (!url.includes('vm.tiktok.com')) {
            return url;
        }

        const response = await fetch(`/api/resolve-url?url=${encodeURIComponent(url)}`);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: `API request failed with status: ${response.status}` }));
            throw new Error(errorData.error || `API request failed with status: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.error);
        }

        if (!data.resolvedUrl) {
            throw new Error('Resolved URL not found in API response');
        }

        return data.resolvedUrl;

    } catch (error) {
        if (error instanceof z.ZodError) {
            throw new Error('Invalid TikTok URL format');
        }
        // Re-throw other errors or wrap them
        throw error instanceof Error ? error : new Error('An unknown error occurred during URL resolution');
    }
} 