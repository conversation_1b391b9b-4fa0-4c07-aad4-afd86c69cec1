/**
 * This is a utility file for testing the user status restriction functionality.
 * It provides functions to temporarily change a user's status for testing purposes.
 * 
 * IMPORTANT: This should only be used in development environments.
 */

import { prisma } from './prismaClient';

/**
 * Temporarily suspend a user for testing purposes
 * @param userId The Clerk user ID to suspend
 * @param durationMinutes How long to suspend the user for (in minutes)
 * @param reason Optional reason for suspension
 */
export async function testSuspendUser(userId: string, durationMinutes: number = 60, reason: string = 'Test suspension') {
  const suspendedUntil = new Date();
  suspendedUntil.setMinutes(suspendedUntil.getMinutes() + durationMinutes);
  
  await prisma.user.update({
    where: { clerkUserId: userId },
    data: {
      status: 'SUSPENDED',
      suspendedUntil,
      suspendedReason: reason
    }
  });
  
  return { 
    message: `User ${userId} suspended until ${suspendedUntil.toISOString()}`,
    suspendedUntil
  };
}

/**
 * Ban a user for testing purposes
 * @param userId The Clerk user ID to ban
 */
export async function testBanUser(userId: string) {
  await prisma.user.update({
    where: { clerkUserId: userId },
    data: {
      status: 'BANNED'
    }
  });
  
  return { message: `User ${userId} banned` };
}

/**
 * Reactivate a previously suspended or banned user
 * @param userId The Clerk user ID to reactivate
 */
export async function testReactivateUser(userId: string) {
  await prisma.user.update({
    where: { clerkUserId: userId },
    data: {
      status: 'ACTIVE',
      suspendedUntil: null,
      suspendedReason: null
    }
  });
  
  return { message: `User ${userId} reactivated` };
}
