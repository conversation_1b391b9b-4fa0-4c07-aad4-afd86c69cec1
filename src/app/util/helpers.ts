/**
 * Strips specific HTML tags from a string while preserving the content between them.
 * Removes <p>, <br>, <h1>, <h2>, <h3>, <b> and their closing tags.
 * @param html - The HTML string to process
 * @returns The string with specified HTML tags removed
 */
export function stripSpecificHtmlTags(html: string): string {
    if (!html) return '';

    return html
        .replace(/<\/?(?:p|br|h[1-3]|b)(?:\s[^>]*)?>/gi, ' ') // Replace tags with space
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .trim(); // Remove leading/trailing spaces
} 