import { NextRequest } from 'next/server';

const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  req: NextRequest, 
  limit: number = 100, 
  windowMs: number = 60000
): boolean {
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < windowStart) {
      rateLimitMap.delete(key);
    }
  }

  const current = rateLimitMap.get(ip);
  
  if (!current) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.resetTime < now) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.count >= limit) {
    return false;
  }

  current.count++;
  return true;
}

// Widget-specific rate limiting configurations
export const WIDGET_RATE_LIMITS = {
  // Public widget data API - more permissive
  PUBLIC_WIDGET_DATA: { limit: 200, windowMs: 60000 }, // 200 requests per minute
  
  // Secure widget data API - configurable per widget
  SECURE_WIDGET_DATA: { limit: 100, windowMs: 3600000 }, // 100 requests per hour (default)
  
  // Widget tracking API - moderate
  WIDGET_TRACKING: { limit: 500, windowMs: 60000 }, // 500 tracking events per minute
  
  // Widget management API - restrictive
  WIDGET_MANAGEMENT: { limit: 30, windowMs: 60000 }, // 30 management operations per minute
};

export function checkWidgetRateLimit(
  req: NextRequest, 
  type: keyof typeof WIDGET_RATE_LIMITS
): boolean {
  const config = WIDGET_RATE_LIMITS[type];
  return checkRateLimit(req, config.limit, config.windowMs);
}