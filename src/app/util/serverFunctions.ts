import {
  iComment,
  iProduct,
  iReview,
  iTag,
  iUser,
  iUserNotification,
  iProductOwnerNotification,
  FetchedNotificationsData,
} from "@/app/util/Interfaces";

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface helpfulData {
  reviewId: string;
  userInDbId: string;
}

// Determine the correct base URL for server-side vs client-side requests
function getBaseUrl(): string {
  // Server-side: use localhost
  if (typeof window === 'undefined') {
    return process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  }
  
  // Client-side: Check if we're in development mode
  const isDev = process.env.NODE_ENV === 'development' || process.env.IS_DEV === 'true';
  
  if (isDev) {
    // In development, always use the current origin (supports ngrok, localhost, etc.)
    return window.location.origin;
  }
  
  // Production: use configured URLs
  return process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;
}

export const baseUrl = getBaseUrl();

// Generic fetch wrapper with proper error handling and types
async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<ApiResponse<T>> {
  try {
    // Detect if the caller already supplied an absolute URL (e.g. https://domain/...)
    const isAbsolute = /^https?:\/\//.test(endpoint);
    
    // Get the appropriate base URL for the current environment
    const currentBaseUrl = getBaseUrl();
    const cleanBaseUrl = currentBaseUrl.endsWith("/") ? currentBaseUrl.slice(0, -1) : currentBaseUrl;

    let url: string;

    if (isAbsolute) {
      // Caller already provided a full URL " use it as-is.
      url = endpoint;
    } else {
      // Build absolute URL using baseUrl regardless of environment
      const cleanEndpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      url = `${cleanBaseUrl}${cleanEndpoint}`;
    }

    console.log("this is the url", url)
    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      credentials: "include",
      cache: "no-store",
    });

    const data = await response.json();
    console.log("**************", data)

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    // Handle both response formats: { success, data } and direct data
    if (data.success === false) {
      throw new Error(data.error || "API request failed");
    }

    return {
      success: true,
      data: data.data || data,
    };
  } catch (error) {
    console.error(`[fetchApi] Error (${endpoint}):`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

export const getUser = async (): Promise<ApiResponse<iUser>> => {
  // console.log("Fetching user...");
  return fetchApi<iUser>("/api/get/user", {
    method: "GET",
    credentials: "include",
  });

};

export const getUserWithId = async (
  userId: string,
): Promise<ApiResponse<iUser>> => {
  return fetchApi<iUser>("/api/get/userwithid", {
    method: "POST",
    body: JSON.stringify({ userId }),
  });
};

export const getNotifications = async (
  userId: string,
): Promise<ApiResponse<FetchedNotificationsData>> => {
  const notificationsUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER;
  if (!notificationsUrl) {
    console.error("Notification server URL not configured");
    return {
      success: false,
      error: "Notification server URL not configured",
    };
  }

  // Ensure the URL is properly formatted
  const baseUrl = notificationsUrl.endsWith("/")
    ? notificationsUrl.slice(0, -1)
    : notificationsUrl;
  const endpoint = `${baseUrl}/notifications?user_id=${userId}`;

  try {
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const serverData = await response.json();

    // Map snake_case to camelCase
    const formattedData: FetchedNotificationsData = {
      userNotifications: serverData.user_notifications || [],
      ownerNotifications: serverData.owner_notifications || [],
      likeNotifications: serverData.like_notifications || [],
    };

    return {
      success: true,
      data: formattedData,
    };
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch notifications",
    };
  }
};

export const updateHelpfulVote = async ({
  reviewId,
  userInDbId,
}: {
  reviewId: string;
  userInDbId: string;
}): Promise<ApiResponse<any>> => {
  return fetchApi("/api/update/helpful", {
    method: "POST",
    body: JSON.stringify({ reviewId, userId: userInDbId }),
  });
};

export const deleteComment = async (
  id: string,
): Promise<ApiResponse<iComment>> => {
  return fetchApi<iComment>("/api/delete/comment", {
    method: "POST",
    body: JSON.stringify({ id }),
  });
};

export const getReview = async (id: string): Promise<ApiResponse<iReview>> => {
  return fetchApi<iReview>("/api/get/review", {
    method: "POST",
    body: JSON.stringify({ id }),
  });
};

export const getReviews = async (
  id: string,
): Promise<ApiResponse<{ reviews: iReview[]; product: any }>> => {
  return fetchApi<{ reviews: iReview[]; product: any }>("/api/get/reviews", {
    method: "POST",
    body: JSON.stringify({
      id,
      isPublic: true,
      user: true,
      product: true,
      comments: true,
      likedBy: true,
    }),
  });
};

// NEW: Fetch comments separately for a specific review
export const getCommentsForReview = async (
  reviewId: string,
): Promise<ApiResponse<iComment[]>> => {
  return fetchApi<iComment[]>(`/api/comments/review/${reviewId}`, {
    method: "GET",
  });
};

export const getLatestReviews = async (
  filter: string = "latest",
): Promise<ApiResponse<iReview[]>> => {
  // Route to appropriate cached endpoints based on filter
  switch (filter) {
    case "popular":
      return fetchApi<iReview[]>("/api/reviews/popular", {
        method: "GET",
      });
    case "trending":
      return fetchApi<iReview[]>("/api/reviews/trending", {
        method: "GET",
      });
    case "latest":
    default:
      return fetchApi<iReview[]>("/api/get/reviews/latest", {
        method: "GET",
      });
  }
};

export const getProduct = async (
  id: string,
): Promise<ApiResponse<iProduct>> => {
  return fetchApi<iProduct>("/api/get/product", {
    method: "POST",
    body: JSON.stringify({ id }),
  });
};

export const getProducts = async (): Promise<ApiResponse<iProduct[]>> => {
  return fetchApi<iProduct[]>("/api/get/all/products", {
    method: "POST",
  });
};

export const createCommentOnReview = async (
  comment: iComment,
): Promise<ApiResponse<iComment>> => {
  if (comment.body === "") {
    return {
      success: false,
      error: "Comment body cannot be empty",
    };
  }
  return fetchApi<iComment>("/api/create/comment", {
    method: "POST",
    body: JSON.stringify(comment),
  });
};

export const createReplyOnComment = async (
  reply: iComment,
): Promise<ApiResponse<iComment>> => {
  if (reply.body === "") {
    return {
      success: false,
      error: "Reply body cannot be empty",
    };
  }
  return fetchApi<iComment>("/api/create/comment/reply", {
    method: "POST",
    body: JSON.stringify(reply),
  });
};

export const editComment = async (
  id: string,
  commentBody: string,
): Promise<ApiResponse<iComment>> => {
  if (commentBody === "") {
    return {
      success: false,
      error: "Comment body cannot be empty",
    };
  }
  return fetchApi<iComment>("/api/update/comment/edit", {
    method: "POST",
    body: JSON.stringify({ id, commentBody }),
  });
};

export const genTags = async (
  description: string,
): Promise<ApiResponse<string[]>> => {
  const aiServer = process.env.NEXT_PUBLIC_AI_SERVER;
  if (!aiServer) {
    return {
      success: false,
      error: "AI server URL not configured",
    };
  }

  // Ensure the URL is properly formatted
  const cleanAiServer = aiServer.endsWith("/")
    ? aiServer.slice(0, -1)
    : aiServer;
  const endpoint = `${cleanAiServer}/gen`;

  try {
    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ description }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || data,
    };
  } catch (error) {
    console.error("Error generating tags:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate tags",
    };
  }
};

export const removeHelpfulVote = async (
  data: helpfulData,
): Promise<ApiResponse<any>> => {
  return fetchApi("/api/update/helpful", {
    method: "POST",
    body: JSON.stringify({
      reviewId: data.reviewId,
      userId: data.userInDbId,
    }),
  });
};

export const incrementProductView = async (
  productId: string,
): Promise<ApiResponse<any>> => {
  return fetchApi("/api/increment-view", {
    method: "POST",
    body: JSON.stringify({ productId }),
  });
};

export const updateCommentVote = async ({
  commentId,
  userId,
  voteType,
}: {
  commentId: string;
  userId: string;
  voteType: "UP" | "DOWN";
}): Promise<ApiResponse<any>> => {
  return fetchApi("/api/comments/vote", {
    method: "POST",
    body: JSON.stringify({ commentId, userId, voteType }),
  });
};

export const removeCommentVote = async ({
  commentId,
  userId,
}: {
  commentId: string;
  userId: string;
}): Promise<ApiResponse<any>> => {
  return fetchApi("/api/comments/vote/remove", {
    method: "POST",
    body: JSON.stringify({ commentId, userId }),
  });
};

// Weighted Rating Functions
export const getGlobalRatingStatsApi = async (): Promise<ApiResponse<{
  averageRating: number;
  totalReviews: number;
  lastCalculated: string;
}>> => {
  return fetchApi("/api/ratings/global-stats", {
    method: "GET",
  });
};

export const getWeightedRatingAnalyticsApi = async (): Promise<ApiResponse<{
  globalStats: {
    averageRating: number;
    totalReviews: number;
    lastCalculated: string;
  };
  thresholdStats: {
    productsAboveThreshold: number;
    productsBelowThreshold: number;
    totalProducts: number;
    percentageAboveThreshold: number;
  };
  confidenceDistribution: {
    high: number;
    medium: number;
    low: number;
  };
  ratingDistribution: {
    weighted: { [key: number]: number };
    traditional: { [key: number]: number };
  };
}>> => {
  return fetchApi("/api/admin/weighted-rating-analytics", {
    method: "GET",
  });
};

export const refreshGlobalRatingStatsApi = async (): Promise<ApiResponse<{ message: string }>> => {
  return fetchApi("/api/admin/refresh-global-stats", {
    method: "POST",
  });
};

// Like/Dislike/Helpful Vote Notification Functions
export const sendLikeNotification = async ({
  targetType,
  targetId,
  fromId,
  fromName,
  productId,
}: {
  targetType: 'comment' | 'review';
  targetId: string;
  fromId: string;
  fromName: string;
  productId?: string;
}): Promise<ApiResponse<any>> => {
  const notificationsUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER;
  if (!notificationsUrl) {
    console.error('Notification server URL not configured');
    return {
      success: false,
      error: 'Notification server URL not configured',
    };
  }

  return fetchApi<any>(`${notificationsUrl}/notifications/like`, {
    method: 'POST',
    body: JSON.stringify({
      target_type: targetType,
      target_id: targetId,
      from_id: fromId,
      from_name: fromName,
      product_id: productId,
    }),
  });
};

// Any other direct fetch calls in your components
// For example, in ReviewCard.tsx or other components that might make API calls directly
