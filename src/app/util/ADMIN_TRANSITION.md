# Admin Authentication Transition Guide

## Overview
This guide outlines the process for safely transitioning from email-based admin authentication to a hybrid system that supports both email-based and role-based authentication. The transition is designed to be gradual and safe, ensuring no disruption to existing admin functionality.

## Current System
- Email-based authentication using `isAdmin()` function
- Checks against a hardcoded list of admin emails
- Used across middleware, API routes, and client components

## New Hybrid System
- Combines email-based and role-based authentication
- Supports `ADMIN` role
- Includes account status verification
- Provides backward compatibility

## Transition Steps

### 1. Database Preparation
- Ensure all current admin users have the correct role in the database
- Run a script to update existing admin users:
```sql
UPDATE "User"
SET role = 'ADMIN'
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);
```

### 2. Testing Phase
1. Test the new hybrid system in a development environment
2. Verify both authentication methods work:
   - Email-based auth continues to work
   - Role-based auth works for users with correct roles
   - Account status checks work correctly

### 3. Gradual Implementation
1. Start with new features:
   - Use `withHybridAdminAuth` for new API routes
   - Use `hybridAdminAuthMiddleware` for new middleware
   - Use `isUserAuthorizedAdmin` for direct checks

2. Migrate existing features in this order:
   a. API Routes (lowest risk)
   b. Middleware
   c. Client Components

### 4. Migration Process for Each Component

#### API Routes
```typescript
// Before
import { withAdminAuth } from "@/app/middleware/adminAuth";
export const GET = withAdminAuth(async (request) => {
    // Handler code
});

// After
import { withHybridAdminAuth } from "@/app/util/adminAuth";
export const GET = withHybridAdminAuth(async (request) => {
    // Handler code
});
```

#### Middleware
```typescript
// Before
if (!isAdmin(email)) {
    return new Response("Forbidden", { status: 403 });
}

// After
const isAuthorized = await isUserAuthorizedAdmin(email, userId);
if (!isAuthorized) {
    return new Response("Forbidden", { status: 403 });
}
```

#### Client Components
```typescript
// Before
const isAdmin = isUserAdmin(email);

// After
const response = await fetch('/api/admin/check-permission');
const { isAdmin } = await response.json();
```

### 5. Monitoring and Rollback Plan
1. Monitor for any authentication issues
2. Keep track of which components have been migrated
3. Have a rollback plan for each component:
   - Keep the old code commented out
   - Document the last working version
   - Maintain the ability to switch back quickly

### 6. Final Steps
1. Once all components are migrated and tested:
   - Monitor for a period (suggest 2-4 weeks)
   - Verify no authentication issues
   - Consider removing email-based auth

## Best Practices During Transition
1. Make changes in small, testable increments
2. Test thoroughly after each change
3. Keep the old system functional as a fallback
4. Document all changes
5. Monitor error logs closely
6. Have a rollback plan for each change

## Troubleshooting
If you encounter issues:
1. Check the user's role in the database
2. Verify the email is in the authorized list
3. Check account status
4. Review error logs
5. Use the rollback plan if necessary

## Support
For questions or issues during the transition:
1. Review this guide
2. Check the error logs
3. Contact the development team

Remember: The goal is a smooth transition with no disruption to admin functionality. Take your time and test thoroughly at each step. 