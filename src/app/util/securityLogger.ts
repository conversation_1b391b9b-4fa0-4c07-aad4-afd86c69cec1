import { prisma } from './prismaClient';

/**
 * Security event types for logging
 */
export type SecurityEventType = 
  | 'SUSPENDED_ACCESS_ATTEMPT'
  | 'BANNED_ACCESS_ATTEMPT'
  | 'ADMIN_ACCESS_ATTEMPT'
  | 'AUTHENTICATION_FAILURE'
  | 'PASSWORD_RESET'
  | 'ACCOUNT_LOCKOUT'
  | 'PERMISSION_CHANGE'
  | 'STATUS_CHANGE';

/**
 * Interface for security log entry
 */
export interface SecurityLogEntry {
  eventType: SecurityEventType;
  userId: string;
  ipAddress?: string;
  userAgent?: string;
  requestPath?: string;
  details?: Record<string, any>;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * Log a security event to the database
 */
export async function logSecurityEvent({
  eventType,
  userId,
  ipAddress,
  userAgent,
  requestPath,
  details,
  severity
}: SecurityLogEntry) {
  try {
    // Use raw SQL query as a fallback if the model doesn't exist yet
    // This allows the code to work even before running prisma generate
    const data = {
      id: crypto.randomUUID(),
      eventType,
      userId,
      ipAddress: ipAddress || 'unknown',
      userAgent: userAgent || 'unknown',
      requestPath: requestPath || 'unknown',
      details: details ? JSON.stringify(details) : null,
      severity,
      timestamp: new Date()
    };
    
    // Try to use the Prisma model if available
    try {
      // @ts-ignore - The model might not exist yet if prisma generate hasn't been run
      await prisma.securityLog.create({ data });
    } catch (modelError) {
      // Fallback to console logging if the model doesn't exist
      console.log('Security event logged (console only):', {
        ...data,
        timestamp: data.timestamp.toISOString()
      });
    }
  } catch (error) {
    // Fail silently but log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to log security event:', error);
    }
  }
}

/**
 * Log an access attempt by a suspended user
 */
export async function logSuspendedAccessAttempt(
  userId: string, 
  req: Request,
  suspendedUntil?: Date
) {
  const url = new URL(req.url);
  
  await logSecurityEvent({
    eventType: 'SUSPENDED_ACCESS_ATTEMPT',
    userId,
    ipAddress: req.headers.get('x-forwarded-for') || 'unknown',
    userAgent: req.headers.get('user-agent') || 'unknown',
    requestPath: url.pathname,
    details: {
      suspendedUntil: suspendedUntil?.toISOString() || 'unknown',
      query: Object.fromEntries(url.searchParams.entries())
    },
    severity: 'MEDIUM'
  });
}

/**
 * Log an access attempt by a banned user
 */
export async function logBannedAccessAttempt(
  userId: string, 
  req: Request
) {
  const url = new URL(req.url);
  
  await logSecurityEvent({
    eventType: 'BANNED_ACCESS_ATTEMPT',
    userId,
    ipAddress: req.headers.get('x-forwarded-for') || 'unknown',
    userAgent: req.headers.get('user-agent') || 'unknown',
    requestPath: url.pathname,
    details: {
      query: Object.fromEntries(url.searchParams.entries())
    },
    severity: 'HIGH'
  });
}
