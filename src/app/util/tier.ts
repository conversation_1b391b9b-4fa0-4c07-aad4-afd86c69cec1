"use client";

/*
 * Tier utilities
 * Phase-1 helper functions for gating features by subscription tier.
 *
 * NOTE: Business.tier field is authoritative. For non-business flows we could
 * later switch to Organization or Account if the schema evolves.
 */

export type Tier = 'starter' | 'pro' | 'enterprise';

// Ordered from lowest privilege to highest.
const TIER_ORDER: Tier[] = ['starter', 'pro', 'enterprise'];

/**
 * Returns a numeric rank to compare tiers.
 */
function tierRank(tier: Tier): number {
  return TIER_ORDER.indexOf(tier);
}

/**
 * True if current tier meets or exceeds the required tier.
 */
export function hasRequiredTier(current: Tier, required: Tier): boolean {
  return tierRank(current) >= tierRank(required);
}

/**
 * Throws an error if the current tier is insufficient. Can be used in API
 * routes and server actions. Error can be caught at the edge to surface an
 * upgrade prompt.
 */
export const TIER_LIMITS_ENABLED = process.env.ENABLE_TIER_LIMITS === 'true';

export function assertTier(current: Tier, required: Tier, featureKey?: string): void {
  if (!hasRequiredTier(current, required)) {
    const err: Error & { status?: number; featureKey?: string } = new Error(
      `Upgrade required: ${required} tier needed${featureKey ? ` for ${featureKey}` : ''}.`,
    );
    err.status = 402; // Payment Required
    if (featureKey) err.featureKey = featureKey;
    throw err;
  }
}

/**
 * Assert tier only when limits are enabled (feature flag).
 */
export function maybeAssertTier(current: Tier, required: Tier, featureKey?: string): void {
  if (!TIER_LIMITS_ENABLED) return;
  assertTier(current, required, featureKey);
}

/**
 * React hook returning the current tier for client components. In Phase 1 this
 * simply derives it from a provided context via props to avoid invasive code
 * changes. Later we can wire it to a proper OrgContext or a Clerk session.
 */
import { useContext, createContext } from 'react';

const TierContext = createContext<Tier>('starter');

export const TierProvider = TierContext.Provider;

export function useTier(): Tier {
  return useContext(TierContext);
}
