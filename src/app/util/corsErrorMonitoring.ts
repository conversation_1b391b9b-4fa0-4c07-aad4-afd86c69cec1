// CORS Error Monitoring and Logging System
// Monitors and logs CORS-related errors for widget embedding

import { NextRequest } from 'next/server';
import { prisma } from '@/app/util/prismaClient';

export interface CorsErrorEvent {
  id?: string;
  timestamp: Date;
  origin: string | null;
  requestUrl: string;
  method: string;
  errorType: CorsErrorType;
  userAgent: string | null;
  referrer: string | null;
  widgetId?: string;
  errorMessage: string;
  requestHeaders: Record<string, string>;
  ipAddress: string;
}

export enum CorsErrorType {
  BLOCKED_ORIGIN = 'blocked_origin',
  MISSING_HEADERS = 'missing_headers',
  INVALID_PREFLIGHT = 'invalid_preflight',
  RATE_LIMITED = 'rate_limited',
  MALFORMED_REQUEST = 'malformed_request',
  UNAUTHORIZED_DOMAIN = 'unauthorized_domain',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity'
}

export interface CorsMonitoringConfig {
  enableLogging: boolean;
  enableAlerts: boolean;
  alertThreshold: number; // Number of errors per minute to trigger alert
  retentionDays: number;
  suspiciousPatterns: {
    maxRequestsPerMinute: number;
    blockedOriginsThreshold: number;
    malformedRequestsThreshold: number;
  };
}

const DEFAULT_CONFIG: CorsMonitoringConfig = {
  enableLogging: true,
  enableAlerts: true,
  alertThreshold: 10,
  retentionDays: 30,
  suspiciousPatterns: {
    maxRequestsPerMinute: 100,
    blockedOriginsThreshold: 5,
    malformedRequestsThreshold: 3
  }
};

class CorsErrorMonitor {
  private config: CorsMonitoringConfig;
  private errorCounts: Map<string, { count: number; resetTime: number }> = new Map();
  private suspiciousIPs: Set<string> = new Set();

  constructor(config: Partial<CorsMonitoringConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Log a CORS-related error event
   */
  async logCorsError(req: NextRequest, errorType: CorsErrorType, errorMessage: string, widgetId?: string) {
    if (!this.config.enableLogging) return;

    const origin = req.headers.get('origin');
    const userAgent = req.headers.get('user-agent');
    const referrer = req.headers.get('referer') || req.headers.get('referrer');
    const ipAddress = this.getClientIP(req);

    const errorEvent: CorsErrorEvent = {
      timestamp: new Date(),
      origin,
      requestUrl: req.url,
      method: req.method,
      errorType,
      userAgent,
      referrer,
      widgetId,
      errorMessage,
      requestHeaders: this.sanitizeHeaders(req.headers),
      ipAddress
    };

    try {
      // Store in database
      await this.storeCorsError(errorEvent);

      // Check for suspicious patterns
      await this.checkSuspiciousActivity(errorEvent);

      // Trigger alerts if threshold exceeded
      if (this.config.enableAlerts) {
        await this.checkAlertThreshold(errorEvent);
      }

      // Log to console for immediate visibility
      console.warn('CORS Error:', {
        type: errorType,
        origin,
        url: req.url,
        message: errorMessage,
        ip: ipAddress
      });

    } catch (error) {
      console.error('Failed to log CORS error:', error);
    }
  }

  /**
   * Store CORS error in database
   */
  private async storeCorsError(errorEvent: CorsErrorEvent) {
    try {
      await prisma.corsErrorLog.create({
        data: {
          timestamp: errorEvent.timestamp,
          origin: errorEvent.origin,
          requestUrl: errorEvent.requestUrl,
          method: errorEvent.method,
          errorType: errorEvent.errorType.toString(),
          userAgent: errorEvent.userAgent,
          referrer: errorEvent.referrer,
          widgetId: errorEvent.widgetId,
          errorMessage: errorEvent.errorMessage,
          requestHeaders: errorEvent.requestHeaders,
          ipAddress: errorEvent.ipAddress
        }
      });
    } catch (error) {
      // If database storage fails, at least log to console
      console.error('Failed to store CORS error in database:', error);
    }
  }

  /**
   * Check for suspicious activity patterns
   */
  private async checkSuspiciousActivity(errorEvent: CorsErrorEvent) {
    const { ipAddress, errorType } = errorEvent;
    const now = Date.now();
    const windowMs = 60000; // 1 minute window

    // Track error counts per IP
    const key = `${ipAddress}:${errorType}`;
    const current = this.errorCounts.get(key);

    if (!current || current.resetTime < now) {
      this.errorCounts.set(key, { count: 1, resetTime: now + windowMs });
    } else {
      current.count++;
    }

    // Check thresholds
    const errorCount = this.errorCounts.get(key)?.count || 0;
    const { suspiciousPatterns } = this.config;

    let isSuspicious = false;

    if (errorType === CorsErrorType.BLOCKED_ORIGIN && errorCount >= suspiciousPatterns.blockedOriginsThreshold) {
      isSuspicious = true;
    } else if (errorType === CorsErrorType.MALFORMED_REQUEST && errorCount >= suspiciousPatterns.malformedRequestsThreshold) {
      isSuspicious = true;
    } else if (errorCount >= suspiciousPatterns.maxRequestsPerMinute) {
      isSuspicious = true;
    }

    if (isSuspicious && !this.suspiciousIPs.has(ipAddress)) {
      this.suspiciousIPs.add(ipAddress);
      await this.handleSuspiciousActivity(errorEvent, errorCount);
    }
  }

  /**
   * Handle suspicious activity detection
   */
  private async handleSuspiciousActivity(errorEvent: CorsErrorEvent, errorCount: number) {
    const suspiciousEvent = {
      ...errorEvent,
      errorType: CorsErrorType.SUSPICIOUS_ACTIVITY,
      errorMessage: `Suspicious activity detected: ${errorCount} ${errorEvent.errorType} errors from IP ${errorEvent.ipAddress}`
    };

    // Log suspicious activity
    await this.storeCorsError(suspiciousEvent);

    // Send alert
    await this.sendAlert({
      type: 'suspicious_activity',
      message: suspiciousEvent.errorMessage,
      ipAddress: errorEvent.ipAddress,
      origin: errorEvent.origin,
      errorCount,
      timestamp: new Date()
    });

    console.error('🚨 SUSPICIOUS CORS ACTIVITY DETECTED:', suspiciousEvent);
  }

  /**
   * Check if alert threshold is exceeded
   */
  private async checkAlertThreshold(errorEvent: CorsErrorEvent) {
    const now = Date.now();
    const windowMs = 60000; // 1 minute window
    const alertKey = 'global_cors_errors';

    const current = this.errorCounts.get(alertKey);
    if (!current || current.resetTime < now) {
      this.errorCounts.set(alertKey, { count: 1, resetTime: now + windowMs });
    } else {
      current.count++;
    }

    const updatedCurrent = this.errorCounts.get(alertKey);
    if (updatedCurrent && updatedCurrent.count >= this.config.alertThreshold) {
      await this.sendAlert({
        type: 'high_error_rate',
        message: `High CORS error rate detected: ${updatedCurrent.count} errors in the last minute`,
        errorCount: updatedCurrent.count,
        threshold: this.config.alertThreshold,
        timestamp: new Date()
      });
    }
  }

  /**
   * Send alert notification
   */
  private async sendAlert(alert: any) {
    try {
      // Store alert in database
      await prisma.corsAlert.create({
        data: {
          type: alert.type,
          message: alert.message,
          metadata: alert,
          timestamp: alert.timestamp,
          isResolved: false
        }
      });

      // In production, you might want to send to external services:
      // - Email notifications
      // - Slack webhooks
      // - PagerDuty
      // - Discord webhooks
      
      console.error('🚨 CORS ALERT:', alert);

    } catch (error) {
      console.error('Failed to send CORS alert:', error);
    }
  }

  /**
   * Get analytics for CORS errors
   */
  async getCorsErrorAnalytics(days: number = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    try {
      const errors = await prisma.corsErrorLog.findMany({
        where: {
          timestamp: {
            gte: startDate
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      // Aggregate analytics
      const analytics = {
        totalErrors: errors.length,
        errorsByType: this.groupBy(errors, 'errorType'),
        errorsByOrigin: this.groupBy(errors, 'origin'),
        errorsByIP: this.groupBy(errors, 'ipAddress'),
        dailyErrorCounts: this.getDailyErrorCounts(errors),
        topErrorMessages: this.getTopErrorMessages(errors),
        suspiciousIPs: this.getSuspiciousIPs(errors)
      };

      return analytics;
    } catch (error) {
      console.error('Failed to get CORS error analytics:', error);
      return null;
    }
  }

  /**
   * Clean up old error logs
   */
  async cleanupOldLogs() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

    try {
      const deleted = await prisma.corsErrorLog.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          }
        }
      });

      // Removed debug console.log statement
      return deleted.count;
    } catch (error) {
      console.error('Failed to cleanup old CORS logs:', error);
      return 0;
    }
  }

  // Utility methods
  private getClientIP(req: NextRequest): string {
    return req.ip || 
           req.headers.get('x-forwarded-for')?.split(',')[0] || 
           req.headers.get('x-real-ip') || 
           'unknown';
  }

  private sanitizeHeaders(headers: Headers): Record<string, string> {
    const sanitized: Record<string, string> = {};
    const allowedHeaders = [
      'origin', 'referer', 'user-agent', 'content-type', 
      'access-control-request-method', 'access-control-request-headers'
    ];

    allowedHeaders.forEach(header => {
      const value = headers.get(header);
      if (value) {
        sanitized[header] = value;
      }
    });

    return sanitized;
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = String(item[key] || 'unknown');
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getDailyErrorCounts(errors: any[]): Record<string, number> {
    return errors.reduce((acc, error) => {
      const date = error.timestamp.toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getTopErrorMessages(errors: any[]): Array<{ message: string; count: number }> {
    const messageCounts = this.groupBy(errors, 'errorMessage');
    return Object.entries(messageCounts)
      .map(([message, count]) => ({ message, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private getSuspiciousIPs(errors: any[]): Array<{ ip: string; errorCount: number; errorTypes: string[] }> {
    const ipErrors = errors.reduce((acc, error) => {
      const ip = error.ipAddress;
      if (!acc[ip]) {
        acc[ip] = { errorCount: 0, errorTypes: new Set<string>() };
      }
      acc[ip].errorCount++;
      acc[ip].errorTypes.add(error.errorType);
      return acc;
    }, {} as Record<string, { errorCount: number; errorTypes: Set<string> }>);

    return Object.entries(ipErrors)
      .filter(([, data]) => (data as { errorCount: number; errorTypes: Set<string> }).errorCount >= 5) // Threshold for suspicious
      .map(([ip, data]) => ({
        ip,
        errorCount: (data as { errorCount: number; errorTypes: Set<string> }).errorCount,
        errorTypes: Array.from((data as { errorCount: number; errorTypes: Set<string> }).errorTypes)
      }))
      .sort((a, b) => b.errorCount - a.errorCount);
  }
}

// Export singleton instance
export const corsErrorMonitor = new CorsErrorMonitor();

// Helper functions for easy integration
export const logCorsError = (req: NextRequest, errorType: CorsErrorType, message: string, widgetId?: string) => {
  return corsErrorMonitor.logCorsError(req, errorType, message, widgetId);
};

export const getCorsAnalytics = (days?: number) => {
  return corsErrorMonitor.getCorsErrorAnalytics(days);
};

export const cleanupCorsLogs = () => {
  return corsErrorMonitor.cleanupOldLogs();
};