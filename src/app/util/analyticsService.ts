import {
  iBusinessAnalyticsEnhanced,
  iProductPerformance,
  iTrafficSourceEnhanced,
  iAnalyticsPeriod,
  iTrafficTrend,
  iProductAnalyticsEnhanced,
} from "./Interfaces";
import { format } from 'date-fns';

/**
 * Get aggregated business analytics for a specific date range
 * @param businessId The business ID to get analytics for
 * @param period The date range period to fetch data for
 */
export async function getBusinessAnalytics(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iBusinessAnalyticsEnhanced> {
  // In a real implementation, this would fetch data from the backend
  try {
    const response = await fetch(
      `/api/analytics/business?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch analytics data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw new Error(`Failed to fetch business analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get traffic sources for a specific business and date range
 * @param businessId The business ID to get traffic sources for
 * @param period The date range period to fetch data for
 */
export async function getTrafficSources(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iTrafficSourceEnhanced[]> {
  // In a real implementation, this would fetch data from the backend
  try {
    const response = await fetch(
      `/api/analytics/traffic?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch traffic source data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw new Error(`Failed to fetch traffic sources: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get product performance data for a specific business and date range
 * @param businessId The business ID to get product performance for
 * @param period The date range period to fetch data for
 */
export async function getProductPerformance(
  businessId: string,
  period: iAnalyticsPeriod
): Promise<iProductPerformance[]> {
  // In a real implementation, this would fetch data from the backend
  try {
    const response = await fetch(
      `/api/analytics/products?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch product performance data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw new Error(`Failed to fetch product performance: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get traffic source trends by date for a specific business and date range
 * @param businessId The business ID to get traffic trends for
 * @param period The date range period to fetch data for
 * @param days Optional number of days to limit the results to
 */
export async function getTrafficTrends(
  businessId: string,
  period: iAnalyticsPeriod,
  days: number = 14
): Promise<iTrafficTrend[]> {
  try {
    const response = await fetch(
      `/api/analytics/traffic-trends?id=${businessId}&start=${period.startDate.toISOString()}&end=${period.endDate.toISOString()}&days=${days}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch traffic trends data: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw new Error(`Failed to fetch traffic trends: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetches real product conversion data for a business
 * @param businessId The ID of the business
 * @returns Array of product performance data with real conversion metrics
 */
export async function getProductConversions(businessId: string): Promise<Array<{
  id: string;
  name: string;
  views: number;
  conversions: number;
  conversionRate: number;
}>> {
  try {
    const response = await fetch(
      `/api/analytics/product-conversions?businessId=${businessId}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(`Error fetching product conversions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Failed to fetch product conversions:', error);
    throw error;
  }
}

/**
 * Fetches real percentage changes for key metrics compared to previous period
 * @param businessId The ID of the business
 * @param days Optional number of days to analyze (defaults to 14)
 * @returns Object containing percentage changes for key metrics
 */
export async function getMetricsChanges(businessId: string, days: number = 14): Promise<{
  totalViews: number;
  uniqueVisitors: number;
  totalReviews: number;
  conversionRate: number;
}> {
  try {
    const response = await fetch(
      `/api/analytics/metrics-change?businessId=${businessId}&days=${days}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(`Error fetching metrics changes: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Failed to fetch metrics changes:', error);
    // Return default values of 0 if API fails
    return {
      totalViews: 0,
      uniqueVisitors: 0,
      totalReviews: 0,
      conversionRate: 0
    };
  }
}

/**
 * Fetches notification analytics data for a business
 * @param businessId The ID of the business
 * @param days Optional number of days to analyze (defaults to 30)
 * @returns Object containing notification analytics data
 */
export async function getNotificationAnalytics(businessId: string, days: number = 30): Promise<{
  totalNotifications: number;
  byType: Array<{ type: string; count: number }>;
  readVsUnread: { read: number; unread: number };
  notificationsPerDay: Array<{ date: string; count: number }>;
  clickThroughRate: number;
}> {
  try {
    const response = await fetch(
      `/api/analytics/notifications?businessId=${businessId}&days=${days}`,
      {
        credentials: 'include'
      }
    );

    if (!response.ok) {
      throw new Error(`Error fetching notification analytics: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Failed to fetch notification analytics:', error);
    // Return default empty values if API fails
    return {
      totalNotifications: 0,
      byType: [],
      readVsUnread: { read: 0, unread: 0 },
      notificationsPerDay: [],
      clickThroughRate: 0
    };
  }
}
