import { iComment, iReview, iProduct } from "./Interfaces";

// Utility functions for comment permissions

export interface PermissionResponse {
  allowed: boolean;
  restrictionType?: 'not_owner_comment' | 'not_reviewer' | 'not_authenticated' | 'no_restriction';
  message?: string;
}

// Check if a comment is made by the owner of the product
// This requires the comment to have the product and business information
// and the comment's user ID to match the business owner ID
export function isOwnerComment(comment: iComment, product?: iProduct | null): boolean {
  // First try the embedded data approach (for backward compatibility)
  if (comment.review?.product?.business) {
    return comment.userId === comment.review.product.business.ownerId;
  }
  
  // Fallback to using the product parameter (new approach)
  if (product) {
    const commentUserId = comment.user?.id || comment.user?.clerkUserId || comment.userId;
    const productOwnerId = product.ownerId || product.business?.ownerId;
    return commentUserId === productOwnerId;
  }
  
  return false;
}

// Check if the current user is the original reviewer of the review
// This requires the review to have the original reviewer's user ID
// We need to handle both Clerk user IDs and database user IDs
export function isOriginalReviewer(userId: string, review: iReview): boolean {
  if (!userId || !review) return false;
  
  // Direct comparison with review.userId (database ID)
  if (userId === review.userId) return true;
  
  // Also check against the review user's clerkUserId if available
  if (review.user?.clerkUserId && userId === review.user.clerkUserId) return true;
  
  // Check against review user's id field as well
  if (review.user?.id && userId === review.user.id) return true;
  
  return false;
}

// Determine if a user can reply to a comment
export function canReplyToComment(
  currentUserId: string | null,
  comment: iComment,
  review: iReview,
  product?: iProduct | null
): PermissionResponse {
  if (!currentUserId) {
    return {
      allowed: false,
      restrictionType: 'not_authenticated',
      message: 'You must be signed in to reply',
    };
  }

  // If the comment is by an owner, then only the original reviewer can reply
  if (isOwnerComment(comment, product)) {
    if (isOriginalReviewer(currentUserId, review)) {
      return {
        allowed: true,
        restrictionType: 'no_restriction',
      };
    } else {
      return {
        allowed: false,
        restrictionType: 'not_reviewer',
        message: 'Only the original reviewer can reply to the owner',
      };
    }
  }

  // For non-owner comments, anyone (authenticated) can reply
  return {
    allowed: true,
    restrictionType: 'no_restriction',
  };
}
