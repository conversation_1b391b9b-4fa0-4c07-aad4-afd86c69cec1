import { redisService } from '../lib/redis';
import { calculateBusinessGrowthMetrics } from '../lib/analytics-engine';
import { prisma } from './prismaClient';

/**
 * Generic function to get cached metrics with fallback to fresh calculation
 * @param cacheKey - Redis cache key
 * @param freshDataFn - Function to calculate fresh data if cache miss
 * @param ttl - Time to live in seconds (default: 3600 = 1 hour)
 * @returns Cached or fresh metric value
 */
export async function getCachedMetric<T>(
  cacheKey: string,
  freshDataFn: () => Promise<T>,
  ttl: number = 3600
): Promise<T | null> {
  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<T>(cacheKey);
    if (cached !== null) {
      return cached;
    }

    // Calculate fresh data
    const freshData = await freshDataFn();
    
    // Cache the result
    if (freshData !== null && freshData !== undefined) {
      await redisService.setInCache(cacheKey, freshData, ttl);
    }
    
    return freshData;
  } catch (error) {
    console.error(`❌ Error getting cached metric ${cacheKey}:`, error);
    
    // Fallback to fresh calculation on error
    try {
      return await freshDataFn();
    } catch (fallbackError) {
      console.error(`❌ Fallback calculation failed for ${cacheKey}:`, fallbackError);
      return null;
    }
  }
}

/**
 * Calculate fresh response time metric for a specific product
 * @param productId - Product ID to calculate response time for
 * @returns Average response time in hours
 */
export async function getFreshResponseMetric(productId: string): Promise<number> {
  try {
    // Get product with its business and reviews
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        business: true,
        reviews: {
          where: {
            ownerRespondedAt: {
              not: null
            }
          },
          select: {
            createdDate: true,
            ownerRespondedAt: true
          }
        }
      }
    });

    if (!product) {
      console.warn(`⚠️ Product ${productId} not found`);
      return 0;
    }

    const respondedReviews = product.reviews.filter(r => r.ownerRespondedAt && r.createdDate);
    
    if (respondedReviews.length === 0) {
      return 0;
    }

    // Calculate average response time
    const totalResponseTime = respondedReviews.reduce((acc, review) => {
      const responseTime = review.ownerRespondedAt!.getTime() - review.createdDate.getTime();
      return acc + responseTime;
    }, 0);

    const averageHours = totalResponseTime / respondedReviews.length / (1000 * 60 * 60);
    
    return Math.round(averageHours * 100) / 100; // Round to 2 decimal places
    
  } catch (error) {
    console.error(`❌ Error calculating fresh response metric for product ${productId}:`, error);
    return 0;
  }
}

/**
 * Calculate fresh response time metric for a business (all products)
 * @param businessId - Business ID to calculate response time for
 * @returns Average response time in hours
 */
export async function getFreshBusinessResponseMetric(businessId: string): Promise<number> {
  try {
    // Calculate and update analytics
    await calculateBusinessGrowthMetrics(businessId);
    
    // Fetch the updated analytics from database
    const analytics = await prisma.businessAnalytics.findUnique({
      where: { businessId },
      select: { averageReviewResponseTime: true }
    });
    
    return analytics?.averageReviewResponseTime || 0;
  } catch (error) {
    console.error(`❌ Error calculating fresh business response metric for ${businessId}:`, error);
    return 0;
  }
}

/**
 * Generate cache key for response time metrics
 * @param type - 'product' or 'business'
 * @param id - Product or business ID
 * @returns Cache key string
 */
export function getResponseTimeCacheKey(type: 'product' | 'business', id: string): string {
  return `response_time:${type}:${id}`;
}