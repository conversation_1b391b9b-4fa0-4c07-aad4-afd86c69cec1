import { iReview } from "@/app/util/Interfaces";
import { calculateWeightedRating } from "./calculateWeightedRating";
import { getGlobalRatingStats } from "./getGlobalRatingStats";

export async function calculateAverageReviewRating(
  reviews: iReview[], 
  useWeighted: boolean = true
) {
  // Fallback to original behavior for backward compatibility
  if (!useWeighted) {
    if (!reviews || reviews.length === 0) return { 
      roundedRating: 3,
      roundedRatingOneDecimalPlace: "3.0",
      numberOfReviews: 0
    };
    
    const totalScore = reviews.reduce((acc, review) => acc + review.rating, 0);
    const averageRating = totalScore / reviews.length;
    const roundedRating = Math.round(averageRating);
    
    return {
      roundedRating: roundedRating,
      roundedRatingOneDecimalPlace: averageRating.toFixed(1),
      numberOfReviews: reviews.length,
    };
  }

  // Get global stats for weighted calculation
  const globalStats = await getGlobalRatingStats();
  
  // Use new weighted rating system
  const weightedResult = calculateWeightedRating(reviews, {
    globalAverageRating: globalStats.averageRating
  });
  
  return {
    ...weightedResult,
    // Keep backward compatibility
    roundedRating: weightedResult.roundedRating,
    roundedRatingOneDecimalPlace: weightedResult.roundedRatingOneDecimalPlace,
    numberOfReviews: weightedResult.numberOfReviews
  };
}

// Synchronous version for immediate use (uses default global average)
export function calculateAverageReviewRatingSync(
  reviews: iReview[], 
  useWeighted: boolean = true
) {
  if (!useWeighted) {
    if (!reviews || reviews.length === 0) return { 
      roundedRating: 3,
      roundedRatingOneDecimalPlace: "3.0",
      numberOfReviews: 0
    };
    
    const totalScore = reviews.reduce((acc, review) => acc + review.rating, 0);
    const averageRating = totalScore / reviews.length;
    const roundedRating = Math.round(averageRating);
    
    return {
      roundedRating: roundedRating,
      roundedRatingOneDecimalPlace: averageRating.toFixed(1),
      numberOfReviews: reviews.length,
    };
  }

  const weightedResult = calculateWeightedRating(reviews);
  return {
    ...weightedResult,
    roundedRating: weightedResult.roundedRating,
    roundedRatingOneDecimalPlace: weightedResult.roundedRatingOneDecimalPlace,
    numberOfReviews: weightedResult.numberOfReviews
  };
}
