// src/app/util/userHelpers.ts
// Utility for user profile URLs and username validation

// Prefer username; fall back to Clerk/DB id
export function profileUrl(user: { userName?: string; id: string }) {
  return `/userprofile/${user.userName ? `@${user.userName}` : user.id}`;
}

// Reserved words and forbidden prefix
const RESERVED = ['admin', 'support', 'help'];
const CLERK_PREFIX = /^user_/i;

export function isValidUsername(name: string) {
  if (!name) return false;
  if (CLERK_PREFIX.test(name)) return false;
  return !RESERVED.includes(name.toLowerCase());
}
