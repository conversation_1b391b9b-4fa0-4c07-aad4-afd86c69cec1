"use client";

// Toast client wrapper for Son<PERSON>
// This provides a consistent interface for the NotificationManager

import { toast } from "sonner";

type ToastProps = {
  title: string;
  message?: string;
  type?: "default" | "success" | "info" | "warning" | "error";
};

export function showToast({ title, message, type = "default" }: ToastProps) {
  switch (type) {
    case "success":
      toast.success(title, { description: message });
      break;
    case "error":
      toast.error(title, { description: message });
      break;
    case "warning":
      toast.warning(title, { description: message });
      break;
    case "info":
      toast.info(title, { description: message });
      break;
    default:
      toast(title, { description: message });
  }
}
