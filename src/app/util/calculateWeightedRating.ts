import { iReview } from "@/app/util/Interfaces";
import { MINIMUM_REVIEWS } from "@/app/config/rating";

export interface WeightedRatingResult {
  displayRating: number;
  roundedRating: number;
  roundedRatingOneDecimalPlace: string;
  numberOfReviews: number;
  hasMinimumReviews: boolean;
  confidence: 'low' | 'medium' | 'high';
  sortingScore: number; // Used for ranking/sorting
}

export function calculateWeightedRating(
  reviews: iReview[],
  options: {
    minimumReviews?: number;
    globalAverageRating?: number;
    confidenceWeight?: number;
    actualReviewCount?: number; // Allow passing the real total count
  } = {}
): WeightedRatingResult {
  const {
    minimumReviews = MINIMUM_REVIEWS,
    globalAverageRating = 3.5,
    confidenceWeight = 10,
    actualReviewCount
  } = options;

  // Use actualReviewCount if provided, otherwise fall back to reviews.length
  const reviewCount = actualReviewCount !== undefined ? actualReviewCount : reviews?.length || 0;

  // Handle empty or invalid reviews
  if (!reviews || reviews.length === 0) {
    return {
      displayRating: 0,
      roundedRating: 0,
      roundedRatingOneDecimalPlace: "0.0",
      numberOfReviews: reviewCount, // Use the actual count even if no reviews array
      hasMinimumReviews: reviewCount >= minimumReviews, // Check against actual count
      confidence: 'low',
      sortingScore: 0
    };
  }
  
  // For calculating average, use the actual reviews array length
  const reviewsArrayLength = reviews?.length || 0;
  const actualAverage = reviewsArrayLength > 0 
    ? reviews.reduce((acc, review) => acc + review.rating, 0) / reviewsArrayLength
    : 0;

  // Calculate Bayesian Average: ((C × m) + (R × v)) / (C + v)
  // C = confidence weight, m = global average, R = actual average, v = number of reviews
  // Use the actual total review count for Bayesian calculation
  const bayesianAverage = (
    (confidenceWeight * globalAverageRating) + (actualAverage * reviewCount)
  ) / (confidenceWeight + reviewCount);

  // Determine confidence level
  let confidence: 'low' | 'medium' | 'high' = 'low';
  if (reviewCount >= minimumReviews * 3) confidence = 'high';
  else if (reviewCount >= minimumReviews) confidence = 'medium';

  const displayRating = reviewCount >= minimumReviews ? actualAverage : bayesianAverage;
  const sortingScore = bayesianAverage;

  return {
    displayRating,
    roundedRating: Math.round(displayRating),
    roundedRatingOneDecimalPlace: displayRating.toFixed(1),
    numberOfReviews: reviewCount,
    hasMinimumReviews: reviewCount >= minimumReviews,
    confidence,
    sortingScore
  };
}