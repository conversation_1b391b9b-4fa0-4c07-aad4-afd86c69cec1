import { iUser } from './Interfaces';
import { redirect } from 'next/navigation';

/**
 * Comprehensive security check for user status
 * This function checks if a user is allowed to access protected content
 * It checks for banned status, suspended status, and deleted status
 * 
 * @param user The user object to check
 * @param redirectOnBanned Whether to redirect to the banned page if user is banned (default: true)
 * @param redirectOnSuspended Whether to redirect to the suspended page if user is suspended (default: true)
 * @param redirectOnDeleted Whether to redirect to the root page if user is deleted (default: true)
 * @returns An object with the result of the check and reason if not allowed
 */
export function checkUserSecureAccess(
  user: iUser | null | undefined,
  redirectOnBanned: boolean = true,
  redirectOnSuspended: boolean = true,
  redirectOnDeleted: boolean = true
): { isAllowed: boolean; reason?: string } {
  // If no user is provided, they are allowed (authentication is handled separately)
  if (!user) {
    return { isAllowed: true };
  }

  // Check for automatic unsuspension before other checks
  if (user.status === 'SUSPENDED' && user.suspendedUntil) {
    const now = new Date();
    const suspensionEndDate = new Date(user.suspendedUntil);
    
    // If suspension period has expired, the user should be considered active
    // Note: The actual database update happens in the main user fetch endpoint
    // This check ensures consistent behavior across all security checks
    if (now >= suspensionEndDate) {
      // User suspension has expired, treat as active
      return { isAllowed: true };
    }
  }

  // Check if user is banned
  if (user.status === 'BANNED') {
    if (redirectOnBanned) {
      redirect('/banned');
    }
    return { isAllowed: false, reason: 'User is banned' };
  }

  // Check if user is suspended
  if (user.status === 'SUSPENDED') {
    if (redirectOnSuspended) {
      const url = new URL('/suspended', process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000');
      if (user.suspendedUntil) {
        url.searchParams.set('until', user.suspendedUntil.toISOString());
      }
      if (user.suspendedReason) {
        url.searchParams.set('reason', user.suspendedReason);
      }
      redirect(url.toString());
    }
    return { isAllowed: false, reason: 'User is suspended' };
  }

  // Check if user is deleted
  if (user.isDeleted) {
    if (redirectOnDeleted) {
      redirect('/');
    }
    return { isAllowed: false, reason: 'User account has been deleted' };
  }

  // User passed all checks
  return { isAllowed: true };
}

/**
 * React hook for checking user status in client components
 * This is a wrapper around checkUserSecureAccess that can be used in client components
 * It will handle redirects and return the result of the check
 * 
 * @param user The user object to check
 * @param options Configuration options for the check
 * @returns An object with the result of the check
 */
export function useSecureUser(
  user: iUser | null | undefined,
  options: {
    redirectOnBanned?: boolean;
    redirectOnSuspended?: boolean;
    redirectOnDeleted?: boolean;
  } = {}
) {
  const { redirectOnBanned = true, redirectOnSuspended = true, redirectOnDeleted = true } = options;
  
  if (!user) {
    return { isAllowed: true, isLoading: true };
  }

  // Use the utility function to check user status
  const result = checkUserSecureAccess(
    user,
    redirectOnBanned,
    redirectOnSuspended,
    redirectOnDeleted
  );

  return {
    ...result,
    isLoading: false,
    user
  };
}

/**
 * Server-side function to check user status and log access attempts
 * This function should be used in server components and API routes
 * 
 * @param user The user object to check
 * @param requestInfo Additional request information for logging
 * @returns An object with the result of the check
 */
export async function checkUserSecureAccessServer(
  user: iUser | null | undefined,
  requestInfo?: {
    ip?: string;
    path?: string;
    userAgent?: string;
  }
) {
  // If no user is provided, they are allowed (authentication is handled separately)
  if (!user) {
    return { isAllowed: true };
  }

  // Import logging functions only when needed (to avoid circular dependencies)
  const { logBannedAccessAttempt, logSuspendedAccessAttempt } = await import('./securityLogger');

  // Check for automatic unsuspension before other checks
  if (user.status === 'SUSPENDED' && user.suspendedUntil) {
    const now = new Date();
    const suspensionEndDate = new Date(user.suspendedUntil);
    
    // If suspension period has expired, the user should be considered active
    // Note: The actual database update happens in the main user fetch endpoint
    // This check ensures consistent behavior across all security checks
    if (now >= suspensionEndDate) {
      // User suspension has expired, treat as active
      return { isAllowed: true };
    }
  }

  // Check if user is banned
  if (user.status === 'BANNED') {
    // Log the access attempt
    if (requestInfo) {
      // Create a mock Request object for the logger
      const mockRequest = new Request(requestInfo.path || 'unknown', {
        headers: {
          'x-forwarded-for': requestInfo.ip || 'unknown',
          'user-agent': requestInfo.userAgent || 'unknown'
        }
      });
      await logBannedAccessAttempt(user.id, mockRequest);
    }
    return { isAllowed: false, reason: 'User is banned', redirectTo: '/banned' };
  }

  // Check if user is suspended
  if (user.status === 'SUSPENDED') {
    // Log the access attempt
    if (requestInfo) {
      // Create a mock Request object for the logger
      const mockRequest = new Request(requestInfo.path || 'unknown', {
        headers: {
          'x-forwarded-for': requestInfo.ip || 'unknown',
          'user-agent': requestInfo.userAgent || 'unknown'
        }
      });
      await logSuspendedAccessAttempt(user.id, mockRequest, user.suspendedUntil);
    }

    const url = new URL('/suspended', process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000');
    if (user.suspendedUntil) {
      url.searchParams.set('until', user.suspendedUntil.toISOString());
    }
    if (user.suspendedReason) {
      url.searchParams.set('reason', user.suspendedReason);
    }
    
    return { 
      isAllowed: false, 
      reason: 'User is suspended', 
      redirectTo: url.toString(),
      suspendedUntil: user.suspendedUntil
    };
  }

  // Check if user is deleted
  if (user.isDeleted) {
    return { isAllowed: false, reason: 'User account has been deleted', redirectTo: '/' };
  }

  // User passed all checks
  return { isAllowed: true };
}
