import { iUser, iProduct } from "./Interfaces";
import { baseUrl } from "./serverFunctions";

/**
 * Get a user by their ID
 * @param userId The ID of the user to fetch
 * @returns The user data
 */
export const getUserById = async (userId: string): Promise<iUser> => {
  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    const response = await fetch(baseUrl + `/api/get/userwithid`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: 'include',
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: `HTTP error! status: ${response.status}` }));
      throw new Error(
        errorData.error || `Failed to fetch user: ${response.status}`
      );
    }

    const data = await response.json();

    if (!data.data) {
      throw new Error("User data not found");
    }

    return data.data;
  } catch (error) {
    console.error("Failed to fetch user:", error);
    throw error;
  }
};

/**
 * Update a user's information
 * @param params Object containing userId and userData
 * @returns The updated user data
 */
export const updateUser = async ({
  userId,
  userData,
}: {
  userId: string;
  userData: Partial<iUser>;
}): Promise<iUser> => {
  if (!userId) {
    throw new Error("User ID is required for update");
  }

  try {
    const response = await fetch(`/api/update/user/${userId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userData),
      credentials: 'include'
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: `HTTP error! status: ${response.status}` }));
      throw new Error(
        errorData.error || `Failed to update user: ${response.status}`
      );
    }

    const data = await response.json();

    if (!data.data) {
      throw new Error("Updated user data not found in response");
    }

    return data.data;
  } catch (error) {
    console.error("Failed to update user:", error);
    throw error;
  }
};

/**
 * Update a product
 * @param product The product data to update
 * @returns The updated product
 */
export async function updateProduct(product: iProduct): Promise<iProduct> {
  if (!product.id) {
    throw new Error("Product ID is missing");
  }

  try {
    const response = await fetch(`/api/update/product/${product.id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(product),
      credentials: 'include'
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error || "Failed to update product");
    }

    if (!data.data) {
      throw new Error("Updated product data not found in response");
    }

    return data.data;
  } catch (error) {
    console.error("Failed to update product:", error);
    throw error;
  }
}
