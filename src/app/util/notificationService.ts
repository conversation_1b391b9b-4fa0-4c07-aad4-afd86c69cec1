/**
 * Notification Service
 * Handles API interactions for notifications including marking as read and navigation
 */

import { iProductOwnerNotification, iUserNotification, LikeNotification } from "./Interfaces";

// Base URL from env or default to production
const BASE_URL = (process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || 'https://notifications.reviewit.gy').replace(/\/$/, '');

export type NotificationType = 'user' | 'owner' | 'like' | 'system';

export interface MarkAsReadResponse {
  message: string;
  notification_id: string;
  success: boolean;
}

export interface MarkAllAsReadResponse {
  message: string;
  user_notifications_updated: number;
  owner_notifications_updated: number;
  like_notifications_updated: number;
  system_notifications_updated: number;
  total_updated: number;
  success: boolean;
}

export type ApiResponse = MarkAsReadResponse | MarkAllAsReadResponse | {
  success: false;
  error: string;
  message: string;
  status: number;
};

/**
 * Mark a single notification as read
 * @param notificationId The ID of the notification to mark as read
 * @param userId The ID of the user making the request
 * @param type The type of notification (user, owner, like, system)
 * @returns Promise resolving to the API response
 */
export async function markAsRead(
  notificationId: string | undefined,
  userId: string,
  type: NotificationType
): Promise<ApiResponse> {
  if (!notificationId) {
    return {
      success: false,
      error: 'Invalid notification ID',
      message: 'Notification ID is required',
      status: 400
    };
  }
  try {
    const response = await fetch(`${BASE_URL}/notifications/${notificationId}/read?user_id=${userId}&type=${type}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `Failed to mark notification as read: ${response.status}`,
        message: errorText || 'Unknown error',
        status: response.status
      };
    }

    // Backend returns plain text: "Notification marked as read"
    const text = await response.text();
    return {
      success: true,
      message: text,
      notification_id: notificationId
    };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return {
      success: false,
      error: 'Network error',
      message: error instanceof Error ? error.message : 'Unknown error',
      status: 0
    };
  }
}

/**
 * Mark all notifications as read for a user
 * @param userId The ID of the user making the request
 * @param type Optional type of notifications to mark as read (if omitted, marks all types as read)
 * @returns Promise resolving to the API response
 */
export async function markAllAsRead(
  userId: string,
  type?: NotificationType
): Promise<ApiResponse> {
  // Backend doesn't have bulk endpoint yet - return helpful message
  console.warn('Backend mark-all endpoint not implemented yet.');
  
  return {
    success: false,
    error: 'Not implemented',
    message: 'Mark all functionality not yet available on backend. Please mark notifications individually.',
    status: 501
  };
}

/**
 * Generate a target URL for a notification based on its type and content
 * @param notification The notification to generate a URL for
 * @returns The target URL for the notification
 */
export function getNotificationTargetUrl(
  notification: iUserNotification | iProductOwnerNotification | LikeNotification
): string {
  // Handle like notifications
  if ('target_type' in notification) {
    if (notification.target_type === 'review' && notification.review_id) {
      return `/review/${notification.review_id}`;
    } else if (notification.target_type === 'comment' && notification.review_id && notification.comment_id) {
      return `/review/${notification.review_id}?commentId=${notification.comment_id}`;
    }
  }
  
  // Handle owner notifications
  if ('owner_id' in notification && notification.review_id) {
    return `/review/${notification.review_id}`;
  }
  
  // Handle user notifications
  if ('content' in notification) {
    if (notification.review_id) {
      if (notification.comment_id) {
        return `/review/${notification.review_id}?commentId=${notification.comment_id}`;
      }
      return `/review/${notification.review_id}`;
    }
  }
  
  // Default fallback
  return '/notifications';
}

/**
 * Navigate to a notification target and mark it as read
 * @param notification The notification to navigate to
 * @param userId The ID of the user making the request
 * @param router Next.js router instance
 * @param markAsReadFn Optional function to mark the notification as read
 */
export async function navigateToNotificationTarget(
  notification: iUserNotification | iProductOwnerNotification | LikeNotification,
  userId: string,
  router: any,
  markAsReadFn?: (id: string | undefined, type: NotificationType) => Promise<void>
): Promise<void> {
  const url = getNotificationTargetUrl(notification);
  
  // Determine notification type
  let type: NotificationType;
  if ('target_type' in notification) {
    type = 'like';
  } else if ('owner_id' in notification) {
    type = 'owner';
  } else {
    type = 'user';
  }
  
  // Mark as read if not already read
  if (!notification.read) {
    if (markAsReadFn) {
      await markAsReadFn(notification.id, type);
    } else {
      await markAsRead(notification.id, userId, type);
    }
  }
  
  // Navigate to target
  router.push(url);
}