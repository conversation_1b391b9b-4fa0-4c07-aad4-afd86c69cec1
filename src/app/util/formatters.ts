import { parsePhoneNumber } from 'libphonenumber-js';

export const formatPhoneNumber = (phoneNumber: string | undefined | null): string => {
    if (!phoneNumber) return '';

    try {
        // Try to parse as an international number
        const parsed = parsePhoneNumber(phoneNumber, 'US');
        if (parsed) {
            return parsed.formatNational(); // Returns format like (*************
        }

        // Fallback formatting for 10-digit numbers
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        }

        // Return original if we can't format it
        return phoneNumber;
    } catch (error) {
        // If parsing fails, return original
        return phoneNumber;
    }
};

export const formatPhoneNumberInternational = (phoneNumber: string | undefined | null): string => {
    if (!phoneNumber) return '';

    try {
        const parsed = parsePhoneNumber(phoneNumber, 'US');
        if (parsed) {
            return parsed.formatInternational(); // Returns format like ****** 123 4567
        }
        return phoneNumber;
    } catch (error) {
        return phoneNumber;
    }
};

// Function to make phone number clickable
export const getPhoneNumberLink = (phoneNumber: string | undefined | null): string => {
    if (!phoneNumber) return '';
    const cleaned = phoneNumber.replace(/\D/g, '');
    return `tel:${cleaned}`;
}; 