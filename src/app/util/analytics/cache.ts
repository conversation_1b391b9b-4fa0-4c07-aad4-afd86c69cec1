/**
 * Cache management utilities for analytics
 */
import { redisService } from '../../lib/redis';
import { CacheStats } from './types';

// Cache versioning for schema changes
const CACHE_VERSION = 'v2';

// Cache key generators for standardized cache keys
function generateCacheKey(prefix: string, ...parts: string[]): string {
  return `reviewit:${CACHE_VERSION}:${prefix}:${parts.join(':')}`;
}

function hashQuery(query: string): string {
  return require('crypto').createHash('md5').update(query.toLowerCase().trim()).digest('hex');
}

// Specific key generators for each endpoint
export const CacheKeys = {
  topReviewers: (limit: number = 6) => generateCacheKey('top_reviewers', limit.toString()),
  productSearch: (query: string) => generateCacheKey('product_search', hashQuery(query)),
  productDetails: (productId: string) => generateCacheKey('product_details', productId),
  productReviews: (productId: string, isPublic: boolean) =>
    generateCacheKey('product_reviews', productId, isPublic.toString()),
  allProducts: () => generateCacheKey('all_products'),
  viewCount: (productId: string) => generateCacheKey('view_count', productId),
  adminRecentReviews: () => generateCacheKey('admin', 'recent_reviews'),
  adminReviewStats: () => generateCacheKey('admin', 'review_stats'),
  adminDashboardMetrics: () => generateCacheKey('admin', 'dashboard_metrics'),
  adminReports: () => generateCacheKey('admin', 'reports'),
  adminReportStats: () => generateCacheKey('admin', 'report_stats'),
  latestReviews: () => generateCacheKey('reviews', 'latest'),
  popularReviews: () => generateCacheKey('reviews', 'popular'),
  trendingReviews: () => generateCacheKey('reviews', 'trending'),
  // New: top attention products (admin global)
  topAttention: (limit: number = 5) => generateCacheKey('attention', 'top', limit.toString()),
  // Owner-admin: top attention products for business
  businessTopAttention: (businessId: string, limit: number = 5) => generateCacheKey('attention', 'business', businessId, limit.toString()),
  // Comment-specific cache keys
  reviewComments: (reviewId: string) => generateCacheKey('comments', 'review', reviewId)
};

// Cache performance tracking
const cacheStats = {
  hits: 0,
  misses: 0,
  errors: 0,
  lastError: null as Date | null
};

// Circuit breaker state
const circuitBreaker = {
  isOpen: false,
  failureCount: 0,
  lastFailureTime: null as Date | null,
  threshold: 5, // Open circuit after 5 failures
  timeout: 30000 // 30 seconds timeout
};

export function trackCacheHit(key: string): void {
  cacheStats.hits++;
}

export function trackCacheMiss(key: string): void {
  cacheStats.misses++;
}

export function getCacheStats(): CacheStats {
  const total = cacheStats.hits + cacheStats.misses;
  return {
    hits: cacheStats.hits,
    misses: cacheStats.misses,
    hitRate: total > 0 ? (cacheStats.hits / total) * 100 : 0,
    totalRequests: total
  };
}

export function getCacheHitRate(): number {
  return getCacheStats().hitRate;
}

// Circuit breaker functions
function recordCacheFailure(): void {
  cacheStats.errors++;
  cacheStats.lastError = new Date();
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = new Date();
  
  if (circuitBreaker.failureCount >= circuitBreaker.threshold) {
    circuitBreaker.isOpen = true;
  }
}

function resetCircuitBreaker(): void {
  circuitBreaker.isOpen = false;
  circuitBreaker.failureCount = 0;
  circuitBreaker.lastFailureTime = null;
}

function shouldUseCache(): boolean {
  if (!circuitBreaker.isOpen) {
    return true;
  }
  
  // Check if timeout has passed
  if (circuitBreaker.lastFailureTime) {
    const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailureTime.getTime();
    if (timeSinceLastFailure > circuitBreaker.timeout) {
      resetCircuitBreaker();
      return true;
    }
  }
  
  return false;
}

// Safe cache operations with circuit breaker
export async function safeGetFromCache<T>(key: string): Promise<T | null> {
  if (!shouldUseCache()) {
    return null;
  }
  
  try {
    const result = await redisService.get(key);
    if (result) {
      trackCacheHit(key);
      return JSON.parse(result);
    } else {
      trackCacheMiss(key);
      return null;
    }
  } catch (error) {
    recordCacheFailure();
    return null;
  }
}

export async function safeSetToCache(key: string, value: any, ttl?: number): Promise<boolean> {
  if (!shouldUseCache()) {
    return false;
  }
  
  try {
    if (ttl) {
      await redisService.set(key, JSON.stringify(value), ttl);
    } else {
      await redisService.setInCache(key, value, 3600); // Default 1 hour TTL
    }
    return true;
  } catch (error) {
    recordCacheFailure();
    return false;
  }
}

// Cache health check
export async function checkCacheHealth(): Promise<{
  isHealthy: boolean;
  stats: CacheStats & { errors: number; lastError: Date | null };
  circuitBreakerStatus: typeof circuitBreaker;
}> {
  const testKey = generateCacheKey('health_check', Date.now().toString());
  const testValue = { timestamp: new Date().toISOString() };
  
  try {
    // Test write
    await redisService.set(testKey, JSON.stringify(testValue), 10);
    
    // Test read
    const retrieved = await redisService.get(testKey);
    const isHealthy = retrieved !== null && JSON.parse(retrieved).timestamp === testValue.timestamp;
    
    // Cleanup
    await redisService.del(testKey);
    
    return {
      isHealthy,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  } catch (error) {
    recordCacheFailure();
    return {
      isHealthy: false,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  }
}

// Cache invalidation utilities
export async function invalidateProductCache(productId: string): Promise<void> {
  const keys = [
    CacheKeys.productDetails(productId),
    CacheKeys.productReviews(productId, true),
    CacheKeys.productReviews(productId, false)
  ];

  for (const key of keys) {
    try {
      await redisService.del(key);
    } catch (error) {
    }
  }
}

// Invalidate product cache when reviews are added/updated
export async function invalidateAggregatedCachesOnReviewChange(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.popularReviews(),
      CacheKeys.trendingReviews(),
      CacheKeys.latestReviews(),
      CacheKeys.topReviewers(6)  // Invalidate top reviewers when review counts change
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

export async function invalidateSearchCache(): Promise<void> {
  try {
    const pattern = generateCacheKey('product_search', '*');
    const keys = await redisService.keys(pattern);
    if (keys.length > 0) {
      for (const key of keys) {
        await redisService.del(key);
      }
    }
  } catch (error) {
  }
}

export async function invalidateAdminCache(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminReports(),
      CacheKeys.adminReportStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

export async function invalidateReviewCaches(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.latestReviews(),
      CacheKeys.popularReviews(),
      CacheKeys.trendingReviews(),
      CacheKeys.topReviewers(6)
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

export async function invalidateAllCaches(): Promise<void> {
  try {
    // Use Redis FLUSHDB to clear all keys in the current database
    // This is more efficient than deleting individual keys
    await redisService.getClient().flushdb();
  } catch (error) {
    // Fallback to individual cache invalidation if FLUSHDB fails
    await invalidateAllProductsCache();
    await invalidateSearchCache();
    await invalidateAdminCache();
    await invalidateReviewCaches();
    
    // Also clear any product-specific caches with wildcard patterns
    try {
      const productPattern = generateCacheKey('product_details', '*');
      const productKeys = await redisService.keys(productPattern);
      for (const key of productKeys) {
        await redisService.del(key);
      }
      
      const reviewPattern = generateCacheKey('product_reviews', '*');
      const reviewKeys = await redisService.keys(reviewPattern);
      for (const key of reviewKeys) {
        await redisService.del(key);
      }
    } catch (patternError) {
    }
  }
}

export async function invalidateAllProductsCache(): Promise<void> {
  try {
    const allProductsKey = CacheKeys.allProducts();
    await redisService.del(allProductsKey);
  } catch (error) {
  }
}

// NEW: Invalidate only comment caches when comments change
export async function invalidateCommentCache(reviewId: string): Promise<void> {
  try {
    const commentCacheKey = CacheKeys.reviewComments(reviewId);
    await redisService.del(commentCacheKey);
  } catch (error) {
  }
}

// UPDATED: This function is now used for votes, not comments
export async function invalidateCachesOnVote(productId: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

// DEPRECATED: Use invalidateCommentCache instead for comment changes
export async function invalidateCachesOnComment(productId: string): Promise<void> {
  return invalidateCachesOnVote(productId);
}

// Business cache invalidation utilities
export async function invalidateBusinessCaches(businessId: string): Promise<void> {
  try {
    const pattern = generateCacheKey('business_analytics', businessId, '*');
    const keys = await redisService.keys(pattern);
    
    const keysToInvalidate = [
      ...keys,
      generateCacheKey('traffic_sources', businessId, '*'),
      generateCacheKey('promotions', 'business', businessId),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        if (key.includes('*')) {
          // Handle wildcard patterns
          const wildcardKeys = await redisService.keys(key);
          for (const wildcardKey of wildcardKeys) {
            await redisService.del(wildcardKey);
          }
        } else {
          await redisService.del(key);
        }
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

// Product ownership change cache invalidation
export async function invalidateCachesOnOwnershipChange(productId: string, businessId?: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
      CacheKeys.allProducts(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    // Add business-specific cache invalidation if businessId is provided
    if (businessId) {
      const businessPattern = generateCacheKey('business_analytics', businessId, '*');
      const businessKeys = await redisService.keys(businessPattern);
      keysToInvalidate.push(...businessKeys);
      keysToInvalidate.push(generateCacheKey('promotions', 'business', businessId));
    }

    // Invalidate search cache as product ownership affects search results
    await invalidateSearchCache();

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
      }
    }
  } catch (error) {
  }
}

// Batch cache invalidation with circuit breaker pattern
export async function batchInvalidateCache(keys: string[], maxRetries: number = 3): Promise<void> {
  // Use the raw Redis client for pipeline operations
  const client = redisService.getClient();
  const pipeline = client.pipeline();
  
  for (const key of keys) {
    pipeline.del(key);
  }

  let retries = 0;
  while (retries < maxRetries) {
    try {
      await pipeline.exec();
      return;
    } catch (error) {
      retries++;
      
      if (retries >= maxRetries) {
        // Fall back to individual deletions
        for (const key of keys) {
          try {
            await redisService.del(key);
          } catch (individualError) {
          }
        }
      } else {
        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
      }
    }
  }
}
