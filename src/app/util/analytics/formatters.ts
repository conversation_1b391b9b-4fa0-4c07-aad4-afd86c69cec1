/**
 * Data formatting utilities for analytics
 */
import { iAnalyticsPeriod } from './types';

export function formatViewsPerDay(data: any[]): Array<{ date: string; views: number }> {
  return data.map((item: any) => ({
    date: item.date instanceof Date ? item.date.toISOString().split('T')[0] : item.date,
    views: parseInt(item.views) || 0
  }));
}

export function formatTrafficSources(data: any[]): Array<{ source: string; count: number }> {
  return data.map((item: any) => ({
    source: item.source || 'Direct',
    count: item._count?.source || item.count || 0
  }));
}

export function formatDeviceTypes(data: any[]): Array<{ type: string; count: number }> {
  return data.map((item: any) => ({
    type: item.deviceType || 'unknown',
    count: item._count?.deviceType || item.count || 0
  }));
}

export function calculateConversionRate(viewCount: number, reviewCount: number): number {
  if (viewCount === 0) return 0;
  return (reviewCount / viewCount) * 100;
}

export function calculateUniqueVisitorConversionRate(uniqueVisitors: number, reviewCount: number): number {
  if (uniqueVisitors === 0) return 0;
  return (reviewCount / uniqueVisitors) * 100;
}

export function formatTopProducts(data: any[]): Array<{ id: string; name: string; views: number; conversion: number }> {
  return data.map((item: any) => ({
    id: item.id,
    name: item.name,
    views: parseInt(item.views) || 0,
    conversion: parseFloat(item.conversion) || 0
  }));
}

export function calculateSourceConversionRate(source: string, productIds: string[], period: iAnalyticsPeriod): number {
  // Implementation would go here
  return 0;
}