import { prisma } from '../prismaClient';
import { subHours } from 'date-fns';
import { redisService } from '../../lib/redis';
import { TopReviewer, TopReviewersResponse } from './types';
import { CacheKeys, trackCacheHit, trackCacheMiss } from './cache';
import { getGlobalRatingStats } from '../getGlobalRatingStats';
import { calculateWeightedRating } from '../calculateWeightedRating';



// Top Reviewers Caching
export async function getTopReviewersFromCache(limit: number = 6): Promise<TopReviewersResponse> {
  const cacheKey = CacheKeys.topReviewers(limit);

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<TopReviewer[]>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return {
        success: true,
        status: 200,
        data: cached,
      };
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database
    const topReviewers = await prisma.user.findMany({
      where: {
        isDeleted: false,
        reviews: {
          some: {
            isDeleted: false,
            isPublic: true,
          },
        },
      },
      include: {
        _count: {
          select: {
            reviews: {
              where: {
                isDeleted: false,
                isPublic: true,
              },
            },
          },
        },
        reviews: {
          where: {
            isDeleted: false,
            isPublic: true,
          },
          include: {
            voteCount: true,
          },
        },
      },
      orderBy: {
        reviews: {
          _count: 'desc',
        },
      },
      take: limit,
    });

    const result: TopReviewer[] = topReviewers.map((user) => {
      const totalHelpfulVotes = user.reviews.reduce(
        (sum, review) => sum + (review.voteCount?.helpfulVotes || 0),
        0
      );

      return {
        userId: user.id,
        reviewCount: user._count.reviews,
        user: {
          id: user.id,
          userName: user.userName,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          avatar: user.avatar
        }
      };
    });

    // Cache the result for 2 minutes (120 seconds) for more responsive updates
    await redisService.setInCache(cacheKey, result, 120);

    return {
      success: true,
      status: 200,
      data: result,
    };
  } catch (error) {
    console.error('Error in getTopReviewersFromCache:', error);
    return {
      success: false,
      status: 500,
      data: [],
    };
  }
}

// All Products caching function with weighted ratings
export async function getAllProductsFromCache(useWeightedRating: boolean = true) {
  const cacheKey = useWeightedRating ? CacheKeys.allProducts() + '_weighted' : CacheKeys.allProducts();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database
    const products = await prisma.product.findMany({
      where: {
        isDeleted: false,
      },
      include: {
        reviews: {
          where: {
            isPublic: true,
            isDeleted: false,
          },
        },
        business: true,
        _count: {
          select: {
            reviews: true,
          },
        },
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    // Add weighted rating calculations if enabled
    if (useWeightedRating) {
      const globalStats = await getGlobalRatingStats();
      
      const productsWithWeightedRatings = products.map((product: any) => {
        if (product.reviews && product.reviews.length > 0) {
          const weightedResult = calculateWeightedRating(product.reviews, {
            globalAverageRating: globalStats.averageRating
          });
          
          return {
            ...product,
            weightedRating: weightedResult
          };
        }
        
        return {
          ...product,
          weightedRating: {
            displayRating: 0,
            roundedRating: 0,
            roundedRatingOneDecimalPlace: "0.0",
            numberOfReviews: 0,
            hasMinimumReviews: false,
            confidence: 'low' as const,
            sortingScore: 0
          }
        };
      });
      
      // Cache the result for 1 hour (3600 seconds)
      await redisService.setInCache(cacheKey, productsWithWeightedRatings, 3600);
      return productsWithWeightedRatings;
    }

    // Cache the result for 1 hour (3600 seconds)
    await redisService.setInCache(cacheKey, products, 3600);
    return products;
  } catch (error) {
    console.error('Error in getAllProductsFromCache:', error);
    throw error;
  }
}

// Product Reviews Caching
export async function getProductReviewsFromCache(
  productId: string,
  isPublic: boolean,
  includeUser: boolean = true,
  includeProduct: boolean = false,
  includeComments: boolean = true
): Promise<{ reviews: any[], product: any | null }> {
  const cacheKey = CacheKeys.productReviews(productId, isPublic);

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Fetch from database with complex nested queries (include deleted reviews for sanitization)
    const reviews = await prisma.review.findMany({
      where: { isPublic: isPublic, productId: productId },
      orderBy: {
        createdDate: "desc",
      },
      include: {
        user: includeUser,
        product: includeProduct,
        comments: includeComments ? {
          include: {
            user: true,
            parent: true,
            votes: {
              include: {
                user: true
              }
            },
          },
        } : false,
        voteCount: true,
        likedBy: true,
      },
    });

    // Always fetch product details for fallback
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    const result = { reviews, product };

    // Cache the result with 10-minute TTL (600 seconds)
    await redisService.setInCache(cacheKey, result, 600);

    return result;
  } catch (error) {
    console.error('Error in getProductReviewsFromCache:', error);

    // Fallback to direct database query (include deleted reviews for sanitization)
    const reviews = await prisma.review.findMany({
      where: { isPublic: isPublic, productId: productId },
      orderBy: {
        createdDate: "desc",
      },
      include: {
        user: includeUser,
        product: includeProduct,
        comments: includeComments ? {
          include: {
            user: true,
            parent: true,
            votes: {
              include: {
                user: true
              }
            },
          },
        } : false,
        voteCount: true,
        likedBy: true,
      },
    });

    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    return { reviews, product };
  }
}

// Admin Recent Reviews Caching
export async function getAdminRecentReviewsFromCache() {
  const cacheKey = CacheKeys.adminRecentReviews();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database
    const reviews = await prisma.review.findMany({
      where: {
        createdDate: {
          gte: subHours(new Date(), 24)
        },
        isDeleted: false
      },
      include: {
        product: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            userName: true
          }
        }
      },
      orderBy: {
        createdDate: 'desc'
      },
      take: 10
    });

    // Cache the result for 5 minutes (300 seconds) for freshness
    await redisService.setInCache(cacheKey, { reviews }, 300);

    return { reviews };
  } catch (error) {
    console.error('Error in getAdminRecentReviewsFromCache:', error);
    throw error;
  }
}

// Admin Review Stats Caching
export async function getAdminReviewStatsFromCache() {
  const cacheKey = CacheKeys.adminReviewStats();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database
    const last24Hours = await prisma.review.count({
      where: {
        createdDate: {
          gte: subHours(new Date(), 24)
        },
        isDeleted: false
      }
    });

    const averagePerHour = Math.round(last24Hours / 24);

    const result = {
      last24Hours,
      averagePerHour
    };

    // Cache the result for 10 minutes (600 seconds)
    await redisService.setInCache(cacheKey, result, 600);

    return result;
  } catch (error) {
    console.error('Error in getAdminReviewStatsFromCache:', error);
    throw error;
  }
}

// Admin Dashboard Metrics Caching
export async function getAdminDashboardMetricsFromCache() {
  const cacheKey = CacheKeys.adminDashboardMetrics();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }

    trackCacheMiss(cacheKey);

    // Cache miss - fetch from database with transaction for consistency
    const metrics = await prisma.$transaction([
      // Total users
      prisma.user.count({
        where: { isDeleted: false }
      }),
      // Total reviews
      prisma.review.count({
        where: { isDeleted: false }
      }),
      // Total products
      prisma.product.count({
        where: { isDeleted: false }
      }),
      // Total reports
      prisma.reviewReport.count(),
      // Recent reports (last 7 days)
      prisma.reviewReport.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ]);

    const result = {
      success: true,
      data: {
        totalUsers: metrics[0],
        totalReviews: metrics[1],
        totalProducts: metrics[2],
        totalReports: metrics[3],
        recentReports: metrics[4]
      }
    };

    // Cache the result for 15 minutes (900 seconds)
    await redisService.setInCache(cacheKey, result, 900);

    return result;
  } catch (error) {
    console.error('Error in getAdminDashboardMetricsFromCache:', error);
    throw error;
  }
}

// Popular Reviews Caching
export async function getPopularReviewsFromCache() {
  const cacheKey = CacheKeys.popularReviews();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit('popular_reviews');
      return cached;
    }

    trackCacheMiss('popular_reviews');

    // Fetch from database if not in cache
    const reviews = await prisma.review.findMany({
      where: {
        isPublic: true,
        isDeleted: false
      },
      include: {
        user: true,
        product: true,
        voteCount: true,
        comments: {
          include: {
            user: true,
            votes: true,
          },
        },
        _count: {
          select: {
            comments: true
          }
        }
      },
      take: 50,
    });

    // Calculate popularity scores and sort
    const reviewsWithScores = reviews.map(review => {
      const helpfulVotes = review.voteCount?.helpfulVotes || 0;
      const unhelpfulVotes = review.voteCount?.unhelpfulVotes || 0;
      const commentCount = review._count?.comments || 0;

      const popularityScore = (helpfulVotes * 2) + (commentCount * 1) - (unhelpfulVotes * 0.5);

      return {
        ...review,
        popularityScore
      };
    });

    const sortedReviews = reviewsWithScores
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, 6);

    const result = {
      success: true,
      status: 200,
      data: sortedReviews,
    };

    // Cache the result for 2 minutes (120 seconds) for more responsive updates
    await redisService.setInCache(cacheKey, result, 120);

    return result;
  } catch (error) {
    console.error('Error in getPopularReviewsFromCache:', error);
    return {
      success: false,
      status: 500,
      data: 'Failed to fetch popular reviews',
    };
  }
}

// Latest Reviews Caching
export async function getLatestReviewsFromCache() {
  const cacheKey = CacheKeys.latestReviews();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit('latest_reviews');
      return cached;
    }

    trackCacheMiss('latest_reviews');

    // Fetch from database if not in cache
    const reviews = await prisma.review.findMany({
      where: {
        isPublic: true,
        isDeleted: false
      },
      include: {
        user: true,
        product: true,
        voteCount: true,
        comments: {
          include: {
            user: true,
            votes: true,
          },
        },
      },
      orderBy: {
        createdDate: "desc",
      },
      take: 6,
    });

    const result = {
      success: true,
      status: 200,
      data: reviews,
    };

    // Cache the result for 10 minutes (600 seconds)
    await redisService.setInCache(cacheKey, result, 600);

    return result;
  } catch (error) {
    console.error('Error in getLatestReviewsFromCache:', error);
    return {
      success: false,
      status: 500,
      data: 'Failed to fetch latest reviews',
    };
  }
}

// Top Attention Products Caching
export async function getTopAttentionProductsFromCache(limit: number = 5) {
  // Cap limit to avoid heavy queries
  const safeLimit = Math.min(Math.max(limit, 1), 20);
  const cacheKey = CacheKeys.topAttention(safeLimit);

  try {
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit('top_attention');
      return cached;
    }

    trackCacheMiss('top_attention');

    // Raw SQL for aggregate attention score (avg scrollDepth * avg duration)
    const rows = await prisma.$queryRawUnsafe<any[]>(
      `SELECT "productId",
              AVG("scrollDepth") AS "avgScrollDepth",
              AVG("duration")    AS "avgDuration",
              COUNT(*)            AS "views",
              AVG("scrollDepth") * AVG("duration") AS "attentionScore"
       FROM   "ProductViewEvent"
       GROUP  BY "productId"
       ORDER  BY "attentionScore" DESC
       LIMIT  ${safeLimit}`);

    // Fetch basic product info for display purposes
    const productIds = rows.map(r => r.productId);
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
      select: { id: true, name: true, display_image: true }
    });

    const data = rows.map(row => ({
      ...row,
      product: products.find(p => p.id === row.productId) || null
    }));

    const result = {
      success: true,
      status: 200,
      data
    };

    await redisService.setInCache(cacheKey, result, 300); // cache 5 min

    return result;
  } catch (error) {
    console.error('Error in getTopAttentionProductsFromCache:', error);
    return {
      success: false,
      status: 500,
      data: 'Failed to fetch top attention products'
    };
  }
}

// Trending Reviews Caching
export async function getTrendingReviewsFromCache() {
  const cacheKey = CacheKeys.trendingReviews();

  try {
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit('trending_reviews');
      return cached;
    }

    trackCacheMiss('trending_reviews');

    // Fetch from database if not in cache
    const sevenDaysAgo = subHours(new Date(), 7 * 24); // 7 days ago

    const reviews = await prisma.review.findMany({
      where: {
        isPublic: true,
        isDeleted: false,
        createdDate: {
          gte: sevenDaysAgo
        }
      },
      include: {
        user: true,
        product: true,
        voteCount: true,
        comments: {
          include: {
            user: true,
            votes: true,
          },
        },
        _count: {
          select: {
            comments: true
          }
        }
      },
      take: 50,
    });

    // Calculate trending scores with time decay
    const now = new Date();
    const reviewsWithTrendingScores = reviews.map(review => {
      const helpfulVotes = review.voteCount?.helpfulVotes || 0;
      const unhelpfulVotes = review.voteCount?.unhelpfulVotes || 0;
      const commentCount = review._count?.comments || 0;

      const ageInHours = (now.getTime() - new Date(review.createdDate).getTime()) / (1000 * 60 * 60);
      const timeDecayFactor = Math.exp(-ageInHours / 24);
      const baseScore = (helpfulVotes * 3) + (commentCount * 2) - (unhelpfulVotes * 1);
      const trendingScore = baseScore * timeDecayFactor;

      return {
        ...review,
        trendingScore,
        ageInHours
      };
    });

    const sortedReviews = reviewsWithTrendingScores
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, 6);

    const result = {
      success: true,
      status: 200,
      data: sortedReviews,
    };

    // Cache the result for 15 minutes (900 seconds)
    await redisService.setInCache(cacheKey, result, 900);

    return result;
  } catch (error) {
    console.error('Error in getTrendingReviewsFromCache:', error);
    return {
      success: false,
      status: 500,
      data: 'Failed to fetch trending reviews',
    };
  }
}

// View Count Management
export async function incrementViewCountInRedis(productId: string): Promise<number> {
  const cacheKey = CacheKeys.viewCount(productId);

  try {
    // Use Redis INCR for atomic increment operation
    const newCount = await redisService.incr(cacheKey);

    // Set expiration to 1 hour if this is the first increment
    if (newCount === 1) {
      await redisService.expire(cacheKey, 3600); // 1 hour TTL
    }

    return newCount;
  } catch (error) {
    console.error('Error incrementing view count in Redis:', error);

    // Fallback to direct database increment
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: { viewCount: { increment: 1 } },
      select: { viewCount: true },
    });

    return updatedProduct.viewCount || 1;
  }
}

// Get current view count (Redis + Database)
export async function getCurrentViewCount(productId: string): Promise<number> {
  try {
    const cacheKey = CacheKeys.viewCount(productId);

    // Get Redis counter
    const redisCount = await redisService.getFromCache<string>(cacheKey);
    const redisIncrement = redisCount ? parseInt(redisCount) : 0;

    // Get database count
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { viewCount: true },
    });

    const dbCount = product?.viewCount || 0;
    return dbCount + redisIncrement;
  } catch (error) {
    console.error('Error getting current view count:', error);

    // Fallback to database only
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { viewCount: true },
    });

    return product?.viewCount || 0;
  }
}

// Batch update view counts from Redis to Database
export async function flushViewCountsToDatabase(): Promise<void> {
  try {
    // Removed debug console.log statements
    // Get all view count keys from Redis
    const pattern = CacheKeys.viewCount('*');
    const keys = await redisService.keys(pattern);

    if (keys.length === 0) {
      // Removed debug console.log statements
      return;
    }

    // Removed debug console.log statements

    // Process in batches to avoid overwhelming the database
    const batchSize = 50;
    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);

      await Promise.all(batch.map(async (key) => {
        try {
          // Extract product ID from cache key
          const productId = key.replace('reviewit:view_count:', '');

          // Get the increment count
          const incrementStr = await redisService.get(key);
          const increment = incrementStr ? parseInt(incrementStr) : 0;

          if (increment > 0) {
            // Update database
            await prisma.product.update({
              where: { id: productId },
              data: { viewCount: { increment } },
            });

            // Remove from Redis after successful database update
            await redisService.del(key);

            // Removed debug console.log statements
          }
        } catch (error) {
          console.error(`Error flushing view count for key ${key}:`, error);
        }
      }));
    }

    // Removed debug console.log statements
  } catch (error) {
    console.error('Error in batch view count flush:', error);
  }
}

// Manual flush function for admin use
export async function manualFlushViewCounts(): Promise<{ flushed: number; errors: number }> {
  try {
    const pattern = CacheKeys.viewCount('*');
    const keys = await redisService.keys(pattern);

    let flushed = 0;
    let errors = 0;

    for (const key of keys) {
      try {
        const productId = key.replace('reviewit:view_count:', '');
        const incrementStr = await redisService.get(key);
        const increment = incrementStr ? parseInt(incrementStr) : 0;

        if (increment > 0) {
          await prisma.product.update({
            where: { id: productId },
            data: { viewCount: { increment } },
          });

          await redisService.del(key);
          flushed++;
        }
      } catch (error) {
        console.error(`Error in manual flush for key ${key}:`, error);
        errors++;
      }
    }

    return { flushed, errors };
  } catch (error) {
    console.error('Error in manual flush:', error);
    return { flushed: 0, errors: 1 };
  }
}

// Test function to get 'ken' key from Redis
export async function testGetKenFromRedis(): Promise<void> {
  try {
    const kenValue = await redisService.getFromCache<string>('ken');
    if (kenValue) {
      // Removed debug console.log statements
    } else {
      // Removed debug console.log statements
    }
  } catch (error) {
    console.error('Error getting ken from Redis:', error);
  }
}

// Weighted Rating Analytics
export interface WeightedRatingAnalytics {
  globalStats: {
    averageRating: number;
    totalReviews: number;
    lastCalculated: Date;
  };
  thresholdStats: {
    productsAboveThreshold: number;
    productsBelowThreshold: number;
    totalProducts: number;
    percentageAboveThreshold: number;
  };
  confidenceDistribution: {
    high: number;
    medium: number;
    low: number;
  };
  ratingDistribution: {
    weighted: { [key: number]: number };
    traditional: { [key: number]: number };
  };
}

export async function getWeightedRatingAnalytics(): Promise<WeightedRatingAnalytics> {
  const cacheKey = 'weighted_rating_analytics';
  
  try {
    // Try cache first
    const cached = await redisService.getFromCache<WeightedRatingAnalytics>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return cached;
    }
    
    trackCacheMiss(cacheKey);
    
    // Get global stats
    const globalStats = await getGlobalRatingStats();
    
    // Get all products with their reviews
    const products = await prisma.product.findMany({
      where: { isDeleted: false },
      include: {
        reviews: {
          where: { isDeleted: false, isPublic: true }
        }
      }
    });
    
    let productsAboveThreshold = 0;
    let productsBelowThreshold = 0;
    const confidenceDistribution = { high: 0, medium: 0, low: 0 };
    const weightedRatingDistribution: { [key: number]: number } = {};
    const traditionalRatingDistribution: { [key: number]: number } = {};
    
    for (const product of products) {
      if (product.reviews.length === 0) continue;
      
      // Calculate weighted rating
      const weightedResult = calculateWeightedRating(product.reviews, {
        globalAverageRating: globalStats.averageRating
      });
      
      // Calculate traditional rating
      const traditionalAvg = product.reviews.reduce((sum: number, r: { rating: number }) => sum + r.rating, 0) / product.reviews.length;
      const traditionalRounded = Math.round(traditionalAvg);
      
      // Track threshold stats
      if (weightedResult.hasMinimumReviews) {
        productsAboveThreshold++;
      } else {
        productsBelowThreshold++;
      }
      
      // Track confidence distribution
      if (weightedResult.confidence) {
        confidenceDistribution[weightedResult.confidence]++;
      }
      
      // Track rating distributions
      const weightedRating = weightedResult.roundedRating;
      const traditionalRating = traditionalRounded;
      
      weightedRatingDistribution[weightedRating] = (weightedRatingDistribution[weightedRating] || 0) + 1;
      traditionalRatingDistribution[traditionalRating] = (traditionalRatingDistribution[traditionalRating] || 0) + 1;
    }
    
    const totalProducts = productsAboveThreshold + productsBelowThreshold;
    const percentageAboveThreshold = totalProducts > 0 ? (productsAboveThreshold / totalProducts) * 100 : 0;
    
    const analytics: WeightedRatingAnalytics = {
      globalStats: {
        averageRating: globalStats.averageRating,
        totalReviews: globalStats.totalReviews,
        lastCalculated: globalStats.lastCalculated
      },
      thresholdStats: {
        productsAboveThreshold,
        productsBelowThreshold,
        totalProducts,
        percentageAboveThreshold
      },
      confidenceDistribution,
      ratingDistribution: {
        weighted: weightedRatingDistribution,
        traditional: traditionalRatingDistribution
      }
    };
    
    // Cache for 1 hour
    await redisService.setInCache(cacheKey, analytics, 3600);
    
    return analytics;
  } catch (error) {
    console.error('Error getting weighted rating analytics:', error);
    throw error;
  }
}

// Force refresh of global rating stats
export async function refreshGlobalRatingStats(): Promise<void> {
  try {
    await getGlobalRatingStats();
    // Clear analytics cache to force recalculation
    await redisService.del('weighted_rating_analytics');
    // Removed debug console.log statements
  } catch (error) {
    console.error('Error refreshing global rating stats:', error);
    throw error;
  }
}