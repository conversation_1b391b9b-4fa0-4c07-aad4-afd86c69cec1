import { iCom<PERSON>, i<PERSON>roduct, iReview, iUser } from "./Interfaces";

/**
 * Checks if a comment is from the owner of THIS SPECIFIC product being reviewed
 * ONLY the owner of the current product should get the "Owner" badge
 *
 * @param comment The comment to check
 * @param product The current product being reviewed
 * @returns boolean indicating if the comment is from THIS product's owner
 */
export function isOwnerComment(comment: iComment, product?: iProduct): boolean {
  // Early returns for invalid data
  if (!comment || !comment.user || !product) {
    return false;
  }

  // Get the comment author's user ID
  const commentUserId = comment.user.id || comment.user.clerkUserId || comment.userId;
  
  if (!commentUserId) {
    console.warn("No user ID found for comment author:", comment.user);
    return false;
  }

  // Get the current product's owner ID
  const productOwnerId = product.ownerId || product.business?.ownerId;
  
  if (!productOwnerId) {
    console.warn("No owner ID found for product:", product.name);
    return false;
  }

  // Simple comparison: comment author must be THIS product's owner
  const isThisProductOwner = commentUserId === productOwnerId;

  // Removed debug console.log statement
  return isThisProductOwner;
}

/**
 * Checks if the current user is the owner of the product
 *
 * @param currentUser The current user
 * @param product The product to check ownership for
 * @returns boolean indicating if the current user is the product owner
 */
export function isCurrentUserProductOwner(
  currentUser: iUser,
  product?: iProduct
): boolean {
  if (!currentUser || !product) {
    return false;
  }

  // Check if the current user is the product owner
  const isOwner =
    (product.ownerId && currentUser.id === product.ownerId) ||
    (product.business?.ownerId && currentUser.id === product.business.ownerId);

  // Ensure we return a boolean value
  return isOwner === true;
}
