"use client";

// NotificationManager.ts
// Utility class wrapping SSE stream and toast plumbing per integration guide.
// Usage: const nm = new NotificationManager(userId); nm.connect();

import { showToast } from "./toastClient";

export type NotificationMessage = {
  user_id: string;
  type: "user" | "owner" | "like" | "system";
  event: string;
  notification: any;
};

export class NotificationManager {
  private userId: string;
  private eventSource?: EventSource;
  private onNotification?: (data: NotificationMessage) => void;
  private baseUrl: string;

  constructor(userId: string, onNotification?: (data: NotificationMessage) => void) {
    this.userId = userId;
    this.onNotification = onNotification;
    this.baseUrl = (process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || "https://notifications.reviewit.gy").replace(/\/$/, "");
  }

  connect() {
    if (this.eventSource) return;
    this.eventSource = new EventSource(`${this.baseUrl}/notifications/stream?user_id=${this.userId}`);

    this.eventSource.onmessage = (event) => {
      try {
        const data: NotificationMessage = JSON.parse(event.data);
        this.handleNotification(data);
      } catch (err) {
        console.error("Invalid notification payload", err);
      }
    };

    this.eventSource.onerror = (err) => {
      console.error("Notification stream error", err);
      // naive reconnect
      this.disconnect();
      setTimeout(() => this.connect(), 3000);
    };
  }

  private handleNotification(data: NotificationMessage) {
    if (this.onNotification) {
      this.onNotification(data);
    } else {
      // default toast behavior
      switch (data.type) {
        case "user":
          showToast({ 
            title: `${data.notification.from_name} replied`, 
            message: data.notification.content,
            type: "info"
          });
          break;
        case "like":
          showToast({ 
            title: `${data.notification.from_name} liked your ${data.notification.target_type}`,
            type: "success" 
          });
          break;
        case "owner":
          showToast({ 
            title: `New review for ${data.notification.product_name}`, 
            message: `${data.notification.from_name} reviewed your product`,
            type: "info"
          });
          break;
        case "system":
          showToast({
            title: data.notification.title || "System Update",
            message: data.notification.message,
            type: data.notification.icon || "info"
          });
          break;
      }
    }
  }

  disconnect() {
    this.eventSource?.close();
    this.eventSource = undefined;
  }
}
