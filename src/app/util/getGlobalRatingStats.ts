import { prisma } from "@/app/util/prismaClient";

interface GlobalRatingStats {
  averageRating: number;
  totalReviews: number;
  lastCalculated: Date;
}

let cachedGlobalStats: GlobalRatingStats | null = null;
let lastCacheTime: number = 0;
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

export async function getGlobalRatingStats(): Promise<GlobalRatingStats> {
  const now = Date.now();
  
  if (cachedGlobalStats && (now - lastCacheTime) < CACHE_DURATION) {
    return cachedGlobalStats;
  }

  try {
    const result = await prisma.review.aggregate({
      where: { isDeleted: false, isPublic: true },
      _avg: { rating: true },
      _count: { rating: true }
    });

    cachedGlobalStats = {
      averageRating: result._avg.rating || 3.5,
      totalReviews: result._count.rating || 0,
      lastCalculated: new Date()
    };
    
    lastCacheTime = now;
    return cachedGlobalStats;
  } catch (error) {
    console.error('Error calculating global rating stats:', error);
    return { averageRating: 3.5, totalReviews: 0, lastCalculated: new Date() };
  }
}