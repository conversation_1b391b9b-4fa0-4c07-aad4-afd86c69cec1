"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0, // Data is immediately stale
      gcTime: 0, // Garbage collect immediately (was cacheTime in v4)
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
      // Additional options to completely disable caching
      retry: false, // Don't retry failed requests
      refetchInterval: false, // Don't auto-refetch
      refetchIntervalInBackground: false,
      // Force network requests every time
      networkMode: 'always', // Always make network requests
    },
  },
});

const QueryProvider = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>
    {children}
  </QueryClientProvider>
);

export default QueryProvider;
