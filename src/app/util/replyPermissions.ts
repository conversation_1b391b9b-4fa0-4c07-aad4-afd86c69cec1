import { iComment, iProduct, iReview, iUser } from "./Interfaces";
import { isOwnerComment } from "./commentHelpers";

// ===== PERMISSION INTERFACES =====

export type ReplyRestrictionType = 'reviewer-only' | 'owner-only' | 'none';

export type ReplyPermissionReason = 
  | 'authentication_required'
  | 'reviewer_only'
  | 'self_reply'
  | 'unauthorized'
  | 'comment_deleted'
  | 'invalid_comment';

export interface ReplyPermissionResponse {
  canReply: boolean;
  reason?: ReplyPermissionReason;
  originalReviewerId?: string;
  restrictionType: ReplyRestrictionType;
  restrictionMessage?: string;
}

export interface ReplyPermissionContext {
  currentUser?: iUser;
  comment: iComment;
  review?: iReview;
  product?: iProduct;
}

// ===== PERMISSION CALCULATION UTILITIES =====

/**
 * Main function to calculate reply permissions for a comment
 * 
 * @param context - The permission context containing user, comment, review, and product
 * @returns ReplyPermissionResponse with permission status and details
 */
export function calculateReplyPermission(
  context: ReplyPermissionContext
): ReplyPermissionResponse {
  const { currentUser, comment, review, product } = context;

  // 1. Check if user is authenticated
  if (!currentUser) {
    return {
      canReply: false,
      reason: 'authentication_required',
      restrictionType: 'none',
      restrictionMessage: 'Please sign in to reply to comments'
    };
  }

  // 2. Check if comment is deleted
  if (comment.isDeleted) {
    return {
      canReply: false,
      reason: 'comment_deleted',
      restrictionType: 'none',
      restrictionMessage: 'Cannot reply to deleted comments'
    };
  }

  // 3. Check if user is trying to reply to their own comment
  const commentUserId = comment.user?.id || comment.user?.clerkUserId || comment.userId;
  const currentUserId = currentUser.id || currentUser.clerkUserId;
  
  if (commentUserId === currentUserId) {
    return {
      canReply: false,
      reason: 'self_reply',
      restrictionType: 'none',
      restrictionMessage: 'You cannot reply to your own comment'
    };
  }

  // 4. Check if this is an owner comment
  const isOwner = product && isOwnerComment(comment, product);
  
  if (isOwner && review) {
    // For owner comments, only the original reviewer can reply
    const isOriginalReviewer = isUserOriginalReviewer(currentUserId, review);
    
    return {
      canReply: isOriginalReviewer,
      reason: isOriginalReviewer ? undefined : 'reviewer_only',
      originalReviewerId: review.userId,
      restrictionType: 'reviewer-only',
      restrictionMessage: isOriginalReviewer 
        ? undefined 
        : 'Only the original reviewer can reply to business owner responses'
    };
  }

  // 5. For regular comments, normal reply rules apply
  return {
    canReply: true,
    restrictionType: 'none'
  };
}

/**
 * Validates if a user can reply to a specific comment via API
 * This is used for backend validation
 * 
 * @param userId - The ID of the user attempting to reply
 * @param comment - The comment being replied to
 * @param review - The review containing the comment
 * @param product - The product being reviewed
 * @returns ReplyPermissionResponse with validation result
 */
export function validateApiReplyPermission(
  userId: string,
  comment: iComment,
  review?: iReview,
  product?: iProduct
): ReplyPermissionResponse {
  // Create a minimal user object for validation
  const userContext = { id: userId, clerkUserId: userId } as iUser;
  
  return calculateReplyPermission({
    currentUser: userContext,
    comment,
    review,
    product
  });
}

// ===== HELPER FUNCTIONS =====

/**
 * Checks if a comment is from the original reviewer of a review
 * 
 * @param comment - The comment to check
 * @param review - The review to compare against
 * @returns boolean indicating if the comment is from the original reviewer
 */
export function isOriginalReviewer(comment: iComment, review?: iReview): boolean {
  if (!comment || !review) {
    return false;
  }

  const commentUserId = comment.user?.id || comment.user?.clerkUserId || comment.userId;
  return isUserOriginalReviewer(commentUserId, review);
}

/**
 * Gets the original reviewer ID from a review
 * 
 * @param review - The review to get the reviewer ID from
 * @returns string | undefined - The reviewer's user ID
 */
export function getOriginalReviewerId(review?: iReview): string | undefined {
  return review?.userId;
}

/**
 * Checks if a user is the original reviewer of a review
 * 
 * @param userId - The user ID to check
 * @param review - The review to compare against
 * @returns boolean indicating if the user is the original reviewer
 */
export function isUserOriginalReviewer(userId: string, review?: iReview): boolean {
  if (!userId || !review) return false;
  
  // Direct comparison with review.userId (database ID)
  if (userId === review.userId) return true;
  
  // Also check against the review user's clerkUserId if available
  if (review.user?.clerkUserId && userId === review.user.clerkUserId) return true;
  
  // Check against review user's id field as well
  if (review.user?.id && userId === review.user.id) return true;
  
  return false;
}

/**
 * Gets a human-readable restriction message based on the restriction type and user context
 * 
 * @param restrictionType - The type of restriction
 * @param isOriginalReviewer - Whether the current user is the original reviewer
 * @param isAuthenticated - Whether the user is authenticated
 * @returns string - The appropriate restriction message
 */
export function getRestrictionMessage(
  restrictionType: ReplyRestrictionType,
  isOriginalReviewer: boolean = false,
  isAuthenticated: boolean = false
): string {
  switch (restrictionType) {
    case 'reviewer-only':
      if (!isAuthenticated) {
        return 'Please sign in to participate in this conversation';
      }
      return isOriginalReviewer 
        ? 'You can reply to this business owner response'
        : 'Only the original reviewer can reply to business owner responses';
    
    case 'owner-only':
      return 'Only the business owner can reply to this comment';
    
    case 'none':
    default:
      return isAuthenticated 
        ? 'You can reply to this comment'
        : 'Please sign in to reply';
  }
}

/**
 * Determines if a comment thread has reply restrictions
 * 
 * @param comment - The comment to check
 * @param product - The product context
 * @returns ReplyRestrictionType - The type of restriction in effect
 */
export function getCommentRestrictionType(
  comment: iComment,
  product?: iProduct
): ReplyRestrictionType {
  if (!product) {
    return 'none';
  }

  const isOwner = isOwnerComment(comment, product);
  
  if (isOwner) {
    return 'reviewer-only';
  }

  return 'none';
}

/**
 * Batch permission calculation for multiple comments
 * Useful for calculating permissions for entire comment threads
 * 
 * @param comments - Array of comments to calculate permissions for
 * @param currentUser - The current user
 * @param review - The review context
 * @param product - The product context
 * @returns Map of comment ID to permission response
 */
export function calculateBatchReplyPermissions(
  comments: iComment[],
  currentUser?: iUser,
  review?: iReview,
  product?: iProduct
): Map<string, ReplyPermissionResponse> {
  const permissionMap = new Map<string, ReplyPermissionResponse>();

  comments.forEach(comment => {
    if (comment.id) {
      const permission = calculateReplyPermission({
        currentUser,
        comment,
        review,
        product
      });
      permissionMap.set(comment.id, permission);
    }
  });

  return permissionMap;
}