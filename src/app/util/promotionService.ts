"use server";

import { prisma } from "./prismaClient";
import { iPromotion } from "./Interfaces";
import { uploadPromotionImageToCloudinary, deletePromotionImageFromCloudinary } from "./uploadImageToCloudinary";
import redisService from "@/app/lib/redis";

// Cache keys
const CACHE_KEYS = {
  ACTIVE_PROMOTIONS: (productId: string) => `promotions:active:${productId}`,
  BUSINESS_PROMOTIONS: (businessId: string) => `promotions:business:${businessId}`,
  PROMOTION_ANALYTICS: (promotionId: string) => `promotions:analytics:${promotionId}`,
  PROMOTION_DETAIL: (promotionId: string) => `promotions:detail:${promotionId}`,
};

// Cache TTL (in seconds)
const CACHE_TTL = {
  ACTIVE_PROMOTIONS: 300, // 5 minutes
  BUSINESS_PROMOTIONS: 600, // 10 minutes
  PROMOTION_ANALYTICS: 1800, // 30 minutes
  PROMOTION_DETAIL: 900, // 15 minutes
};

export interface CreatePromotionData {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  discountPercentage?: number;
  discountAmount?: number;
  promotionCode?: string;
  isActive?: boolean;
  image?: string; // Base64 or URL
  productId: string;
  businessId: string;
  createdById: string;
}

export interface UpdatePromotionData {
  title?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  discountPercentage?: number;
  discountAmount?: number;
  promotionCode?: string;
  isActive?: boolean;
  image?: string; // Base64 or URL
}

// CRUD Operations
export async function createPromotion(data: CreatePromotionData): Promise<iPromotion> {
  try {
    // Validate dates
    if (!(await validatePromotionDates(data.startDate, data.endDate))) {
      throw new Error("End date must be after start date");
    }

    // Check promotion limits for business (if needed)
    const canCreate = await checkPromotionLimits(data.businessId);
    if (!canCreate) {
      throw new Error("Promotion limit reached for this business");
    }

      let imageUrl: string | undefined;
      let imagePublicId: string | undefined;

      // Handle image upload if provided
      if (data.image && data.image.startsWith('data:')) {
        try {
          const uploadResult = await uploadPromotionImageToCloudinary(data.image);
          imageUrl = uploadResult.secure_url;
          imagePublicId = uploadResult.public_id;
        } catch (error) {
          console.error("Failed to upload promotion image:", error);
          // Continue without image rather than failing
        }
      } else if (data.image && data.image.startsWith('http')) {
        imageUrl = data.image;
      }

      // Create promotion
      const promotion = await prisma.promotion.create({
        data: {
          title: data.title,
          description: data.description,
          startDate: data.startDate,
          endDate: data.endDate,
          discountPercentage: data.discountPercentage,
          discountAmount: data.discountAmount,
          promotionCode: data.promotionCode,
          isActive: data.isActive ?? true,
          image: imageUrl,
          imagePublicId: imagePublicId,
          productId: data.productId,
          businessId: data.businessId,
          createdById: data.createdById,
        },
        include: {
          product: true,
          business: true,
          createdBy: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            }
          },
          analytics: true,
        },
      });

      // Create analytics record
      await prisma.promotionAnalytics.create({
        data: {
          promotionId: promotion.id,
        },
      });

      // Invalidate relevant caches
      await invalidatePromotionCaches(data.productId, data.businessId);

      return promotion as unknown as iPromotion;
    } catch (error) {
      console.error("Error creating promotion:", error);
      throw error;
    }
}

export async function updatePromotion(id: string, data: UpdatePromotionData): Promise<iPromotion> {
  try {
    // Get existing promotion
    const existingPromotion = await prisma.promotion.findUnique({
      where: { id },
      select: { imagePublicId: true, productId: true, businessId: true }
    });

    if (!existingPromotion) {
      throw new Error("Promotion not found");
    }

    // Validate dates if provided
     if (data.startDate && data.endDate && !(await validatePromotionDates(data.startDate, data.endDate))) {
       throw new Error("End date must be after start date");
     }

      let imageUrl: string | undefined;
      let imagePublicId: string | undefined;

      // Handle image update
      if (data.image) {
        // Delete old image if it exists
        if (existingPromotion.imagePublicId) {
          try {
            await deletePromotionImageFromCloudinary(existingPromotion.imagePublicId);
          } catch (error) {
            console.warn("Failed to delete old promotion image:", error);
          }
        }

        // Upload new image
        if (data.image.startsWith('data:')) {
          try {
            const uploadResult = await uploadPromotionImageToCloudinary(data.image);
            imageUrl = uploadResult.secure_url;
            imagePublicId = uploadResult.public_id;
          } catch (error) {
            console.error("Failed to upload new promotion image:", error);
          }
        } else if (data.image.startsWith('http')) {
          imageUrl = data.image;
          imagePublicId = undefined;
        }
      }

      // Update promotion
      const updateData: any = {
        ...data,
        updatedAt: new Date(),
      };

      if (imageUrl !== undefined) {
        updateData.image = imageUrl;
        updateData.imagePublicId = imagePublicId;
      }

      const promotion = await prisma.promotion.update({
        where: { id },
        data: updateData,
        include: {
          product: true,
          business: true,
          createdBy: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            }
          },
          analytics: true,
        },
      });

      // Invalidate relevant caches
      await invalidatePromotionCaches(existingPromotion.productId, existingPromotion.businessId, id);

      return promotion as unknown as iPromotion;
    } catch (error) {
      console.error("Error updating promotion:", error);
      throw error;
    }
}

export async function deletePromotion(id: string): Promise<void> {
  try {
    // Get promotion details for cache invalidation
    const promotion = await prisma.promotion.findUnique({
      where: { id },
      select: { imagePublicId: true, productId: true, businessId: true }
    });

    if (!promotion) {
      throw new Error("Promotion not found");
    }

      // Delete image from Cloudinary if it exists
      if (promotion.imagePublicId) {
        try {
          await deletePromotionImageFromCloudinary(promotion.imagePublicId);
        } catch (error) {
          console.warn("Failed to delete promotion image from Cloudinary:", error);
        }
      }

      // Delete promotion (analytics will be deleted due to cascade)
      await prisma.promotion.delete({
        where: { id },
      });

      // Invalidate relevant caches
      await invalidatePromotionCaches(promotion.productId, promotion.businessId, id);
    } catch (error) {
      console.error("Error deleting promotion:", error);
      throw error;
    }
}

export async function getPromotionById(id: string): Promise<iPromotion | null> {
  try {
    // Try cache first
    const cacheKey = CACHE_KEYS.PROMOTION_DETAIL(id);
    const cached = await redisService.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

      const promotion = await prisma.promotion.findUnique({
        where: { id },
        include: {
          product: true,
          business: true,
          createdBy: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            }
          },
          analytics: true,
        },
      });

      if (promotion) {
        // Cache the result
        await redisService.set(cacheKey, JSON.stringify(promotion), CACHE_TTL.PROMOTION_DETAIL);
      }

      return promotion as unknown as iPromotion | null;
    } catch (error) {
      console.error("Error getting promotion by ID:", error);
      throw error;
    }
}

// Business Logic
export async function getActivePromotionsForProduct(productId: string): Promise<iPromotion[]> {
  try {
    // Try cache first
    const cacheKey = CACHE_KEYS.ACTIVE_PROMOTIONS(productId);
    const cached = await redisService.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

      const now = new Date();
      const promotions = await prisma.promotion.findMany({
        where: {
          productId,
          isActive: true,
          startDate: { lte: now },
          endDate: { gte: now },
        },
        include: {
          product: true,
          business: true,
          createdBy: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            }
          },
          analytics: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Cache the result
      await redisService.set(cacheKey, JSON.stringify(promotions), CACHE_TTL.ACTIVE_PROMOTIONS);

      return promotions as unknown as iPromotion[];
    } catch (error) {
      console.error("Error getting active promotions for product:", error);
      throw error;
    }
}

export async function getPromotionsForBusiness(businessId: string, includeInactive = true): Promise<iPromotion[]> {
  try {
    // Try cache first (only for active promotions)
    if (!includeInactive) {
      const cacheKey = CACHE_KEYS.BUSINESS_PROMOTIONS(businessId);
      const cached = await redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    }

      const whereClause: any = { businessId };
      if (!includeInactive) {
        whereClause.isActive = true;
      }

      const promotions = await prisma.promotion.findMany({
        where: whereClause,
        include: {
          product: true,
          business: true,
          createdBy: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            }
          },
          analytics: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Cache active promotions only
      if (!includeInactive) {
        const cacheKey = CACHE_KEYS.BUSINESS_PROMOTIONS(businessId);
        await redisService.set(cacheKey, JSON.stringify(promotions), CACHE_TTL.BUSINESS_PROMOTIONS);
      }

      return promotions as unknown as iPromotion[];
    } catch (error) {
      console.error("Error getting promotions for business:", error);
      throw error;
    }
}

export async function validatePromotionDates(startDate: Date, endDate: Date): Promise<boolean> {
  return endDate > startDate;
}

export async function checkPromotionLimits(businessId: string): Promise<boolean> {
  try {
    // Get business subscription status
    const business = await prisma.business.findUnique({
      where: { id: businessId },
      select: { subscriptionStatus: true }
    });

    if (!business) {
      return false;
    }

      // For now, allow unlimited promotions
      // In the future, implement subscription-based limits
      const activePromotionsCount = await prisma.promotion.count({
        where: {
          businessId,
          isActive: true,
        },
      });

      // Example limits based on subscription
      const limits = {
        'FREE': 2,
        'BASIC': 5,
        'PREMIUM': 20,
        'ENTERPRISE': 100,
      };

      const limit = limits[business.subscriptionStatus as keyof typeof limits] || 2;
      return activePromotionsCount < limit;
    } catch (error) {
      console.error("Error checking promotion limits:", error);
      return true; // Allow creation if check fails
    }
}

// Analytics
export async function trackPromotionView(promotionId: string): Promise<void> {
  try {
    const today = new Date().toISOString().split('T')[0];

      // Update promotion view count
      await prisma.promotion.update({
        where: { id: promotionId },
        data: {
          viewCount: { increment: 1 },
        },
      });

      // Update analytics
      const analytics = await prisma.promotionAnalytics.findUnique({
        where: { promotionId },
      });

      if (analytics) {
        const dailyViews = analytics.dailyViews as Record<string, number>;
        dailyViews[today] = (dailyViews[today] || 0) + 1;

        await prisma.promotionAnalytics.update({
          where: { promotionId },
          data: {
            dailyViews,
            lastUpdated: new Date(),
          },
        });
      }

      // Invalidate analytics cache
      const cacheKey = CACHE_KEYS.PROMOTION_ANALYTICS(promotionId);
      await redisService.del(cacheKey);
    } catch (error) {
      console.error("Error tracking promotion view:", error);
      // Don't throw error for analytics tracking failures
    }
}

export async function trackPromotionClick(promotionId: string): Promise<void> {
  try {
    const today = new Date().toISOString().split('T')[0];

      // Update promotion click count
      await prisma.promotion.update({
        where: { id: promotionId },
        data: {
          clickCount: { increment: 1 },
        },
      });

      // Update analytics
      const analytics = await prisma.promotionAnalytics.findUnique({
        where: { promotionId },
      });

      if (analytics) {
        const dailyClicks = analytics.dailyClicks as Record<string, number>;
        dailyClicks[today] = (dailyClicks[today] || 0) + 1;

        // Calculate CTR
        const totalViews = Object.values(analytics.dailyViews as Record<string, number>).reduce((sum, views) => sum + views, 0);
        const totalClicks = Object.values(dailyClicks).reduce((sum, clicks) => sum + clicks, 0);
        const ctr = totalViews > 0 ? (totalClicks / totalViews) * 100 : 0;

        await prisma.promotionAnalytics.update({
          where: { promotionId },
          data: {
            dailyClicks,
            ctr,
            lastUpdated: new Date(),
          },
        });
      }

      // Invalidate analytics cache
      const cacheKey = CACHE_KEYS.PROMOTION_ANALYTICS(promotionId);
      await redisService.del(cacheKey);
    } catch (error) {
      console.error("Error tracking promotion click:", error);
      // Don't throw error for analytics tracking failures
    }
}

export async function trackPromotionConversion(promotionId: string, orderValue?: number): Promise<void> {
  try {
    const today = new Date().toISOString().split('T')[0];

      // Update promotion conversion count
      await prisma.promotion.update({
        where: { id: promotionId },
        data: {
          conversionCount: { increment: 1 },
        },
      });

      // Update analytics
      const analytics = await prisma.promotionAnalytics.findUnique({
        where: { promotionId },
      });

      if (analytics) {
        const dailyConversions = analytics.dailyConversions as Record<string, number>;
        dailyConversions[today] = (dailyConversions[today] || 0) + 1;

        // Calculate conversion rate
        const totalClicks = Object.values(analytics.dailyClicks as Record<string, number>).reduce((sum, clicks) => sum + clicks, 0);
        const totalConversions = Object.values(dailyConversions).reduce((sum, conversions) => sum + conversions, 0);
        const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

        // Update average order value if provided
        let averageOrderValue = analytics.averageOrderValue;
        if (orderValue && averageOrderValue) {
          averageOrderValue = (averageOrderValue + orderValue) / 2;
        } else if (orderValue) {
          averageOrderValue = orderValue;
        }

        await prisma.promotionAnalytics.update({
          where: { promotionId },
          data: {
            dailyConversions,
            conversionRate,
            averageOrderValue,
            lastUpdated: new Date(),
          },
        });
      }

      // Invalidate analytics cache
      const cacheKey = CACHE_KEYS.PROMOTION_ANALYTICS(promotionId);
      await redisService.del(cacheKey);
    } catch (error) {
      console.error("Error tracking promotion conversion:", error);
      // Don't throw error for analytics tracking failures
    }
}

// Cache Management
async function invalidatePromotionCaches(productId: string, businessId: string, promotionId?: string): Promise<void> {
  try {
    const keysToDelete = [
      CACHE_KEYS.ACTIVE_PROMOTIONS(productId),
      CACHE_KEYS.BUSINESS_PROMOTIONS(businessId),
    ];

    if (promotionId) {
      keysToDelete.push(
        CACHE_KEYS.PROMOTION_DETAIL(promotionId),
        CACHE_KEYS.PROMOTION_ANALYTICS(promotionId)
      );
    }

    await Promise.all(keysToDelete.map(key => redisService.del(key)));
  } catch (error) {
    console.warn("Failed to invalidate promotion caches:", error);
    // Don't throw error for cache invalidation failures
  }
}