import {
  iNotification,
  iUser,
  iProductOwnerNotification,
  iUserNotification,
  LikeNotification,
} from "./Interfaces";

// Base URL from env or default to production
const BASE_URL = (process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || 'https://notifications.reviewit.gy').replace(/\/$/, '');

export async function markNotificationAsRead(
  notificationId: string,
  userId: string,
  notificationType: "owner" | "user" | "like",
) {
  if (!BASE_URL) {
    throw new Error("Notification server URL not configured");
  }

  // Follow the backend spec: PUT /notifications/{notification_id}/read?user_id={userId}&type={type}
  const endpoint = `${BASE_URL}/notifications/${notificationId}/read?user_id=${userId}&type=${notificationType}`;

  try {
    const response = await fetch(endpoint, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
    });

    if (!response.ok) {
      // Backend returns plain text errors
      const errorText = await response.text();
      throw new Error(errorText || `HTTP ${response.status}`);
    }

    // Backend returns plain text: "Notification marked as read"
    const text = await response.text();
    return { success: true, message: text };
  } catch (error) {
    console.error("Error marking notification as read:", error);
    throw error;
  }
}

export async function createProductOwnerNotification(review: any) {
  const notificationUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL;
  if (!notificationUrl) {
    console.error("Notification server URL not configured");
    return;
  }

  // Ensure the URL is properly formatted
  const cleanUrl = notificationUrl.endsWith('/') ? notificationUrl.slice(0, -1) : notificationUrl;
  const endpoint = `${BASE_URL}/notifications/product-owner`;

  if (!review?.product) {
    console.error("Invalid review data for notification");
    return;
  }

  const payload: iProductOwnerNotification = {
    id: generateUniqueId(),
    owner_id: review.product?.ownerId || review.product?.business?.ownerId,
    business_id: review.product?.businessId,
    review_title: review.title || "New Review",
    from_name: review.createdBy || "Anonymous",
    from_id: review.userId,
    read: false,
    product_id: review.product.id,
    product_name: review.product.name || "Unknown Product",
    review_id: review.id,
    comment_id: null,
    notification_type: "review",
  };

  fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  })
    .then((response) => {
      if (!response.ok) {
        console.error(
          "Failed to create product owner notification:",
          response.status,
          response.statusText,
        );
      } else {
        // Removed debug console.log statements
      }
    })
    .catch((error) => {
      console.error("Error creating product owner notification:", error);
    });
}

export async function createLikeNotification(notification: LikeNotification) {
  const notificationUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL;
  if (!notificationUrl) {
    console.error("Notification server URL not configured");
    return;
  }

  // Ensure the URL is properly formatted
  const cleanUrl = notificationUrl.endsWith('/') ? notificationUrl.slice(0, -1) : notificationUrl;
  const endpoint = `${BASE_URL}/notifications/like`;

  if (!notification) {
    console.error("Invalid like notification data");
    return;
  }

  fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(notification),
  })
    .then((response) => {
      if (!response.ok) {
        console.error(
          "Failed to create like notification:",
          response.status,
          response.statusText,
        );
      }
    })
    .catch((error) => {
      console.error("Error creating like notification:", error);
    });
}

/**
 * Sends a notification for a comment on a review.
 * Uses the /notifications/comment endpoint which expects parent_user_id to be the review author.
 */
export async function sendCommentNotification(notification: iUserNotification) {
  const notificationUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL;
  if (!notificationUrl) {
    console.error("Notification server URL not configured");
    return;
  }

  // Ensure the URL is properly formatted
  const cleanUrl = notificationUrl.endsWith('/') ? notificationUrl.slice(0, -1) : notificationUrl;
  const endpoint = `${BASE_URL}/notifications/comment`;

  if (!notification) {
    console.error("Invalid notification data");
    return;
  }

  // Ensure notification_type is set correctly
  if (notification.notification_type !== "comment") {
    notification.notification_type = "comment";
  }

  return fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(notification),
  })
    .then((response) => {
      if (!response.ok) {
        console.error(
          "Failed to create comment notification:",
          response.status,
          response.statusText,
        );
        return false;
      } else {
        return true;
      }
    })
    .catch((error) => {
      console.error("Error creating comment notification:", error);
      return false;
    });
}

/**
 * Sends a notification for a reply to a comment.
 * Uses the /notifications/reply endpoint which expects parent_id to be the parent comment ID
 * and parent_user_id to be the comment author.
 */
export async function sendReplyNotification(notification: iUserNotification) {
  const notificationUrl = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER || process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_LOCAL;
  if (!notificationUrl) {
    console.error("Notification server URL not configured");
    return;
  }

  // Ensure the URL is properly formatted
  const cleanUrl = notificationUrl.endsWith('/') ? notificationUrl.slice(0, -1) : notificationUrl;
  const endpoint = `${BASE_URL}/notifications/reply`;

  if (!notification) {
    console.error("Invalid notification data");
    return;
  }
  
  // Ensure notification_type is set correctly
  if (notification.notification_type !== "reply") {
    notification.notification_type = "reply";
  }

  return fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(notification),
  })
    .then((response) => {
      if (!response.ok) {
        console.error(
          "Failed to create reply notification:",
          response.status,
          response.statusText,
        );
        return false;
      } else {
        return true;
      }
    })
    .catch((error) => {
      console.error("Error creating reply notification:", error);
      return false;
    });
}

/**
 * Creates or updates a user in the notification service database.
 * This MUST be called before sending notifications to ensure the user exists.
 * The notification service requires users to exist before creating notifications for them.
 */
export async function createUser(user: iUser): Promise<boolean> {
  const baseUrl = BASE_URL;
  if (!baseUrl) {
    console.error("Notification server URL not configured");
    return false;
  }
  
  // Ensure the URL is properly formatted
  const cleanUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  const notificationUrl = `${BASE_URL}/users`;
  
  // Match the User model in the notification service (models/types.go)
  // type User struct {
  //   ID       string `db:"id" json:"id"`
  //   Username string `db:"username" json:"username"`
  //   FullName string `db:"full_name" json:"full_name"`
  // }
  return fetch(notificationUrl, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      id: user.id,
      username: user.userName || user.id, // Ensure username is never empty
      full_name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User',
    }),
  })
    .then((response) => {
      // Treat 201 Created or 200 OK as success. If the user already exists,
      // backend SHOULD return HTTP 409 (Conflict) or 400 with a clear message.
      if (response.ok || response.status === 409) {
        // 409 means "user already exists" – that's fine for our purposes.
        return true;
      }

      console.error(
        "Failed to create user:",
        response.status,
        response.statusText,
      );
      return false;
    })
    .catch((error) => {
      console.error("Error creating user:", error);
      return false;
    });
}

// Legacy function - deprecated, use createProductOwnerNotification instead
export function createReviewNotification(review: any) {
  console.warn('createReviewNotification is deprecated. Use createProductOwnerNotification instead.');
  return createProductOwnerNotification(review);
}

// ---- Additional Helpers from Integration Guide ----

export async function getAllNotifications(userId: string) {
  const res = await fetch(`${BASE_URL}/notifications?user_id=${userId}`, {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    cache: 'no-store' // Prevent caching of old data
  });
  if (!res.ok) throw new Error('Failed to fetch notifications');
  return await res.json();
}

export async function getUnreadNotifications(userId: string) {
  const res = await fetch(`${BASE_URL}/notifications/unread?user_id=${userId}`, {
    method: 'GET', 
    headers: { 'Content-Type': 'application/json' },
    cache: 'no-store' // Prevent caching of old data
  });
  if (!res.ok) throw new Error('Failed to fetch unread notifications');
  return await res.json();
}

export async function sendNotificationWithRetry(url: string, data: any, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const resp = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (resp.ok) return true;
      if (resp.status === 400) {
        console.error('Bad request:', await resp.text());
        return false;
      }
    } catch (err) {
      console.error(`Attempt ${i + 1} failed:`, err);
      if (i === maxRetries - 1) return false;
      await new Promise(r => setTimeout(r, 1000 * (i + 1)));
    }
  }
  return false;
}

// Mark all as read function - FALLBACK IMPLEMENTATION
// Backend doesn't have bulk endpoint yet, so we'll mark individually
export async function markAllNotificationsAsRead(
  userId: string,
  type?: "owner" | "user" | "like"
) {
  console.warn("Backend mark-all endpoint not implemented yet. Using individual calls as fallback.");
  
  // For now, return a simulated success response
  // TODO: Remove this when backend implements the bulk endpoint
  return { 
    success: true, 
    message: "Mark all functionality not yet available - please mark notifications individually",
    user_notifications_updated: 0,
    owner_notifications_updated: 0,
    like_notifications_updated: 0,
    total_updated: 0
  };
}

// Backward compatibility aliases (deprecated - remove after migration)
export const createUserForNotification = createUser;
export const createUserNotification = sendReplyNotification;

// Helper function to generate a unique ID (you may want to use a more robust method)
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
