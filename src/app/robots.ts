import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy'

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/browse',
          '/reviews/*',
          '/category/*',
          '/search',
          '/reviews?id=*',  // Allow product review pages
          '/category?type=*', // Allow category pages
          '/search?q=*',     // Allow search pages
          '/api/og/*',       // Allow Open Graph API endpoints
          '/api/get/reviews/latest', // Allow latest reviews API
          '/api/get/reviews/approved', // Allow approved reviews API
          '/api/get/product', // Allow product API
          '/api/get/all/products', // Allow all products API
          '/api/words', // Allow words API
        ],
        disallow: [
          '/api/*',
          '/admin/*',
          '/private/*',
          '/*/draft',
          '/*.json$',
          '/cr',
          '/login',
          '/signup',
          '/dashboard',
          '/settings',
          '/user/*',
          '/*.php$',
          '/*.sql$',
          '/tmp/*',
          '/temp/*',
          // Block Next.js internal parameters
          '/*?_rsc=*',
          '/*?*_rsc=*',
        ],
      },
      {
        userAgent: 'GPTBot',
        disallow: ['/'],
      },
      {
        userAgent: 'CCBot',
        disallow: ['/'],
      },
      // Note: Googlebot will follow the default '*' rules unless specifically overridden.
      // Removing crawlDelay and redundant disallows.
      // If specific Googlebot rules are needed later, they can be added here.
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
  }
}
