import QuickTabs from "./components/QuickTabs";
import TopReviews from "./components/TopReviews";
import HeroSection from "./components/HeroSection";
import ReviewCategories from "./components/CompanyCategories";
import ValueProposition from "./components/ValueProposition";
import { Metadata } from "next";
import AdvertCard from "./components/advert-components/AdvertCard";
import TopReviewers from "./components/TopReviewers";
import Link from "next/link";
import { Button } from "@/components/ui/button";

// Override any noindex directives with explicit indexing instructions
export const metadata: Metadata = {
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
    },
  },
};

export default function Home() {
  return (
    <div className="flex h-full w-full flex-col justify-start bg-gradient-to-b from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <div className="relative z-[50]">
        <HeroSection />
      </div>

      {/* Write a Review CTA - Enhanced */}
      <div className="flex justify-center py-8 md:py-12 px-4 bg-white">
        <Link
          href="/write-review"
          className="group relative inline-flex items-center gap-3 bg-gradient-to-r from-myTheme-primary to-myTheme-secondary text-white font-semibold text-lg px-8 py-4 rounded-full hover:shadow-lg transition-all duration-300 hover:scale-105"
        >
          <span className="text-xl">✍️</span>
          <span>Write a Review</span>
          <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        </Link>
      </div>

      {/* Recent Reviews Section - Enhanced spacing */}
      <section className="flex w-full flex-col justify-center relative z-[4] mb-12 md:mb-16">
        <div className="flex w-full h-full flex-row flex-wrap sm:px-4 px-4 md:px-2 pb-4">
          <TopReviews />
        </div>
      </section>

      {/* Quick Categories - Enhanced */}
      <section className="relative z-[1]">
        <QuickTabs />
      </section>

      {/* Additional Sections with improved spacing */}
      <section className="flex w-full flex-col justify-center relative z-[4] py-12 md:py-16">
        <div className="max-w-7xl mx-auto w-full px-4 space-y-16 md:space-y-20">
          {/* Top Reviewers */}
          <div className="mb-8">
            <TopReviewers />
          </div>
          
          {/* Review Categories */}
          <div className="mb-8">
            <ReviewCategories />
          </div>
          
          {/* Value Proposition */}
          <div className="mb-8">
            <ValueProposition />
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary py-16">
        <div className="max-w-4xl mx-auto text-center px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to share your experience?
          </h2>
          <p className="text-xl text-gray-200 mb-8">
            Join thousands of Guyanese helping others make informed decisions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/write-review">
              <Button className="bg-white text-myTheme-primary hover:bg-gray-100 font-semibold px-8 py-3 rounded-lg transition-all duration-200 hover:scale-105">
                Write a Review
              </Button>
            </Link>
            <Link href="/browse">
              <Button variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-myTheme-primary font-semibold px-8 py-3 rounded-lg transition-all duration-200">
                Browse Reviews
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
