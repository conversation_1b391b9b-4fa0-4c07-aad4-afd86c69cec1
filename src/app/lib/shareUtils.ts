import { OGImage } from '../util/Interfaces';
import { generateComponentMetadata } from './componentMetadata';
import { Metadata } from 'next';

export type ShareMetadata = Partial<Metadata>;

export interface ShareOptions {
    title: string;
    description: string;
    url: string;
    imageUrl?: string;
    rating?: number;
    reviewCount?: number;
}

export function generateShareMetadata(options: ShareOptions): ShareMetadata {
    // Optimize text lengths for social sharing
    const optimizedTitle = options.title.substring(0, 70)  // Twitter's max
    const optimizedDescription = options.description.substring(0, 180)  // Under Twitter's 200 limit

    return generateComponentMetadata({
        title: optimizedTitle,
        description: optimizedDescription,
        imageUrl: options.imageUrl ? (options.imageUrl.startsWith('http') ? options.imageUrl : `${process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy'}${options.imageUrl.startsWith('/') ? '' : '/'}${options.imageUrl}`) : undefined,
        url: options.url,
        type: 'article',
        rating: options.rating,
        reviewCount: options.reviewCount,
    });
}

export function getShareImageUrl(metadata: ShareMetadata): string | undefined {
    return Array.isArray(metadata.openGraph?.images)
        ? (metadata.openGraph?.images[0] as OGImage)?.url
        : (metadata.openGraph?.images as OGImage)?.url;
} 