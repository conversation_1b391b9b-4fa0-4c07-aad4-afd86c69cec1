import { Metadata } from 'next';
import { baseUrl } from '@/app/util/serverFunctions';

interface ComponentMetadataProps {
    title: string;
    description: string;
    imageUrl?: string;
    url: string;
    type?: 'website' | 'article';
    rating?: number;
    reviewCount?: number;
}

export function generateComponentMetadata({
    title,
    description = "Review It - Your Voice, Your Choice. A website where you can share and read reviews on anything.",
    imageUrl,
    url,
    type = 'website',
    rating,
    reviewCount,
}: ComponentMetadataProps): Partial<Metadata> {

    const defaultImage = `${baseUrl}/logo.png`;

    // Ensure image URL is absolute and valid
    const absoluteImageUrl = imageUrl
        ? imageUrl.startsWith('http')
            ? imageUrl
            : `${baseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`
        : defaultImage;

    // Generate title with rating if available (max 70 chars for Twitter)
    const fullTitle = rating && reviewCount
        ? `${title} | ${rating}/5 (${reviewCount} reviews)`.substring(0, 70)
        : title.substring(0, 70);

    // Truncate description for Twitter (max 180 chars to leave room for rating)
    const twitterDescription = description.length > 180
        ? `${description.substring(0, 177)}...`
        : description;

    return {
        title: fullTitle,
        description: twitterDescription,
        openGraph: {
            title: fullTitle,
            description: twitterDescription,
            url: url.startsWith('http') ? url : `${baseUrl}${url}`,
            type,
            siteName: 'Review It',
            locale: 'en_US',
            images: [{
                url: absoluteImageUrl,
                width: 1200,
                height: 630,
                alt: title.substring(0, 70)  // Keep alt text consistent with title length
            }]
        },
        twitter: {
            card: 'summary_large_image',
            title: fullTitle,
            description: twitterDescription,
            images: [absoluteImageUrl],
            site: '@reviewitgy',
            creator: '@reviewitgy'
        },
    };
} 
