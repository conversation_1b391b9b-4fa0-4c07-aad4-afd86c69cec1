import { Metadata } from 'next'
import { baseUrl } from '@/app/util/serverFunctions';

interface MetadataImage {
  url: string;
  width?: number;
  height?: number;
  alt?: string;
  type?: string;
}

interface GenerateMetadataProps {
  title: string
  description?: string
  images?: (string | MetadataImage)[]
  type?: 'website' | 'article'
  noIndex?: boolean
  path?: string
  category?: string
}

export function generateMetadata({
  title,
  description = "ReviewIt.gy - Your trusted source for reviews in Guyana. Find and share authentic reviews on businesses, products, and services. Also known as Review It Guyana.",
  images,
  type = 'website',
  noIndex = false,
  path = '',
  category,
}: GenerateMetadataProps): Metadata {


  // If it's a product page (determined by category presence), use 'article' type
  const ogType = category ? 'article' : type;

  // Generate category-specific descriptions
  const getDescription = () => {
    if (description) return description
    if (category) {
      return `Find and review the best ${category.toLowerCase()} in Guyana. Read real customer reviews, ratings, and experiences about ${title}.`
    }
    return 'Review It is a website where you can share and read reviews on anything in Guyana. Find trusted reviews, ratings, and experiences from real customers.'
  }

  // Add this function to handle different image aspect ratios
  function getImageDimensions(imageUrl?: string): { width: number, height: number } {
    // Default dimensions for OG images
    const defaultDimensions = { width: 800, height: 420 };
    
    // If no image URL, return default dimensions
    if (!imageUrl) return defaultDimensions;
    
    // For images that might be square (like profile pictures)
    if (imageUrl.includes('profile') || imageUrl.includes('avatar')) {
      return { width: 600, height: 600 };
    }
    
    // For other images, use default dimensions
    return defaultDimensions;
  }

  // Process images to ensure they have proper metadata
  const processImages = (images: (string | MetadataImage)[]) => {
    return images.map(img => {
      const imgUrl = typeof img === 'string' ? img : img.url;
      const dimensions = getImageDimensions(imgUrl);
      
      return {
        url: imgUrl,
        width: dimensions.width,
        height: dimensions.height,
        alt: typeof img === 'string' ? title : (img.alt || title),
        type: 'image/jpeg',
      };
    });
  }

  // For regular images, use the product image as provided
  // For OG images, use our generator to make them social-media friendly
  const getOgImages = () => {
    if (images && images.length > 0) {
      return processImages(images);
    }

    // Default OG image with dynamic title
    return [{
      url: `${baseUrl}/api/og?title=${encodeURIComponent(title)}&description=${encodeURIComponent(getDescription())}&forceGenerate=true`,
      width: 1200,
      height: 630,
      alt: title,
      type: 'image/png',
    }];
  }

  // Get processed images for different contexts
  const processedImages = getOgImages();

  // Add keywords for category pages
  const getKeywords = () => {
    const baseKeywords = [
      "reviewit", 
      "review it", 
      "reviewit guyana", 
      "reviewitgy", 
      "reviewit.gy",
      "review it guyana",
      "reviewitguyana",
      "review it gy",
      "reviews in guyana",
      "guyana reviews",
      "guyana review platform"
    ];
    if (category) {
      return [...baseKeywords, `${category} reviews guyana`, `${category} in guyana`, `best ${category} guyana`];
    }
    return baseKeywords;
  };

  return {
    title,
    description: getDescription(),
    keywords: getKeywords().join(", "),
    openGraph: {
      title,
      description: getDescription(),
      url: `${baseUrl}${path}`,
      siteName: 'Review It',
      type: ogType,
      images: processedImages,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description: getDescription(),
      images: processedImages.map(img => ({
        url: img.url,
        alt: img.alt,
      })),
    },
    alternates: {
      canonical: `${baseUrl}${path}`,
    },
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  }
}
