interface ShareData {
    title: string;
    text: string;
    url: string;
    imageUrl?: string;
}

interface SocialShareOptions {
    platform: 'twitter' | 'facebook' | 'whatsapp' | 'linkedin' | 'reddit' | 'email' | 'copy';
    data: ShareData;
}

export function getSocialShareUrl(options: SocialShareOptions): string {
    const { platform, data } = options;
    const { title, text, url, imageUrl } = data;
    const encodedUrl = encodeURIComponent(url);
    const encodedTitle = encodeURIComponent(title);
    const encodedText = encodeURIComponent(text);
    const encodedImageUrl = imageUrl ? encodeURIComponent(imageUrl) : '';

    // Removed debug console.log statements

    let shareUrl: string;
    switch (platform) {
        case 'twitter':
            // Twitter doesn't support direct image URLs in the share URL
            shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
            break;
        case 'facebook':
            // Facebook uses OpenGraph metadata for images
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
            break;
        case 'whatsapp':
            // WhatsApp doesn't support direct image URLs in the share URL
            shareUrl = `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
            break;
        case 'linkedin':
            // LinkedIn uses OpenGraph metadata for images
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedText}`;
            break;
        case 'reddit':
            // Reddit doesn't support direct image URLs in the share URL
            shareUrl = `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`;
            break;
        case 'email':
            // Email doesn't support direct image URLs in the share URL
            shareUrl = `mailto:?subject=${encodedTitle}&body=${encodedText}%20${encodedUrl}`;
            break;
        case 'copy':
            // Copy link doesn't need image URL
            shareUrl = '#';
            break;
        default:
            shareUrl = '#';
    }

    return shareUrl;
}

export const socialPlatforms = [
    { id: 'twitter', name: 'Twitter' },
    { id: 'facebook', name: 'Facebook' },
    { id: 'whatsapp', name: 'WhatsApp' },
    { id: 'linkedin', name: 'LinkedIn' },
    { id: 'reddit', name: 'Reddit' },
    { id: 'email', name: 'Email' },
    { id: 'copy', name: 'Copy Link' },
] as const; 
