"use client";

import { useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import { iBugReport } from "@/app/util/Interfaces";
import { toast } from "sonner";
import Link from "next/link";
import BugReportDetailsModal from "../components/bug-reports/BugReportDetailsModal";

const STATUS_COLORS = {
    OPEN: "bg-yellow-100 text-yellow-800",
    IN_PROGRESS: "bg-blue-100 text-blue-800",
    RESOLVED: "bg-green-100 text-green-800",
    CLOSED: "bg-gray-100 text-gray-800",
    WONT_FIX: "bg-red-100 text-red-800",
} as const;

type BugReportType = 'all' | 'active' | 'my-reports';
type BugStatus = 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED' | 'WONT_FIX';
type BugStatusFilter = BugStatus | 'all';

interface BugReportsResponse {
    success: boolean;
    data: {
        activeBugs: iBugReport[];
        userBugs: iBugReport[];
    };
}

const ITEMS_PER_PAGE = 10;

export default function BugReportsPage() {
    const { isSignedIn } = useUser();
    const [activeBugs, setActiveBugs] = useState<iBugReport[]>([]);
    const [userBugs, setUserBugs] = useState<iBugReport[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [reportType, setReportType] = useState<BugReportType>('my-reports');
    const [statusFilter, setStatusFilter] = useState<BugStatusFilter>('all');
    const [selectedBug, setSelectedBug] = useState<iBugReport | null>(null);
    const [isModalLoading, setIsModalLoading] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);

    useEffect(() => {
        const fetchBugReports = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const response = await fetch("/api/user/bugreports");
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || "Failed to fetch bug reports");
                }
                const data = await response.json() as BugReportsResponse;
                setActiveBugs(data.data.activeBugs);
                setUserBugs(data.data.userBugs);
            } catch (error) {
                console.error("Error fetching bug reports:", error);
                setError(error instanceof Error ? error.message : "Failed to load bug reports");
                toast.error("Failed to load bug reports");
            } finally {
                setIsLoading(false);
            }
        };

        if (isSignedIn) {
            fetchBugReports();
        }
    }, [isSignedIn]);

    const getFilteredBugs = (): iBugReport[] => {
        let bugs: iBugReport[] = [];

        // First, select which set of bugs to show
        if (reportType === 'active') {
            bugs = activeBugs;
        } else if (reportType === 'my-reports') {
            bugs = userBugs;
        } else {
            // For 'all', combine active bugs and user's bugs
            const allBugs = [...activeBugs, ...userBugs];
            bugs = Array.from(new Map(allBugs.map(bug => [bug.id, bug])).values());
        }

        // Then apply status filter if not 'all'
        if (statusFilter !== 'all') {
            bugs = bugs.filter(bug => bug.status === statusFilter);
        }

        return bugs;
    };

    const handleRowClick = async (bug: iBugReport): Promise<void> => {
        setIsModalLoading(true);
        setSelectedBug(bug);
        // Simulate loading state for better UX
        await new Promise(resolve => setTimeout(resolve, 300));
        setIsModalLoading(false);
    };

    // Reset to first page when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [reportType, statusFilter]);

    const filteredBugs = getFilteredBugs();
    const totalPages = Math.ceil(filteredBugs.length / ITEMS_PER_PAGE);
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const paginatedBugs = filteredBugs.slice(startIndex, startIndex + ITEMS_PER_PAGE);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        // Scroll to top of the table
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    if (!isSignedIn) {
        return (
            <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-3xl mx-auto">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold text-gray-900">Bug Reports</h1>
                        <p className="mt-4 text-lg text-gray-600">
                            Please sign in to view bug reports
                        </p>
                        <div className="mt-6">
                            <Link
                                href="https://accounts.reviewit.gy/sign-in"
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Sign In
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-3xl mx-auto">
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-3xl mx-auto">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold text-gray-900">Bug Reports</h1>
                        <div className="mt-4 text-red-600">{error}</div>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-3xl mx-auto">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-3xl font-bold text-gray-900">Bug Reports</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            View and track bug reports across the platform
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/bug-reports/new"
                            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Report a Bug
                        </Link>
                    </div>
                </div>

                {/* Filters */}
                <div className="mt-6 flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                        <label htmlFor="report-type" className="block text-sm font-medium text-gray-700">
                            Report Type
                        </label>
                        <select
                            id="report-type"
                            value={reportType}
                            onChange={(e) => setReportType(e.target.value as BugReportType)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            title="Choose which bug reports to display"
                        >
                            <option value="my-reports">My Bug Reports</option>
                            <option value="all">All Active & My Bugs</option>
                            <option value="active">All Active Bugs</option>
                        </select>
                    </div>
                    <div className="flex-1">
                        <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">
                            Status
                        </label>
                        <select
                            id="status-filter"
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value as BugStatusFilter)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                        >
                            <option value="all">All Statuses</option>
                            <option value="OPEN">Open</option>
                            <option value="IN_PROGRESS">In Progress</option>
                            <option value="RESOLVED">Resolved</option>
                            <option value="CLOSED">Closed</option>
                            <option value="WONT_FIX">Will Not Fix</option>
                        </select>
                    </div>
                </div>

                <div className="mt-8 flex flex-col">
                    <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                {filteredBugs.length === 0 ? (
                                    <div className="text-center py-12">
                                        <p className="text-gray-500">No bug reports found matching your filters.</p>
                                        <Link
                                            href="/bug-reports/new"
                                            className="mt-4 inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500"
                                        >
                                            Report a new bug
                                            <span aria-hidden="true"> &rarr;</span>
                                        </Link>
                                    </div>
                                ) : (
                                    <>
                                        <table className="min-w-full divide-y divide-gray-300">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                                        Title
                                                    </th>
                                                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Status
                                                    </th>
                                                    <th scope="col" className="hidden sm:table-cell px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Reporter
                                                    </th>
                                                    <th scope="col" className="hidden sm:table-cell px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Created
                                                    </th>
                                                    <th scope="col" className="hidden md:table-cell px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Resolution
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="divide-y divide-gray-200 bg-white">
                                                {paginatedBugs.map((report) => (
                                                    <tr
                                                        key={report.id}
                                                        onClick={() => handleRowClick(report)}
                                                        className="cursor-pointer hover:bg-gray-50"
                                                    >
                                                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                            <div className="flex flex-col">
                                                                <span>{report.title}</span>
                                                                <span className="sm:hidden text-xs text-gray-500 mt-1">
                                                                    {report.reporter?.userName || 'Unknown'} • {new Date(report.created_at).toLocaleDateString()}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td className="whitespace-nowrap px-3 py-4 text-sm">
                                                            <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${STATUS_COLORS[report.status as keyof typeof STATUS_COLORS]}`}>
                                                                {report.status}
                                                            </span>
                                                        </td>
                                                        <td className="hidden sm:table-cell whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            {report.reporter?.userName || 'Unknown'}
                                                        </td>
                                                        <td className="hidden sm:table-cell whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            {new Date(report.created_at).toLocaleDateString()}
                                                        </td>
                                                        <td className="hidden md:table-cell px-3 py-4 text-sm text-gray-500">
                                                            {report.resolution_notes || "No resolution notes yet"}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>

                                        {/* Pagination */}
                                        {totalPages > 1 && (
                                            <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                                                <div className="flex flex-1 justify-between sm:hidden">
                                                    <button
                                                        onClick={() => handlePageChange(currentPage - 1)}
                                                        disabled={currentPage === 1}
                                                        className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    >
                                                        Previous
                                                    </button>
                                                    <button
                                                        onClick={() => handlePageChange(currentPage + 1)}
                                                        disabled={currentPage === totalPages}
                                                        className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    >
                                                        Next
                                                    </button>
                                                </div>
                                                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                                    <div>
                                                        <p className="text-sm text-gray-700">
                                                            Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                                                            <span className="font-medium">
                                                                {Math.min(startIndex + ITEMS_PER_PAGE, filteredBugs.length)}
                                                            </span>{' '}
                                                            of <span className="font-medium">{filteredBugs.length}</span> results
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                                            <button
                                                                onClick={() => handlePageChange(currentPage - 1)}
                                                                disabled={currentPage === 1}
                                                                className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                                                            >
                                                                <span className="sr-only">Previous</span>
                                                                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                    <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                                                                </svg>
                                                            </button>
                                                            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                                                <button
                                                                    key={page}
                                                                    onClick={() => handlePageChange(page)}
                                                                    className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${page === currentPage
                                                                        ? 'z-10 bg-indigo-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                                                                        : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                                                                        }`}
                                                                >
                                                                    {page}
                                                                </button>
                                                            ))}
                                                            <button
                                                                onClick={() => handlePageChange(currentPage + 1)}
                                                                disabled={currentPage === totalPages}
                                                                className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                                                            >
                                                                <span className="sr-only">Next</span>
                                                                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                    <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                                                                </svg>
                                                            </button>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <BugReportDetailsModal
                isOpen={!!selectedBug}
                onClose={() => setSelectedBug(null)}
                bugReport={selectedBug}
                isLoading={isModalLoading}
            />
        </div>
    );
} 