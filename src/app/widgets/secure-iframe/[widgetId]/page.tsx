"use client";

import { useEffect, useState, useCallback } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { WidgetRenderer } from '@/app/components/widgets/WidgetRenderer';

interface SecureWidgetData {
  id: string;
  businessId: string;
  productId: string | null;
  name: string;
  type: string;
  config: any;
  isActive: boolean;
  securityLevel: string;
  apiKey: string | null;
  tokenExpiry: number;
  maxRequestsPerHour: number;
  theme: string;
  primaryColor: string | null;
  borderRadius: string;
  showLogo: boolean;
  showPoweredBy: boolean;
  maxReviews: number;
  showRating: boolean;
  showReviewText: boolean;
  showReviewDate: boolean;
  showReviewerName: boolean;
  allowedDomains: string[];
  viewCount: number;
  clickCount: number;
  conversionCount: number;
  blockedCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastUsed: Date | null;
  business: {
    id: string;
    ownerName: string;
    isVerified: boolean;
  };
  product: {
    id: string;
    name: string;
    display_image: string;
    rating: number;
    rating1Star: number;
    rating2Stars: number;
    rating3Stars: number;
    rating4Stars: number;
    rating5Stars: number;
    reviews: {
      id: string;
      title: string;
      body: string;
      rating: number;
      createdDate: Date;
      user: {
        firstName: string;
        lastName: string;
        avatar: string | null;
      };
    }[];
  } | null;
  reviews: {
    id: string;
    title: string;
    body: string;
    rating: number;
    createdDate: Date;
    user: {
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  }[];
}

export default function SecureWidgetIframe() {
  const params = useParams();
  const searchParams = useSearchParams();
  const widgetId = params.widgetId as string;
  const token = searchParams.get('token');
  const domain = searchParams.get('domain');
  
  const [widgetData, setWidgetData] = useState<SecureWidgetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const sendResizeMessage = useCallback(() => {
    if (typeof window !== 'undefined' && window.parent) {
      const height = document.body.scrollHeight;
      window.parent.postMessage({
        type: 'secure-widget-resize',
        widgetId: widgetId,
        token: token,
        height: height
      }, '*');
    }
  }, [widgetId, token]);

  const loadSecureWidget = useCallback(async () => {
    try {
      const response = await fetch(`/api/public/widgets/secure/${widgetId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Domain': domain!,
          'X-Referrer': document.referrer || window.location.href,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to load widget data');
      }

      setWidgetData(data.data);
      
      // Send resize message to parent
      setTimeout(() => {
        sendResizeMessage();
      }, 100);
      
    } catch (err: any) {
      console.error('Failed to load secure widget:', err);
      setError(err.message || 'Failed to load widget');
    } finally {
      setLoading(false);
    }
  }, [widgetId, token, domain, sendResizeMessage]);

  useEffect(() => {
    if (!widgetId || !token || !domain) {
      setError('Missing required parameters');
      setLoading(false);
      return;
    }

    loadSecureWidget();
  }, [widgetId, token, domain, loadSecureWidget]);

  // Send resize messages when content changes
  useEffect(() => {
    if (widgetData && !loading) {
      const resizeObserver = new ResizeObserver(() => {
        sendResizeMessage();
      });
      
      resizeObserver.observe(document.body);
      
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [widgetData, loading, sendResizeMessage]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[100px] p-4">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-sm text-gray-600">🔒 Loading secure widget...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center space-x-2 text-red-700">
          <span className="text-lg">🔒</span>
          <div>
            <div className="font-semibold">Secure Widget Error</div>
            <div className="text-sm">{error}</div>
            <div className="text-xs mt-1 opacity-75">
              Domain: {domain} | Widget: {widgetId}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!widgetData) {
    return (
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="text-center text-gray-600">
          <span className="text-lg">🔒</span>
          <div className="text-sm">No widget data available</div>
        </div>
      </div>
    );
  }

  return (
    <div className="secure-widget-container">
      {/* Security indicator */}
      <div className="flex items-center justify-between mb-2 text-xs text-green-700 bg-green-50 px-2 py-1 rounded">
        <span className="flex items-center space-x-1">
          <span>🔒</span>
          <span>Verified Secure Widget</span>
        </span>
        <span className="opacity-75">{domain}</span>
      </div>
      
      {/* Widget content */}
      <WidgetRenderer 
        widget={widgetData} 
        isSecure={true}
        domain={domain}
      />
      
      {/* Powered by (if enabled) */}
      {widgetData.showPoweredBy && (
        <div className="mt-2 text-center">
          <a 
            href="https://reviewit.gy" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-xs text-gray-500 hover:text-gray-700 flex items-center justify-center space-x-1"
          >
            <span>🔒</span>
            <span>Powered by ReviewIt</span>
          </a>
        </div>
      )}
      
      <style jsx>{`
        .secure-widget-container {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          max-width: 100%;
          overflow: hidden;
        }
        
        /* Prevent clickjacking */
        body {
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }
        
        /* Security styles */
        .secure-widget-container * {
          box-sizing: border-box;
        }
      `}</style>
    </div>
  );
}