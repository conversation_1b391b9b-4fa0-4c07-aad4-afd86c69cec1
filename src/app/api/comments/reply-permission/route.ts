import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { iComment, iReview } from "@/app/util/Interfaces";
import { canReplyToComment } from "@/app/util/commentPermissions";
import { getReview } from "@/app/util/serverFunctions";

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { commentId } = body;

    if (!commentId) {
      return NextResponse.json(
        { success: false, message: "Missing commentId" },
        { status: 400 }
      );
    }

    const comment = await prisma.comment.findUnique({
      where: { id: commentId },
      include: {
        review: {
          include: {
            user: true,
            product: {
              include: {
                business: {
                  include: {
                    owner: true
                  }
                }
              }
            }
          }
        }
      }
    }) as iComment;

    if (!comment) {
      return NextResponse.json(
        { success: false, message: "Comment not found" },
        { status: 404 }
      );
    }

    const reviewResponse = await getReview(comment.reviewId);
    if (!reviewResponse.success || !reviewResponse.data) {
      return NextResponse.json(
        { success: false, message: "Review not found" },
        { status: 404 }
      );
    }
    const review = reviewResponse.data;

    // Convert isDeleted to boolean if needed
    const commentForPermission: iComment = {
      ...comment,
      isDeleted: comment.isDeleted ?? false
    };

    const permission = canReplyToComment(userId, commentForPermission, review, review.product);

    return NextResponse.json({ success: true, permission });
  } catch (error) {
    console.error("Error checking reply permission:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
