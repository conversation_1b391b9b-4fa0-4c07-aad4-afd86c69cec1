// Importing necessary modules and packages
import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { clerkClient, auth } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { iComment, UserDATA, iUserNotification } from "@/app/util/Interfaces";
import { sendCommentNotification, sendReplyNotification, createUser } from "@/app/util/NotificationFunctions";
import { invalidateCommentCache } from "@/app/util/analytics/cache";
import { maybeAssertTier, Tier } from "@/app/util/tier";
import { canReplyToComment } from "@/app/util/commentPermissions";
import { getReview } from "@/app/util/serverFunctions";

// Exporting the POST function that handles the API request
export async function POST(request: NextRequest) {
  try {
    const comment = await request.json();
    const { userId: userIdFromClerk } = await auth();
    const clerkUserData = await (
      await clerkClient()
    ).users.getUser(userIdFromClerk as string);

    // If this is a reply (has parentId), check permissions
    if (comment.parentId) {
      // Fetch the parent comment
      const parentComment = await prisma.comment.findUnique({
        where: { id: comment.parentId },
        include: {
          review: {
            include: {
              user: true,
              product: {
                include: {
                  business: {
                    include: {
                      owner: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!parentComment) {
        return NextResponse.json(
          { success: false, message: "Parent comment not found" },
          { status: 404 }
        );
      }

      // Fetch the full review
      const reviewResponse = await getReview(parentComment.reviewId);
      if (!reviewResponse.success || !reviewResponse.data) {
        return NextResponse.json(
          { success: false, message: "Review not found" },
          { status: 404 }
        );
      }
      const review = reviewResponse.data;

      // Check permissions
      const parentCommentForPermission: iComment = {
        ...parentComment,
        isDeleted: parentComment.isDeleted ?? false
      };
      const permission = canReplyToComment(userIdFromClerk as string, parentCommentForPermission, review, review.product);
      if (!permission.allowed) {
        return NextResponse.json(
          { success: false, message: permission.message || "Not allowed to reply" },
          { status: 403 }
        );
      }
    }

    const createdComment = await prisma.comment.create({
      data: {
        body: comment.body,
        createdDate: new Date(),
        reviewId: comment.reviewId,
        userId: userIdFromClerk as string,
        parentId: comment.parentId,
      },
      include: {
        review: {
          include: {
            user: {
              select: { id: true, userName: true, firstName: true, lastName: true },
            },
            product: {
              include: {
                business: {
                  select: {
                    id: true,
                    ownerId: true,
                    tier: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // If the commenter is the business owner, ensure Pro tier before allowing response
    if (createdComment.review.product?.business?.ownerId === userIdFromClerk) {
      const bizTier = (createdComment.review.product?.business?.tier || 'starter') as Tier;
      maybeAssertTier(bizTier, 'pro', 'reply_as_business');
    }

    if (
      createdComment.review.product?.business?.ownerId === userIdFromClerk &&
      !createdComment.review.ownerRespondedAt
    ) {
      await prisma.review.update({
        where: {
          id: createdComment.reviewId,
        },
        data: {
          ownerRespondedAt: new Date(),
        },
      });
    }

    // Create notification for the review owner
    const userNotification: iUserNotification = {
      id: createdComment.id,
      user_id: createdComment.review.userId, // Add the review owner's ID
      content: createdComment.body,
      read: false,
      notification_type: "comment",
      comment_id: createdComment.id,
      review_id: createdComment.reviewId,
      from_name: clerkUserData?.firstName || "Anonymous",
      from_id: userIdFromClerk as string,
      created_at: new Date(),
      parent_id: createdComment.reviewId, // For comments on reviews, parent_id should be the review ID
      parent_user_id: createdComment.review.user?.id || createdComment.review.userId, // User who should receive the notification
      product_id: createdComment.review.productId,
    };
    
    console.log("Notification payload - parent_id (review ID):", userNotification.parent_id);
    console.log("Notification payload - parent_user_id (user to notify):", userNotification.parent_user_id);

    // 1. Ensure the commenter (from_id) exists in notification service
    // Get or create user in our database first
    const userInDatabase = await userInDb(userIdFromClerk as string);
    if (!userInDatabase) {
      // Convert Clerk user to UserDATA format
      const userData: UserDATA = {
        userId: userIdFromClerk as string,
        userName: clerkUserData.username || '',
        firstName: clerkUserData.firstName || '',
        lastName: clerkUserData.lastName || '',
        fullName: `${clerkUserData.firstName || ''} ${clerkUserData.lastName || ''}`.trim(),
        email: clerkUserData.emailAddresses?.[0]?.emailAddress || '',
        avatar: clerkUserData.imageUrl || '',
        // Required fields for UserDATA interface
        azp: '',
        exp: 0,
        iat: 0,
        iss: '',
        jti: '',
        nbf: 0,
        sub: userIdFromClerk as string
      };
      
      await addUserToDb(userData);
    }
    
    // Now ensure the commenter exists in notification service
    console.log("Creating commenter in notification service:", userIdFromClerk);
    const commenterCreated = await createUser({
      id: userIdFromClerk as string,
      userName: clerkUserData.username || '',
      firstName: clerkUserData.firstName || '',
      lastName: clerkUserData.lastName || '',
      avatar: '',
      createdDate: new Date(),
      email: '',
      clerkUserId: userIdFromClerk as string,
      isDeleted: false,
      role: 'USER',
    } as any);
    
    console.log("Commenter creation result:", commenterCreated);
    
    // 2. Ensure parent user (review author) exists in notification service
    let parentUserCreated = false;
    if (createdComment.review.user) {
      const parentUserId = createdComment.review.user.id;
      console.log("Creating parent user in notification service:", parentUserId);
      
      // Make sure we're using the correct ID format for the parent user
      // This should match one of the IDs in the notification service DB
      parentUserCreated = await createUser({
        id: parentUserId,
        userName: createdComment.review.user.userName || 'user_' + parentUserId.substring(0, 8),
        firstName: createdComment.review.user.firstName || '',
        lastName: createdComment.review.user.lastName || '',
        avatar: '',
        createdDate: new Date(),
        email: '',
        clerkUserId: '',
        isDeleted: false,
        role: 'USER',
      } as any);
      
      console.log("Parent user creation result:", parentUserCreated);
      
      // Double-check the parent user ID in the notification payload
      if (userNotification.parent_user_id !== parentUserId) {
        console.log(`Fixing parent_user_id mismatch: ${userNotification.parent_user_id} -> ${parentUserId}`);
        userNotification.parent_user_id = parentUserId;
      }
    } else {
      console.error("Cannot create notification: Review user is missing");
      throw new Error("Review user is missing, cannot create notification");
    }
    
    console.log("comment notification payload", userNotification);
    
    // Only send the notification if both users were created successfully
    if (commenterCreated && parentUserCreated) {
      // Use the new comment notification endpoint for direct comments on reviews
      // This is more appropriate than the reply endpoint for this case
      const notificationSent = await sendCommentNotification(userNotification);
      console.log("Comment notification sent:", notificationSent);
    } else {
      console.error("Skipping notification: User creation failed", { commenterCreated, parentUserCreated });
    }

    // Only invalidate comment cache, not the entire review cache
    await invalidateCommentCache(createdComment.reviewId);

    return NextResponse.json({
      success: true,
      status: 200,
      data: createdComment,
    });
  } catch (error: unknown) {
    const err = error as (Error & { status?: number; featureKey?: string });
    console.error("[CreateComment] Error:", err);
    const statusCode = err.status && typeof err.status === 'number' ? err.status : 500;
    return NextResponse.json(
      {
        success: false,
        message: err.message || 'Unexpected server error',
        featureKey: err.featureKey,
      },
      { status: statusCode },
    );
  }
}
