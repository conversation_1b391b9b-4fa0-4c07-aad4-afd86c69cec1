// Example using Vercel Cron Jobs
import { calculateBusinessGrowthMetrics } from '@/app/lib/analytics-engine';
import { prisma } from '@/app/util/prismaClient';
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

function isLocalhost(request: NextRequest): boolean {
  // Get the IP address from various headers (for different deployment scenarios)
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for') || 
                     request.headers.get('cf-connecting-ip') ||
                     request.ip;
  
  // Extract the first IP if there are multiple (comma-separated)
  const clientIp = forwarded?.split(',')[0]?.trim() || 
                   realIp || 
                   remoteAddr || 
                   'unknown';

  // Check for localhost variations
  const localhostPatterns = [
    '127.0.0.1',
    '::1',
    'localhost',
    '::ffff:127.0.0.1', // IPv4-mapped IPv6 localhost
  ];

  // Also check the host header for localhost
  const host = request.headers.get('host');
  const isLocalhostHost = host?.includes('localhost') || host?.includes('127.0.0.1');

  return localhostPatterns.includes(clientIp) || isLocalhostHost || false;
}

export async function GET(request: NextRequest) {
  // Security check: Only allow requests from localhost
  if (!isLocalhost(request)) {
    console.warn(`Unauthorized cron access attempt from IP: ${request.headers.get('x-forwarded-for') || request.ip || 'unknown'}`);
    return NextResponse.json(
      { error: 'Forbidden: This endpoint only accepts requests from localhost' },
      { status: 403 }
    );
  }

  try {
    const businesses = await prisma.business.findMany({ select: { id: true } });
    
    const results = {
      total: businesses.length,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };
    
    for (const business of businesses) {
      try {
        await calculateBusinessGrowthMetrics(business.id);
        results.successful++;
      } catch (error) {
        results.failed++;
        const errorMessage = `Business ${business.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        results.errors.push(errorMessage);
        console.error(`Failed to calculate analytics for business ${business.id}:`, error);
        // Continue with other businesses instead of stopping
      }
    }
    
    // Return success if at least some businesses were processed successfully
    const isSuccess = results.successful > 0 || results.total === 0;
    
    return NextResponse.json({ 
      ok: isSuccess,
      message: `Analytics calculation completed. ${results.successful}/${results.total} businesses processed successfully`,
      results,
      timestamp: new Date().toISOString()
    }, { 
      status: isSuccess ? 200 : 207 // 207 = Multi-Status for partial success
    });
  } catch (error) {
    console.error('Critical error in analytics calculation:', error);
    return NextResponse.json(
      { 
        ok: false,
        error: 'Critical error during analytics calculation',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
