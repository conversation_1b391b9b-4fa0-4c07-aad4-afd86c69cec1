import { NextResponse } from 'next/server';
import { uploadClaimImageToCloudinary } from '@/app/util/uploadImageToCloudinary';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: Request) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json({
                success: false,
                status: 401,
                message: 'Unauthorized: Please sign in to upload images',
                errorCode: 'UNAUTHORIZED',
            });
        }

        const formData = await request.formData();
        const file = formData.get('file');

        if (!file || !(file instanceof File)) {
            return NextResponse.json({
                success: false,
                status: 400,
                message: 'No file provided or invalid file format',
                errorCode: 'INVALID_FILE',
            });
        }

        // Validate file type
        const fileType = file.type;
        if (!fileType.startsWith('image/')) {
            return NextResponse.json({
                success: false,
                status: 400,
                message: 'Only image files are accepted',
                errorCode: 'INVALID_FILE_TYPE',
            });
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            return NextResponse.json({
                success: false,
                status: 400,
                message: 'File is too large (max 5MB)',
                errorCode: 'FILE_TOO_LARGE',
            });
        }

        const buffer = await file.arrayBuffer();
        const base64 = Buffer.from(buffer).toString('base64');
        const dataURI = `data:${file.type};base64,${base64}`;

        const result = await uploadClaimImageToCloudinary(dataURI);

        return NextResponse.json({
            success: true,
            status: 200,
            message: 'Image uploaded successfully',
            imageUrl: result.secure_url,
            publicId: result.public_id,
        });
    } catch (error) {
        console.error('Error uploading claim image:', error);
        const e = error as Error;
        return NextResponse.json({
            success: false,
            status: 500,
            message: e.message || 'An unexpected error occurred while uploading the image',
            errorCode: 'UPLOAD_ERROR',
        });
    }
} 