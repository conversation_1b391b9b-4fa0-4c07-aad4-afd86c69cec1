import { NextResponse } from 'next/server';

const WORD_SERVER_URL = process.env.WORD_SERVER_URL;

if (!WORD_SERVER_URL) {
  throw new Error('WORD_SERVER_URL environment variable is not set');
}

export async function DELETE(
  request: Request,
  { params }: { params: { word: string } }
) {
  try {
    const response = await fetch(`${WORD_SERVER_URL}/word/${params.word}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete word');
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete word' },
      { status: 500 }
    );
  }
} 