import { NextResponse } from 'next/server';

const WORD_SERVER_URL = process.env.WORD_SERVER_URL;

if (!WORD_SERVER_URL) {
  throw new Error('WORD_SERVER_URL environment variable is not set');
}

export async function GET() {
  try {
    const response = await fetch(`${WORD_SERVER_URL}/words`);
    if (!response.ok) {
      throw new Error('Failed to fetch words');
    }
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to connect to word server' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const response = await fetch(`${WORD_SERVER_URL}/word`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    
    if (!response.ok) {
      throw new Error('Failed to add word');
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to add word' },
      { status: 500 }
    );
  }
} 