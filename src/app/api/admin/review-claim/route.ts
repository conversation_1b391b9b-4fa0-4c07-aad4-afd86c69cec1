import { prisma } from "@/app/util/prismaClient";
import { invalidateAdminCache, invalidateCachesOnOwnershipChange, invalidateBusinessCaches } from "@/app/util/databaseAnalytics";
import { NextResponse, NextRequest } from "next/server";
import { auth } from "@clerk/nextjs/server";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json({
                success: false,
                message: 'Unauthorized',
                status: 401,
            });
        }

        // Check if user has admin role in database
        const user = await prisma.user.findFirst({
            where: {
                clerkUserId: userId
            },
            select: {
                role: true,
                status: true
            }
        });

        if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
            return NextResponse.json({
                success: false,
                message: 'Forbidden',
                status: 403,
            });
        }

        const body = await request.json();
        const { claimId, status, feedback } = body;

        if (!claimId || !status) {
            return NextResponse.json({
                success: false,
                message: 'Missing required fields',
                status: 400,
            });
        }

        // Validate status
        if (!['APPROVED', 'REJECTED'].includes(status)) {
            return NextResponse.json({
                success: false,
                message: 'Invalid status',
                status: 400,
            });
        }

        // Get the claim
        const claim = await prisma.productClaim.findUnique({
            where: { id: claimId },
            include: {
                product: true,
                user: {
                    include: {
                        businesses: true
                    }
                }
            },
        });

        if (!claim) {
            return NextResponse.json({
                success: false,
                message: 'Claim not found',
                status: 404,
            });
        }

        // Check if claim is already reviewed
        if (claim.status !== 'PENDING') {
            return NextResponse.json({
                success: false,
                message: 'Claim has already been reviewed',
                status: 400,
            });
        }

        // Update claim and product in a transaction
        const result = await prisma.$transaction(async (tx) => {
            // Update claim with all required fields
            const updatedClaim = await tx.productClaim.update({
                where: { id: claimId },
                data: {
                    status,
                    rejectionReason: status === 'REJECTED' ? feedback : null,
                    reviewedAt: new Date(),
                    reviewedBy: userId,
                    updatedAt: new Date(),
                },
                include: {
                    product: true,
                    user: true,
                    reviewer: true
                }
            });

            // If approved, update product ownership and user interaction
            if (status === 'APPROVED') {
                // Get the user's first business if available
                const userBusiness = claim.user?.businesses?.[0];

                // If the user doesn't have a business, create one for them
                let businessId = userBusiness?.id;

                if (!businessId) {
                    const newBusiness = await tx.business.create({
                        data: {
                            ownerId: claim.userId,
                            subscriptionStatus: 'FREE',
                            ownerName: `${claim.user.firstName} ${claim.user.lastName}`,
                            isVerified: true,
                            createdDate: new Date(),
                            subscriptionExpiry: null
                        }
                    });
                    businessId = newBusiness.id;
                } else {
                    // Update existing business to set isVerified to true
                    await tx.business.update({
                        where: { id: businessId },
                        data: { isVerified: true }
                    });
                }

                // Update product ownership with business relation
                const updatedProduct = await tx.product.update({
                    where: { id: claim.productId },
                    data: {
                        ownerId: claim.userId,
                        hasOwner: true,
                        businessId: businessId
                    },
                });

                // Create or update user product interaction
                await tx.userProductInteraction.upsert({
                    where: {
                        userId_productId: {
                            userId: claim.userId,
                            productId: claim.productId
                        }
                    },
                    create: {
                        userId: claim.userId,
                        productId: claim.productId,
                        lastViewed: new Date(),
                        viewCount: 1,
                        hasReviewed: false,
                        hasLiked: false,
                        averageTimeSpent: 0
                    },
                    update: {
                        lastViewed: new Date(),
                        viewCount: {
                            increment: 1
                        }
                    }
                });
            }

            return updatedClaim;
        });

        // Invalidate relevant caches after claim review
        try {
            await invalidateAdminCache();
            
            if (status === 'APPROVED') {
                // Invalidate caches related to product ownership change
                await invalidateCachesOnOwnershipChange(claim.productId, result.product?.businessId || undefined);
                
                // If a business was created or updated, invalidate business caches
                if (result.product?.businessId) {
                    await invalidateBusinessCaches(result.product.businessId);
                }
            }
            
            // Removed console.log statement
        } catch (cacheError) {
            console.warn('Failed to invalidate cache after claim review:', cacheError);
            // Don't fail the request if cache invalidation fails
        }

        return NextResponse.json({
            success: true,
            claim: result,
        });
    } catch (error) {
        console.error('Error reviewing claim:', error);
        return NextResponse.json({
            success: false,
            message: 'Failed to review claim',
            status: 500,
        });
    }
} 