export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";

export async function GET(req: NextRequest) {
    try {
        // Check admin authentication
        const { userId } = await auth();
        if (!userId) {
            console.error("Unauthorized access attempt to bug reports");
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Get user and check admin role
        const user = await prisma.user.findFirst({
            where: { clerkUserId: userId },
        });

        if (!user || user.role !== "ADMIN") {
            console.error(`Forbidden access attempt to bug reports by user ${userId}`);
            return NextResponse.json(
                { error: "Forbidden: Admin access required" },
                { status: 403 }
            );
        }

        // Get all bug reports with related data
        const bugReports = await prisma.bugReport.findMany({
            include: {
                reporter: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        userName: true,
                        email: true,
                    },
                },
                resolver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        userName: true,
                    },
                },
            },
            orderBy: {
                created_at: "desc",
            },
        });

        // Ensure all bug reports have valid data
        const sanitizedBugReports = bugReports.map(report => ({
            ...report,
            reporter: report.reporter || null,
            resolver: report.resolver || null,
            status: report.status || 'OPEN',
        }));

        return NextResponse.json(sanitizedBugReports);
    } catch (error) {
        console.error("Error retrieving bug reports:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}