import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";

export async function GET(
    req: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        // Check admin authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Get user and check admin role
        const user = await prisma.user.findFirst({
            where: { clerkUserId: userId },
        });

        if (!user || user.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Forbidden: Admin access required" },
                { status: 403 }
            );
        }

        // Get bug report with related data
        const bugReport = await prisma.bugReport.findUnique({
            where: { id: params.id },
            include: {
                reporter: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        userName: true,
                        email: true,
                    },
                },
                resolver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        userName: true,
                    },
                },
                adminActions: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 10, // Get last 10 actions
                },
            },
        });

        if (!bugReport) {
            return NextResponse.json(
                { error: "Bug report not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(bugReport);
    } catch (error) {
        console.error("Error retrieving bug report:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 