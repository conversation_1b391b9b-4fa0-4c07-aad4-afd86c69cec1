import { NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
    const userId = params.id;
    const body = await request.json();

    const { status, suspendedUntil: suspendedUntilInput, suspendedReason: suspendedReasonInput } = body;

    let dataToUpdate: any = {
        status,
    };

    if (status === "SUSPENDED") {
        dataToUpdate.suspendedReason = suspendedReasonInput || null;
        if (suspendedUntilInput) {
            const date = new Date(suspendedUntilInput);
            if (!isNaN(date.getTime())) { // Check if date is valid
                dataToUpdate.suspendedUntil = date;
            } else {
                // Handle invalid date string, perhaps return an error or log it
                console.warn(`Invalid date string received for suspendedUntil: ${suspendedUntilInput}`);
                dataToUpdate.suspendedUntil = null; // Or omit, or return error
            }
        } else {
            dataToUpdate.suspendedUntil = null; // Explicitly set to null if not provided for suspension
        }
    } else {
        // If status is not SUSPENDED, clear suspension fields
        dataToUpdate.suspendedReason = null;
        dataToUpdate.suspendedUntil = null;
    }

    try {
        const updatedUser = await prisma.user.update({
            where: {
                id: userId,
            },
            data: dataToUpdate,
        });

        return NextResponse.json({
            success: true,
            data: updatedUser,
        });
    } catch (error) {
        console.error('Error updating user status:', error);
        return NextResponse.json(
            { error: 'Failed to update user status' },
            { status: 500 }
        );
    }
}
