import { NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
    const userId = params.id;
    const { role } = await request.json();
    
    try {
        const updatedUser = await prisma.user.update({
            where: {
                id: userId,
            },
            data: {
                role,
            },
        });

        return NextResponse.json({
            success: true,
            data: updatedUser,
        });
    } catch (error) {
        console.error('Error updating user role:', error);
        return NextResponse.json(
            { error: 'Failed to update user role' },
            { status: 500 }
        );
    }
}
