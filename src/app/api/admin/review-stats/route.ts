import { NextResponse } from "next/server";
import { getAdminReviewStatsFromCache } from "@/app/util/databaseAnalytics";

export async function GET() {
  try {
    // Use cached function for review statistics
    const result = await getAdminReviewStatsFromCache();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching review statistics:', error);
    return NextResponse.json({ error: 'Failed to fetch review statistics' }, { status: 500 });
  }
}