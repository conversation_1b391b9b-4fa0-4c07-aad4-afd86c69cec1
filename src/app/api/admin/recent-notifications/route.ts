import { NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { subHours } from "date-fns";

export async function GET() {
  try {
    // Get recent reviews (these can be considered as notifications)
    const recentReviews = await prisma.review.findMany({
      where: {
        createdDate: {
          gte: subHours(new Date(), 24)
        },
        isDeleted: false,
        isPublic: true
      },
      include: {
        user: {
          select: {
            userName: true
          }
        },
        product: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdDate: 'desc'
      },
      take: 10
    });

    // Get recent comments (these can also be considered as notifications)
    const recentComments = await prisma.comment.findMany({
      where: {
        createdDate: {
          gte: subHours(new Date(), 24)
        },
        isDeleted: false,
        review: {
          isPublic: true
        }
      },
      include: {
        user: {
          select: {
            userName: true
          }
        },
        review: {
          select: {
            title: true,
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdDate: 'desc'
      },
      take: 10
    });

    // Format the activities
    const activities = [
      // Format reviews as activities
      ...recentReviews.map(review => ({
        type: "review",
        message: `${review.user?.userName || 'A user'} reviewed ${review.product?.name || 'a product'}`,
        created_at: review.createdDate,
        notification_type: "review"
      })),

      // Format comments as activities
      ...recentComments.map(comment => ({
        type: "comment",
        message: `${comment.user?.userName || 'A user'} commented on review: ${comment.review?.title || 'Unknown review'}`,
        created_at: comment.createdDate,
        notification_type: "comment"
      }))
    ].sort((a, b) => b.created_at.getTime() - a.created_at.getTime())
      .slice(0, 10); // Keep only the 10 most recent activities

    return NextResponse.json({ notifications: activities });
  } catch (error) {
    console.error('Error fetching recent activities:', error);
    return NextResponse.json({ error: 'Failed to fetch recent activities' }, { status: 500 });
  }
} 