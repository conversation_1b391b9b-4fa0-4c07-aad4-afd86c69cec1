import { prisma } from "@/app/util/prismaClient";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export const dynamic = 'force-dynamic';

export async function GET() {
    try {
        // Get the auth state
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json({
                success: false,
                isAdmin: false,
                message: 'Unauthorized',
                errorCode: 'UNAUTHORIZED',
            });
        }

        // Get the user to check permissions
        const user = await prisma.user.findFirst({
            where: {
                clerkUserId: userId
            },
            select: {
                id: true,
                role: true
            }
        });

        if (!user) {
            return NextResponse.json({
                success: false,
                isAdmin: false,
                message: 'User not found',
                errorCode: 'USER_NOT_FOUND',
            });
        }

        // Check if user has admin role
        const isAdmin = user.role === 'ADMIN';

        return NextResponse.json({
            success: true,
            isAdmin,
            role: user.role
        });
    } catch (error) {
        console.error("Error checking admin permission:", error);
        const e = error as Error;
        return NextResponse.json({
            success: false,
            isAdmin: false,
            message: e.message || 'An unexpected error occurred',
            errorCode: 'SERVER_ERROR',
        });
    }
} 