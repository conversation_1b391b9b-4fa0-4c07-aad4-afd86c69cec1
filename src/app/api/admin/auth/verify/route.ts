import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Get authentication from Clerk
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized: Not authenticated" },
        { status: 401 }
      );
    }

    // Get user details from database
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: userId,
        isDeleted: false,
      },
      select: {
        id: true,
        role: true,
        status: true,
        userName: true,
        firstName: true,
        lastName: true,
        avatar: true,
      },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Invalid admin user" },
        { status: 401 }
      );
    }

    if (user.status === "SUSPENDED" || user.status === "BANNED") {
      return NextResponse.json(
        { error: "Account is suspended or banned" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        role: user.role,
        status: user.status,
        userName: user.userName,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
      },
    });
  } catch (error) {
    console.error("Admin verification error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
