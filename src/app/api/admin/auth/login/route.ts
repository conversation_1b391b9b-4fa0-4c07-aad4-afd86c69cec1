import { NextResponse } from "next/server";
import { createInitialAdmin } from "@/app/util/admin-setup";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  try {
    // Get authentication from Clerk
    const { userId, sessionClaims } = await auth();
    const email = sessionClaims?.email as string;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized: Not authenticated" },
        { status: 401 }
      );
    }

    // Check if user exists and is an admin
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: userId,
        isDeleted: false,
      },
      select: {
        id: true,
        role: true,
        status: true,
        lastLoginAt: true,
        loginCount: true,
      },
    });

    if (!user) {
      // Try to create initial admin if no users exist
      const admin = await createInitialAdmin({
        userId,
        email,
        userName: email.split("@")[0],
        firstName: "Admin",
        lastName: "User",
        fullName: "Admin User",
        avatar: null,
        azp: "",
        exp: 0,
        iat: 0,
        iss: "",
        jti: "",
        nbf: 0,
        sub: "",
        metadata: { id: userId, userInDb: false },
      });

      if (!admin) {
        return NextResponse.json(
          { error: "Unauthorized: Not an admin user" },
          { status: 403 }
        );
      }
    } else if (user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized: Not an admin user" },
        { status: 403 }
      );
    }

    if (user?.status === "SUSPENDED" || user?.status === "BANNED") {
      return NextResponse.json(
        { error: "Account is suspended or banned" },
        { status: 403 }
      );
    }

    // Update login information
    await prisma.user.update({
      where: { id: user?.id },
      data: {
        lastLoginAt: new Date(),
        loginCount: (user?.loginCount || 0) + 1,
      },
    });

    // No longer setting a cookie - using Clerk's session management instead

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Admin login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
