import { NextResponse } from "next/server";

export async function POST() {
  try {
    // No longer need to clear cookies as we're using <PERSON>'s session management
    // Clerk handles session termination through its own mechanisms

    return NextResponse.json({
      success: true,
      message:
        "Logout successful. Use Clerk's signOut() method on the client to complete the logout process.",
    });
  } catch (error) {
    console.error("Admin logout error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
