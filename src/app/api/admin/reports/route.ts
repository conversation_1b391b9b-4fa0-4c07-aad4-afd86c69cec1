export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { clerkUserId: userId },
    select: { role: true },
  });

  return user?.role === "ADMIN";
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check admin authorization
    if (!(await isAdmin(userId))) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const reportType = searchParams.get("type") || "all"; // 'review', 'product', or 'all'
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {
      ...(status && { status }),
    };

    let reports: any[] = [];
    let total = 0;

    if (reportType === "review" || reportType === "all") {
      // Get review reports
      const reviewReportTotal = await prisma.reviewReport.count({ where });
      const reviewReports = await prisma.reviewReport.findMany({
        where,
        include: {
          review: {
            select: {
              id: true,
              title: true,
              rating: true,
              body: true,
              product: {
                select: {
                  id: true,
                  name: true,
                },
              },
              user: {
                select: {
                  id: true,
                  userName: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            },
          },
          resolver: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: reportType === "review" ? skip : 0,
        take: reportType === "review" ? limit : undefined,
      });

      // Add type identifier to review reports
      const typedReviewReports = reviewReports.map((report) => ({
        ...report,
        reportType: "review",
      }));

      reports = [...reports, ...typedReviewReports];
      if (reportType === "review") {
        total = reviewReportTotal;
      }
    }

    if (reportType === "product" || reportType === "all") {
      // Get product reports
      const productReportTotal = await prisma.productReport.count({ where });
      const productReports = await prisma.productReport.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              description: true,
              createdBy: {
                select: {
                  id: true,
                  userName: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            },
          },
          resolver: {
            select: {
              id: true,
              userName: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: reportType === "product" ? skip : 0,
        take: reportType === "product" ? limit : undefined,
      });

      // Add type identifier to product reports
      const typedProductReports = productReports.map((report) => ({
        ...report,
        reportType: "product",
      }));

      reports = [...reports, ...typedProductReports];
      if (reportType === "product") {
        total = productReportTotal;
      }
    }

    if (reportType === "all") {
      // For 'all' type, combine totals and sort/paginate the combined results
      const reviewTotal = await prisma.reviewReport.count({ where });
      const productTotal = await prisma.productReport.count({ where });
      total = reviewTotal + productTotal;

      // Sort combined results
      reports.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];

        if (sortOrder === "desc") {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });

      // Apply pagination to combined results
      reports = reports.slice(skip, skip + limit);
    }

    return NextResponse.json({
      success: true,
      data: {
        reports,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
        reportType,
      },
    });
  } catch (error) {
    console.error("Error fetching reports:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
