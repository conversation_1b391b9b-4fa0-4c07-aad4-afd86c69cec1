export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { Prisma } from '@prisma/client';

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
        where: { clerkUserId: userId },
        select: { role: true },
    });

    return user?.role === "ADMIN";
}

// Input validation
function validateUpdateInput(status: string, notes?: string, reviewAction?: { action: string, reason?: string }): { isValid: boolean; error?: string } {
    if (!status || typeof status !== 'string') {
        return { isValid: false, error: 'Status is required' };
    }

    if (!['PENDING', 'REVIEWED', 'RESOLVED'].includes(status)) {
        return { isValid: false, error: 'Invalid status value' };
    }

    if (notes && (typeof notes !== 'string' || notes.length > 1000)) {
        return { isValid: false, error: 'Notes must not exceed 1000 characters' };
    }

    if (reviewAction && !['HIDE', 'DELETE', 'VERIFY', 'NONE'].includes(reviewAction.action)) {
        return { isValid: false, error: 'Invalid review action' };
    }

    if (reviewAction?.action === 'HIDE' && !reviewAction.reason) {
        return { isValid: false, error: 'Reason is required for hiding a review' };
    }

    return { isValid: true };
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { reportId: string } }
) {
    try {
        // Check authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized: Please sign in to access this resource' },
                { status: 401 }
            );
        }

        // Check admin authorization
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { error: 'Forbidden: Admin access required' },
                { status: 403 }
            );
        }

        const { reportId } = params;
        const { status, notes, reviewAction } = await request.json();

        // Validate input
        const validation = validateUpdateInput(status, notes, reviewAction);
        if (!validation.isValid) {
            return NextResponse.json(
                { error: validation.error },
                { status: 400 }
            );
        }

        // Check if report exists
        const existingReport = await prisma.reviewReport.findUnique({
            where: { id: reportId },
            select: { id: true, status: true, reviewId: true }
        });

        if (!existingReport) {
            return NextResponse.json(
                { error: 'Report not found' },
                { status: 404 }
            );
        }

        // Prevent status update if already resolved
        if (existingReport.status === 'RESOLVED' && status !== 'RESOLVED') {
            return NextResponse.json(
                { error: 'Cannot update status of a resolved report' },
                { status: 400 }
            );
        }

        // Update report and handle review action in a transaction
        const updatedReport = await prisma.$transaction(async (tx) => {
            // Update report
            const report = await tx.reviewReport.update({
                where: { id: reportId },
                data: {
                    status,
                    notes,
                    ...(status === 'RESOLVED' && {
                        resolvedAt: new Date(),
                        resolvedBy: userId,
                    }),
                },
                include: {
                    review: {
                        select: {
                            id: true,
                            title: true,
                            rating: true,
                            body: true,
                            user: {
                                select: {
                                    id: true,
                                    userName: true,
                                    firstName: true,
                                    lastName: true,
                                },
                            },
                        },
                    },
                    user: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    resolver: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                },
            });

            // Handle review action if provided
            if (reviewAction && reviewAction.action !== 'NONE') {
                const reviewData: any = {};

                switch (reviewAction.action) {
                    case 'HIDE':
                        reviewData.isPublic = false;
                        break;
                    case 'DELETE':
                        reviewData.isDeleted = true;
                        break;
                    case 'VERIFY':
                        reviewData.isVerified = true;
                        reviewData.verifiedAt = new Date();
                        reviewData.verifiedBy = userId;
                        break;
                }

                if (Object.keys(reviewData).length > 0) {
                    await tx.review.update({
                        where: { id: existingReport.reviewId },
                        data: reviewData,
                    });

                    // Create moderation event
                    await tx.moderationEvent.create({
                        data: {
                            reviewId: existingReport.reviewId,
                            adminId: userId,
                            action: reviewAction.action,
                            reason: reviewAction.reason || notes || 'No reason provided',
                        },
                    });
                }
            }

            return report;
        });

        // Create audit log entry
        await prisma.adminAction.create({
            data: {
                adminId: userId,
                actionType: 'REPORT_STATUS_UPDATE',
                targetId: reportId,
                targetType: 'REVIEW_REPORT',
                description: `Updated report status to ${status}${notes ? ` with notes: ${notes}` : ''}${reviewAction ? ` and took action: ${reviewAction.action}` : ''}`,
            },
        });

        return NextResponse.json({
            success: true,
            data: updatedReport,
            message: 'Report updated successfully',
        });
    } catch (error) {
        console.error('Error updating report:', error);

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            if (error.code === 'P2025') {
                return NextResponse.json(
                    { error: 'Report not found' },
                    { status: 404 }
                );
            }
            return NextResponse.json(
                {
                    error: 'Database error occurred',
                    code: error.code,
                    message: 'There was an issue updating the report'
                },
                { status: 500 }
            );
        }

        if (error instanceof Prisma.PrismaClientValidationError) {
            return NextResponse.json(
                {
                    error: 'Validation error',
                    message: 'Invalid data format in request'
                },
                { status: 400 }
            );
        }

        // Generic error response
        return NextResponse.json(
            {
                error: 'Internal server error',
                message: 'An unexpected error occurred while updating the report'
            },
            { status: 500 }
        );
    }
}

export async function GET(
    request: NextRequest,
    { params }: { params: { reportId: string } }
) {
    try {
        // Check authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized: Please sign in to access this resource' },
                { status: 401 }
            );
        }

        // Check admin authorization
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { error: 'Forbidden: Admin access required' },
                { status: 403 }
            );
        }

        const { reportId } = params;

        // Get report with all related data
        const report = await prisma.reviewReport.findUnique({
            where: { id: reportId },
            include: {
                review: {
                    select: {
                        id: true,
                        title: true,
                        rating: true,
                        body: true,
                        isVerified: true,
                        isPublic: true,
                        isDeleted: true,
                        images: true,
                        videos: true,
                        createdDate: true,
                        product: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        user: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                        comments: {
                            select: {
                                id: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                resolver: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });

        if (!report) {
            return NextResponse.json(
                { error: 'Report not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({
            success: true,
            data: {
                ...report,
                reportType: 'review'
            },
        });
    } catch (error) {
        console.error('Error fetching report:', error);

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            if (error.code === 'P2025') {
                return NextResponse.json(
                    { error: 'Report not found' },
                    { status: 404 }
                );
            }
            return NextResponse.json(
                {
                    error: 'Database error occurred',
                    code: error.code,
                    message: 'There was an issue fetching the report'
                },
                { status: 500 }
            );
        }

        // Generic error response
        return NextResponse.json(
            {
                error: 'Internal server error',
                message: 'An unexpected error occurred while fetching the report'
            },
            { status: 500 }
        );
    }
} 