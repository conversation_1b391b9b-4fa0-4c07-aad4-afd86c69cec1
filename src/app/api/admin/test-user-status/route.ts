import { NextRequest, NextResponse } from 'next/server';
import { testSuspendUser, testBanUser, testReactivateUser } from '@/app/util/test-status';
import { auth } from '@clerk/nextjs/server';

/**
 * Admin-only API route for testing user status functionality
 * This allows admins to temporarily change a user's status for testing purposes
 * 
 * POST /api/admin/test-user-status
 * Body: { userId: string, action: 'suspend' | 'ban' | 'reactivate', durationMinutes?: number }
 */
export async function POST(req: NextRequest) {
  try {
    // Verify admin status
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { userId: targetUserId, action, durationMinutes = 60, reason = 'Test action' } = body;
    
    if (!targetUserId || !action) {
      return NextResponse.json(
        { error: 'Missing required parameters' }, 
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'suspend':
        result = await testSuspendUser(targetUserId, durationMinutes, reason);
        break;
      case 'ban':
        result = await testBanUser(targetUserId);
        break;
      case 'reactivate':
        result = await testReactivateUser(targetUserId);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be "suspend", "ban", or "reactivate"' }, 
          { status: 400 }
        );
    }
    
    return NextResponse.json({ 
      success: true,
      ...result,
      message: `User status updated: ${action}`
    });
    
  } catch (error) {
    console.error('Error in test-user-status API:', error);
    return NextResponse.json(
      { error: 'Failed to update user status' }, 
      { status: 500 }
    );
  }
}
