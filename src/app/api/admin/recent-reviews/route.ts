import { NextResponse } from "next/server";
import { getAdminRecentReviewsFromCache } from "@/app/util/databaseAnalytics";

export async function GET() {
  try {
    // Use cached function for recent reviews
    const result = await getAdminRecentReviewsFromCache();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching recent reviews:', error);
    return NextResponse.json({ error: 'Failed to fetch recent reviews' }, { status: 500 });
  }
}