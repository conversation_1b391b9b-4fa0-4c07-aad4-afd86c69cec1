import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { testGetKenFromRedis } from '@/app/util/databaseAnalytics';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

async function handler() {
  // Test endpoint for admin functionality
  
  // Test Redis get for 'ken' key
  await testGetKenFromRedis();
  
  return NextResponse.json({ success: true });
}

export const GET = withAdminAuth(handler);
