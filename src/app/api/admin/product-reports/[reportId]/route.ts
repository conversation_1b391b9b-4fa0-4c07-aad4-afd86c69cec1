export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server';
import { prisma } from "@/app/util/prismaClient";
import { Prisma } from '@prisma/client';

// Function to check if the user is an admin
async function isAdmin(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
        where: { clerkUserId: userId },
        select: { role: true },
    });

    return user?.role === "ADMIN";
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { reportId: string } }
) {
    try {
        // Check authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Check admin authorization
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { error: 'Forbidden: Admin access required' },
                { status: 403 }
            );
        }

        const { reportId } = params;
        const { status, notes } = await request.json();

        // Validate input
        if (!status || typeof status !== 'string') {
            return NextResponse.json(
                { error: 'Status is required' },
                { status: 400 }
            );
        }

        const validStatuses = ['PENDING', 'REVIEWED', 'RESOLVED', 'DISMISSED'];
        if (!validStatuses.includes(status)) {
            return NextResponse.json(
                { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
                { status: 400 }
            );
        }

        // Get the admin user from database
        const adminUser = await prisma.user.findUnique({
            where: { clerkUserId: userId },
            select: { id: true }
        });

        if (!adminUser) {
            return NextResponse.json(
                { error: 'Admin user not found' },
                { status: 404 }
            );
        }

        // Check if report exists
        const existingReport = await prisma.productReport.findUnique({
            where: { id: reportId },
            include: {
                product: {
                    select: {
                        id: true,
                        name: true,
                    }
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    }
                }
            }
        });

        if (!existingReport) {
            return NextResponse.json(
                { error: 'Product report not found' },
                { status: 404 }
            );
        }

        // Update report
        const updateData: any = {
            status,
            updatedAt: new Date(),
            resolvedBy: adminUser.id,
        };

        // If status is resolved or dismissed, set resolvedAt timestamp
        if (status === 'RESOLVED' || status === 'DISMISSED') {
            updateData.resolvedAt = new Date();
        }

        // Add notes if provided
        if (notes && typeof notes === 'string') {
            updateData.notes = notes.trim().slice(0, 1000); // Limit notes to 1000 characters
        }

        const updatedReport = await prisma.productReport.update({
            where: { id: reportId },
            data: updateData,
            include: {
                product: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        createdBy: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                            }
                        }
                    }
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    }
                },
                resolver: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    }
                }
            }
        });

        // Log admin action
        await prisma.adminAction.create({
            data: {
                adminId: adminUser.id,
                actionType: 'PRODUCT_REPORT_UPDATE',
                targetId: reportId,
                targetType: 'PRODUCT_REPORT',
                description: `Updated product report status from "${existingReport.status}" to "${status}" for product "${existingReport.product.name}"`,
            }
        });

        return NextResponse.json({
            success: true,
            data: {
                ...updatedReport,
                reportType: 'product'
            },
            message: 'Product report updated successfully'
        });

    } catch (error) {
        console.error('Error updating product report:', error);

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            return NextResponse.json(
                {
                    error: 'Database error occurred',
                    code: error.code,
                    message: 'There was an issue updating the report'
                },
                { status: 500 }
            );
        }

        if (error instanceof Prisma.PrismaClientValidationError) {
            return NextResponse.json(
                {
                    error: 'Validation error',
                    message: 'Invalid data format in request'
                },
                { status: 400 }
            );
        }

        // Generic error response
        return NextResponse.json(
            {
                error: 'Internal server error',
                message: 'An unexpected error occurred while updating the report'
            },
            { status: 500 }
        );
    }
}

export async function GET(
    request: NextRequest,
    { params }: { params: { reportId: string } }
) {
    try {
        // Check authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Check admin authorization
        if (!(await isAdmin(userId))) {
            return NextResponse.json(
                { error: 'Forbidden: Admin access required' },
                { status: 403 }
            );
        }

        const { reportId } = params;

        // Get report details
        const report = await prisma.productReport.findUnique({
            where: { id: reportId },
            include: {
                product: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        display_image: true,
                        hasOwner: true,
                        ownerId: true,
                        business: {
                            select: {
                                id: true,
                                isVerified: true,
                                owner: {
                                    select: {
                                        id: true,
                                        firstName: true,
                                        lastName: true,
                                        avatar: true,
                                    }
                                }
                            }
                        },
                        createdBy: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                            }
                        }
                    }
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    }
                },
                resolver: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    }
                }
            }
        });

        if (!report) {
            return NextResponse.json(
                { error: 'Product report not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({
            success: true,
            data: {
                ...report,
                reportType: 'product'
            }
        });

    } catch (error) {
        console.error('Error fetching product report:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
