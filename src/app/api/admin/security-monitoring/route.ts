// Security Monitoring Dashboard API
// Provides real-time security alerts and CORS monitoring for administrators

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { restrictedCorsMiddleware } from '@/app/util/corsMiddleware';
import { getCorsAnalytics } from '@/app/util/corsErrorMonitoring';

// GET: Retrieve security monitoring dashboard data
export async function GET(req: NextRequest): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    // Check if user is admin (you may need to adjust this based on your admin logic)
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // For now, we'll allow all authenticated users. In production, add admin role check
    // if (!user.isAdmin) {
    //   return NextResponse.json(
    //     { error: 'Admin access required' },
    //     { status: 403, headers: corsHeaders }
    //   );
    // }

    const { searchParams } = new URL(req.url);
    const hours = parseInt(searchParams.get('hours') || '24');
    const includeResolved = searchParams.get('includeResolved') === 'true';

    const startTime = new Date();
    startTime.setHours(startTime.getHours() - hours);

    // Get recent CORS errors
    const recentErrors = await prisma.corsErrorLog.findMany({
      where: {
        timestamp: {
          gte: startTime
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 100
    });

    // Get security alerts
    const alertsWhere: any = {
      timestamp: {
        gte: startTime
      }
    };

    if (!includeResolved) {
      alertsWhere.isResolved = false;
    }

    const securityAlerts = await prisma.corsAlert.findMany({
      where: alertsWhere,
      orderBy: {
        timestamp: 'desc'
      },
      take: 50
    });

    // Calculate security metrics
    const securityMetrics = {
      totalErrors: recentErrors.length,
      totalAlerts: securityAlerts.length,
      unresolvedAlerts: securityAlerts.filter(alert => !alert.isResolved).length,
      criticalAlerts: securityAlerts.filter(alert => 
        alert.type === 'suspicious_activity' || 
        alert.type === 'high_error_rate'
      ).length,
      period: `${hours} hours`
    };

    // Analyze error patterns
    const errorAnalysis = {
      byType: aggregateByField(recentErrors, 'errorType'),
      byOrigin: aggregateByField(recentErrors, 'origin'),
      byIP: aggregateByField(recentErrors, 'ipAddress'),
      byUserAgent: aggregateByField(recentErrors, 'userAgent'),
      hourlyDistribution: getHourlyDistribution(recentErrors)
    };

    // Identify suspicious patterns
    const suspiciousPatterns = {
      highVolumeIPs: getHighVolumeIPs(recentErrors),
      repeatedOrigins: getRepeatedOrigins(recentErrors),
      unusualUserAgents: getUnusualUserAgents(recentErrors),
      rapidFireRequests: getRapidFireRequests(recentErrors)
    };

    // Get widget security status
    const widgetSecurityStatus = await getWidgetSecurityStatus();

    // Real-time threat indicators
    const threatIndicators = {
      activeThreatLevel: calculateThreatLevel(recentErrors, securityAlerts),
      blockedAttacks: recentErrors.filter(error => 
        error.errorType === 'blocked_origin' || 
        error.errorType === 'unauthorized_domain'
      ).length,
      potentialBotActivity: recentErrors.filter(error => 
        error.userAgent?.includes('bot') || 
        error.userAgent?.includes('crawler') ||
        !error.userAgent
      ).length,
      geographicDistribution: getGeographicDistribution(recentErrors)
    };

    const dashboardData = {
      timestamp: new Date().toISOString(),
      metrics: securityMetrics,
      errorAnalysis,
      suspiciousPatterns,
      threatIndicators,
      widgetSecurityStatus,
      recentAlerts: securityAlerts.slice(0, 10),
      recentErrors: recentErrors.slice(0, 20),
      recommendations: generateSecurityRecommendations(recentErrors, securityAlerts)
    };

    return NextResponse.json(
      { dashboard: dashboardData },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Security monitoring dashboard error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

// POST: Resolve security alerts
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    const body = await req.json();
    const { alertIds, action } = body;

    if (!Array.isArray(alertIds) || !action) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    if (action === 'resolve') {
      await prisma.corsAlert.updateMany({
        where: {
          id: {
            in: alertIds
          },
          isResolved: false
        },
        data: {
          isResolved: true,
          resolvedAt: new Date(),
          resolvedBy: user.id
        }
      });

      return NextResponse.json(
        { 
          message: `${alertIds.length} alerts resolved successfully`,
          resolvedBy: user.id,
          resolvedAt: new Date().toISOString()
        },
        { 
          status: 200,
          headers: corsHeaders
        }
      );
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { 
        status: 400,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Security alert resolution error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return restrictedCorsMiddleware(req) as NextResponse;
}

// Helper functions
function aggregateByField(errors: any[], field: string): Record<string, number> {
  return errors.reduce((acc, error) => {
    const value = error[field] || 'unknown';
    acc[value] = (acc[value] || 0) + 1;
    return acc;
  }, {});
}

function getHourlyDistribution(errors: any[]): Record<string, number> {
  return errors.reduce((acc, error) => {
    const hour = new Date(error.timestamp).getHours().toString().padStart(2, '0');
    acc[hour] = (acc[hour] || 0) + 1;
    return acc;
  }, {});
}

function getHighVolumeIPs(errors: any[]): Array<{ ip: string; count: number; errorTypes: string[] }> {
  const ipCounts = errors.reduce((acc, error) => {
    const ip = error.ipAddress;
    if (!acc[ip]) {
      acc[ip] = { count: 0, errorTypes: new Set<string>() };
    }
    acc[ip].count++;
    acc[ip].errorTypes.add(error.errorType);
    return acc;
  }, {} as Record<string, { count: number; errorTypes: Set<string> }>);

  return Object.entries(ipCounts)
    .filter(([, data]) => (data as { count: number; errorTypes: Set<string> }).count >= 5) // Threshold for high volume
    .map(([ip, data]) => ({
      ip,
      count: (data as { count: number; errorTypes: Set<string> }).count,
      errorTypes: Array.from((data as { count: number; errorTypes: Set<string> }).errorTypes)
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getRepeatedOrigins(errors: any[]): Array<{ origin: string; count: number; lastSeen: string }> {
  const originCounts = errors.reduce((acc, error) => {
    const origin = error.origin || 'unknown';
    if (!acc[origin]) {
      acc[origin] = { count: 0, lastSeen: error.timestamp };
    }
    acc[origin].count++;
    if (new Date(error.timestamp) > new Date(acc[origin].lastSeen)) {
      acc[origin].lastSeen = error.timestamp;
    }
    return acc;
  }, {} as Record<string, { count: number; lastSeen: string }>);

  return Object.entries(originCounts)
    .filter(([, data]) => (data as { count: number; lastSeen: string }).count >= 3)
    .map(([origin, data]) => ({
      origin,
      count: (data as { count: number; lastSeen: string }).count,
      lastSeen: (data as { count: number; lastSeen: string }).lastSeen
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getUnusualUserAgents(errors: any[]): Array<{ userAgent: string; count: number }> {
  const userAgentCounts = aggregateByField(errors, 'userAgent');
  const suspiciousPatterns = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget'];
  
  return Object.entries(userAgentCounts)
    .filter(([userAgent]) => 
      !userAgent || 
      userAgent === 'unknown' ||
      suspiciousPatterns.some(pattern => userAgent.toLowerCase().includes(pattern))
    )
    .map(([userAgent, count]) => ({ userAgent, count: count as number }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getRapidFireRequests(errors: any[]): Array<{ ip: string; requestsPerMinute: number; timeWindow: string }> {
  const timeWindows = new Map<string, Map<string, number>>();
  
  errors.forEach(error => {
    const timestamp = new Date(error.timestamp);
    const minute = new Date(timestamp.getFullYear(), timestamp.getMonth(), timestamp.getDate(), 
                           timestamp.getHours(), timestamp.getMinutes()).toISOString();
    const ip = error.ipAddress;
    
    if (!timeWindows.has(minute)) {
      timeWindows.set(minute, new Map());
    }
    
    const minuteMap = timeWindows.get(minute)!;
    minuteMap.set(ip, (minuteMap.get(ip) || 0) + 1);
  });
  
  const rapidFire: Array<{ ip: string; requestsPerMinute: number; timeWindow: string }> = [];
  
  timeWindows.forEach((ipCounts, minute) => {
    ipCounts.forEach((count, ip) => {
      if (count >= 10) { // Threshold for rapid fire
        rapidFire.push({
          ip,
          requestsPerMinute: count,
          timeWindow: minute
        });
      }
    });
  });
  
  return rapidFire
    .sort((a, b) => b.requestsPerMinute - a.requestsPerMinute)
    .slice(0, 10);
}

async function getWidgetSecurityStatus() {
  const widgets = await prisma.widget.findMany({
    select: {
      id: true,
      name: true,
      allowedDomains: true,
      blockedCount: true,
      viewCount: true,
      businessId: true
    }
  });

  return widgets.map(widget => ({
    id: widget.id,
    title: widget.name,
    businessName: 'Business', // Simplified for now
    isPublic: Array.isArray(widget.allowedDomains) && widget.allowedDomains.includes('*'),
    domainCount: Array.isArray(widget.allowedDomains) ? widget.allowedDomains.length : 0,
    blockedCount: widget.blockedCount || 0,
    viewCount: widget.viewCount || 0,
    riskLevel: calculateWidgetRiskLevel(widget)
  }));
}

function calculateWidgetRiskLevel(widget: any): 'low' | 'medium' | 'high' {
  const isPublic = Array.isArray(widget.allowedDomains) && widget.allowedDomains.includes('*');
  const blockedRatio = widget.viewCount > 0 ? (widget.blockedCount || 0) / widget.viewCount : 0;
  
  if (isPublic && blockedRatio > 0.1) return 'high';
  if (isPublic || blockedRatio > 0.05) return 'medium';
  return 'low';
}

function calculateThreatLevel(errors: any[], alerts: any[]): 'low' | 'medium' | 'high' | 'critical' {
  const errorCount = errors.length;
  const criticalAlerts = alerts.filter(alert => 
    alert.type === 'suspicious_activity' || alert.type === 'high_error_rate'
  ).length;
  
  if (criticalAlerts > 5 || errorCount > 100) return 'critical';
  if (criticalAlerts > 2 || errorCount > 50) return 'high';
  if (criticalAlerts > 0 || errorCount > 20) return 'medium';
  return 'low';
}

function getGeographicDistribution(errors: any[]): Record<string, number> {
  // This is a simplified version. In production, you'd use IP geolocation
  const ipCounts = aggregateByField(errors, 'ipAddress');
  return Object.fromEntries(
    Object.entries(ipCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
  );
}

function generateSecurityRecommendations(errors: any[], alerts: any[]): string[] {
  const recommendations: string[] = [];
  
  const errorCount = errors.length;
  const unresolvedAlerts = alerts.filter(alert => !alert.isResolved).length;
  
  if (unresolvedAlerts > 5) {
    recommendations.push('High number of unresolved security alerts. Review and resolve critical alerts immediately.');
  }
  
  if (errorCount > 50) {
    recommendations.push('High CORS error rate detected. Review widget configurations and allowed domains.');
  }
  
  const suspiciousIPs = getHighVolumeIPs(errors);
  if (suspiciousIPs.length > 0) {
    recommendations.push(`${suspiciousIPs.length} IP addresses showing suspicious activity. Consider implementing IP-based rate limiting.`);
  }
  
  const publicWidgets = errors.filter(error => error.origin === '*').length;
  if (publicWidgets > 0) {
    recommendations.push('Consider restricting public widget access to specific domains for better security.');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Security status looks good. Continue monitoring for any unusual patterns.');
  }
  
  return recommendations;
}