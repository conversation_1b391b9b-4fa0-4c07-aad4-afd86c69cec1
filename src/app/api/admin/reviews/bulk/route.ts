import { NextResponse } from "next/server";
import { withAdminAuth } from "@/app/middleware/adminAuth";
import { Prisma } from "@prisma/client";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";
import {
  invalidateProductCache,
  invalidateAggregatedCachesOnReviewChange,
  batchInvalidateCache,
  CacheKeys,
} from "@/app/util/analytics/cache";

export const dynamic = "force-dynamic";

async function handler(request: Request) {
  try {
    const { reviewIds, action, reason } = await request.json();

    if (!Array.isArray(reviewIds) || reviewIds.length === 0 || !action) {
      return NextResponse.json(
        { error: "Invalid request parameters" },
        { status: 400 },
      );
    }

    // Validate action type
    if (!["APPROVE", "REJECT", "FLAG"].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action type" },
        { status: 400 },
      );
    }

    // Get the admin user from Clerk auth
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Admin ID not found" },
        { status: 401 },
      );
    }

    // Get the actual database user ID from the Clerk ID
    const adminUser = await prisma.user.findFirst({
      where: { clerkUserId: userId },
      select: { id: true, userName: true },
    });

    if (!adminUser) {
      console.error(
        `[REVIEW-MODERATE] Could not find admin user with Clerk ID ${userId}`,
      );
      return NextResponse.json(
        { error: "Admin user not found in database" },
        { status: 404 },
      );
    }

    // Process each review WITHOUT using a transaction (to avoid rollback issues)
    const results = [];
    const rejectedProductIds = new Set<string>(); // Track products that need cache invalidation

    for (const reviewId of reviewIds) {
      try {
        // First update the review separately to ensure it gets updated even if the event creation fails
        const isApprove = action === "APPROVE";
        const isFlag = action === "FLAG";
        const shouldVerify = !isFlag;

        const updateData = {
          isPublic: isApprove,
          isVerified: shouldVerify,
          verifiedBy: shouldVerify ? adminUser.id : undefined,
          verifiedAt: shouldVerify ? new Date() : undefined,
        };

        // Get the review before update (include productId for cache invalidation)
        const reviewBefore = await prisma.review.findUnique({
          where: { id: reviewId },
          select: {
            id: true,
            isPublic: true,
            isVerified: true,
            verifiedBy: true,
            verifiedAt: true,
            productId: true,
          },
        });

        if (!reviewBefore) {
          throw new Error(`Review with ID ${reviewId} not found`);
        }

        // Track rejected reviews for cache invalidation
        if (action === "REJECT" && reviewBefore.productId) {
          rejectedProductIds.add(reviewBefore.productId);
        }

        // Update review status first as a separate operation
        const updatedReview = await prisma.review.update({
          where: { id: reviewId },
          data: updateData,
          select: {
            id: true,
            isPublic: true,
            isVerified: true,
            verifiedBy: true,
            verifiedAt: true,
          },
        });

        let moderationEvent = null;
        let moderationError = null;

        // Try to create the moderation event as a separate operation
        try {
          moderationEvent = await prisma.moderationEvent.create({
            data: {
              reviewId,
              adminId: adminUser.id,
              action:
                action === "APPROVE"
                  ? "APPROVED"
                  : action === "REJECT"
                    ? "REJECTED"
                    : "FLAGGED",
              reason,
              createdAt: new Date(),
            },
            select: { id: true, action: true, createdAt: true },
          });
        } catch (eventError) {
          console.error(
            `[REVIEW-MODERATE] Error creating moderation event:`,
            eventError,
          );
          moderationError =
            eventError instanceof Error ? eventError.message : "Unknown error";
        }

        // Double-check that the review was actually updated
        const finalCheck = await prisma.review.findUnique({
          where: { id: reviewId },
          select: { isVerified: true, isPublic: true },
        });

        results.push({
          reviewId,
          success: true,
          reviewData: updatedReview,
          moderationEvent,
          moderationError,
        });
      } catch (error) {
        console.error(
          `[REVIEW-MODERATE] Error processing review ${reviewId}:`,
          error,
        );
        results.push({
          reviewId,
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "An unknown error occurred",
        });
      }
    }

    // Smart cache invalidation based on action type
    if (rejectedProductIds.size > 0) {
      try {
        // Invalidate product-specific caches for rejected reviews
        for (const productId of rejectedProductIds) {
          await invalidateProductCache(productId);
        }

        // Invalidate aggregated caches that might include the rejected reviews
        await invalidateAggregatedCachesOnReviewChange();
      } catch (cacheError) {
        console.error(
          `[CACHE-INVALIDATION] Failed to invalidate caches for rejected reviews:`,
          cacheError,
        );
        // Don't fail the whole operation if cache invalidation fails
      }
    } else if (action === "APPROVE") {
    } else if (action === "FLAG") {
    }

    // Calculate summary
    const summary = {
      total: results.length,
      successCount: results.filter((r) => r.success).length,
      failureCount: results.filter((r) => !r.success).length,
    };

    return NextResponse.json({
      success: true,
      data: {
        summary,
        results,
      },
    });
  } catch (error) {
    console.error("[REVIEW-MODERATE] Error in bulk review processing:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process bulk action" },
      { status: 500 },
    );
  }
}

export const POST = withAdminAuth(handler);
