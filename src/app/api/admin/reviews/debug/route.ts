import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { prisma } from '@/app/util/prismaClient';
import { auth } from '@clerk/nextjs/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

/**
 * This is a debug endpoint to directly verify and test review updates
 * It provides a way to attempt updating a review directly without going through 
 * the normal update flow to help diagnose database issues
 */
async function handler(request: Request) {
    try {
        // Get the review ID from the query string
        const { searchParams } = new URL(request.url);
        const reviewId = searchParams.get('reviewId');
        const action = searchParams.get('action') || 'APPROVE';
        const skipEvent = searchParams.get('skipEvent') === 'true';

        if (!reviewId) {
            return NextResponse.json(
                { error: 'reviewId is required' },
                { status: 400 }
            );
        }

        // Get the admin user from Clerk auth
        const { userId } = await auth();

        // First, check if the admin user exists in the database
        let adminUser = null;
        if (userId) {
            adminUser = await prisma.user.findFirst({
                where: { clerkUserId: userId },
                select: { id: true, userName: true }
            });
        }

        // Get the current state of the review
        const reviewBefore = await prisma.review.findUnique({
            where: { id: reviewId },
            select: {
                id: true,
                isPublic: true,
                isVerified: true,
                verifiedBy: true,
                verifiedAt: true
            }
        });

        if (!reviewBefore) {
            return NextResponse.json(
                { error: `Review with ID ${reviewId} not found` },
                { status: 404 }
            );
        }

        // Prepare the update based on the action
        const isApprove = action === 'APPROVE';
        const shouldSetVerified = action !== 'FLAG';

        const updateData = {
            isPublic: isApprove,
            isVerified: shouldSetVerified,
            verifiedBy: shouldSetVerified ? (adminUser?.id || 'debug-admin') : undefined,
            verifiedAt: shouldSetVerified ? new Date() : undefined,
        };

        // Try a direct update outside of a transaction to isolate the issue
        const updatedReview = await prisma.review.update({
            where: { id: reviewId },
            data: updateData,
            select: {
                id: true,
                isPublic: true,
                isVerified: true,
                verifiedBy: true,
                verifiedAt: true
            }
        });

        // Add moderation event only if we don't skip it and have a valid admin user
        if (!skipEvent && adminUser) {
            try {
                const moderationEvent = await prisma.moderationEvent.create({
                    data: {
                        reviewId,
                        adminId: adminUser.id,
                        action: action === 'APPROVE' ? 'APPROVED' :
                            action === 'REJECT' ? 'REJECTED' : 'FLAGGED',
                        reason: 'Debug approved via direct endpoint',
                        createdAt: new Date()
                    },
                    select: { id: true, action: true, adminId: true }
                });

                return NextResponse.json({
                    success: true,
                    initialState: reviewBefore,
                    afterUpdate: updatedReview,
                    moderationEvent,
                    isVerifiedSet: updatedReview.isVerified === shouldSetVerified
                });
            } catch (eventError) {
                return NextResponse.json({
                    success: true,
                    initialState: reviewBefore,
                    afterUpdate: updatedReview,
                    moderationEventError: eventError instanceof Error ? eventError.message : 'Unknown error',
                    isVerifiedSet: updatedReview.isVerified === shouldSetVerified
                });
            }
        }

        return NextResponse.json({
            success: true,
            initialState: reviewBefore,
            afterUpdate: updatedReview,
            skipModeration: skipEvent,
            isVerifiedSet: updatedReview.isVerified === shouldSetVerified
        });
    } catch (error) {
        return NextResponse.json(
            {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined
            },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler); 