import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';

async function handler(request: Request) {
    try {
        // Get query parameters for filtering and pagination
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const status = searchParams.get('status') || 'PENDING'; // PENDING, APPROVED, REJECTED
        const rating = searchParams.get('rating');
        const search = searchParams.get('search');

        // Build where clause based on filters
        const where: any = {
            isDeleted: false,
        };

        if (status === 'PENDING') {
            // For pending, we consider any review that's not verified yet (regardless of isPublic)
            where.OR = [
                { isVerified: false },
                { isVerified: null }
            ];
        } else if (status === 'APPROVED') {
            where.isPublic = true;
            where.isVerified = true;
        } else if (status === 'REJECTED') {
            where.isPublic = false;
            where.isVerified = true;
        }

        if (rating) {
            where.rating = parseInt(rating);
        }

        if (search) {
            const searchConditions = [
                { title: { contains: search, mode: 'insensitive' } },
                { body: { contains: search, mode: 'insensitive' } },
                { product: { name: { contains: search, mode: 'insensitive' } } },
                { user: { userName: { contains: search, mode: 'insensitive' } } },
            ];

            if (where.OR) {
                const verifiedConditions = where.OR;
                delete where.OR;

                where.AND = [
                    { OR: verifiedConditions },
                    { OR: searchConditions }
                ];
            } else {
                where.OR = searchConditions;
            }
        }

        // Get total count for pagination
        const total = await prisma.review.count({ where });

        // Get reviews with pagination and related data
        const reviews = await prisma.review.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                product: {
                    select: {
                        id: true,
                        name: true,
                        display_image: true,
                    },
                },
                moderationHistory: {
                    include: {
                        admin: {
                            select: {
                                id: true,
                                userName: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                },
                voteCount: true,
            },
            orderBy: {
                createdDate: 'desc',
            },
            skip: (page - 1) * limit,
            take: limit,
        });

        return NextResponse.json({
            success: true,
            data: {
                reviews,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            },
        });
    } catch (error) {
        console.error('Error fetching review queue:', error);
        return NextResponse.json(
            { error: 'Failed to fetch review queue' },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler); 