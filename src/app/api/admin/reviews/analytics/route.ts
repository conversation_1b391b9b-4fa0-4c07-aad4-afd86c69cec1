import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { subDays, startOfDay, endOfDay, format } from 'date-fns';
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';

async function handler(request: Request) {
    try {
        // Get query parameters for time range
        const { searchParams } = new URL(request.url);
        const days = parseInt(searchParams.get('days') || '30');
        const includeDaily = searchParams.get('includeDaily') === 'true';

        // Calculate date range
        const endDate = endOfDay(new Date());
        const startDate = startOfDay(subDays(endDate, days));

        // Get review volume trends with daily average ratings if requested
        let reviewVolume;
        
        if (includeDaily) {
            // Use raw SQL to get both count and average rating per day
            reviewVolume = await prisma.$queryRaw`
                SELECT 
                    DATE("createdDate") as "createdDate",
                    COUNT(id) as count,
                    AVG(rating) as "avgRating"
                FROM "Review"
                WHERE 
                    "createdDate" >= ${startDate}::timestamp AND 
                    "createdDate" <= ${endDate}::timestamp AND
                    "isDeleted" = false
                GROUP BY DATE("createdDate")
                ORDER BY "createdDate" ASC
            `;
            
            // Format the result to match the expected structure
            reviewVolume = (reviewVolume as any[]).map(item => ({
                createdDate: format(new Date(item.createdDate), 'yyyy-MM-dd'),
                _count: { id: Number(item.count) },
                avgRating: Number(item.avgRating)
            }));
        } else {
            // Use the original Prisma query when daily averages aren't needed
            reviewVolume = await prisma.review.groupBy({
                by: ['createdDate'],
                where: {
                    createdDate: {
                        gte: startDate,
                        lte: endDate,
                    },
                    isDeleted: false,
                },
                _count: {
                    id: true,
                },
                orderBy: {
                    createdDate: 'asc',
                },
            });
        }

        // Get rating distribution
        const ratingDistribution = await prisma.review.groupBy({
            by: ['rating'],
            where: {
                isDeleted: false,
            },
            _count: {
                rating: true,
            },
            orderBy: {
                rating: 'desc',
            },
        });

        // Get moderation queue stats
        const moderationStats = await prisma.review.groupBy({
            by: ['isPublic', 'isVerified'],
            where: {
                isDeleted: false,
            },
            _count: {
                id: true,
            },
        });

        // Calculate review quality metrics
        const qualityMetrics = await prisma.review.aggregate({
            where: {
                isDeleted: false,
                isPublic: true,
            },
            _avg: {
                rating: true,
            },
            _count: {
                id: true,
            },
        });

        // Get most active reviewers
        const activeReviewers = await prisma.review.groupBy({
            by: ['userId'],
            where: {
                isDeleted: false,
                isPublic: true,
            },
            _count: {
                id: true,
            },
            orderBy: {
                _count: {
                    id: 'desc',
                },
            },
            take: 10,
        });

        // Get reviewer details
        const reviewerDetails = await prisma.user.findMany({
            where: {
                id: {
                    in: activeReviewers.map(r => r.userId),
                },
            },
            select: {
                id: true,
                userName: true,
                firstName: true,
                lastName: true,
            },
        });

        // Combine reviewer data
        const topReviewers = activeReviewers.map(reviewer => ({
            ...reviewer,
            user: reviewerDetails.find(detail => detail.id === reviewer.userId),
        }));

        return NextResponse.json({
            success: true,
            data: {
                reviewVolume,
                ratingDistribution,
                moderationStats,
                qualityMetrics,
                topReviewers,
                timeRange: {
                    start: startDate,
                    end: endDate,
                },
            },
        });
    } catch (error) {
        console.error('Error fetching review analytics:', error);
        return NextResponse.json(
            { error: 'Failed to fetch review analytics' },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler); 