export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

/**
 * GET /api/admin/security-logs
 * Fetch security logs with pagination and filtering
 * Admin only endpoint
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication and admin status
    const { userId } = await auth();
    
    if (!userId) {
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }
    
    const clerkUserId = userId;

    // Check if user is admin
    const dbUser = await prisma.user.findUnique({
      where: { id: clerkUserId },
      select: { role: true },
    });

    if (!dbUser || dbUser.role !== "ADMIN") {
      return new NextResponse(JSON.stringify({ error: "Forbidden" }), {
        status: 403,
      });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const eventType = url.searchParams.get("eventType") || undefined;
    const severity = url.searchParams.get("severity") || undefined;
    const filterUserId = url.searchParams.get("userId") || undefined;

    // Build where clause for filtering
    const where: any = {};
    
    if (eventType) {
      where.eventType = eventType;
    }
    
    if (severity) {
      where.severity = severity;
    }
    
    if (filterUserId) {
      where.userId = filterUserId;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch logs with pagination
    // Using @ts-ignore because the securityLog model might not be recognized by TypeScript yet
    // but it exists in the database after running prisma db push
    const [logs, total] = await Promise.all([
      // @ts-ignore
      prisma.securityLog.findMany({
        where,
        orderBy: { timestamp: "desc" },
        skip,
        take: limit,
      }),
      // @ts-ignore
      prisma.securityLog.count({ where }),
    ]);

    return NextResponse.json({
      logs,
      total,
    });
  } catch (error) {
    console.error("Error fetching security logs:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch security logs" }),
      { status: 500 }
    );
  }
}
