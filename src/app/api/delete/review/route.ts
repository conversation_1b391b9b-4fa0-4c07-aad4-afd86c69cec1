import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { iReview } from "@/app/util/Interfaces";
import { addUserToDb } from "@/app/util/addUserToDb";

// Interface representing user data
interface UserDATA {
  avatar?: string;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata: { userInDb: boolean, id: string }
}

export async function POST(request: NextRequest) {
  const review: { id: string } = await request.json();
  // Initialize a variable to store the Clerk user data
  let clerkUserData = null;
  let userIdFromClerk = null;

  if (!review.id) {
    return NextResponse.json({
      success: false,
      status: 400,
      message: "Review ID is required",
    });
  }

  try {
    // Extract the session claims from the request
    const { sessionClaims } = getAuth(request as any);
    // Cast the session claims to the `UserDATA` type
    const clerkClaimsData = sessionClaims as unknown as UserDATA;

    // Check if the user already exists in the database
    if (!(await userInDb(clerkClaimsData.userId))) {
      // If the user doesn't exist, create them
      clerkUserData = await addUserToDb(clerkClaimsData);
    } else {
      // If the user already exists, retrieve their data from the database
      clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
      // then add publicMetaData.id to the object
      if (clerkUserData.publicMetadata.id !== undefined) {
        userIdFromClerk = clerkUserData.publicMetadata
          .id as string;
      }
    }

    // First, get the review to check ownership
    const existingReview = await prisma.review.findUnique({
      where: {
        id: review.id,
      },
      include: {
        user: {
          select: {
            id: true,
            clerkUserId: true,
          },
        },
      },
    });

    if (!existingReview) {
      return NextResponse.json({
        success: false,
        status: 404,
        message: "Review not found",
      });
    }

    // Check if the user is the owner of the review
    const isReviewOwner = existingReview.user.id === userIdFromClerk || 
                         existingReview.user.clerkUserId === clerkClaimsData.userId;

    if (!isReviewOwner) {
      return NextResponse.json({
        success: false,
        status: 403,
        message: "You can only delete your own reviews",
      });
    }

    // Mark the review as deleted
    const deletedReview = await prisma.review.update({
      where: {
        id: review.id,
      },
      data: {
        isDeleted: true,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            userName: true,
            avatar: true,
            clerkUserId: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Sanitize the deleted review
    const sanitizedReview = {
      ...deletedReview,
      title: "Review Deleted",
      body: "This review has been deleted.",
      user: {
        ...deletedReview.user,
        userName: "Review Deleted",
        firstName: "Review",
        lastName: "Deleted",
        avatar: "/deleted-user.svg",
      },
    };

    return NextResponse.json({
      success: true,
      status: 200,
      data: sanitizedReview,
    });

  } catch (error) {
    let e = error as Error;
    console.error("Error deleting review:", e);
    // Return an error response
    return NextResponse.json({
      success: false,
      status: 500,
      message: "Failed to delete review",
      data: e.message,
    });
  }
}