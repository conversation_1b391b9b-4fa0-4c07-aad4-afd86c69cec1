import { ImageResponse } from 'next/og';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

// This route should be public - no authentication required
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get image URL from the search params
    const imageUrl = searchParams.get('image');
    const title = searchParams.get('title') || 'Review It';
    
    if (imageUrl) {
      // If an image URL is provided, redirect to it
      return NextResponse.redirect(decodeURIComponent(imageUrl), 302);
    }
    
    // Otherwise, generate a default OG image
    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#fff',
            padding: '40px',
          }}
        >
          <div
            style={{
              display: 'flex',
              fontSize: 60,
              fontWeight: 'bold',
              textAlign: 'center',
              color: '#000',
              marginBottom: '20px',
            }}
          >
            {title}
          </div>
          <div
            style={{
              display: 'flex',
              fontSize: 30,
              textAlign: 'center',
              color: '#666',
            }}
          >
            Review It - Share your experiences
          </div>
        </div>
      ),
      {
        width: 800,
        height: 420,
      }
    );
  } catch (error) {
    console.error('OG image generation error:', error);
    return new Response(`Failed to generate image`, {
      status: 500,
    });
  }
}
