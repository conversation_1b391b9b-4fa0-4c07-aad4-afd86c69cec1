import { ImageResponse } from 'next/og';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Get parameters from the search params
    const imageUrl = searchParams.get('image');
    const title = searchParams.get('title') || 'Product Review';
    const rating = searchParams.get('rating') || '0';
    const reviews = searchParams.get('reviews') || '0';

    // Check if it's a social media crawler - prioritize image generation for these
    const userAgent = request.headers.get('user-agent') || '';
    const isSocialMediaCrawler =
      userAgent.includes('facebookexternalhit') ||
      userAgent.includes('Twitterbot') ||
      userAgent.includes('LinkedInBot') ||
      userAgent.includes('WhatsApp') ||
      userAgent.includes('Discordbot') ||
      userAgent.includes('Slackbot') ||
      // Removed TelegramBot
      userAgent.includes('bot') ||
      userAgent.includes('crawler');

    // For social crawlers or if explicitly requested, always generate the image
    // Only redirect for normal browsers and if there's an image
    const forceGenerate = searchParams.get('forceGenerate') === 'true';
    if (imageUrl && !isSocialMediaCrawler && !forceGenerate) {
      return NextResponse.redirect(decodeURIComponent(imageUrl), 302);
    }

    // Handle image URL safely - provide a fallback if missing or invalid
    let safeImageUrl = null;
    if (imageUrl) {
      try {
        safeImageUrl = decodeURIComponent(imageUrl);
        // Validate the URL format
        new URL(safeImageUrl);
      } catch (err) {
        console.error('Invalid image URL:', imageUrl, err);
        safeImageUrl = null;
      }
    }

    // Default fallback image - the site logo
    const fallbackImage = 'https://reviewit.gy/logo.png';

    // Generate star rating display
    const fullStars = Math.floor(Number(rating));
    const hasHalfStar = Number(rating) - fullStars >= 0.5;

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#fff',
            position: 'relative',
          }}
        >
          {/* Background with product image - only use as blur background if available */}
          {safeImageUrl && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: `url(${safeImageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                filter: 'blur(8px)',
                opacity: 0.2,
              }}
            />
          )}

          {/* Overlay to ensure text readability */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
            }}
          />

          {/* Content container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '40px',
              position: 'relative',
              width: '100%',
              height: '100%',
            }}
          >
            {/* Logo and site name in smaller size */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                position: 'absolute',
                top: '20px',
                left: '20px',
              }}
            >
              <img
                src="https://reviewit.gy/logo.png"
                alt="Review It Logo"
                width="36"
                height="36"
              />
              <p
                style={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  color: '#000',
                  marginLeft: '8px',
                }}
              >
                reviewit.gy
              </p>
            </div>

            {/* Product image - with flexible aspect ratio handling */}
            <div
              style={{
                width: '400px',
                height: '300px',  // Slightly reduced height
                borderRadius: '12px',
                overflow: 'hidden',
                marginBottom: '24px',
                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.15)',
                border: '4px solid white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <img
                src={safeImageUrl || fallbackImage}
                alt={title}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',  // This ensures the image maintains its aspect ratio
                  backgroundColor: 'white',
                }}
              />
            </div>

            {/* Product title */}
            <h1
              style={{
                fontSize: '48px',
                fontWeight: 'bold',
                color: '#000',
                textAlign: 'center',
                marginBottom: '16px',
                maxWidth: '90%',
              }}
            >
              {decodeURIComponent(title)}
            </h1>

            {/* Rating display */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '16px',
              }}
            >
              {/* Star rating */}
              <div style={{ display: 'flex', marginRight: '16px' }}>
                {Array.from({ length: 5 }).map((_, i) => {
                  const isFilled = i < fullStars;
                  const isHalf = !isFilled && i === fullStars && hasHalfStar;

                  return (
                    <svg
                      key={i}
                      width="36"
                      height="36"
                      viewBox="0 0 24 24"
                      fill={isFilled ? '#FFD700' : (isHalf ? 'url(#halfGradient)' : 'none')}
                      stroke="#FFD700"
                      strokeWidth="1"
                    >
                      <defs>
                        <linearGradient id="halfGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="50%" stopColor="#FFD700" />
                          <stop offset="50%" stopColor="transparent" />
                        </linearGradient>
                      </defs>
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                    </svg>
                  );
                })}
              </div>

              {/* Numeric rating */}
              <p
                style={{
                  fontSize: '32px',
                  fontWeight: 'bold',
                  color: '#000',
                }}
              >
                {rating}/5
              </p>
            </div>

            {/* Review count */}
            <p
              style={{
                fontSize: '24px',
                color: '#444',
                fontWeight: 'medium',
              }}
            >
              Based on {reviews} {Number(reviews) === 1 ? 'review' : 'reviews'}
            </p>
          </div>
        </div>
      ),
      {
        width: 800,
        height: 420,
      }
    );
  } catch (e) {
    console.error('Error generating product OG image:', e);

    // Fallback to a simple text-based OG image if there's an error
    try {
      return new ImageResponse(
        (
          <div
            style={{
              height: '100%',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#fff',
              padding: '40px',
            }}
          >
            <img
              src="https://reviewit.gy/logo.png"
              alt="Review It Logo"
              width="120"
              height="120"
              style={{ marginBottom: '20px' }}
            />
            <h1
              style={{
                fontSize: '48px',
                fontWeight: 'bold',
                color: '#000',
                textAlign: 'center',
                marginBottom: '16px',
              }}
            >
              Review It
            </h1>
            <p
              style={{
                fontSize: '24px',
                color: '#444',
                textAlign: 'center',
              }}
            >
              Share and read reviews on anything
            </p>
          </div>
        ),
        {
          width: 800,
          height: 420,
        }
      );
    } catch (fallbackError) {
      console.error('Even fallback OG image generation failed:', fallbackError);
      return new Response('Failed to generate OG image', { status: 500 });
    }
  }
}
