import { NextRequest, NextResponse } from 'next/server';
import { recordProductView } from '@/app/util/analyticsUtils';
import { getAuth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const { productId, source, deviceType } = await request.json();

        if (!productId) {
            return NextResponse.json(
                { error: 'Product ID is required' },
                { status: 400 }
            );
        }

        // Record the view event
        await recordProductView(
            productId,
            userId || undefined,
            source,
            deviceType
        );

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('[Analytics API] Error logging view:', error);
        return NextResponse.json(
            { error: 'Failed to log view' },
            { status: 500 }
        );
    }
} 