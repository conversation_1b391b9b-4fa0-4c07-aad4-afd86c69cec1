import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { getAuth } from '@clerk/nextjs/server';

interface EventBody {
    productId: string;
    eventType: string;
    metadata?: Record<string, any>;
}

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const eventData: EventBody = await request.json();

        // Validate required fields
        if (!eventData.productId || !eventData.eventType) {
            return NextResponse.json(
                { error: 'Product ID and event type are required' },
                { status: 400 }
            );
        }

        // Create event using raw SQL
        const eventQuery = `
            INSERT INTO "UserProductInteraction" (
                "id", "userId", "productId", "lastViewed", "viewCount", 
                "hasReviewed", "hasLiked", "averageTimeSpent"
            ) VALUES (
                gen_random_uuid(), '${userId || 'anonymous'}', '${eventData.productId}', 
                NOW(), 1, 
                ${eventData.eventType === 'review' ? 'true' : 'false'}, 
                ${eventData.eventType === 'like' ? 'true' : 'false'}, 
                0
            ) RETURNING *
        `;

        const event = await prisma.$queryRawUnsafe(eventQuery) as any[];

        // If this is a review event, update the hasReviewed flag
        if (eventData.eventType === 'review' && userId) {
            const updateQuery = `
                UPDATE "UserProductInteraction" 
                SET "hasReviewed" = true
                WHERE "userId" = '${userId}' AND "productId" = '${eventData.productId}'
                RETURNING *
            `;

            await prisma.$queryRawUnsafe(updateQuery);
        }

        // If this is a like event, update the hasLiked flag
        if (eventData.eventType === 'like' && userId) {
            const updateQuery = `
                UPDATE "UserProductInteraction" 
                SET "hasLiked" = true
                WHERE "userId" = '${userId}' AND "productId" = '${eventData.productId}'
                RETURNING *
            `;

            await prisma.$queryRawUnsafe(updateQuery);
        }

        return NextResponse.json({ success: true, event: event[0] });
    } catch (error) {
        console.error('[Analytics API] Error logging event:', error);
        return NextResponse.json(
            { error: 'Failed to log event' },
            { status: 500 }
        );
    }
} 