import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { Prisma } from '@prisma/client';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const businessId = searchParams.get('businessId');
    
    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    // Check if the user is authenticated
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Verify user has access to this business
    const userBusiness = await prisma.business.findFirst({
      where: {
        id: businessId,
        ownerId: userId
      }
    });
    
    if (!userBusiness) {
      return NextResponse.json(
        { error: 'Unauthorized to access this business data' },
        { status: 403 }
      );
    }

    // Get products for this business
    const products = await prisma.product.findMany({
      where: {
        businessId,
        isDeleted: false,
      },
      select: {
        id: true,
        name: true,
      }
    });

    const productIds = products.map(p => p.id);

    // Get product views
    const productViews = await prisma.productViewEvent.groupBy({
      by: ['productId'],
      where: {
        productId: {
          in: productIds
        }
      },
      _count: {
        id: true
      }
    });

    // Get product conversions (reviews and interactions)
    const reviews = await prisma.review.groupBy({
      by: ['productId'],
      where: {
        productId: {
          in: productIds
        },
        isDeleted: false
      },
      _count: {
        id: true
      }
    });

    // Get interactions (clicks on contact/buy buttons)
    const interactions = await prisma.$queryRaw`
      SELECT 
        product_id as productId, 
        COUNT(*) as count
      FROM 
        ProductViewEvent
      WHERE 
        product_id IN (${Prisma.join(productIds)})
        AND event_type IN ('CONTACT_CLICK', 'BUY_CLICK')
      GROUP BY 
        product_id
    `;

    // Combine all data
    const productPerformance = products.map(product => {
      const views = productViews.find(p => p.productId === product.id)?._count.id || 0;
      
      // Real conversion data from different sources
      const reviewCount = reviews.find((r) => r.productId === product.id)?._count?.id || 0;
      const interactionCount = Number((interactions as any[]).find((i: any) => i.productId === product.id)?.count || 0);
      
      // Total conversions (sum of all conversion types)
      const conversions = reviewCount + interactionCount;
      
      // Calculate real conversion rate
      const conversionRate = views > 0 ? (conversions / views) * 100 : 0;
      
      return {
        id: product.id,
        name: product.name,
        views,
        conversions,
        conversionRate: parseFloat(conversionRate.toFixed(2))
      };
    });

    return NextResponse.json({
      success: true,
      data: productPerformance
    });
  } catch (error) {
    console.error('Error fetching product conversions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product conversions' },
      { status: 500 }
    );
  }
}


