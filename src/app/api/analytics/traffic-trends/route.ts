import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';

/**
 * GET /api/analytics/traffic-trends
 * Fetches traffic source trends data for a specific business by date
 */
export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get query parameters
        const searchParams = req.nextUrl.searchParams;
        const businessId = searchParams.get('id');
        const startDate = searchParams.get('start');
        const endDate = searchParams.get('end');
        const days = parseInt(searchParams.get('days') || '14'); // Default to 14 days

        if (!businessId) {
            return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
        }

        // Verify user has access to this business
        const business = await prisma.business.findUnique({
            where: {
                id: businessId,
                ownerId: userId,
            },
        });

        if (!business) {
            return NextResponse.json({ error: 'Business not found or access denied' }, { status: 403 });
        }

        // Get products for this business
        const products = await prisma.product.findMany({
            where: { businessId, isDeleted: false },
            select: { id: true }
        });

        const productIds = products.map(p => p.id);
        
        // Use provided dates or default to last N days
        const end = endDate ? new Date(endDate) : new Date();
        const start = startDate ? new Date(startDate) : new Date(end);
        start.setDate(start.getDate() - (startDate ? 0 : days));

        // Get traffic source data by date
        const trafficTrendsData = await prisma.$queryRaw`
            SELECT 
                DATE("timestamp") as date,
                source,
                COUNT(*) as views,
                COUNT(DISTINCT "sessionId") as unique_visitors
            FROM "ProductViewEvent"
            WHERE "productId" = ANY(${productIds}::text[])
            AND "timestamp" BETWEEN ${start.toISOString()}::timestamp AND ${end.toISOString()}::timestamp
            AND source IS NOT NULL
            GROUP BY DATE("timestamp"), source
            ORDER BY date ASC, views DESC
        `;

        // Format the data for frontend consumption
        const formattedData = formatTrafficTrends(trafficTrendsData as any[]);

        return NextResponse.json(formattedData);

    } catch (error) {
        console.error('Error fetching traffic trends:', error);

        // Return error response
        return NextResponse.json(
            { 
                error: 'Failed to fetch traffic trends', 
                message: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }, 
            { status: 500 }
        );
    }
}

// Define interface for traffic data by date
interface TrafficByDate {
    date: string;
    google: number;
    direct: number;
    social: number;
    other: number;
    total: number;
}

/**
 * Format traffic trends data for frontend consumption
 */
function formatTrafficTrends(rawData: any[]) {
    // Group by date and calculate source totals
    const groupedByDate = rawData.reduce((acc, item) => {
        const date = item.date;
        
        if (!acc[date]) {
            acc[date] = {
                date,
                google: 0,
                direct: 0,
                social: 0,
                other: 0,
                total: 0
            };
        }
        
        // Add counts by source
        const source = item.source.toLowerCase();
        if (source.includes('google')) {
            acc[date].google += Number(item.views);
        } else if (source === 'direct') {
            acc[date].direct += Number(item.views);
        } else if (['facebook', 'twitter', 'instagram', 'linkedin'].some(s => source.includes(s))) {
            acc[date].social += Number(item.views);
        } else {
            acc[date].other += Number(item.views);
        }
        
        // Update total
        acc[date].total += Number(item.views);
        
        return acc;
    }, {} as Record<string, TrafficByDate>);
    
    // Convert to array and sort by date
    return (Object.values(groupedByDate) as TrafficByDate[]).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
    );
}
