import { NextRequest, NextResponse } from 'next/server';
import { getTopAttentionProductsFromCache } from '@/app/util/analytics/admin';
import { getAuth } from '@clerk/nextjs/server';

// Force dynamic rendering since we use request.headers via getAuth
export const dynamic = 'force-dynamic';

// GET /api/analytics/top-attention?limit=5
export async function GET(request: NextRequest) {
  try {
    // Enforce admin authentication (simple check – adjust if you have role helper)
    const { userId, sessionClaims } = getAuth(request);

    // Clerk types don’t include custom metadata; cast to any for safe access
    const meta = (sessionClaims as any)?.metadata || {};

        const isAdmin = meta.role === 'ADMIN' || meta.isAdmin === true;
    if (!userId || !isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const url = new URL(request.url);
    const limitParam = url.searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 5;

    const result = await getTopAttentionProductsFromCache(limit);
    return NextResponse.json(result, { status: result.status });
  } catch (error) {
    console.error('[top-attention] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch top attention data' }, { status: 500 });
  }
}
