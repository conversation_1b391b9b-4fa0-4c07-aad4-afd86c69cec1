import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { getAuth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const { productId, startDate, endDate, interval } = await request.json();

        if (!productId) {
            return NextResponse.json(
                { error: 'Product ID is required' },
                { status: 400 }
            );
        }

        // Check if user has access to this product's analytics
        const product = await prisma.product.findUnique({
            where: { id: productId },
            select: { businessId: true }
        });

        if (!product) {
            return NextResponse.json(
                { error: 'Product not found' },
                { status: 404 }
            );
        }

        // TODO: Check if user is a business admin for this product
        // For now, we'll just check if the user is authenticated

        // Build query conditions
        let viewEventsQuery = `SELECT * FROM "ProductViewEvent" WHERE "productId" = '${productId}'`;
        let interactionsQuery = `SELECT * FROM "UserProductInteraction" WHERE "productId" = '${productId}'`;

        if (startDate) {
            viewEventsQuery += ` AND "timestamp" >= '${new Date(startDate).toISOString()}'`;
            interactionsQuery += ` AND "lastViewed" >= '${new Date(startDate).toISOString()}'`;
        }

        if (endDate) {
            viewEventsQuery += ` AND "timestamp" <= '${new Date(endDate).toISOString()}'`;
            interactionsQuery += ` AND "lastViewed" <= '${new Date(endDate).toISOString()}'`;
        }

        viewEventsQuery += ` ORDER BY "timestamp" ASC`;
        interactionsQuery += ` ORDER BY "lastViewed" ASC`;

        // Get view events for the product
        const viewEvents = await prisma.$queryRawUnsafe(viewEventsQuery) as any[];

        // Get user interactions
        const interactions = await prisma.$queryRawUnsafe(interactionsQuery) as any[];

        // Group data by interval
        const intervalMs = getIntervalMs(interval);
        const groupedData = groupDataByInterval(viewEvents, interactions, intervalMs);

        return NextResponse.json(groupedData);
    } catch (error) {
        console.error('[Analytics API] Error getting trends:', error);
        return NextResponse.json(
            { error: 'Failed to get analytics trends' },
            { status: 500 }
        );
    }
}

function getIntervalMs(interval: string): number {
    switch (interval) {
        case 'hour':
            return 60 * 60 * 1000;
        case 'day':
            return 24 * 60 * 60 * 1000;
        case 'week':
            return 7 * 24 * 60 * 60 * 1000;
        case 'month':
            return 30 * 24 * 60 * 60 * 1000;
        default:
            return 24 * 60 * 60 * 1000; // Default to daily
    }
}

function groupDataByInterval(viewEvents: any[], interactions: any[], intervalMs: number) {
    const groupedData: Record<string, any> = {};

    // Group view events
    viewEvents.forEach(event => {
        const timestamp = new Date(event.timestamp).getTime();
        const intervalStart = new Date(Math.floor(timestamp / intervalMs) * intervalMs).toISOString();

        if (!groupedData[intervalStart]) {
            groupedData[intervalStart] = {
                views: 0,
                uniqueVisitors: new Set(),
                totalDuration: 0,
                totalScrollDepth: 0,
                interactions: {}
            };
        }

        groupedData[intervalStart].views++;
        if (event.userId || event.sessionId) {
            groupedData[intervalStart].uniqueVisitors.add(event.userId || event.sessionId);
        }
        if (event.duration) {
            groupedData[intervalStart].totalDuration += event.duration;
        }
        if (event.scrollDepth) {
            groupedData[intervalStart].totalScrollDepth += event.scrollDepth;
        }
    });

    // Group interactions
    interactions.forEach(interaction => {
        const timestamp = new Date(interaction.lastViewed).getTime();
        const intervalStart = new Date(Math.floor(timestamp / intervalMs) * intervalMs).toISOString();

        if (!groupedData[intervalStart]) {
            groupedData[intervalStart] = {
                views: 0,
                uniqueVisitors: new Set(),
                totalDuration: 0,
                totalScrollDepth: 0,
                interactions: {}
            };
        }

        if (!groupedData[intervalStart].interactions[interaction.eventType]) {
            groupedData[intervalStart].interactions[interaction.eventType] = 0;
        }
        groupedData[intervalStart].interactions[interaction.eventType]++;
    });

    // Convert Sets to counts and calculate averages
    const result: Record<string, any> = {};
    Object.entries(groupedData).forEach(([intervalStart, data]) => {
        result[intervalStart] = {
            views: data.views,
            uniqueVisitors: data.uniqueVisitors.size,
            averageDuration: data.views > 0 ? data.totalDuration / data.views : 0,
            averageScrollDepth: data.views > 0 ? data.totalScrollDepth / data.views : 0,
            interactions: data.interactions
        };
    });

    return result;
} 