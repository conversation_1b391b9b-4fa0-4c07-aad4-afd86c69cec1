import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { getAuth } from '@clerk/nextjs/server';
import { iProductViewEvent, iUserProductInteraction } from '@/app/util/Interfaces';

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const { productId, startDate, endDate } = await request.json();

        if (!productId) {
            return NextResponse.json(
                { error: 'Product ID is required' },
                { status: 400 }
            );
        }

        // Check if user has access to this product's analytics
        const product = await prisma.product.findUnique({
            where: { id: productId },
            select: { businessId: true }
        });

        if (!product) {
            return NextResponse.json(
                { error: 'Product not found' },
                { status: 404 }
            );
        }

        // TODO: Check if user is a business admin for this product
        // For now, we'll just check if the user is authenticated

        // Build query conditions
        let viewEventsQuery = `SELECT * FROM "ProductViewEvent" WHERE "productId" = '${productId}'`;
        let interactionsQuery = `SELECT * FROM "UserProductInteraction" WHERE "productId" = '${productId}'`;

        if (startDate) {
            viewEventsQuery += ` AND "timestamp" >= '${new Date(startDate).toISOString()}'`;
            interactionsQuery += ` AND "lastViewed" >= '${new Date(startDate).toISOString()}'`;
        }

        if (endDate) {
            viewEventsQuery += ` AND "timestamp" <= '${new Date(endDate).toISOString()}'`;
            interactionsQuery += ` AND "lastViewed" <= '${new Date(endDate).toISOString()}'`;
        }

        viewEventsQuery += ` ORDER BY "timestamp" DESC`;
        interactionsQuery += ` ORDER BY "lastViewed" DESC`;

        // Get view events for the product
        const viewEvents = await prisma.$queryRawUnsafe(viewEventsQuery) as any[];

        // Get user interactions
        const interactions = await prisma.$queryRawUnsafe(interactionsQuery) as any[];

        // Calculate metrics
        const totalViews = viewEvents.length;
        const uniqueVisitors = new Set(viewEvents.map(event => event.userId || event.sessionId)).size;
        const averageDuration = viewEvents.reduce((sum, event) => sum + (event.duration || 0), 0) / totalViews || 0;
        const totalInteractions = interactions.length;

        // Group interactions by type
        const interactionsByType = interactions.reduce((acc: Record<string, number>, interaction) => {
            const type = interaction.hasReviewed ? 'review' : (interaction.hasLiked ? 'like' : 'view');
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        // Get referral sources
        const referralSources = viewEvents.reduce((acc: Record<string, number>, event) => {
            if (event.source) {
                acc[event.source] = (acc[event.source] || 0) + 1;
            }
            return acc;
        }, {} as Record<string, number>);

        // Get device types
        const deviceTypes = viewEvents.reduce((acc: Record<string, number>, event) => {
            if (event.deviceType) {
                acc[event.deviceType] = (acc[event.deviceType] || 0) + 1;
            }
            return acc;
        }, {} as Record<string, number>);

        return NextResponse.json({
            totalViews,
            uniqueVisitors,
            averageDuration,
            totalInteractions,
            interactionsByType,
            referralSources,
            deviceTypes,
            viewEvents,
            interactions
        });
    } catch (error) {
        console.error('[Analytics API] Error getting stats:', error);
        return NextResponse.json(
            { error: 'Failed to get analytics stats' },
            { status: 500 }
        );
    }
} 