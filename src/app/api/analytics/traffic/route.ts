import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { getTrafficSourcesFromDB } from '@/app/util/databaseAnalytics';
import { iTrafficSource } from '@/app/util/Interfaces';

export const dynamic = 'force-dynamic';

/**
 * GET /api/analytics/traffic
 * Fetches traffic source data for a specific business
 */
export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get query parameters
        const searchParams = req.nextUrl.searchParams;
        const businessId = searchParams.get('id');
        const startDate = searchParams.get('start');
        const endDate = searchParams.get('end');

        if (!businessId) {
            return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
        }

        // Verify user has access to this business
        const business = await prisma.business.findUnique({
            where: {
                id: businessId,
                ownerId: userId,
            },
        });

        if (!business) {
            return NextResponse.json({ error: 'Business not found or access denied' }, { status: 403 });
        }

        // Use real database analytics instead of mock data
        const period = {
            startDate: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            endDate: endDate ? new Date(endDate) : new Date()
        };

        const trafficSources = await getTrafficSourcesFromDB(businessId, period);
        return NextResponse.json(trafficSources);

    } catch (error) {
        console.error('Error fetching traffic sources:', error);

        // Return error response instead of mock data
        return NextResponse.json(
            { 
                error: 'Failed to fetch traffic sources', 
                message: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }, 
            { status: 500 }
        );
    }
}

