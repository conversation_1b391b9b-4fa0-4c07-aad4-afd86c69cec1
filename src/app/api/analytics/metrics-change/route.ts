import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { subDays } from 'date-fns';

export const dynamic = 'force-dynamic';

/**
 * GET /api/analytics/metrics-change
 * Calculates percentage changes for key metrics compared to the previous period
 */
export async function GET(req: NextRequest) {
  try {
    const { userId } = getAuth(req);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const businessId = searchParams.get('businessId');
    const days = parseInt(searchParams.get('days') || '14');

    if (!businessId) {
      return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
    }

    // Verify user has access to this business
    const business = await prisma.business.findFirst({
      where: {
        id: businessId,
        ownerId: userId
      }
    });

    if (!business) {
      return NextResponse.json({ error: 'Unauthorized to access this business data' }, { status: 403 });
    }

    // Calculate date ranges
    const endDate = new Date();
    const startDate = subDays(endDate, days);
    const previousEndDate = startDate;
    const previousStartDate = subDays(previousEndDate, days);

    // Get current period metrics
    const currentPeriodViews = await prisma.productViewEvent.count({
      where: {
        product: {
          businessId
        },
        timestamp: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    const currentPeriodUniqueVisitors = await prisma.productViewEvent.groupBy({
      by: ['userId'],
      where: {
        product: {
          businessId
        },
        timestamp: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    const currentPeriodReviews = await prisma.review.count({
      where: {
        product: {
          businessId
        },
        createdDate: {
          gte: startDate,
          lte: endDate
        },
        isDeleted: false
      }
    });

    // Get previous period metrics
    const previousPeriodViews = await prisma.productViewEvent.count({
      where: {
        product: {
          businessId
        },
        timestamp: {
          gte: previousStartDate,
          lte: previousEndDate
        }
      }
    });

    const previousPeriodUniqueVisitors = await prisma.productViewEvent.groupBy({
      by: ['userId'],
      where: {
        product: {
          businessId
        },
        timestamp: {
          gte: previousStartDate,
          lte: previousEndDate
        }
      }
    });

    const previousPeriodReviews = await prisma.review.count({
      where: {
        product: {
          businessId
        },
        createdDate: {
          gte: previousStartDate,
          lte: previousEndDate
        },
        isDeleted: false
      }
    });

    // Calculate conversion rates
    const currentConversionRate = currentPeriodUniqueVisitors.length > 0 
      ? (currentPeriodReviews / currentPeriodUniqueVisitors.length) * 100 
      : 0;
    
    const previousConversionRate = previousPeriodUniqueVisitors.length > 0 
      ? (previousPeriodReviews / previousPeriodUniqueVisitors.length) * 100 
      : 0;

    // Calculate percentage changes
    const calculateChange = (current: number, previous: number): number => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const changes = {
      totalViews: calculateChange(currentPeriodViews, previousPeriodViews),
      uniqueVisitors: calculateChange(currentPeriodUniqueVisitors.length, previousPeriodUniqueVisitors.length),
      totalReviews: calculateChange(currentPeriodReviews, previousPeriodReviews),
      conversionRate: calculateChange(currentConversionRate, previousConversionRate)
    };

    return NextResponse.json({
      success: true,
      data: changes
    });
  } catch (error) {
    console.error('Error calculating metrics changes:', error);
    return NextResponse.json(
      { error: 'Failed to calculate metrics changes' },
      { status: 500 }
    );
  }
}
