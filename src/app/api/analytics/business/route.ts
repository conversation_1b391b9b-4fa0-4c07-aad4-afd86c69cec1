import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { getBusinessAnalyticsFromDB } from '@/app/util/databaseAnalytics';
import { iBusinessAnalyticsEnhanced } from '@/app/util/Interfaces';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

/**
 * GET /api/analytics/business
 * Fetches business analytics data for a specific business
 */
export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get query parameters
        const searchParams = req.nextUrl.searchParams;
        const businessId = searchParams.get('id');
        const startDate = searchParams.get('start');
        const endDate = searchParams.get('end');

        if (!businessId) {
            return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
        }

        // Verify user has access to this business
        const business = await prisma.business.findUnique({
            where: {
                id: businessId,
                ownerId: userId,
            },
        });

        if (!business) {
            return NextResponse.json({ error: 'Business not found or access denied' }, { status: 403 });
        }

        // Use real database analytics instead of mock data
        const period = {
            startDate: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Default to last 30 days
            endDate: endDate ? new Date(endDate) : new Date()
        };

        const analytics = await getBusinessAnalyticsFromDB(businessId, period);
        return NextResponse.json(analytics);

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        console.error('Error fetching business analytics:', {
            message: errorMessage,
            stack: error instanceof Error ? error.stack : 'No stack available',
            details: error
        });

        // Return error response instead of mock data
        return NextResponse.json(
            { 
                error: 'Failed to fetch analytics data', 
                message: errorMessage,
                timestamp: new Date().toISOString()
            }, 
            { status: 500 }
        );
    }
}

