import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { subDays } from 'date-fns';

export const dynamic = 'force-dynamic';

/**
 * GET /api/analytics/notifications
 * Returns analytics data for notifications
 */
export async function GET(req: NextRequest) {
  try {
    const { userId } = getAuth(req);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const businessId = searchParams.get('businessId');
    const days = parseInt(searchParams.get('days') || '30');

    if (!businessId) {
      return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
    }

    // Verify user has access to this business
    const business = await prisma.business.findFirst({
      where: {
        id: businessId,
        ownerId: userId
      }
    });

    if (!business) {
      return NextResponse.json({ error: 'Unauthorized to access this business data' }, { status: 403 });
    }

    // Calculate date ranges
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get notification counts by type
    const notificationCounts = await prisma.$queryRaw`
      SELECT 
        type, 
        COUNT(*) as count
      FROM 
        Notification
      WHERE 
        business_id = ${businessId}
        AND created_at >= ${startDate}
        AND created_at <= ${endDate}
      GROUP BY 
        type
    `;

    // Get read vs unread counts
    const readUnreadCounts = await prisma.$queryRaw`
      SELECT 
        read, 
        COUNT(*) as count
      FROM 
        Notification
      WHERE 
        business_id = ${businessId}
        AND created_at >= ${startDate}
        AND created_at <= ${endDate}
      GROUP BY 
        read
    `;

    // Get notification counts per day
    const notificationsPerDay = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date, 
        COUNT(*) as count
      FROM 
        Notification
      WHERE 
        business_id = ${businessId}
        AND created_at >= ${startDate}
        AND created_at <= ${endDate}
      GROUP BY 
        DATE(created_at)
      ORDER BY 
        date ASC
    `;

    // Get click-through rate (notifications that were clicked)
    const clickedResult = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count
      FROM 
        Notification
      WHERE 
        business_id = ${businessId}
        AND clicked = true
        AND created_at >= ${startDate}
        AND created_at <= ${endDate}
    `;
    
    const totalResult = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count
      FROM 
        Notification
      WHERE 
        business_id = ${businessId}
        AND created_at >= ${startDate}
        AND created_at <= ${endDate}
    `;
    
    const clickedCount = Number((clickedResult as any)[0]?.count || 0);
    const totalCount = Number((totalResult as any)[0]?.count || 0);

    const clickThroughRate = totalCount > 0 ? (clickedCount / totalCount) * 100 : 0;

    // Format the response
    const formattedNotificationCounts = (notificationCounts as any[]).map((item: any) => ({
      type: item.type,
      count: Number(item.count)
    }));

    const readCount = Number((readUnreadCounts as any[]).find((item: any) => item.read === true)?.count || 0);
    const unreadCount = Number((readUnreadCounts as any[]).find((item: any) => item.read === false)?.count || 0);

    return NextResponse.json({
      success: true,
      data: {
        totalNotifications: totalCount,
        byType: formattedNotificationCounts,
        readVsUnread: {
          read: readCount,
          unread: unreadCount
        },
        notificationsPerDay,
        clickThroughRate
      }
    });
  } catch (error) {
    console.error('Error fetching notification analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification analytics' },
      { status: 500 }
    );
  }
}
