import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { getAuth } from '@clerk/nextjs/server';
import { iProductViewEvent } from '@/app/util/Interfaces';

export async function POST(request: NextRequest) {
    try {
        const { userId } = getAuth(request);
        const eventData = await request.json();

        // Validate required fields
        if (!eventData.productId) {
            return NextResponse.json(
                { error: 'Product ID is required' },
                { status: 400 }
            );
        }

        // Create view event using raw SQL
        const viewEventQuery = `
            INSERT INTO "ProductViewEvent" (
                "id", "productId", "userId", "sessionId", "timestamp", 
                "duration", "source", "deviceType", "isNewUser", "isThrottled"
            ) VALUES (
                gen_random_uuid(), '${eventData.productId}', 
                ${userId ? `'${userId}'` : 'NULL'}, 
                '${eventData.sessionId}', NOW(), 
                ${eventData.duration || 0}, 
                '${eventData.source || 'direct'}', 
                '${eventData.deviceType || 'desktop'}', 
                ${eventData.isNewUser ? 'true' : 'false'}, 
                ${eventData.isThrottled ? 'true' : 'false'}
            ) RETURNING *
        `;

        const viewEvent = await prisma.$queryRawUnsafe(viewEventQuery) as any[];

        // Update or create user interaction if user is authenticated
        if (userId) {
            // Check if interaction exists
            const checkInteractionQuery = `
                SELECT * FROM "UserProductInteraction" 
                WHERE "userId" = '${userId}' AND "productId" = '${eventData.productId}'
            `;

            const existingInteraction = await prisma.$queryRawUnsafe(checkInteractionQuery) as any[];

            if (existingInteraction.length > 0) {
                // Update existing interaction
                const updateQuery = `
                    UPDATE "UserProductInteraction" 
                    SET "lastViewed" = NOW(), "viewCount" = "viewCount" + 1
                    WHERE "userId" = '${userId}' AND "productId" = '${eventData.productId}'
                    RETURNING *
                `;

                await prisma.$queryRawUnsafe(updateQuery);
            } else {
                // Create new interaction
                const createQuery = `
                    INSERT INTO "UserProductInteraction" (
                        "id", "userId", "productId", "lastViewed", "viewCount", 
                        "hasReviewed", "hasLiked", "averageTimeSpent"
                    ) VALUES (
                        gen_random_uuid(), '${userId}', '${eventData.productId}', 
                        NOW(), 1, false, false, 0
                    ) RETURNING *
                `;

                await prisma.$queryRawUnsafe(createQuery);
            }
        }

        return NextResponse.json({ success: true, event: viewEvent[0] });
    } catch (error) {
        console.error('[Analytics API] Error tracking event:', error);
        return NextResponse.json(
            { error: 'Failed to track event' },
            { status: 500 }
        );
    }
} 