import { prisma } from "@/app/util/prismaClient";
import { iProduct } from "@/app/util/Interfaces";
import { getProductSearchFromCache } from "@/app/util/databaseAnalytics";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query } = body;

    // Use cached search function
    const result = await getProductSearchFromCache(query);
    
    // Ensure we have a valid array
    const products = Array.isArray(result) ? result as iProduct[] : [];

    return NextResponse.json({
      success: true,
      status: 200,
      dataLength: products.length,
      data: products,
    });
  } catch (error) {
    let e = error as Error;
    console.error("Error in product search:", e.message);
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}
