import { NextRequest } from 'next/server';
import { prisma } from '@/app/util/prismaClient';

export async function trackWidgetView(widgetId: string, req: NextRequest) {
  try {
    const referrer = req.headers.get('referer') || req.headers.get('x-referrer');
    const userAgent = req.headers.get('user-agent');

    // Update view count
    await prisma.widget.update({
      where: { id: widgetId },
      data: { viewCount: { increment: 1 }, lastUsed: new Date() }
    });

    // Store detailed analytics
    const today = new Date().toISOString().split('T')[0];
    const analytics = await prisma.widgetAnalytics.findUnique({ where: { widgetId } });

    await prisma.widgetAnalytics.upsert({
      where: { widgetId },
      create: { widgetId, dailyViews: { [today]: 1 } },
      update: {
        dailyViews: {
          ...(analytics?.dailyViews as Record<string, number> || {}),
          [today]: ((analytics?.dailyViews as Record<string, number>)?.[today] || 0) + 1
        }
      }
    });
  } catch (error) {
    console.error('Failed to track widget view:', error);
    // Analytics failure shouldn't break widget loading
  }
}
