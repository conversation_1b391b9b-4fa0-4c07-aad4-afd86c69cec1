import { NextRequest } from 'next/server';

export interface EmbedInfo {
  referrerDomain: string;
  actualEmbeddingDomain: string;
  embedDomain: string | null;
  embedContext: string | null;
  frameDepth: string | null;
  parentReferrer: string | null;
  origin: string | null;
  referrer: string | null;
  isPreview: boolean;
  userAgent: string | null;
  timestamp: string;
  ip: string;
}

export function getEmbedInfo(req: NextRequest): EmbedInfo {
  const referrer = req.headers.get('referer') || req.headers.get('x-referrer');
  const origin = req.headers.get('origin');
  const userAgent = req.headers.get('user-agent');
  const url = new URL(req.url);
  const isPreview = url.searchParams.get('preview') === 'true';
  const parentReferrer = req.headers.get('x-parent-referrer');
  const embedContext = req.headers.get('x-embed-context');
  const frameDepth = req.headers.get('x-frame-depth');
  // Get embedDomain from URL parameters (passed by embed.js) or headers
  const embedDomain = url.searchParams.get('embedDomain') || req.headers.get('x-embed-domain');

  let referrerDomain = 'direct-access';
  let actualEmbeddingDomain = 'unknown';

  if (referrer) {
    try {
      const referrerUrl = new URL(referrer);
      referrerDomain = referrerUrl.hostname;
    } catch {
      referrerDomain = 'invalid-referrer';
    }
  }

  if (embedDomain && embedDomain !== 'unknown') {
    actualEmbeddingDomain = embedDomain;
  } else if (parentReferrer && parentReferrer !== 'cross-origin-iframe') {
    try {
      const parentUrl = new URL(parentReferrer);
      actualEmbeddingDomain = parentUrl.hostname;
    } catch {
      actualEmbeddingDomain = 'invalid-parent-referrer';
    }
  } else if (embedContext === 'embedded-iframe') {
    actualEmbeddingDomain = 'cross-origin-embedded';
  } else {
    actualEmbeddingDomain = referrerDomain;
  }

  const timestamp = new Date().toISOString();
  const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

  return {
    referrerDomain,
    actualEmbeddingDomain,
    embedDomain,
    embedContext,
    frameDepth,
    parentReferrer,
    origin,
    referrer,
    isPreview,
    userAgent,
    timestamp,
    ip
  };
}

export function logWidgetRequest(widgetId: string, info: EmbedInfo) {
  // Removed debug console.log statement
}
