// In-memory cache for domain registrations
// This avoids needing a database migration
const domainRegistrationCache = new Map<string, Set<string>>();

// Store domain in memory cache
export function storeDomainInfo(widgetId: string, domain: string) {
  if (!domainRegistrationCache.has(widgetId)) {
    domainRegistrationCache.set(widgetId, new Set());
  }
  
  const domains = domainRegistrationCache.get(widgetId);
  if (domains) {
    domains.add(domain);
  }
}

// Check if domain is registered for widget
export function isDomainRegistered(widgetId: string, domain: string): boolean {
  const domains = domainRegistrationCache.get(widgetId);
  if (!domains) return false;
  return domains.has(domain);
}