import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { widgetCorsMiddleware } from '@/app/util/corsMiddleware';
import { storeDomainInfo } from '../domainRegistry';

export async function POST(req: NextRequest): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return widgetCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = widgetCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const body = await req.json();
    const { widgetId, embedDomain, referrer, userAgent } = body;

    if (!widgetId || !embedDomain) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    // Store the domain information in memory cache
    storeDomainInfo(widgetId, embedDomain);
    
    // Also update the widget's analytics to track this domain as a referrer
    // This leverages the existing analytics system that's already working
    await updateWidgetAnalytics(widgetId, embedDomain, referrer);

    return NextResponse.json(
      { success: true },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Domain registration error:', error);
    return NextResponse.json(
      { error: 'Registration failed' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export function OPTIONS(req: NextRequest): NextResponse {
  const corsResult = widgetCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  // If it's just headers, return a proper NextResponse
  return new NextResponse(null, { status: 200, headers: corsResult });
}



// Update widget analytics to track this domain as a referrer
async function updateWidgetAnalytics(widgetId: string, domain: string, referrer: string | null) {
  try {
    // Get existing analytics
    const analytics = await prisma.widgetAnalytics.findUnique({
      where: { widgetId }
    });

    if (analytics) {
      // Update top referrers
      const topReferrers = analytics.topReferrers as Record<string, number>;
      topReferrers[domain] = (topReferrers[domain] || 0) + 1;

      // Update analytics record
      await prisma.widgetAnalytics.update({
        where: { widgetId },
        data: {
          topReferrers,
          lastUpdated: new Date()
        }
      });
    } else {
      // Create new analytics record
      await prisma.widgetAnalytics.create({
        data: {
          widgetId,
          topReferrers: { [domain]: 1 },
          dailyViews: {},
          dailyClicks: {},
          dailyConversions: {},
          blockedEvents: {},
          averageLoadTime: 0,
          errorCount: 0,
          lastUpdated: new Date()
        }
      });
    }
  } catch (error) {
    console.error('Failed to update widget analytics:', error);
  }
}
