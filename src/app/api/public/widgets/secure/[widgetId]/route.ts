import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { widgetCorsMiddleware } from '@/app/util/corsMiddleware';
import { checkWidgetRateLimit } from '@/app/util/rateLimiting';
import { SecureWidgetRequest, WidgetTokenPayload, WidgetSecurityLevel } from '@/app/util/Interfaces';
import { $Enums } from '@prisma/client';
import jwt from 'jsonwebtoken';

export async function GET(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Get CORS headers for all responses
  const corsResult = widgetCorsMiddleware(req);
  const corsHeaders: Record<string, string> = corsResult instanceof NextResponse ? {} : corsResult;

  try {
    // Get token from Authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        {
          status: 401,
          headers: corsHeaders
        }
      );
    }

    // Get domain from header
    const domain = req.headers.get('x-domain') || req.headers.get('x-embed-domain');
    const referrer = req.headers.get('referer') || req.headers.get('referrer');

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain header required' },
        {
          status: 400,
          headers: corsHeaders
        }
      );
    }

    // Get widget and verify it's secure
    const widget = await prisma.widget.findUnique({
      where: {
        id: params.widgetId,
        isActive: true,
        securityLevel: 'SECURE'
      },
      select: {
        id: true,
        securityLevel: true,
        apiKey: true,
        maxRequestsPerHour: true,
        allowedDomains: true,
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json(
        { error: 'Secure widget not found or inactive' },
        {
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Verify JWT token
    let tokenPayload: WidgetTokenPayload;
    try {
      const jwtSecret = widget.apiKey || process.env.WIDGET_JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT secret not configured');
      }

      tokenPayload = jwt.verify(token, jwtSecret) as WidgetTokenPayload;
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        {
          status: 401,
          headers: corsHeaders
        }
      );
    }

    // Verify token belongs to this widget
    if (tokenPayload.widgetId !== params.widgetId) {
      return NextResponse.json(
        { error: 'Token widget mismatch' },
        {
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Verify domain matches token
    if (tokenPayload.domain !== domain) {
      return NextResponse.json(
        { error: 'Token domain mismatch' },
        {
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Check if token exists in database and is active
    const dbToken = await prisma.widgetToken.findUnique({
      where: {
        token: token
      }
    });

    if (!dbToken || !dbToken.isActive || dbToken.expiresAt < new Date()) {
      return NextResponse.json(
        { error: 'Token not found, inactive, or expired' },
        {
          status: 401,
          headers: corsHeaders
        }
      );
    }

    // Apply rate limiting based on widget settings
    const rateLimitKey = `secure_widget_${params.widgetId}_${domain}`;
    if (!checkWidgetRateLimit(req, 'SECURE_WIDGET_DATA')) {
      return NextResponse.json(
        { error: 'Rate limit exceeded for this domain' },
        {
          status: 429,
          headers: {
            ...corsHeaders,
            'Retry-After': '3600', // 1 hour
            'X-RateLimit-Limit': widget.maxRequestsPerHour.toString(),
            'X-RateLimit-Remaining': '0'
          }
        }
      );
    }

    // Update token usage
    await prisma.widgetToken.update({
      where: { id: dbToken.id },
      data: {
        lastUsed: new Date(),
        requestCount: { increment: 1 }
      }
    });

    // Get full widget data
    const fullWidget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: true,
        product: {
          include: {
            reviews: {
              where: {
                isVerified: true,
                isDeleted: false,
                isPublic: true
              },
              orderBy: { createdDate: 'desc' },
              take: 10,
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!fullWidget) {
      return NextResponse.json(
        { error: 'Widget data not found' },
        {
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Track widget view for analytics
    await trackSecureWidgetView(params.widgetId, domain, req);

    // Prepare widget data for public consumption (same as regular widget but with security validation)
    const widgetData = {
      id: fullWidget.id,
      name: fullWidget.name,
      type: fullWidget.type,
      config: fullWidget.config,
      theme: fullWidget.theme,
      primaryColor: fullWidget.primaryColor,
      borderRadius: fullWidget.borderRadius,
      showLogo: fullWidget.showLogo,
      showPoweredBy: fullWidget.showPoweredBy,
      maxReviews: fullWidget.maxReviews,
      showRating: fullWidget.showRating,
      showReviewText: fullWidget.showReviewText,
      showReviewDate: fullWidget.showReviewDate,
      showReviewerName: fullWidget.showReviewerName,
      securityLevel: fullWidget.securityLevel,
      business: fullWidget.business ? {
        id: fullWidget.business.id,
        ownerName: fullWidget.business.ownerName,
        isVerified: fullWidget.business.isVerified
      } : null,
      product: fullWidget.product ? {
        id: fullWidget.product.id,
        name: fullWidget.product.name,
        display_image: fullWidget.product.display_image,
        rating: fullWidget.product.rating,
        rating1Star: fullWidget.product.rating1Star,
        rating2Stars: fullWidget.product.rating2Stars,
        rating3Stars: fullWidget.product.rating3Stars,
        rating4Stars: fullWidget.product.rating4Stars,
        rating5Stars: fullWidget.product.rating5Stars,
        reviews: fullWidget.product.reviews?.map(review => ({
          id: review.id,
          title: review.title,
          body: review.body,
          rating: review.rating,
          createdDate: review.createdDate,
          user: {
            firstName: review.user.firstName,
            lastName: review.user.lastName,
            avatar: review.user.avatar
          }
        })) || []
      } : null,
      reviews: fullWidget.product?.reviews?.map(review => ({
        id: review.id,
        title: review.title,
        body: review.body,
        rating: review.rating,
        createdDate: review.createdDate,
        user: {
          firstName: review.user.firstName,
          lastName: review.user.lastName,
          avatar: review.user.avatar
        }
      })) || []
    };

    return NextResponse.json(
      {
        success: true,
        data: widgetData,
        security: {
          level: 'SECURE',
          domain: domain,
          tokenValid: true
        }
      },
      {
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Secure widget API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      {
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export function OPTIONS(req: NextRequest): NextResponse {
  // Handle preflight requests
  return widgetCorsMiddleware(req) as NextResponse;
}

// Helper function to track secure widget views
async function trackSecureWidgetView(widgetId: string, domain: string, req: NextRequest) {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // Update widget view count
    await prisma.widget.update({
      where: { id: widgetId },
      data: { 
        viewCount: { increment: 1 },
        lastUsed: new Date()
      }
    });

    // Update analytics
    const analytics = await prisma.widgetAnalytics.findUnique({
      where: { widgetId: widgetId }
    });

    const dailyViews = (analytics?.dailyViews as Record<string, number>) || {};
    const topReferrers = (analytics?.topReferrers as Record<string, number>) || {};

    dailyViews[today] = (dailyViews[today] || 0) + 1;
    topReferrers[domain] = (topReferrers[domain] || 0) + 1;

    await prisma.widgetAnalytics.upsert({
      where: { widgetId: widgetId },
      create: {
        widgetId: widgetId,
        dailyViews: dailyViews,
        topReferrers: topReferrers
      },
      update: {
        dailyViews: dailyViews,
        topReferrers: topReferrers,
        lastUpdated: new Date()
      }
    });

  } catch (error) {
    console.error('Error tracking secure widget view:', error);
    // Don't fail the request if analytics tracking fails
  }
}