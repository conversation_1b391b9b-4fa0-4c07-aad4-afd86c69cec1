import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { widgetCorsMiddleware } from '@/app/util/corsMiddleware';
import { checkWidgetRateLimit } from '@/app/util/rateLimiting';
import { getEmbedInfo, logWidgetRequest } from '../helpers';
import { trackWidgetView } from '../analytics';
import { isDomainRegistered } from '../domainRegistry';

export async function GET(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Get CORS headers for all responses
  const corsResult = widgetCorsMiddleware(req);
  const corsHeaders: Record<string, string> = corsResult instanceof NextResponse ? {} : corsResult;

  try {
    // Apply rate limiting
    if (!checkWidgetRateLimit(req, 'PUBLIC_WIDGET_DATA')) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        {
          status: 429,
          headers: {
            ...corsHeaders,
            'Retry-After': '60'
          }
        }
      );
    }

    const widget = await prisma.widget.findUnique({
      where: {
        id: params.widgetId,
        isActive: true
      },
      select: {
        id: true,
        allowedDomains: true,
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        }
      }
    });

    // Embed context parsing and logging
    const embedInfo = getEmbedInfo(req);
    logWidgetRequest(params.widgetId, embedInfo);
    
    const { referrerDomain, actualEmbeddingDomain, embedDomain, isPreview } = embedInfo;
    const referrer = embedInfo.referrer;

    // Domain validation for widget embedding
    if (widget && widget.allowedDomains && widget.allowedDomains.length > 0) {
      // Allow preview mode - check multiple conditions
      let shouldSkipValidation = false;
      
      if (isPreview) {
        // Check if it's from same domain
        if (referrer) {
          try {
            const requestUrl = new URL(req.url);
            const isSameDomain = referrerDomain === requestUrl.hostname;
            const isFromWidgetIframe = referrer.includes('/widgets/iframe/');
            
            shouldSkipValidation = isSameDomain || isFromWidgetIframe;
          } catch (e) {
            // If referrer parsing fails but it's a preview, allow it
            shouldSkipValidation = true;
          }
        } else {
          // No referrer but explicit preview parameter - allow it
          shouldSkipValidation = true;
        }
      }
      
      // Skip domain validation for preview mode
      if (!shouldSkipValidation) {
        if (referrer && referrerDomain !== 'direct-access' && referrerDomain !== 'invalid-referrer') {
          try {
            // Use actual embedding domain for validation if available
            // Priority: embedDomain from iframe > actualEmbeddingDomain > referrerDomain
            let domainToCheck = referrerDomain;
            
            if (embedDomain && embedDomain !== 'unknown') {
              // Use the domain passed from embed.js (most reliable for cross-origin iframes)
              domainToCheck = embedDomain;
            } else if (actualEmbeddingDomain !== 'unknown' && actualEmbeddingDomain !== 'cross-origin-embedded') {
              // Use detected embedding domain if available
              domainToCheck = actualEmbeddingDomain;
            }
            // Otherwise fall back to referrerDomain
            
            // Special handling for cross-origin-embedded scenario with no embedDomain
            // This means we're in a cross-origin iframe but couldn't get the parent domain
            if (actualEmbeddingDomain === 'cross-origin-embedded' && !embedDomain) {
              // In this case, try to extract domain from referrer URL
              if (referrer && referrer.includes('://')) {
                try {
                  const referrerUrl = new URL(referrer);
                  // If referrer is from our own domain but in iframe context, it's likely embedded
                  // Allow it for better user experience, but log the situation
                  if (referrerUrl.hostname === req.headers.get('host')) {
                    shouldSkipValidation = true;
                  }
                } catch (e) {
                  // Invalid referrer URL, treat as blocked
                }
              }
            }
            
            // Check if the domain was registered by the embed.js script
            // This is our most reliable source of domain information
            if (!shouldSkipValidation && domainToCheck) {
              const isDomainRegisteredForWidget = isDomainRegistered(params.widgetId, domainToCheck);
              if (isDomainRegisteredForWidget) {
                shouldSkipValidation = true;
              }
            }
            
            // Check if domain is in allowed list
            const isAllowed = widget.allowedDomains.some((domain: string) => {
              // Remove protocol and trailing slashes for comparison
              const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/\/$/, '');
              
              // Exact match
              if (domainToCheck === cleanDomain) {
                return true;
              }
              
              // Subdomain match (e.g., subdomain.example.com matches example.com)
              if (domainToCheck.endsWith('.' + cleanDomain)) {
                return true;
              }
              
              // Special handling for common development/testing platforms
              if (cleanDomain === 'jsfiddle.net') {
                return domainToCheck.includes('jsfiddle') || 
                       domainToCheck.includes('fiddle.jshell.net') ||
                       domainToCheck.includes('jshell.net');
              }
              
              if (cleanDomain === 'codepen.io') {
                return domainToCheck.includes('codepen') || 
                       domainToCheck.includes('cdpn.io');
              }
              
              if (cleanDomain === 'codesandbox.io') {
                return domainToCheck.includes('codesandbox') ||
                       domainToCheck.includes('csb.app');
              }
              
              return false;
            });
            
            if (!isAllowed) {
              // Check if this is a development/testing environment
              // Check both the domainToCheck and referrerDomain for development platforms
              const developmentPlatforms = [
                'localhost',
                '127.0.0.1',
              ];
              
              const isDevelopmentPlatform = developmentPlatforms.some(platform => 
                domainToCheck.includes(platform) || referrerDomain.includes(platform)
              );
              
              // Allow development platforms even if not explicitly in allowed domains
              if (isDevelopmentPlatform) {
                // Continue to widget loading (don't return error)
              } else {
                // Track blocked request
                await prisma.widget.update({
                  where: { id: params.widgetId },
                  data: { blockedCount: { increment: 1 } }
                });
                
                // Track blocked analytics
                const today = new Date().toISOString().split('T')[0];
                const analytics = await prisma.widgetAnalytics.findUnique({
                  where: { widgetId: params.widgetId }
                });
      
                await prisma.widgetAnalytics.upsert({
                  where: { widgetId: params.widgetId },
                  create: {
                    widgetId: params.widgetId,
                    blockedEvents: { [today]: 1 }
                  },
                  update: {
                    blockedEvents: {
                      ...(analytics?.blockedEvents as Record<string, number> || {}),
                      [today]: ((analytics?.blockedEvents as Record<string, number>)?.[today] || 0) + 1
                    }
                  }
                });
      
                return NextResponse.json(
                  { 
                    error: 'Embedding not allowed from this domain',
                    domain: domainToCheck,
                    allowedDomains: widget.allowedDomains,
                    hint: 'Add this domain to your widget\'s allowed domains list or use a development platform'
                  },
                  {
                    status: 403,
                    headers: corsHeaders
                  }
                );
              }
            }
          } catch (e) {
            // Invalid referrer URL, treat as blocked
            await prisma.widget.update({
              where: { id: params.widgetId },
              data: { blockedCount: { increment: 1 } }
            });
    
            return NextResponse.json(
              { error: 'Invalid referrer' },
              {
                status: 403,
                headers: corsHeaders
              }
            );
          }
        }
      }
    }

    // Continue with widget loading
    const fullWidget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: true,
        product: {
          include: {
            reviews: {
              where: {
                isVerified: true,
                isDeleted: false,
                isPublic: true
              },
              orderBy: { createdDate: 'desc' },
              take: 10,
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!fullWidget) {
      return NextResponse.json(
        { error: 'Widget not found' },
        {
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Track widget view
    await trackWidgetView(params.widgetId, req);

    // Prepare widget data for public consumption
    const widgetData = {
      id: fullWidget.id,
      name: fullWidget.name,
      type: fullWidget.type,
      config: fullWidget.config,
      theme: fullWidget.theme,
      primaryColor: fullWidget.primaryColor,
      borderRadius: fullWidget.borderRadius,
      showLogo: fullWidget.showLogo,
      showPoweredBy: fullWidget.showPoweredBy,
      maxReviews: fullWidget.maxReviews,
      showRating: fullWidget.showRating,
      showReviewText: fullWidget.showReviewText,
      showReviewDate: fullWidget.showReviewDate,
      showReviewerName: fullWidget.showReviewerName,
      business: fullWidget.business ? {
        id: fullWidget.business.id,
        ownerName: fullWidget.business.ownerName,
        isVerified: fullWidget.business.isVerified
      } : null,
      product: fullWidget.product ? {
        id: fullWidget.product.id,
        name: fullWidget.product.name,
        display_image: fullWidget.product.display_image,
        rating: fullWidget.product.rating,
        rating1Star: fullWidget.product.rating1Star,
        rating2Stars: fullWidget.product.rating2Stars,
        rating3Stars: fullWidget.product.rating3Stars,
        rating4Stars: fullWidget.product.rating4Stars,
        rating5Stars: fullWidget.product.rating5Stars,
        reviews: fullWidget.product.reviews?.map(review => ({
          id: review.id,
          title: review.title,
          body: review.body,
          rating: review.rating,
          createdDate: review.createdDate,
          user: {
            firstName: review.user.firstName,
            lastName: review.user.lastName,
            avatar: review.user.avatar
          }
        })) || []
      } : null,
      reviews: fullWidget.product?.reviews?.map(review => ({
        id: review.id,
        title: review.title,
        body: review.body,
        rating: review.rating,
        createdDate: review.createdDate,
        user: {
          firstName: review.user.firstName,
          lastName: review.user.lastName,
          avatar: review.user.avatar
        }
      })) || []
    };

    return NextResponse.json(
      {
        success: true,
        data: widgetData
      },
      {
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Widget API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      {
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export function OPTIONS(req: NextRequest): NextResponse {
  // Handle preflight requests
  return widgetCorsMiddleware(req) as NextResponse;
}
