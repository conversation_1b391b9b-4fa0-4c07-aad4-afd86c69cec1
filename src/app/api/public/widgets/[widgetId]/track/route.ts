import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';
import { widgetCorsMiddleware } from '@/app/util/corsMiddleware';

export async function POST(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return widgetCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = widgetCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const body = await req.json();
    const { event, referrer, userAgent, elementType, embedDomain } = body;

    // Validate event type
    const validEvents = ['view', 'click', 'interaction', 'lead_generated'];
    if (!validEvents.includes(event)) {
      return NextResponse.json(
        { error: 'Invalid event type' },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    // Update widget analytics based on event type
    const updateData: any = { lastUsed: new Date() };
    
    if (event === 'view') {
      updateData.viewCount = { increment: 1 };
    } else if (event === 'click') {
      updateData.clickCount = { increment: 1 };
    } else if (event === 'lead_generated') {
      updateData.conversionCount = { increment: 1 };
    }

    await prisma.widget.update({
      where: { id: params.widgetId },
      data: updateData
    });

    // Store detailed analytics
    await storeWidgetAnalytics(params.widgetId, {
      event,
      referrer,
      userAgent,
      elementType,
      embedDomain,
      timestamp: new Date()
    });

    return NextResponse.json(
      { success: true },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Widget tracking error:', error);
    return NextResponse.json(
      { error: 'Tracking failed' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  const corsResult = widgetCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  // If it's just headers, return a proper NextResponse
  return new NextResponse(null, { status: 200, headers: corsResult });
}

async function storeWidgetAnalytics(widgetId: string, data: any) {
  try {
    // Get or create widget analytics record
    const analytics = await prisma.widgetAnalytics.upsert({
      where: { widgetId },
      create: {
        widgetId,
        dailyViews: {},
        dailyClicks: {},
        dailyConversions: {},
        topReferrers: {},
        lastUpdated: new Date()
      },
      update: {
        lastUpdated: new Date()
      }
    });

    // Update daily metrics
    const today = new Date().toISOString().split('T')[0];
    const dailyViews = analytics.dailyViews as Record<string, number>;
    const dailyClicks = analytics.dailyClicks as Record<string, number>;
    const dailyConversions = analytics.dailyConversions as Record<string, number>;
    const topReferrers = analytics.topReferrers as Record<string, number>;

    if (data.event === 'view') {
      dailyViews[today] = (dailyViews[today] || 0) + 1;
    } else if (data.event === 'click') {
      dailyClicks[today] = (dailyClicks[today] || 0) + 1;
    } else if (data.event === 'lead_generated') {
      dailyConversions[today] = (dailyConversions[today] || 0) + 1;
    }

    // Track referrers - use embedDomain if available, otherwise extract from referrer
    let domain = null;
    if (data.embedDomain && data.embedDomain !== 'unknown') {
      domain = data.embedDomain;
    } else if (data.referrer) {
      try {
        domain = new URL(data.referrer).hostname;
      } catch (e) {
        console.warn('Failed to parse referrer URL:', data.referrer);
      }
    }
    
    if (domain) {
      topReferrers[domain] = (topReferrers[domain] || 0) + 1;
    }

    // Update analytics record
    await prisma.widgetAnalytics.update({
      where: { widgetId },
      data: {
        dailyViews,
        dailyClicks,
        dailyConversions,
        topReferrers,
        lastUpdated: new Date()
      }
    });

  } catch (error) {
    console.error('Failed to store widget analytics:', error);
  }
}