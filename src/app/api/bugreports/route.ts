import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";

export async function POST(req: NextRequest) {
    try {
        // Get authenticated user
        let userId;
        try {
            const authResult = await auth();
            userId = authResult.userId;
        } catch (authError) {
            console.error("Authentication error:", authError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Authentication failed",
                    message: "Please sign in to submit a bug report"
                },
                { status: 401 }
            );
        }

        if (!userId) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Unauthorized",
                    message: "Please sign in to submit a bug report"
                },
                { status: 401 }
            );
        }

        // Get user from database
        let user;
        try {
            user = await prisma.user.findFirst({
                where: { clerkUserId: userId },
            });
        } catch (dbError) {
            console.error("Database error while fetching user:", dbError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Database error",
                    message: "Failed to verify user account"
                },
                { status: 500 }
            );
        }

        if (!user) {
            return NextResponse.json(
                {
                    success: false,
                    error: "User not found",
                    message: "Your account could not be found. Please try signing in again."
                },
                { status: 404 }
            );
        }

        // Parse request body
        let body;
        try {
            body = await req.json();
        } catch (parseError) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Invalid request",
                    message: "Invalid JSON in request body"
                },
                { status: 400 }
            );
        }

        // Simple validation
        if (!body.title || typeof body.title !== 'string' || body.title.trim() === '') {
            return NextResponse.json(
                {
                    success: false,
                    error: "Validation error",
                    message: "Title is required and cannot be empty"
                },
                { status: 400 }
            );
        }

        if (!body.description || typeof body.description !== 'string' || body.description.trim() === '') {
            return NextResponse.json(
                {
                    success: false,
                    error: "Validation error",
                    message: "Description is required and cannot be empty"
                },
                { status: 400 }
            );
        }

        // Create bug report using Prisma
        let bugReport;
        try {
            bugReport = await prisma.bugReport.create({
                data: {
                    title: body.title.trim(),
                    description: body.description.trim(),
                    browser: body.browser || null,
                    device: body.device || null,
                    mobile_os: body.mobile_os || null,
                    status: 'OPEN',
                    reporterId: user.id,
                },
            });
        } catch (dbError) {
            console.error("Database error while creating bug report:", dbError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Database error",
                    message: "Failed to create bug report. Please try again later."
                },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            message: "Bug report created successfully",
            data: {
                id: bugReport.id,
                title: bugReport.title,
                description: bugReport.description,
                status: bugReport.status,
                created_at: bugReport.created_at
            }
        });
    } catch (error) {
        console.error("BUG REPORT API ERROR:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Internal server error",
                message: "An unexpected error occurred. Please try again later."
            },
            { status: 500 }
        );
    }
} 