import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { invalidateCommentCache } from "@/app/util/analytics/cache";

export async function POST(request: NextRequest) {
    try {
        const { commentId, voteType } = await request.json();
        const { sessionClaims } = getAuth(request as any);
        const clerkClaimsData = sessionClaims as any;

        // Ensure user exists in database
        if (!(await userInDb(clerkClaimsData.userId))) {
            await addUserToDb(clerkClaimsData);
        }

        // Get user's database ID from Clerk metadata
        const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
        const userId = clerkUserData.publicMetadata.id as string;

        // Prevent users from voting on their own comment
        const commentOwner = await prisma.comment.findUnique({
            where: { id: commentId },
            select: { userId: true }
        });

        if (!commentOwner) {
            return NextResponse.json({ success: false, error: "Comment not found." }, { status: 404 });
        }

        if (commentOwner.userId === userId) {
            return NextResponse.json({ success: false, error: "You cannot vote on your own comment." }, { status: 403 });
        }

        // Check if user has already voted
        const existingVote = await prisma.commentVote.findUnique({
            where: {
                commentId_clerkUserId: {
                    commentId,
                    clerkUserId: clerkClaimsData.userId,
                },
            },
        });

        // Start a transaction to handle the vote
        const result = await prisma.$transaction(async (tx) => {
            if (existingVote) {
                // If user has already voted, update or remove the vote
                if (existingVote.voteType === voteType) {
                    // Remove vote if clicking the same button again
                    await tx.commentVote.delete({
                        where: {
                            id: existingVote.id,
                        },
                    });

                    // Update comment vote counts
                    await tx.comment.update({
                        where: { id: commentId },
                        data: {
                            upvotes: voteType === 'UP' ? { decrement: 1 } : undefined,
                            downvotes: voteType === 'DOWN' ? { decrement: 1 } : undefined,
                        },
                    });

                    return { action: 'removed' };
                } else {
                    // Change vote type
                    await tx.commentVote.update({
                        where: {
                            id: existingVote.id,
                        },
                        data: {
                            voteType,
                        },
                    });

                    // Update comment vote counts
                    await tx.comment.update({
                        where: { id: commentId },
                        data: {
                            upvotes: voteType === 'UP' ? { increment: 1 } : { decrement: 1 },
                            downvotes: voteType === 'DOWN' ? { increment: 1 } : { decrement: 1 },
                        },
                    });

                    return { action: 'changed' };
                }
            } else {
                // Create new vote
                await tx.commentVote.create({
                    data: {
                        commentId,
                        userId,
                        clerkUserId: clerkClaimsData.userId,
                        voteType,
                    },
                });

                // Update comment vote counts
                await tx.comment.update({
                    where: { id: commentId },
                    data: {
                        upvotes: voteType === 'UP' ? { increment: 1 } : undefined,
                        downvotes: voteType === 'DOWN' ? { increment: 1 } : undefined,
                    },
                });

                return { action: 'added' };
            }
        });

        // Fetch the updated comment with its votes
        const updatedComment = await prisma.comment.findUnique({
            where: { id: commentId },
            include: {
                review: {
                    select: {
                        productId: true,
                    },
                },
                votes: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                userName: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                                clerkUserId: true,
                            }
                        }
                    }
                },
                user: {
                    select: {
                        id: true,
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                        clerkUserId: true,
                        email: true,
                        createdDate: true,
                        isDeleted: true,
                    }
                },
                replies: {
                    include: {
                        votes: {
                            include: {
                                user: {
                                    select: {
                                        id: true,
                                        userName: true,
                                        firstName: true,
                                        lastName: true,
                                        avatar: true,
                                        clerkUserId: true,
                                    }
                                }
                            }
                        },
                        user: true,
                    }
                }
            },
        });

        // Look for the user's vote in the updated comment
        const userVoteInUpdatedComment = updatedComment?.votes?.find(vote => vote.clerkUserId === clerkClaimsData.userId);

        // Create notification for like/dislike votes
        if (updatedComment && (result.action === 'added' || result.action === 'changed')) {
            const normalizedVoteType = voteType.toUpperCase();
            const isUpVote = normalizedVoteType === 'UP';
            // Only send notifications for likes (upvotes)
            if (isUpVote) {
                try {
                // Get the comment author to notify them
                const commentAuthor = await prisma.comment.findUnique({
                    where: { id: commentId },
                    include: { 
                        user: true,
                        review: {
                            include: {
                                product: true
                            }
                        }
                    }
                });

                if (commentAuthor && commentAuthor.user && commentAuthor.userId !== clerkClaimsData.id) {
                    // Don't notify if user is voting on their own comment
                    const { createLikeNotification } = await import("@/app/util/NotificationFunctions");
                    
                    // prepare payload for like notification
                    const actionText = 'liked';
                    
                    const likeNotification = {
                        target_type: 'comment' as const,
                        target_id: commentId,
                        from_id: clerkClaimsData.userId,
                        from_name: `${clerkClaimsData.firstName || ''} ${clerkClaimsData.lastName || ''}`.trim() || 'Anonymous',
                        product_id: commentAuthor.review?.productId || '',
                        review_id: commentAuthor.reviewId,
                        comment_id: commentId,
                        target_user_id: commentAuthor.userId, // ensure we use DB user id, not username
                        read: false,
                    };
                    
                    await createLikeNotification(likeNotification);
                } 
                } catch (notificationError) {
                    console.error('Failed to create vote notification:', notificationError);
                    // Don't fail the vote if notification fails
                }
            }
        }

        if (updatedComment) {
            await invalidateCommentCache(updatedComment.reviewId);
        }

        return NextResponse.json({
            success: true,
            status: 200,
            data: {
                ...result,
                comment: updatedComment,
            },
        });

    } catch (error) {
        console.error('Error processing comment vote:', error);
        return NextResponse.json({
            success: false,
            status: 500,
            error: 'Failed to process comment vote',
        });
    }
} 