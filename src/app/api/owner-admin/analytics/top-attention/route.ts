import { NextRequest, NextResponse } from 'next/server';
import { getBusinessTopAttentionFromCache } from '@/app/util/analytics/business';
import { getAuth } from '@clerk/nextjs/server';

// Force dynamic rendering since we use request.headers via getAuth
export const dynamic = 'force-dynamic';

// GET /api/owner-admin/analytics/top-attention?businessId=xxx&limit=5
export async function GET(request: NextRequest) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');
    const limitParam = url.searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 5;

    if (!businessId) {
      return NextResponse.json({ error: 'businessId query param required' }, { status: 400 });
    }

    // TODO: verify the user owns/is admin of this business (skipped for now)

    const result = await getBusinessTopAttentionFromCache(businessId, limit);
    return NextResponse.json(result, { status: result.status });
  } catch (error) {
    console.error('[owner top-attention] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch top attention data' }, { status: 500 });
  }
}
