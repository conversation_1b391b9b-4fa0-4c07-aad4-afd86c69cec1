import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";

export async function GET(request: NextRequest) {
  try {
    // Get the current user (admin)
    const { userId } = getAuth(request);
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        status: 401, 
        message: "Unauthorized" 
      });
    }

    // Get all users
    const users = await prisma.user.findMany({
      where: { 
        isDeleted: false 
      },
      include: {
        _count: {
          select: {
            reviews: true,
            comments: true,
            product: true,
          }
        }
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    return NextResponse.json({
      success: true,
      status: 200,
      dataLength: users.length,
      data: users,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      message: e.message.slice(0, 500) + "...",
    });
  }
} 