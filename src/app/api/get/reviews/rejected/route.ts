import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(request: NextRequest) {
  try {
    // Get the current user (admin)
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        message: "Unauthorized"
      });
    }

    // Get rejected reviews (not public but have been verified)
    const reviews = await prisma.review.findMany({
      where: {
        isPublic: false,
        isVerified: true,
        isDeleted: false
      },
      include: {
        user: true,
        product: true,
        comments: {
          include: {
            user: true,
            parent: true,
          },
        },
        voteCount: true,
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    const treatedReviews = sanitizeDeletedCommentsInReviews(reviews as iReview[]);

    return NextResponse.json({
      success: true,
      status: 200,
      dataLength: treatedReviews.length,
      data: treatedReviews,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      message: e.message.slice(0, 500) + "...",
    });
  }
} 