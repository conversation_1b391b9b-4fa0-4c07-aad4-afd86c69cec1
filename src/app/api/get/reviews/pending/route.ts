import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    // Get the current user (admin)
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Get pending reviews (those that need moderation)
    // We consider a review pending if it's not verified (either false or null),
    // regardless of its isPublic status
    const reviews = await prisma.review.findMany({
      where: {
        isDeleted: false,
        OR: [
          { isVerified: false },
          { isVerified: null }
        ]
      },
      include: {
        user: true,
        product: true,
        comments: {
          include: {
            user: true,
            parent: true,
          },
        },
        voteCount: true,
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    const treatedReviews = sanitizeDeletedCommentsInReviews(reviews as iReview[]);

    return NextResponse.json({
      success: true,
      dataLength: treatedReviews.length,
      data: treatedReviews,
    });
  } catch (error) {
    console.error('Error fetching pending reviews:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pending reviews' },
      { status: 500 }
    );
  }
} 