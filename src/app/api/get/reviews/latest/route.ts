import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { getLatestReviewsFromCache } from "@/app/util/databaseAnalytics";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import { cleanReview, cleanReviews, createFilter } from "@/app/store/badWordsFilter";

// Force dynamic rendering to ensure fresh data on every request
export const dynamic = 'force-dynamic';
export const revalidate = 0;

/**
 * Handler function for GET requests to fetch latest reviews
 * Using GET is more RESTful for data retrieval operations
 */
async function handleLatestReviews() {
  try {
    // Use cached latest reviews function
    const result = await getLatestReviewsFromCache();
    console.log("this is the original", result.data.length);

    if (!result.success) {
      throw new Error(result.data || 'Failed to fetch latest reviews');
    }

    const badWordsFilter = await createFilter();

    // Apply bad words filter to the reviews
    const treatedReviews = sanitizeDeletedCommentsInReviews(result.data as iReview[]);
    console.log(treatedReviews.length)
    let cleanedReviews;
    try {
      cleanedReviews = cleanReviews(badWordsFilter, treatedReviews);
      console.log(cleanedReviews.length)
    } catch {
      cleanedReviews = treatedReviews;
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: cleanedReviews,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}

// Only support GET method for retrieving latest reviews
export const GET = handleLatestReviews;
