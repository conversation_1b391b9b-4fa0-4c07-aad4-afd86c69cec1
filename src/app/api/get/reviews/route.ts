import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { getProductReviewsFromCache } from "@/app/util/databaseAnalytics";
import { sanitizeDeletedCommentsInReviews, sanitizeDeletedReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import {
  cleanReview,
  cleanReviews,
  createFilter,
} from "@/app/store/badWordsFilter";
const filter = createFilter();

export async function POST(request: NextRequest) {
  interface Body {
    id: string;
    isPublic: boolean;
    user: boolean;
    product: boolean;
    comments: boolean;
  }

  const body: Body = await request.json();

  try {
    // Use cached reviews with complex nested data
    const { reviews: cachedReviews, product } = await getProductReviewsFromCache(
      body.id,
      body.isPublic,
      body.user,
      body.product,
      body.comments
    );
    
    let reviews = cachedReviews as iReview[];
    // First sanitize deleted reviews, then sanitize deleted comments within reviews
    const reviewSanitized = sanitizeDeletedReviews(reviews as iReview[]);
    const commentSanitized = sanitizeDeletedCommentsInReviews(reviewSanitized as iReview[]);
    let cleaned = commentSanitized;
    try {
      cleaned = cleanReviews(await filter, commentSanitized);
    } catch (error) {
      cleaned = commentSanitized;
    }
    // Restore relational fields (e.g., voteCount) that may have been stripped by cleanReviews
    // and attach product data to each review if not already included
    reviews = cleaned.map((rev, idx) => ({
      ...rev,
      voteCount: (commentSanitized[idx] as any).voteCount,
      product: rev.product || product, // Attach product data if not already included
    })) as iReview[];
    return NextResponse.json({
      success: true,
      status: 200,
      data: reviews,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}
