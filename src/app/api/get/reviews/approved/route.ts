import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { sanitizeDeletedCommentsInReviews } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(request: NextRequest) {
  try {
    // Get the current user (admin)
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Get approved reviews (public and verified)
    const reviews = await prisma.review.findMany({
      where: {
        isPublic: true,
        isVerified: true,
        isDeleted: false
      },
      include: {
        user: true,
        product: true,
        comments: {
          include: {
            user: true,
            parent: true,
            votes: true,
          },
        },
        voteCount: true,
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    const treatedReviews = sanitizeDeletedCommentsInReviews(reviews as iReview[]);

    return NextResponse.json({
      success: true,
      status: 200,
      dataLength: treatedReviews.length,
      data: treatedReviews,
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to fetch approved reviews' },
      { status: 500 }
    );
  }
} 