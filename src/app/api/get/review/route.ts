import { iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { sanitizeDeletedCommentsInReview, sanitizeDeletedReview } from "@/app/util/sanitizeDeletedComments";
import { NextRequest, NextResponse } from "next/server";
import { cleanReview, cleanReviews, createFilter } from "@/app/store/badWordsFilter";
const filter = createFilter();

// Function to check if the request is from a bot or social media crawler
function isBot(request: NextRequest) {
  const userAgent = request.headers.get("user-agent") || "";
  const lowerUserAgent = userAgent.toLowerCase();

  const botPatterns = [
    "googlebot",
    "bingbot",
    "slurp",
    "duckduckbot",
    "yandex",
    "baiduspider",
    "facebookexternalhit",
    "twitterbot",
    "linkedinbot",
    "whatsapp",
    "slackbot",
    "discordbot",
    "telegrambot",
    "viber",
    "pinterest",
    "bot",
    "crawler",
    "spider"
  ];

  return botPatterns.some((pattern) => lowerUserAgent.includes(pattern));
}

export async function POST(request: NextRequest) {
  interface Body {
    id: string;
  }

  const body: Body = await request.json();
  try {
    const review = await prisma.review.findFirst({
      where: { isPublic: true, id: body.id },
      include: {
        user: true,
        product: true,
        comments: {
          include: {
            user: true,
            votes: true,
          },
        },
        voteCount: true,
        likedBy: true,
      }
    })

    // First sanitize deleted review, then sanitize deleted comments within review
    let reviewSanitized = sanitizeDeletedReview(review as iReview);
    let commentSanitized = sanitizeDeletedCommentsInReview(reviewSanitized);
    let treatedReview = cleanReview(await filter, commentSanitized);

    return NextResponse.json({
      success: true,
      status: 200,
      data: treatedReview,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}

