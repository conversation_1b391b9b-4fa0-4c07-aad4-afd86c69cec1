import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        success: false,
        status: 400,
        error: "Invalid request body",
      });
    }

    const { userId } = body;

    if (!userId) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: "User ID is required",
      });
    }

    let user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      include: {
        comments: {
          include: {
            review: true,
          },
        },
        reviews: {
          include: {
            product: true,
            user: true,
            voteCount: true,
            comments: {
              include: {
                user: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
            comments: true,
          },
        },
        likedReviews: true,
      },
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: "User not found",
      });
    }

    // Check for automatic unsuspension
    if (user.status === 'SUSPENDED' && user.suspendedUntil) {
      const now = new Date();
      const suspensionEndDate = new Date(user.suspendedUntil);
      
      // If suspension period has expired, automatically unsuspend the user
      if (now >= suspensionEndDate) {
        user = await prisma.user.update({
          where: {
            id: userId,
          },
          data: {
            status: 'ACTIVE',
            suspendedUntil: null,
            suspendedReason: null,
          },
          include: {
            comments: {
              include: {
                review: true,
              },
            },
            reviews: {
              include: {
                product: true,
                user: true,
                voteCount: true,
                comments: {
                  include: {
                    user: true,
                  },
                },
              },
            },
            _count: {
              select: {
                reviews: true,
                comments: true,
              },
            },
            likedReviews: true,
          },
        });
      }
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: user,
    });
  } catch (error) {
    let errorMessage = "An unknown error occurred";

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      success: false,
      status: 500,
      error: errorMessage,
    });
  }
}
