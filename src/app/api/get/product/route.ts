import { prisma } from "@/app/util/prismaClient";
import { getProductDetailsFromCache } from "@/app/util/databaseAnalytics";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  interface Body {
    id: string;
  }

  const body: Body = await request.json();
  try {
    // Use cached product details function
    const product = await getProductDetailsFromCache(body.id);
    
    return NextResponse.json({
      success: true,
      status: 200,
      data: product,
    });
  } catch (error) {
    let e = error as Error;
    return NextResponse.json({
      success: false,
      status: 500,
      data: e.message.slice(0, 500) + "...",
    });
  }
}
