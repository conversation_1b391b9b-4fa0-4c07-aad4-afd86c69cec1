import { prisma } from '@/app/util/prismaClient';

async function main() {
    try {
        // Removed debug console.log statements

        // Try to access the ProductClaim model
        const claims = await prisma.productClaim.findMany({
            take: 1,
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

main(); 