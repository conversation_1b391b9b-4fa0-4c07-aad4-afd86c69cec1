import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";
import { invalidateCachesOnComment } from "@/app/util/analytics/cache";

export async function POST(request: NextRequest) {
  const body: { userId: string; reviewId: string } = await request.json();

  if (!body.userId || !body.reviewId) {
    return NextResponse.json(
      { success: false, error: "User ID and Review ID are required." },
      { status: 400 }
    );
  }

  try {
    const review = await prisma.review.findUnique({
      where: { id: body.reviewId },
      include: {
        likedBy: { select: { id: true } }, // Select only IDs for efficiency
        user: { select: { id: true } } // Select only the author's ID
      },
    });

    if (!review) {
      return NextResponse.json(
        { success: false, error: "Review not found." },
        { status: 404 }
      );
    }

    // Prevent OP from voting on their own review
    if (review.userId === body.userId) {
      return NextResponse.json(
        { success: false, error: "You cannot vote on your own review." },
        { status: 403 } // Forbidden
      );
    }

    const hasLiked = review.likedBy.some(user => user.id === body.userId);

    if (hasLiked) {
      // User wants to un-like
      await prisma.$transaction([
        prisma.voteCount.update({
          where: { reviewId: body.reviewId },
          data: { helpfulVotes: { decrement: 1 } },
        }),
        prisma.review.update({
          where: { id: body.reviewId },
          data: { likedBy: { disconnect: { id: body.userId } } },
        }),
        prisma.user.update({
          where: { id: body.userId },
          data: { likedReviews: { disconnect: { id: body.reviewId } } },
        }),
      ]);

      // Invalidate cache
      await invalidateCachesOnComment(review.productId);

      return NextResponse.json({
        success: true,
        message: "Vote removed successfully.",
      });
    } else {
      // User wants to like
      await prisma.$transaction([
        prisma.voteCount.update({
          where: { reviewId: body.reviewId },
          data: { helpfulVotes: { increment: 1 } },
        }),
        prisma.review.update({
          where: { id: body.reviewId },
          data: { likedBy: { connect: { id: body.userId } } },
        }),
        prisma.user.update({
          where: { id: body.userId },
          data: { likedReviews: { connect: { id: body.reviewId } } },
        }),
      ]);

      // Create notification for helpful vote
      try {
        const { createLikeNotification } = await import("@/app/util/NotificationFunctions");
        
        // Get the user who made the vote to get their name
        const voter = await prisma.user.findUnique({
          where: { id: body.userId },
          select: { firstName: true, lastName: true }
        });
        
        const likeNotification = {
          target_id: body.reviewId,
          
          
          read: false,
          
          
          
          from_id: body.userId,
          from_name: `${voter?.firstName || 'Anonymous'} ${voter?.lastName || ''}`.trim(),
          
          product_id: review.productId || '',
          target_user_id: review.userId,
          
          target_type: 'review' as const,
        };
        
        // Removed console.log statement
        await createLikeNotification(likeNotification);
      } catch (notificationError) {
        console.error('Failed to create helpful vote notification:', notificationError);
        // Don't fail the vote if notification fails
      }

      // Invalidate cache
      await invalidateCachesOnComment(review.productId);

      return NextResponse.json({
        success: true,
        message: "Vote added successfully.",
      });
    }
  } catch (error) {
    console.error(`Error updating vote: ${error}`);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error updating vote."
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
