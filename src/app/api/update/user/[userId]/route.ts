import { prisma } from "@/app/util/prismaClient";
import { isValidUsername } from "@/app/util/userHelpers";
import { differenceInDays } from "date-fns";
import { redisService } from "@/app/lib/redis";
import { <PERSON><PERSON><PERSON>eys } from "@/app/util/analytics/cache";
import { NextRequest, NextResponse } from "next/server";
import { iUser } from "@/app/util/Interfaces";
import { Prisma } from "@prisma/client";
import { clerkClient } from "@clerk/nextjs/server";
import { auth } from "@clerk/nextjs/server";
import { invalidateSearchCache } from "@/app/util/databaseAnalytics";

// Helper function to convert iUser partial to Prisma UserUpdateInput
function convertToPrismaUserUpdateInput(
  partialUser: Partial<iUser>,
): Prisma.UserUpdateInput {
  const allowedFields: (keyof iUser)[] = [
    "bio",
    "userName",
    "avatar",

    "email",
    "firstName",
    "lastName",
  ];

  const updateInput: Prisma.UserUpdateInput = {};

  for (const field of allowedFields) {
    if (field in partialUser) {
      switch (field) {
        case "bio":
        case "userName":
        case "avatar":
        // case "email":
        case "firstName":
        case "lastName":
          updateInput[field] = partialUser[field] as string;
          break;
      }
    }
  }

  return updateInput;
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } },
) {
  const userId = params.userId;
  const updatedFields = (await request.json()) as Partial<iUser>;
  const MIN_DAYS_BETWEEN_CHANGES = 30;

  try {
    // Authenticate the request
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId || userId !== clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // When userName change requested, validate and rate-limit
    if (updatedFields.userName) {
      // Validate format & reserved words
      if (!isValidUsername(updatedFields.userName)) {
        return NextResponse.json(
          { error: "Invalid username" },
          { status: 400 },
        );
      }

      // Fetch current user to check last change timestamp
      // @ts-ignore - the field usernameChangedAt will exist after migration
      const existing = await prisma.user.findUnique({
        where: { id: userId },
        select: { userName: true, usernameChangedAt: true },
      });

      if (!existing) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      if (
        (existing as any).usernameChangedAt &&
        differenceInDays(new Date(), (existing as any).usernameChangedAt || new Date()) < MIN_DAYS_BETWEEN_CHANGES
      ) {
        return NextResponse.json(
          {
            error: `Username can only be changed every ${MIN_DAYS_BETWEEN_CHANGES} days.`,
          },
          { status: 429 },
        );
      }
    }

    // Update user in your database
    const prismaUpdateInput = convertToPrismaUserUpdateInput(updatedFields);
    if (updatedFields.userName) {
      // also stamp the change time
      (prismaUpdateInput as any).usernameChangedAt = new Date();
    }
    const updatedUser = await prisma.user.update({
      where: {
        id: userId,
      },
      data: prismaUpdateInput,
    });

    // Prepare data for Clerk update
    const clerkUpdateData: any = {};
    if (updatedFields.firstName)
      clerkUpdateData.firstName = updatedFields.firstName;
    if (updatedFields.lastName)
      clerkUpdateData.lastName = updatedFields.lastName;
    if (updatedFields.userName)
      clerkUpdateData.username = updatedFields.userName;
    // if (updatedFields.avatar) clerkUpdateData.imageUrl = updatedFields.avatar;

    // Update Clerk user data if there are fields to update
    if (Object.keys(clerkUpdateData).length > 0) {
      const res = await (await clerkClient()).users.updateUser(
        clerkUserId,
        clerkUpdateData,
      );
    }

    // Invalidate relevant caches after successful user update
    // This ensures top reviewers and other user-dependent cached data is refreshed
    try {
      await invalidateSearchCache();

      // Invalidate top reviewers cache
      await redisService.del(CacheKeys.topReviewers(6));
    } catch (cacheError) {
      // Don't fail the request if cache invalidation fails
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: updatedUser,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 },
    );
  } finally {
    await prisma.$disconnect();
  }
}
