import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    try {
        if (!imageUrl) {
            return NextResponse.json({ error: 'Image URL parameter is required' }, { status: 400 });
        }

        // Try to fetch the image to check if it's accessible
        const response = await fetch(imageUrl, {
            method: 'HEAD',  // Only fetch headers, not the entire image
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'
            }
        });

        // Get image information from headers
        const contentType = response.headers.get('content-type');
        const contentLength = response.headers.get('content-length');
        const status = response.status;

        return NextResponse.json({
            url: imageUrl,
            accessible: response.ok,
            status,
            contentType,
            contentLength: contentLength ? parseInt(contentLength) : null,
            headers: Object.fromEntries(response.headers.entries()),
        });
    } catch (error) {
        console.error('Error checking image URL:', error);

        // If the error is a fetch error, it might indicate CORS or network issues
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        return NextResponse.json({
            error: 'Failed to check image URL',
            details: errorMessage,
            url: imageUrl,
            accessible: false
        }, { status: 500 });
    }
} 