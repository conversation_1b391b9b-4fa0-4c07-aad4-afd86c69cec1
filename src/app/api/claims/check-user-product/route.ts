import { prisma } from "@/app/util/prismaClient";
import { NextResponse, NextRequest } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { UserDATA } from "@/app/util/Interfaces";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    try {
        const { sessionClaims } = getAuth(request);
        const clerkClaimsData = sessionClaims as unknown as UserDATA;

        if (!clerkClaimsData?.userId) {
            return NextResponse.json({
                success: false,
                message: "Unauthorized",
                errorCode: "UNAUTHORIZED",
            }, { status: 401 });
        }

        const { searchParams } = new URL(request.url);
        const productId = searchParams.get("productId");

        if (!productId) {
            return NextResponse.json({
                success: false,
                message: "Product ID is required",
                errorCode: "MISSING_PRODUCT_ID",
            }, { status: 400 });
        }

        const existingClaim = await prisma.productClaim.findFirst({
            where: {
                userId: clerkClaimsData.userId,
                productId: productId,
            },
            select: {
                id: true,
                status: true,
                createdAt: true,
                rejectionReason: true,
            }
        });

        if (existingClaim) {
            return NextResponse.json({
                success: true,
                hasExistingClaim: true,
                claimDetails: existingClaim,
            });
        } else {
            return NextResponse.json({
                success: true,
                hasExistingClaim: false,
            });
        }

    } catch (error) {
        console.error("Error checking user product claim:", error);
        const e = error as Error;
        return NextResponse.json({
            success: false,
            message: e.message || "An unexpected error occurred",
            errorCode: "SERVER_ERROR",
        }, { status: 500 });
    }
} 