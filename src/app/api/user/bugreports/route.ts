export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";

export async function GET(req: NextRequest) {
    try {
        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Unauthorized",
                    message: "Please sign in to view bug reports"
                },
                { status: 401 }
            );
        }

        // Get user from database
        const user = await prisma.user.findFirst({
            where: { clerkUserId: userId },
        });

        if (!user) {
            return NextResponse.json(
                {
                    success: false,
                    error: "User not found",
                    message: "Your account could not be found. Please try signing in again."
                },
                { status: 404 }
            );
        }

        // Get active bugs (not resolved)
        const activeBugs = await prisma.bugReport.findMany({
            where: {
                status: {
                    in: ['OPEN', 'IN_PROGRESS']
                }
            },
            orderBy: {
                created_at: 'desc'
            },
            include: {
                reporter: {
                    select: {
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true
                    }
                },
                resolver: {
                    select: {
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true
                    }
                }
            }
        });

        // Get user's bug reports
        const userBugs = await prisma.bugReport.findMany({
            where: {
                reporterId: user.id
            },
            orderBy: {
                created_at: 'desc'
            },
            include: {
                reporter: {
                    select: {
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true
                    }
                },
                resolver: {
                    select: {
                        userName: true,
                        firstName: true,
                        lastName: true,
                        avatar: true
                    }
                }
            }
        });

        return NextResponse.json({
            success: true,
            data: {
                activeBugs,
                userBugs
            }
        });
    } catch (error) {
        console.error("Error fetching bug reports:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Internal server error",
                message: "Failed to fetch bug reports. Please try again later."
            },
            { status: 500 }
        );
    }
}