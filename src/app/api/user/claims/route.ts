import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { iProductClaim } from "@/app/util/Interfaces";

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json({
                success: false,
                status: 401,
                message: "Unauthorized. Please sign in.",
                errorCode: "UNAUTHORIZED",
            });
        }

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get("page") || "1");
        const limit = parseInt(searchParams.get("limit") || "10");
        const status = searchParams.get("status");
        const sortOrder = searchParams.get("sortOrder") || "desc";
        const search = searchParams.get("search");

        const skip = (page - 1) * limit;

        // Build filter conditions
        const where = {
            userId,
            ...(status && status !== "all" ? { status } : {}),
            ...(search ? {
                OR: [
                    { contactInfo: { contains: search, mode: 'insensitive' as const } },
                    { additionalInfo: { contains: search, mode: 'insensitive' as const } },
                ],
            } : {}),
        };

        // Fetch claims with pagination
        const [claims, total] = await Promise.all([
            prisma.productClaim.findMany({
                where,
                include: {
                    product: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            display_image: true,
                            address: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: sortOrder as "asc" | "desc",
                },
                skip,
                take: limit,
            }),
            prisma.productClaim.count({ where }),
        ]);

        // Filter claims by product name if search is provided
        let filteredClaims = claims as iProductClaim[];
        if (search) {
            filteredClaims = claims.filter((claim: iProductClaim) =>
                claim.product?.name.toLowerCase().includes(search.toLowerCase())
            );
        }

        // Ensure we always return an array, even if empty
        const safeClaims = Array.isArray(filteredClaims) ? filteredClaims : [];
        const hasMore = skip + safeClaims.length < total;

        return NextResponse.json({
            success: true,
            status: 200,
            claims: safeClaims,
            hasMore,
            total,
            page,
            totalPages: Math.ceil(total / limit),
        });
    } catch (error) {
        console.error("Error fetching user claims:", error);
        const e = error as Error;
        return NextResponse.json({
            success: false,
            status: 500,
            message: e.message || "An unexpected error occurred",
            errorCode: "FETCH_CLAIMS_ERROR",
        });
    }
} 