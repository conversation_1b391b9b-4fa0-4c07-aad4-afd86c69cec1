import { NextRequest, NextResponse } from "next/server";
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from "@/app/util/prismaClient";
import { Prisma } from '@prisma/client';

// Input validation and sanitization
function sanitizeInput(input: string): string {
    return input.trim().replace(/[<>]/g, ''); // Basic XSS prevention
}

function validateReportInput(reason: string, additionalNotes?: string): { isValid: boolean; error?: string } {
    if (!reason || typeof reason !== 'string') {
        return { isValid: false, error: 'Reason is required' };
    }

    const sanitizedReason = sanitizeInput(reason);
    if (sanitizedReason.length < 10) {
        return { isValid: false, error: 'Reason must be at least 10 characters long' };
    }
    if (sanitizedReason.length > 500) {
        return { isValid: false, error: 'Reason must not exceed 500 characters' };
    }

    if (additionalNotes) {
        const sanitizedNotes = sanitizeInput(additionalNotes);
        if (sanitizedNotes.length > 1000) {
            return { isValid: false, error: 'Additional notes must not exceed 1000 characters' };
        }
    }

    return { isValid: true };
}

export async function POST(
    request: NextRequest,
    { params }: { params: { reviewId: string } }
) {
    try {
        // Check authentication
        const { userId } = getAuth(request);
        if (!userId) {
            return NextResponse.json(
                { error: 'Unauthorized: Please sign in to submit a report' },
                { status: 401 }
            );
        }

        const { reviewId } = params;
        const { reason, additionalNotes } = await request.json();

        // Validate and sanitize input
        const validation = validateReportInput(reason, additionalNotes);
        if (!validation.isValid) {
            return NextResponse.json(
                { error: validation.error },
                { status: 400 }
            );
        }

        // Check if review exists
        const review = await prisma.review.findUnique({
            where: { id: reviewId },
            select: { id: true, userId: true }
        });

        if (!review) {
            return NextResponse.json(
                { error: 'Review not found' },
                { status: 404 }
            );
        }

        // Prevent self-reporting
        if (review.userId === userId) {
            return NextResponse.json(
                { error: 'You cannot report your own review' },
                { status: 400 }
            );
        }

        // Check if user has already reported this review
        const existingReport = await prisma.reviewReport.findFirst({
            where: {
                reviewId,
                userId,
            },
        });

        if (existingReport) {
            return NextResponse.json(
                { error: 'You have already reported this review' },
                { status: 400 }
            );
        }

        // Create report with sanitized input
        const report = await prisma.reviewReport.create({
            data: {
                reviewId,
                userId,
                reason: sanitizeInput(reason),
                notes: additionalNotes ? sanitizeInput(additionalNotes) : null,
            },
        });

        return NextResponse.json({
            success: true,
            reportId: report.id,
            message: 'Report submitted successfully',
        });
    } catch (error) {
        console.error('Error submitting report:', error);

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
                return NextResponse.json(
                    { error: 'A report for this review already exists' },
                    { status: 409 }
                );
            }
            return NextResponse.json(
                {
                    error: 'Database error occurred',
                    code: error.code,
                    message: 'There was an issue processing your report'
                },
                { status: 500 }
            );
        }

        if (error instanceof Prisma.PrismaClientValidationError) {
            return NextResponse.json(
                {
                    error: 'Validation error',
                    message: 'Invalid data format in request'
                },
                { status: 400 }
            );
        }

        // Generic error response
        return NextResponse.json(
            {
                error: 'Internal server error',
                message: 'An unexpected error occurred while submitting your report'
            },
            { status: 500 }
        );
    }
} 