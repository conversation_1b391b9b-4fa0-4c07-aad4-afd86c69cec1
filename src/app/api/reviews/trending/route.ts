import { NextResponse } from 'next/server';
import { getTrendingReviewsFromCache } from '@/app/util/databaseAnalytics';
import { cleanText, createFilter } from '@/app/store/badWordsFilter';
import { sanitizeDeletedCommentsInReviews } from '@/app/util/sanitizeDeletedComments';

export async function GET() {
  try {
    const result = await getTrendingReviewsFromCache();
    const filter = await createFilter();

    // Apply bad words filter to the reviews
    const filteredReviews = result.data.map((review: any) => ({
      ...review,
      comments: review.comments?.map((comment: any) => ({
        ...comment,
        content: filter.clean(comment.content),
      })) || [],
      content: filter.clean(review.content),
    }));

    return NextResponse.json({
      success: true,
      status: 200,
      data: filteredReviews
    });
  } catch (error) {
    console.error('Error fetching trending reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trending reviews' },
      { status: 500 }
    );
  }
}