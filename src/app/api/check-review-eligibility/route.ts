import { prisma } from "@/app/util/prismaClient";
import { NextResponse } from "next/server";

// Get cooldown days from env variable with fallback to 7 days
const REVIEW_COOLDOWN_DAYS = process.env.REVIEW_COOLDOWN_DAYS
    ? parseInt(process.env.REVIEW_COOLDOWN_DAYS)
    : 7;

export async function POST(request: Request) {
    try {
        const requestData = await request.json();
        const { userId, productId } = requestData || {};

        if (!userId || !productId) {
            return NextResponse.json({
                isEligible: false,
                message: "Missing required userId or productId",
                nextEligibleDate: null
            }, { status: 400 });
        }

        // Calculate cooldown date
        const cooldownDate = new Date();
        cooldownDate.setDate(cooldownDate.getDate() - REVIEW_COOLDOWN_DAYS);

        // Check for existing reviews
        const existingReview = await prisma.review.findFirst({
            where: {
                userId,
                productId,
                createdDate: { gte: cooldownDate }
            },
            orderBy: {
                createdDate: 'desc'
            }
        });

        if (existingReview) {
            // Calculate next eligible date
            const nextEligibleDate = new Date(existingReview.createdDate);
            nextEligibleDate.setDate(nextEligibleDate.getDate() + REVIEW_COOLDOWN_DAYS);

            // Calculate days remaining
            const daysRemaining = Math.max(1, Math.ceil(
                (nextEligibleDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
            ));

            return NextResponse.json({
                isEligible: false,
                message: `You can only submit one review for this product every ${REVIEW_COOLDOWN_DAYS} days.`,
                nextEligibleDate: nextEligibleDate.toISOString(),
                daysRemaining
            });
        }

        return NextResponse.json({
            isEligible: true,
            message: "",
            nextEligibleDate: null,
            daysRemaining: 0
        });
    } catch (error) {
        console.error("Error checking review eligibility:", error);
        return NextResponse.json({
            isEligible: false,
            message: "Error checking review eligibility",
            nextEligibleDate: null
        }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
} 