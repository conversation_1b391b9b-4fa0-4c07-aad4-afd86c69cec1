import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";

// GET /api/promotions/[id]/analytics - Get promotion analytics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get promotion with analytics
    const promotion = await prisma.promotion.findUnique({
      where: { id: params.id },
      include: {
        analytics: true,
        product: {
          select: { name: true }
        },
        business: {
          select: { id: true, ownerName: true }
        }
      }
    });

    if (!promotion) {
      return NextResponse.json(
        { success: false, error: "Promotion not found" },
        { status: 404 }
      );
    }

    // TODO: Add authorization check - user should own the business or be admin

    // Calculate additional metrics
    const analytics = promotion.analytics;
    if (!analytics) {
      return NextResponse.json(
        { success: false, error: "Analytics not found" },
        { status: 404 }
      );
    }

    // Calculate daily performance
    const dailyViews = analytics.dailyViews as Record<string, number>;
    const dailyClicks = analytics.dailyClicks as Record<string, number>;
    const dailyConversions = analytics.dailyConversions as Record<string, number>;

    // Get date range for the last 30 days
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    // Prepare daily data
    const dailyData = last30Days.map(date => ({
      date,
      views: dailyViews[date] || 0,
      clicks: dailyClicks[date] || 0,
      conversions: dailyConversions[date] || 0,
      ctr: dailyViews[date] ? ((dailyClicks[date] || 0) / dailyViews[date]) * 100 : 0,
      conversionRate: dailyClicks[date] ? ((dailyConversions[date] || 0) / dailyClicks[date]) * 100 : 0,
    }));

    // Calculate totals
    const totalViews = Object.values(dailyViews).reduce((sum, views) => sum + views, 0);
    const totalClicks = Object.values(dailyClicks).reduce((sum, clicks) => sum + clicks, 0);
    const totalConversions = Object.values(dailyConversions).reduce((sum, conversions) => sum + conversions, 0);

    // Calculate performance metrics
    const overallCtr = totalViews > 0 ? (totalClicks / totalViews) * 100 : 0;
    const overallConversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

    // Calculate trends (comparing last 7 days to previous 7 days)
    const last7Days = last30Days.slice(-7);
    const previous7Days = last30Days.slice(-14, -7);

    const last7DaysViews = last7Days.reduce((sum, date) => sum + (dailyViews[date] || 0), 0);
    const previous7DaysViews = previous7Days.reduce((sum, date) => sum + (dailyViews[date] || 0), 0);
    const viewsTrend = previous7DaysViews > 0 ? ((last7DaysViews - previous7DaysViews) / previous7DaysViews) * 100 : 0;

    const last7DaysClicks = last7Days.reduce((sum, date) => sum + (dailyClicks[date] || 0), 0);
    const previous7DaysClicks = previous7Days.reduce((sum, date) => sum + (dailyClicks[date] || 0), 0);
    const clicksTrend = previous7DaysClicks > 0 ? ((last7DaysClicks - previous7DaysClicks) / previous7DaysClicks) * 100 : 0;

    const response = {
      success: true,
      data: {
        promotion: {
          id: promotion.id,
          title: promotion.title,
          productName: promotion.product.name,
          isActive: promotion.isActive,
          startDate: promotion.startDate,
          endDate: promotion.endDate,
        },
        overview: {
          totalViews,
          totalClicks,
          totalConversions,
          ctr: overallCtr,
          conversionRate: overallConversionRate,
          averageOrderValue: analytics.averageOrderValue,
        },
        trends: {
          viewsTrend,
          clicksTrend,
          period: "7 days",
        },
        dailyData,
        deviceBreakdown: analytics.deviceBreakdown,
        topReferrers: analytics.topReferrers,
        lastUpdated: analytics.lastUpdated,
      }
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error fetching promotion analytics:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch analytics" },
      { status: 500 }
    );
  }
}