import { NextRequest, NextResponse } from "next/server";
import { trackPromotionView, trackPromotionClick, trackPromotionConversion } from "@/app/util/promotionService";

// POST /api/promotions/[id]/track - Track promotion interactions
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { action, orderValue } = await request.json();

    if (!action || !['view', 'click', 'conversion'].includes(action)) {
      return NextResponse.json(
        { success: false, error: "Valid action (view, click, conversion) is required" },
        { status: 400 }
      );
    }

    // Track the interaction
    switch (action) {
      case 'view':
        await trackPromotionView(params.id);
        break;
      case 'click':
        await trackPromotionClick(params.id);
        break;
      case 'conversion':
        await trackPromotionConversion(params.id, orderValue);
        break;
    }

    return NextResponse.json(
      { success: true, message: `${action} tracked successfully` },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error tracking promotion ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: "Failed to track interaction" },
      { status: 500 }
    );
  }
}