import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { CreateWidgetRequest, WidgetType, WidgetSecurityLevel } from "@/app/util/Interfaces";
import { $Enums } from '@prisma/client';
import { checkWidgetRateLimit } from "@/app/util/rateLimiting";

// GET /api/widgets - Get all widgets for a business
export async function GET(request: NextRequest) {
  // Apply rate limiting for widget management
  if (!checkWidgetRateLimit(request, 'WIDGET_MANAGEMENT')) {
    return NextResponse.json({
      success: false,
      status: 429,
      error: 'Rate limit exceeded'
    });
  }

  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const { searchParams } = new URL(request.url);
    const businessId = searchParams.get('businessId');

    if (!businessId) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Business ID is required'
      });
    }

    // Verify business ownership
    const business = await prisma.business.findUnique({
      where: { 
        id: businessId,
        ownerId: userId 
      }
    });

    if (!business) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Business not found or access denied'
      });
    }

    // Get widgets for the business
    const widgets = await prisma.widget.findMany({
      where: { businessId },
      include: {
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            display_image: true
          }
        },
        analytics: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      status: 200,
      data: widgets
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Get widgets error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// POST /api/widgets - Create a new widget
export async function POST(request: NextRequest) {
  // Apply rate limiting for widget management
  if (!checkWidgetRateLimit(request, 'WIDGET_MANAGEMENT')) {
    return NextResponse.json({
      success: false,
      status: 429,
      error: 'Rate limit exceeded'
    });
  }

  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const body: CreateWidgetRequest = await request.json();
    const { businessId, productId, name, type, securityLevel, config, styling, content, allowedDomains } = body;

    // Input validation and sanitization
    if (!businessId || !name?.trim() || !type) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Business ID, name, and type are required'
      });
    }

    // Sanitize input
    const sanitizedName = name.trim().slice(0, 100); // Limit name length

    // Validate widget type
    const validTypes: WidgetType[] = [
      'REVIEW_CAROUSEL', 'REVIEW_GRID', 'RATING_SUMMARY', 
      'MINI_REVIEW', 'BUSINESS_CARD', 'TRUST_BADGE', 'REVIEW_POPUP'
    ];
    
    if (!validTypes.includes(type)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Invalid widget type'
      });
    }

    // Verify business ownership
    const business = await prisma.business.findUnique({
      where: { 
        id: businessId,
        ownerId: userId 
      }
    });

    if (!business) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Business not found or access denied'
      });
    }

    // If productId is provided, verify it belongs to the business
    if (productId) {
      const product = await prisma.product.findUnique({
        where: { 
          id: productId,
          businessId: businessId
        }
      });

      if (!product) {
        return NextResponse.json({
          success: false,
          status: 400,
          error: 'Product not found or does not belong to this business'
        });
      }
    }

    // Get current domain for default allowed domains
    const requestUrl = new URL(request.url);
    const currentDomain = requestUrl.hostname;
    
    // Generate API key for secure widgets
    const finalSecurityLevel = securityLevel || 'SIMPLE';
    let apiKey = null;
    if (finalSecurityLevel === 'SECURE') {
      const crypto = require('crypto');
      apiKey = crypto.randomBytes(32).toString('hex');
    }
    
    // Create widget with default values
    const widgetData = {
        businessId,
        productId: productId || null,
        name: sanitizedName,
        type: type as $Enums.WidgetType,
        securityLevel: finalSecurityLevel as $Enums.WidgetSecurityLevel,
        apiKey: apiKey,
        tokenExpiry: 3600, // 1 hour default
        maxRequestsPerHour: 1000, // Default rate limit
        config: config || {},
        theme: styling?.theme || 'light',
        primaryColor: styling?.primaryColor || null,
        borderRadius: styling?.borderRadius || '8px',
        showLogo: content?.showLogo ?? true,
        showPoweredBy: content?.showPoweredBy ?? true,
        maxReviews: Math.min(Math.max(content?.maxReviews || 5, 1), 20), // Limit between 1-20
        showRating: content?.showRating ?? true,
        showReviewText: content?.showReviewText ?? true,
        showReviewDate: content?.showReviewDate ?? true,
        showReviewerName: content?.showReviewerName ?? true,
        allowedDomains: allowedDomains && allowedDomains.length > 0 
          ? [...new Set([...allowedDomains, 'reviewit.gy'])] // Always include reviewit.gy
          : ['reviewit.gy'], // Default to reviewit.gy
      };
    
    const widget = await prisma.widget.create({
      data: widgetData,
      include: {
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            display_image: true
          }
        }
      }
    });

    // Create analytics record
    await prisma.widgetAnalytics.create({
      data: {
        widgetId: widget.id,
        dailyViews: {},
        dailyClicks: {},
        dailyConversions: {},
        blockedEvents: {},
        topReferrers: {},
      }
    });

    return NextResponse.json({
      success: true,
      status: 201,
      data: widget
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Create widget error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}