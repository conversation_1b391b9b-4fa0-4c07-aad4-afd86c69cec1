import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy';
  
  const embedScript = `(function() {
  'use strict';
  
  // Widget embed configuration
  const WIDGET_BASE_URL = '${baseUrl}';
  const IFRAME_BASE_URL = WIDGET_BASE_URL + '/widgets/iframe/';
  
  // Widget embed class
  class ReviewItWidget {
    constructor(config) {
      this.config = {
        widgetId: config.widgetId,
        container: config.container || 'reviewit-widget',
        width: config.width || '100%',
        height: config.height || 'auto',
        maxHeight: config.maxHeight || '600px',
        loading: config.loading !== false, // Default to true
        ...config
      };
      
      this.iframe = null;
      this.container = null;
      this.init();
    }
    
    init() {
      // Find container element
      if (typeof this.config.container === 'string') {
        this.container = document.getElementById(this.config.container);
      } else if (this.config.container instanceof HTMLElement) {
        this.container = this.config.container;
      }
      
      if (!this.container) {
        console.error('ReviewIt Widget: Container not found');
        return;
      }
      
      if (!this.config.widgetId) {
        console.error('ReviewIt Widget: Widget ID is required');
        return;
      }
      
      this.createWidget();
    }
    
    createWidget() {
      // Create loading state
      if (this.config.loading) {
        this.showLoading();
      }
      
      // Create iframe
      this.iframe = document.createElement('iframe');
      this.iframe.src = IFRAME_BASE_URL + this.config.widgetId;
      this.iframe.style.width = this.config.width;
      this.iframe.style.height = this.config.height === 'auto' ? '200px' : this.config.height;
      this.iframe.style.maxHeight = this.config.maxHeight;
      this.iframe.style.border = 'none';
      this.iframe.style.overflow = 'hidden';
      this.iframe.style.display = 'block';
      this.iframe.setAttribute('scrolling', 'no');
      this.iframe.setAttribute('frameborder', '0');
      this.iframe.setAttribute('allowtransparency', 'true');
      
      // Add security attributes
      this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox');
      
      // Handle iframe load
      this.iframe.onload = () => {
        this.hideLoading();
        this.trackEvent('widget-loaded');
      };
      
      this.iframe.onerror = () => {
        this.showError('Failed to load widget');
      };
      
      // Listen for resize messages from iframe
      window.addEventListener('message', (event) => {
        if (event.origin !== WIDGET_BASE_URL) return;
        
        if (event.data.type === 'widget-resize' && 
            event.data.widgetId === this.config.widgetId) {
          this.resizeWidget(event.data.height);
        }
      });
      
      // Replace loading with iframe
      if (this.config.loading) {
        setTimeout(() => {
          this.container.innerHTML = '';
          this.container.appendChild(this.iframe);
        }, 100);
      } else {
        this.container.appendChild(this.iframe);
      }
    }
    
    showLoading() {
      this.container.innerHTML = \`
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100px;
          padding: 20px;
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          color: #6b7280;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
          "></div>
          Loading reviews...
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      \`;
    }
    
    hideLoading() {
      // Loading is replaced by iframe, nothing to do
    }
    
    showError(message) {
      this.container.innerHTML = \`
        <div style="
          padding: 20px;
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          color: #dc2626;
          text-align: center;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <strong>Widget Error:</strong> \${message}
        </div>
      \`;
    }
    
    resizeWidget(height) {
      if (this.iframe && this.config.height === 'auto') {
        this.iframe.style.height = Math.min(height, parseInt(this.config.maxHeight)) + 'px';
      }
    }
    
    trackEvent(event) {
      try {
        // Track widget events
        fetch(WIDGET_BASE_URL + '/api/public/widgets/' + this.config.widgetId + '/track', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event: event,
            referrer: document.referrer || window.location.href,
            userAgent: navigator.userAgent
          })
        }).catch(err => {
          // Silently fail tracking
          console.warn('Widget tracking failed:', err);
        });
      } catch (e) {
        // Silently fail
      }
    }
    
    destroy() {
      if (this.iframe && this.iframe.parentNode) {
        this.iframe.parentNode.removeChild(this.iframe);
      }
      this.iframe = null;
      this.container = null;
    }
  }
  
  // Auto-initialize widgets with data attributes
  function autoInitialize() {
    const widgets = document.querySelectorAll('[data-reviewit-widget]');
    widgets.forEach(element => {
      const widgetId = element.getAttribute('data-reviewit-widget');
      const width = element.getAttribute('data-width') || '100%';
      const height = element.getAttribute('data-height') || 'auto';
      const maxHeight = element.getAttribute('data-max-height') || '600px';
      
      if (widgetId && !element.hasAttribute('data-reviewit-initialized')) {
        new ReviewItWidget({
          widgetId: widgetId,
          container: element,
          width: width,
          height: height,
          maxHeight: maxHeight
        });
        element.setAttribute('data-reviewit-initialized', 'true');
      }
    });
  }
  
  // Expose global API
  window.ReviewItWidget = ReviewItWidget;
  window.ReviewItWidget.autoInitialize = autoInitialize;
  
  // Auto-initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInitialize);
  } else {
    autoInitialize();
  }
  
  // Re-initialize when new elements are added
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      let shouldReinit = false;
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              if (node.hasAttribute && node.hasAttribute('data-reviewit-widget')) {
                shouldReinit = true;
              } else if (node.querySelector) {
                const widgets = node.querySelectorAll('[data-reviewit-widget]');
                if (widgets.length > 0) {
                  shouldReinit = true;
                }
              }
            }
          });
        }
      });
      
      if (shouldReinit) {
        setTimeout(autoInitialize, 100);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
})();`;

  return new NextResponse(embedScript, {
    headers: {
      'Content-Type': 'application/javascript',
      'Access-Control-Allow-Origin': '*',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    },
  });
}