// CORS Analytics API for Widget Monitoring
// Provides detailed analytics about CORS errors and widget embedding issues

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { getCorsAnalytics } from '@/app/util/corsErrorMonitoring';
import { restrictedCorsMiddleware } from '@/app/util/corsMiddleware';

export async function GET(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    const { searchParams } = new URL(req.url);
    const days = parseInt(searchParams.get('days') || '7');
    const includeDetails = searchParams.get('details') === 'true';

    // Verify widget ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json(
        { error: 'Widget not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Check if user owns the widget's business
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user || widget.business.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden: You do not own this widget' },
        { 
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Get CORS analytics for this widget
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const corsErrors = await prisma.corsErrorLog.findMany({
      where: {
        widgetId: params.widgetId,
        timestamp: {
          gte: startDate
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: includeDetails ? 100 : undefined
    });

    // Get CORS alerts for this widget
    const corsAlerts = await prisma.corsAlert.findMany({
      where: {
        timestamp: {
          gte: startDate
        },
        // Filter alerts that mention this widget ID in metadata
        OR: [
          {
            metadata: {
              path: ['widgetId'],
              equals: params.widgetId
            }
          },
          {
            type: 'high_error_rate' // Global alerts are relevant to all widgets
          }
        ]
      },
      orderBy: {
        timestamp: 'desc'
      }
    });

    // Aggregate analytics
    const analytics: any = {
      summary: {
        totalErrors: corsErrors.length,
        totalAlerts: corsAlerts.length,
        period: `${days} days`,
        widgetId: params.widgetId
      },
      errorsByType: aggregateByField(corsErrors, 'errorType'),
      errorsByOrigin: aggregateByField(corsErrors, 'origin'),
      errorsByIP: aggregateByField(corsErrors, 'ipAddress'),
      dailyErrorCounts: getDailyErrorCounts(corsErrors),
      topErrorMessages: getTopErrorMessages(corsErrors),
      suspiciousActivity: getSuspiciousActivity(corsErrors),
      recentAlerts: corsAlerts.slice(0, 10),
      blockedDomains: getBlockedDomains(corsErrors),
      errorTrends: getErrorTrends(corsErrors, days)
    };

    if (includeDetails) {
      analytics.recentErrors = corsErrors.slice(0, 50);
    }

    return NextResponse.json(
      { analytics },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('CORS analytics API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  // If it's just headers, return a proper NextResponse
  return new NextResponse(null, { status: 200, headers: corsResult });
}

// Helper functions for analytics aggregation
function aggregateByField(errors: any[], field: string): Record<string, number> {
  return errors.reduce((acc, error) => {
    const value = error[field] || 'unknown';
    acc[value] = (acc[value] || 0) + 1;
    return acc;
  }, {});
}

function getDailyErrorCounts(errors: any[]): Record<string, number> {
  return errors.reduce((acc, error) => {
    const date = new Date(error.timestamp).toISOString().split('T')[0];
    acc[date] = (acc[date] || 0) + 1;
    return acc;
  }, {});
}

function getTopErrorMessages(errors: any[]): Array<{ message: string; count: number }> {
  const messageCounts = aggregateByField(errors, 'errorMessage');
  return Object.entries(messageCounts)
    .map(([message, count]) => ({ message, count: count as number }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getSuspiciousActivity(errors: any[]): Array<{ ip: string; errorCount: number; errorTypes: string[] }> {
  const ipErrors = errors.reduce((acc, error) => {
    const ip = error.ipAddress;
    if (!acc[ip]) {
      acc[ip] = { errorCount: 0, errorTypes: new Set<string>() };
    }
    acc[ip].errorCount++;
    acc[ip].errorTypes.add(error.errorType);
    return acc;
  }, {} as Record<string, { errorCount: number; errorTypes: Set<string> }>);

  return Object.entries(ipErrors)
    .filter(([, data]) => (data as { errorCount: number; errorTypes: Set<string> }).errorCount >= 3) // Threshold for suspicious
    .map(([ip, data]) => ({
      ip,
      errorCount: (data as { errorCount: number; errorTypes: Set<string> }).errorCount,
      errorTypes: Array.from((data as { errorCount: number; errorTypes: Set<string> }).errorTypes)
    }))
    .sort((a, b) => b.errorCount - a.errorCount)
    .slice(0, 10);
}

function getBlockedDomains(errors: any[]): Array<{ domain: string; blockCount: number; lastBlocked: string }> {
  const blockedOrigins = errors
    .filter(error => error.errorType === 'blocked_origin' || error.errorType === 'unauthorized_domain')
    .reduce((acc, error) => {
      const origin = error.origin || 'unknown';
      if (!acc[origin]) {
        acc[origin] = { blockCount: 0, lastBlocked: error.timestamp };
      }
      acc[origin].blockCount++;
      if (new Date(error.timestamp) > new Date(acc[origin].lastBlocked)) {
        acc[origin].lastBlocked = error.timestamp;
      }
      return acc;
    }, {} as Record<string, { blockCount: number; lastBlocked: string }>);

  return Object.entries(blockedOrigins)
    .map(([domain, data]) => ({
      domain,
      blockCount: (data as { blockCount: number; lastBlocked: string }).blockCount,
      lastBlocked: (data as { blockCount: number; lastBlocked: string }).lastBlocked
    }))
    .sort((a, b) => b.blockCount - a.blockCount)
    .slice(0, 10);
}

function getErrorTrends(errors: any[], days: number): { 
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercentage: number;
  comparison: string;
} {
  const midpoint = Math.floor(days / 2);
  const midDate = new Date();
  midDate.setDate(midDate.getDate() - midpoint);

  const recentErrors = errors.filter(error => new Date(error.timestamp) >= midDate);
  const olderErrors = errors.filter(error => new Date(error.timestamp) < midDate);

  const recentCount = recentErrors.length;
  const olderCount = olderErrors.length;

  let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
  let changePercentage = 0;

  if (olderCount > 0) {
    changePercentage = ((recentCount - olderCount) / olderCount) * 100;
    if (changePercentage > 10) {
      trend = 'increasing';
    } else if (changePercentage < -10) {
      trend = 'decreasing';
    }
  } else if (recentCount > 0) {
    trend = 'increasing';
    changePercentage = 100;
  }

  return {
    trend,
    changePercentage: Math.round(changePercentage),
    comparison: `${recentCount} recent vs ${olderCount} older errors`
  };
}