// Domain Management API for Widget CORS Configuration
// Allows business owners to manage allowed domains for widget embedding

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { restrictedCorsMiddleware } from '@/app/util/corsMiddleware';
import { z } from 'zod';

// Validation schemas
const domainSchema = z.object({
  domain: z.string()
    .min(1, 'Domain cannot be empty')
    .regex(/^(localhost(:[0-9]+)?|[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}(:[0-9]+)?)$/, 'Invalid domain format')
    .transform(domain => domain.toLowerCase())
});

const domainsUpdateSchema = z.object({
  domains: z.array(z.string())
    .max(50, 'Maximum 50 domains allowed')
    .transform(domains => domains.map(d => d.toLowerCase()))
});

const domainActionSchema = z.object({
  action: z.enum(['add', 'remove']),
  domain: z.string()
    .min(1, 'Domain cannot be empty')
    .transform(domain => domain.toLowerCase())
});

// GET: Retrieve allowed domains for a widget
export async function GET(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    // Verify widget ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json(
        { error: 'Widget not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Check if user owns the widget's business
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user || widget.business.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden: You do not own this widget' },
        { 
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Parse allowed domains
    const allowedDomains = Array.isArray(widget.allowedDomains) 
      ? widget.allowedDomains 
      : [];

    return NextResponse.json(
      {
        success: true,
        widgetId: params.widgetId,
        allowedDomains,
        totalDomains: allowedDomains.length,
        maxDomains: 50,
        isPublic: allowedDomains.includes('*')
      },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    console.error('Domain management GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

// PUT: Update the complete list of allowed domains
export async function PUT(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    const body = await req.json();
    const validatedData = domainsUpdateSchema.parse(body);

    // Verify widget ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json(
        { error: 'Widget not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Check if user owns the widget's business
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user || widget.business.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden: You do not own this widget' },
        { 
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Validate domains
    const validDomains = [];
    const invalidDomains = [];

    for (const domain of validatedData.domains) {
      if (domain === '*') {
        validDomains.push(domain); // Allow wildcard for public widgets
      } else {
        try {
          domainSchema.parse({ domain });
          validDomains.push(domain);
        } catch {
          invalidDomains.push(domain);
        }
      }
    }

    if (invalidDomains.length > 0) {
      return NextResponse.json(
        { 
          error: 'Invalid domains found',
          invalidDomains,
          validDomains
        },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    // Remove duplicates
    const uniqueDomains = [...new Set(validDomains)];

    // Update widget with new domains
    const updatedWidget = await prisma.widget.update({
      where: { id: params.widgetId },
      data: {
        allowedDomains: uniqueDomains,
        updatedAt: new Date()
      }
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Domains updated successfully',
        widgetId: params.widgetId,
        allowedDomains: uniqueDomains,
        totalDomains: uniqueDomains.length,
        isPublic: uniqueDomains.includes('*')
      },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors
        },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    console.error('Domain management PUT error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

// POST: Add or remove a single domain
export async function POST(
  req: NextRequest,
  { params }: { params: { widgetId: string } }
): Promise<NextResponse> {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return restrictedCorsMiddleware(req) as NextResponse;
  }

  // Get CORS headers for all responses
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  const corsHeaders: Record<string, string> = corsResult;

  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { 
          status: 401,
          headers: corsHeaders
        }
      );
    }

    const body = await req.json();
    const validatedData = domainActionSchema.parse(body);

    // Verify widget ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json(
        { error: 'Widget not found' },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }

    // Check if user owns the widget's business
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId }
    });

    if (!user || widget.business.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden: You do not own this widget' },
        { 
          status: 403,
          headers: corsHeaders
        }
      );
    }

    // Validate domain (unless it's wildcard)
    if (validatedData.domain !== '*') {
      try {
        domainSchema.parse({ domain: validatedData.domain });
      } catch {
        return NextResponse.json(
          { error: 'Invalid domain format' },
          { 
            status: 400,
            headers: corsHeaders
          }
        );
      }
    }

    // Get current domains
    const currentDomains = Array.isArray(widget.allowedDomains) 
      ? widget.allowedDomains 
      : [];

    let updatedDomains: string[];
    let message: string;

    if (validatedData.action === 'add') {
      if (currentDomains.includes(validatedData.domain)) {
        return NextResponse.json(
          { error: 'Domain already exists' },
          { 
            status: 409,
            headers: corsHeaders
          }
        );
      }

      if (currentDomains.length >= 50) {
        return NextResponse.json(
          { error: 'Maximum number of domains (50) reached' },
          { 
            status: 400,
            headers: corsHeaders
          }
        );
      }

      updatedDomains = [...currentDomains, validatedData.domain];
      message = 'Domain added successfully';
    } else {
      if (!currentDomains.includes(validatedData.domain)) {
        return NextResponse.json(
          { error: 'Domain not found' },
          { 
            status: 404,
            headers: corsHeaders
          }
        );
      }

      updatedDomains = currentDomains.filter(d => d !== validatedData.domain);
      message = 'Domain removed successfully';
    }

    // Update widget
    await prisma.widget.update({
      where: { id: params.widgetId },
      data: {
        allowedDomains: updatedDomains,
        updatedAt: new Date()
      }
    });

    return NextResponse.json(
      {
        success: true,
        message,
        action: validatedData.action,
        domain: validatedData.domain,
        widgetId: params.widgetId,
        allowedDomains: updatedDomains,
        totalDomains: updatedDomains.length,
        isPublic: updatedDomains.includes('*')
      },
      { 
        status: 200,
        headers: corsHeaders
      }
    );

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors
        },
        { 
          status: 400,
          headers: corsHeaders
        }
      );
    }

    console.error('Domain management POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { 
        status: 500,
        headers: corsHeaders
      }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  const corsResult = restrictedCorsMiddleware(req);
  if (corsResult instanceof NextResponse) {
    return corsResult;
  }
  // If it's just headers, return a proper NextResponse
  return new NextResponse(null, { status: 200, headers: corsResult });
}