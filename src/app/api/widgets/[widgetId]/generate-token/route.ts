import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { TokenGenerationRequest, TokenGenerationResponse, WidgetTokenPayload, VerificationMethod } from "@/app/util/Interfaces";
import { $Enums } from '@prisma/client';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// POST /api/widgets/[widgetId]/generate-token - Generate secure token for verified domain
export async function POST(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const body: TokenGenerationRequest = await request.json();
    const { domain } = body;

    if (!domain) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain is required'
      });
    }

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Check if widget is secure
    if (widget.securityLevel !== 'SECURE') {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Token generation is only available for secure widgets'
      });
    }

    // Verify domain ownership
    const domainVerification = await prisma.domainVerification.findUnique({
      where: {
        widgetId_domain: {
          widgetId: params.widgetId,
          domain: domain
        }
      }
    });

    if (!domainVerification || !domainVerification.isVerified) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain must be verified before generating tokens'
      });
    }

    // Check if domain verification has expired
    if (domainVerification.expiresAt < new Date()) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain verification has expired. Please re-verify the domain.'
      });
    }

    // Generate JWT token
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = new Date(Date.now() + (widget.tokenExpiry * 1000));
    
    const payload: WidgetTokenPayload = {
      widgetId: params.widgetId,
      domain: domain,
      iat: now,
      exp: now + widget.tokenExpiry,
      permissions: ['read']
    };

    // Use widget's API key as JWT secret (or fallback to env secret)
    const jwtSecret = widget.apiKey || process.env.WIDGET_JWT_SECRET;
    if (!jwtSecret) {
      return NextResponse.json({
        success: false,
        status: 500,
        error: 'JWT secret not configured'
      });
    }

    const token = jwt.sign(payload, jwtSecret);

    // Store token in database
    await prisma.widgetToken.upsert({
      where: {
        widgetId_domain: {
          widgetId: params.widgetId,
          domain: domain
        }
      },
      create: {
        widgetId: params.widgetId,
        domain: domain,
        token: token,
        expiresAt: expiresAt
      },
      update: {
        token: token,
        expiresAt: expiresAt,
        isActive: true,
        requestCount: 0
      }
    });

    // Generate embed code
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://reviewit.gy';
    const embedCode = `<div data-reviewit-secure-widget="${params.widgetId}" data-token="${token}"></div>
<script src="${baseUrl}/widgets/secure-embed.js"></script>`;

    const response: TokenGenerationResponse = {
      success: true,
      data: {
        token: token,
        domain: domain,
        expiresAt: expiresAt,
        embedCode: embedCode
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Token generation error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// GET /api/widgets/[widgetId]/generate-token - Get existing tokens for widget
export async function GET(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        },
        tokens: {
          where: {
            isActive: true
          },
          select: {
            domain: true,
            expiresAt: true,
            requestCount: true,
            lastUsed: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Filter out expired tokens and mark them as inactive
    const now = new Date();
    const activeTokens = [];
    const expiredTokenIds = [];

    for (const token of widget.tokens) {
      if (token.expiresAt < now) {
        expiredTokenIds.push(token);
      } else {
        activeTokens.push({
          domain: token.domain,
          expiresAt: token.expiresAt,
          requestCount: token.requestCount,
          lastUsed: token.lastUsed,
          createdAt: token.createdAt,
          isExpired: false
        });
      }
    }

    // Mark expired tokens as inactive
    if (expiredTokenIds.length > 0) {
      await prisma.widgetToken.updateMany({
        where: {
          widgetId: params.widgetId,
          expiresAt: {
            lt: now
          }
        },
        data: {
          isActive: false
        }
      });
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        tokens: activeTokens,
        totalActive: activeTokens.length,
        securityLevel: widget.securityLevel
      }
    });

  } catch (error) {
    console.error('Get tokens error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// DELETE /api/widgets/[widgetId]/generate-token - Revoke token for specific domain
export async function DELETE(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const { domain } = await request.json();

    if (!domain) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain is required'
      });
    }

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Revoke token
    const result = await prisma.widgetToken.updateMany({
      where: {
        widgetId: params.widgetId,
        domain: domain,
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    if (result.count === 0) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Active token not found for this domain'
      });
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        message: 'Token revoked successfully',
        domain: domain
      }
    });

  } catch (error) {
    console.error('Token revocation error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}