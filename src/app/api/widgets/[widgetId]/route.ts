import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { UpdateWidgetRequest } from "@/app/util/Interfaces";


// GET /api/widgets/[widgetId] - Get specific widget
export async function GET(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            id: true,
            ownerId: true,
            ownerName: true,
            isVerified: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            display_image: true,
            rating: true,
            _count: {
              select: {
                reviews: true
              }
            }
          }
        },
        analytics: true
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    // Verify ownership
    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Note: Removed temporary testing override - domain validation now works properly
    // with embedDomain parameter from embed.js script

    return NextResponse.json({
      success: true,
      status: 200,
      data: widget
    });

  } catch (error) {
    console.error('Get widget error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// PUT /api/widgets/[widgetId] - Update widget
export async function PUT(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const body: UpdateWidgetRequest = await request.json();
    const { name, config, styling, content, isActive, allowedDomains } = body;

    // Get existing widget and verify ownership
    const existingWidget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!existingWidget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (existingWidget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Build update data
    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name;
    if (config !== undefined) updateData.config = config;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (allowedDomains !== undefined) updateData.allowedDomains = allowedDomains;
    
    // Handle styling updates
    if (styling) {
      if (styling.theme !== undefined) updateData.theme = styling.theme;
      if (styling.primaryColor !== undefined) updateData.primaryColor = styling.primaryColor;
      if (styling.borderRadius !== undefined) updateData.borderRadius = styling.borderRadius;
    }
    
    // Handle content updates
    if (content) {
      if (content.maxReviews !== undefined) updateData.maxReviews = content.maxReviews;
      if (content.showLogo !== undefined) updateData.showLogo = content.showLogo;
      if (content.showPoweredBy !== undefined) updateData.showPoweredBy = content.showPoweredBy;
      if (content.showRating !== undefined) updateData.showRating = content.showRating;
      if (content.showReviewText !== undefined) updateData.showReviewText = content.showReviewText;
      if (content.showReviewDate !== undefined) updateData.showReviewDate = content.showReviewDate;
      if (content.showReviewerName !== undefined) updateData.showReviewerName = content.showReviewerName;
    }

    // Update widget
    const updatedWidget = await prisma.widget.update({
      where: { id: params.widgetId },
      data: updateData,
      include: {
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            display_image: true
          }
        },
        analytics: true
      }
    });

    return NextResponse.json({
      success: true,
      status: 200,
      data: updatedWidget
    });

  } catch (error) {
    console.error('Update widget error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// DELETE /api/widgets/[widgetId] - Delete widget
export async function DELETE(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Delete widget (this will cascade delete analytics due to onDelete: Cascade)
    await prisma.widget.delete({
      where: { id: params.widgetId }
    });

    return NextResponse.json({
      success: true,
      status: 200,
      data: { message: 'Widget deleted successfully' }
    });

  } catch (error) {
    console.error('Delete widget error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}