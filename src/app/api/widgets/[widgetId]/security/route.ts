import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { WidgetSecuritySettings, WidgetSecurityLevel } from "@/app/util/Interfaces";
import { $Enums } from '@prisma/client';
import crypto from 'crypto';

// GET /api/widgets/[widgetId]/security - Get widget security configuration
export async function GET(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        },
        domainVerifications: {
          select: {
            domain: true,
            isVerified: true,
            verifiedAt: true,
            method: true,
            expiresAt: true
          }
        },
        tokens: {
          where: {
            isActive: true,
            expiresAt: {
              gt: new Date()
            }
          },
          select: {
            domain: true,
            expiresAt: true,
            requestCount: true,
            lastUsed: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    // Verify ownership
    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    const securityConfig: WidgetSecuritySettings & {
      verifiedDomains: any[];
      activeTokens: any[];
    } = {
      securityLevel: widget.securityLevel,
      allowedDomains: widget.allowedDomains,
      tokenExpiry: widget.tokenExpiry,
      maxRequestsPerHour: widget.maxRequestsPerHour,
      verifiedDomains: widget.domainVerifications,
      activeTokens: widget.tokens
    };

    return NextResponse.json({
      success: true,
      status: 200,
      data: securityConfig
    });

  } catch (error) {
    console.error('Get widget security error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// PUT /api/widgets/[widgetId]/security - Update widget security settings
export async function PUT(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const body: Partial<WidgetSecuritySettings> = await request.json();
    const { securityLevel, allowedDomains, tokenExpiry, maxRequestsPerHour } = body;

    // Get existing widget and verify ownership
    const existingWidget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!existingWidget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (existingWidget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Validate security level change
    if (securityLevel && !['SIMPLE', 'SECURE'].includes(securityLevel)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Invalid security level'
      });
    }

    // Validate token expiry (1 hour to 30 days)
    if (tokenExpiry && (tokenExpiry < 3600 || tokenExpiry > 2592000)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Token expiry must be between 1 hour and 30 days'
      });
    }

    // Validate rate limit (10 to 10000 requests per hour)
    if (maxRequestsPerHour && (maxRequestsPerHour < 10 || maxRequestsPerHour > 10000)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Rate limit must be between 10 and 10000 requests per hour'
      });
    }

    // Build update data
    const updateData: any = {};
    
    if (securityLevel !== undefined) {
      updateData.securityLevel = securityLevel as $Enums.WidgetSecurityLevel;
      
      // Generate API key for secure widgets
      if (securityLevel === 'SECURE' && !existingWidget.apiKey) {
        updateData.apiKey = crypto.randomBytes(32).toString('hex');
      }
      
      // If changing from SECURE to SIMPLE, invalidate all tokens
      if (securityLevel === 'SIMPLE' && existingWidget.securityLevel === 'SECURE') {
        await prisma.widgetToken.updateMany({
          where: { widgetId: params.widgetId },
          data: { isActive: false }
        });
      }
    }
    
    if (allowedDomains !== undefined) updateData.allowedDomains = allowedDomains;
    if (tokenExpiry !== undefined) updateData.tokenExpiry = tokenExpiry;
    if (maxRequestsPerHour !== undefined) updateData.maxRequestsPerHour = maxRequestsPerHour;

    // Update widget
    const updatedWidget = await prisma.widget.update({
      where: { id: params.widgetId },
      data: updateData,
      include: {
        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true
          }
        },
        domainVerifications: {
          select: {
            domain: true,
            isVerified: true,
            verifiedAt: true,
            method: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        securityLevel: updatedWidget.securityLevel,
        allowedDomains: updatedWidget.allowedDomains,
        tokenExpiry: updatedWidget.tokenExpiry,
        maxRequestsPerHour: updatedWidget.maxRequestsPerHour,
        apiKey: updatedWidget.apiKey ? '***' + updatedWidget.apiKey.slice(-4) : null,
        verifiedDomains: updatedWidget.domainVerifications
      }
    });

  } catch (error) {
    console.error('Update widget security error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}