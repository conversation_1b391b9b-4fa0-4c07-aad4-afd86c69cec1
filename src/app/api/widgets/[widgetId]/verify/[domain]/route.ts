import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { VerificationMethod } from "@/app/util/Interfaces";
import { $Enums } from '@prisma/client';
import dns from 'dns';
import { promisify } from 'util';

const resolveTxt = promisify(dns.resolveTxt);

// POST /api/widgets/[widgetId]/verify/[domain] - Verify domain ownership
export async function POST(
  request: NextRequest,
  { params }: { params: { widgetId: string; domain: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const { method } = await request.json();

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Get verification record
    const verification = await prisma.domainVerification.findUnique({
      where: {
        widgetId_domain: {
          widgetId: params.widgetId,
          domain: params.domain
        }
      }
    });

    if (!verification) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Verification record not found'
      });
    }

    // Check if verification has expired
    if (verification.expiresAt < new Date()) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Verification code has expired. Please generate a new one.'
      });
    }

    // Perform verification based on method
    let isVerified = false;
    let errorMessage = '';

    try {
      switch (verification.method) {
        case 'HTML_FILE':
          isVerified = await verifyHtmlFile(params.domain, verification.verificationCode);
          if (!isVerified) {
            errorMessage = 'HTML verification file not found or invalid';
          }
          break;

        case 'DNS_TXT':
          isVerified = await verifyDnsRecord(params.domain, verification.verificationCode);
          if (!isVerified) {
            errorMessage = 'DNS TXT record not found or invalid';
          }
          break;

        case 'META_TAG':
          isVerified = await verifyMetaTag(params.domain, verification.verificationCode);
          if (!isVerified) {
            errorMessage = 'Meta tag not found or invalid';
          }
          break;

        default:
          return NextResponse.json({
            success: false,
            status: 400,
            error: 'Invalid verification method'
          });
      }
    } catch (error) {
      console.error('Verification error:', error);
      errorMessage = 'Verification failed due to technical error';
    }

    if (isVerified) {
      // Mark domain as verified
      await prisma.domainVerification.update({
        where: { id: verification.id },
        data: {
          isVerified: true,
          verifiedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        status: 200,
        data: {
          message: 'Domain verified successfully',
          domain: params.domain,
          verifiedAt: new Date(),
          method: verification.method
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        status: 400,
        error: errorMessage || 'Domain verification failed'
      });
    }

  } catch (error) {
    console.error('Domain verification error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// Verification helper functions
async function verifyHtmlFile(domain: string, verificationCode: string): Promise<boolean> {
  try {
    const verificationUrl = `https://${domain}/reviewit-verification-${verificationCode}.html`;
    
    const response = await fetch(verificationUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'ReviewIt-Verification-Bot/1.0'
      },
      // Add timeout
      signal: AbortSignal.timeout(10000) // 10 seconds
    });

    if (!response.ok) {
      // Try HTTP if HTTPS fails
      const httpUrl = `http://${domain}/reviewit-verification-${verificationCode}.html`;
      const httpResponse = await fetch(httpUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'ReviewIt-Verification-Bot/1.0'
        },
        signal: AbortSignal.timeout(10000)
      });
      
      if (!httpResponse.ok) {
        return false;
      }
      
      const content = await httpResponse.text();
      return content.includes(verificationCode);
    }

    const content = await response.text();
    return content.includes(verificationCode);
  } catch (error) {
    console.error('HTML file verification error:', error);
    return false;
  }
}

async function verifyDnsRecord(domain: string, verificationCode: string): Promise<boolean> {
  try {
    const txtRecords = await resolveTxt(domain);
    
    // Check for reviewit-verification TXT record
    for (const record of txtRecords) {
      const recordValue = Array.isArray(record) ? record.join('') : record;
      if (recordValue.includes(`reviewit-verification=${verificationCode}`) || 
          recordValue === verificationCode) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('DNS verification error:', error);
    return false;
  }
}

async function verifyMetaTag(domain: string, verificationCode: string): Promise<boolean> {
  try {
    // Try HTTPS first, then HTTP
    let response;
    try {
      response = await fetch(`https://${domain}`, {
        method: 'GET',
        headers: {
          'User-Agent': 'ReviewIt-Verification-Bot/1.0'
        },
        signal: AbortSignal.timeout(10000)
      });
    } catch (httpsError) {
      response = await fetch(`http://${domain}`, {
        method: 'GET',
        headers: {
          'User-Agent': 'ReviewIt-Verification-Bot/1.0'
        },
        signal: AbortSignal.timeout(10000)
      });
    }

    if (!response.ok) {
      return false;
    }

    const html = await response.text();
    
    // Check for meta tag with verification code
    const metaTagRegex = /<meta\s+name=["']reviewit-verification["']\s+content=["']([^"']+)["']\s*\/?>/i;
    const match = html.match(metaTagRegex);
    
    if (match && match[1] === verificationCode) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Meta tag verification error:', error);
    return false;
  }
}