import { NextResponse } from 'next/server';
import { prisma } from '@/app/util/prismaClient';

export async function GET() {
  try {
    const topReviewers = await prisma.user.findMany({
      where: {
        reviews: {
          some: {
            isPublic: true,
            isDeleted: false
          }
        }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        avatar: true,
        _count: {
          select: {
            reviews: {
              where: {
                isPublic: true,
                isDeleted: false
              }
            }
          }
        }
      },
      orderBy: {
        reviews: {
          _count: 'desc'
        }
      },
      take: 6
    });

    const response = NextResponse.json({
      success: true,
      data: topReviewers
    });

    // Add headers to prevent caching
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('Surrogate-Control', 'no-store');

    return response;
  } catch (error) {
    console.error('Error fetching top reviewers:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch top reviewers' },
      { status: 500 }
    );
  }
}