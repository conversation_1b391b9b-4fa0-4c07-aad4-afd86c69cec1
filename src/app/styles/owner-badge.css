/* Owner badge animation */
@keyframes shine {
  0% {
    opacity: 0.8;
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    filter: brightness(1.2);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
    filter: brightness(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes border-shine {
  0% {
    border-image: linear-gradient(
        90deg,
        #1e40af,
        #3b82f6,
        #60a5fa,
        #3b82f6,
        #1e40af
      )
      1;
    border-image-slice: 1;
  }
  50% {
    border-image: linear-gradient(
        90deg,
        #60a5fa,
        #1e40af,
        #3b82f6,
        #1e40af,
        #60a5fa
      )
      1;
    border-image-slice: 1;
  }
  100% {
    border-image: linear-gradient(
        90deg,
        #1e40af,
        #3b82f6,
        #60a5fa,
        #3b82f6,
        #1e40af
      )
      1;
    border-image-slice: 1;
  }
}

/* Reflective badge animation */
@keyframes reflect {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced reflective effect for badges */
@keyframes badge-shine {
  0% {
    background-position: 0% 50%;
    box-shadow:
      0 0 5px rgba(59, 130, 246, 0.5),
      0 0 10px rgba(59, 130, 246, 0.2),
      inset 0 0 3px rgba(255, 255, 255, 0.6);
  }
  50% {
    background-position: 100% 50%;
    box-shadow:
      0 0 10px rgba(59, 130, 246, 0.7),
      0 0 20px rgba(59, 130, 246, 0.3),
      inset 0 0 5px rgba(255, 255, 255, 0.8);
  }
  100% {
    background-position: 0% 50%;
    box-shadow:
      0 0 5px rgba(59, 130, 246, 0.5),
      0 0 10px rgba(59, 130, 246, 0.2),
      inset 0 0 3px rgba(255, 255, 255, 0.6);
  }
}

.owner-badge {
  position: relative;
  background: linear-gradient(
    90deg,
    #1e40af,
    #3b82f6,
    #60a5fa,
    #3b82f6,
    #1e40af
  );
  background-size: 200% 100%;
  animation:
    shimmer 3s infinite linear,
    badge-shine 4s infinite ease-in-out;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Reflective overlay */
.owner-badge::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: reflect 2s infinite;
  pointer-events: none;
}

/* Special styling for notification badges */
.notification-owner-badge {
  composes: owner-badge;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  letter-spacing: 0.5px;
  transform: translateY(0);
  transition: transform 0.2s ease;
}

.notification-owner-badge:hover {
  transform: translateY(-1px);
}

/* Legacy owner comment styles - now handled by Tailwind */
.owner-comment {
  /* Removed dark border styling - now using Tailwind classes */
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Blue shimmer effect for text */
.owner-text {
  color: #1a202c; /* Dark gray for better contrast by default */
  font-weight: 600;
}

@media (prefers-reduced-motion: no-preference) {
  .owner-text {
    background: linear-gradient(
      90deg,
      #1e40af,
      #2563eb,
      #3b82f6,
      #2563eb,
      #1e40af
    );
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: text-shimmer 4s linear infinite;
  }
}

@keyframes text-shimmer {
  to {
    background-position: 200% center;
  }
}

/* Unread notification styling */
.unread-notification {
  position: relative;
}

.unread-notification::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.unread-dot {
  position: relative;
  width: 8px;
  height: 8px;
  background: linear-gradient(to bottom right, #3b82f6, #60a5fa);
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Reduced motion preference support */
@media (prefers-reduced-motion: reduce) {
  .owner-badge,
  .owner-comment,
  .owner-text,
  .notification-owner-badge {
    animation: none;
    background: #dbeafe;
    border: 2px solid #3b82f6;
    -webkit-text-fill-color: initial;
    color: #1e40af;
    background-clip: initial;
  }

  .owner-badge::after,
  .notification-owner-badge::after {
    display: none;
  }

  .unread-dot {
    animation: none;
    box-shadow: none;
  }
}
