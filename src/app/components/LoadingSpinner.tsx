import React from "react";
import PulseLoader from "react-spinners/PulseLoader";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = "md" }) => {
  // Size mapping for PulseLoader
  const sizeMap = {
    sm: 5,
    md: 8,
    lg: 12
  };

  // Container size mapping
  const containerSizeMap = {
    sm: "h-8 w-8",
    md: "h-12 w-12",
    lg: "h-16 w-16"
  };

  return (
    <div className="flex flex-col justify-center items-center text-center">
      <div className={`${containerSizeMap[size]} flex justify-center items-center`} role="status">
        <PulseLoader color="green" loading={true} size={sizeMap[size]} />
      </div>
      {/* <div className="w-full h-full flex justify-center items-center" role="status"> */}
      {/* <p className="text-gray-400 animate-pulse">Loading...</p> */}
      {/* </div> */}
    </div>
  );
};

export default LoadingSpinner;
