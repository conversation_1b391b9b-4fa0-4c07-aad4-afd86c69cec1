import React, { Dispatch, SetStateAction } from 'react';
import { MoreHorizontal, PencilIcon, TrashIcon } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface OptionsMenuProps {
  onEdit: () => void;
  onDelete: () => void;
  setIsEditing: Dispatch<SetStateAction<boolean>>;
}

const OptionsMenu: React.FC<OptionsMenuProps> = ({ onEdit, onDelete, setIsEditing }) => {
  const handleEdit = () => {
    setIsEditing(true);
    onEdit();
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-5 w-5 p-0 text-gray-400 hover:text-gray-600">
          <MoreHorizontal className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="text-xs">
        <DropdownMenuItem onClick={handleEdit} className="py-1.5 px-2">
          <PencilIcon className="mr-1.5 h-3 w-3" />
          <span>Edit</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onDelete} className="py-1.5 px-2 text-red-500 focus:text-red-500">
          <TrashIcon className="mr-1.5 h-3 w-3" />
          <span>Delete</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default OptionsMenu;
