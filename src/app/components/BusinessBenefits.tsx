"use client";

import React from "react";
import { CheckCircle, Target, Star } from "lucide-react";

interface Benefit {
  icon: React.ReactNode;
  text: string;
}

const BusinessBenefits: React.FC = () => {
  const benefits: Benefit[] = [
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Increase customer trust and credibility through verified reviews",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Improve online visibility and search rankings with a strong review profile",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Get valuable customer feedback and actionable insights",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Respond to reviews and build stronger customer relationships",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Track performance with detailed analytics and reporting",
    },
    {
      icon: <CheckCircle className="w-5 h-5 text-green-600" />,
      text: "Stand out from competitors with verified reviews and badges",
    },
  ];

  return (
    <section className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Choose ReviewIt for Your Business?
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Join hundreds of successful Guyanese businesses that trust
              ReviewIt to manage their online reputation and grow their
              customer base.
            </p>

            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  {benefit.icon}
                  <span className="text-gray-700 font-medium">
                    {benefit.text}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="relative">
            <div className="bg-white rounded-2xl shadow-xl p-8 transform rotate-2 hover:rotate-0 transition-transform duration-300">
              <div className="flex items-center gap-3 mb-6">
                <Target className="w-8 h-8 text-myTheme-primary" />
                <h3 className="text-2xl font-bold text-gray-900">
                  Success Story
                </h3>
              </div>
              <blockquote className="text-gray-700 italic mb-4">
                &ldquo;Since joining ReviewIt, our customer engagement
                increased by 200% and we gained valuable insights that helped
                us improve our services significantly. The ability to respond to reviews
                directly has transformed our customer relationships.&rdquo;
              </blockquote>
              <div className="flex items-center gap-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  - Georgetown Business Owner
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BusinessBenefits;
