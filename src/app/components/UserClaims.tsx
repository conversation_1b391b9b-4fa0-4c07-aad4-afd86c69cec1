"use client";

import React, { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { iProductClaim } from "@/app/util/Interfaces";
import Link from "next/link";
import { formatDistanceToNow, format } from "date-fns";
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";

interface UserClaimsProps {
    limit?: number;
    showViewAll?: boolean;
}

export default function UserClaims({ limit = 5, showViewAll = true }: UserClaimsProps) {
    const { userId } = useAuth();
    const [claims, setClaims] = useState<iProductClaim[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState<string>("all");

    useEffect(() => {
        const fetchClaims = async () => {
            try {
                const response = await fetch(`/api/user/claims${limit ? `?limit=${limit}` : ''}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch claims');
                }
                const data = await response.json();
                setClaims(data);
            } catch (err) {
                setError('Error loading claims');
                console.error('Error fetching claims:', err);
            } finally {
                setLoading(false);
            }
        };

        if (userId) {
            fetchClaims();
        }
    }, [userId, limit]);

    const getStatusBadge = (status: string) => {
        switch (status.toUpperCase()) {
            case 'PENDING':
                return <Badge variant="secondary">Pending Review</Badge>;
            case 'APPROVED':
                return <Badge variant="default">Approved</Badge>;
            case 'REJECTED':
                return <Badge variant="destructive">Rejected</Badge>;
            default:
                return <Badge variant="outline">Unknown</Badge>;
        }
    };

    if (!userId) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <p className="text-center text-muted-foreground">Please sign in to view your claims</p>
                </CardContent>
            </Card>
        );
    }

    if (loading) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <p className="text-center text-muted-foreground">Loading claims...</p>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <p className="text-center text-destructive">{error}</p>
                </CardContent>
            </Card>
        );
    }

    if (claims.length === 0) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <p className="text-center text-muted-foreground">You have not made any claims yet</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full">
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>My Claims</CardTitle>
                    <CardDescription>View and track your product claims</CardDescription>
                </div>
                {showViewAll && claims.length > 0 && (
                    <Button variant="outline" size="sm" asChild>
                        <Link href="/claims">View All</Link>
                    </Button>
                )}
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="all">All</TabsTrigger>
                        <TabsTrigger value="pending">Pending</TabsTrigger>
                        <TabsTrigger value="approved">Approved</TabsTrigger>
                        <TabsTrigger value="rejected">Rejected</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all">
                        {claims.map((claim) => (
                            <div key={claim.id} className="mb-4 rounded-lg border p-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-semibold">{claim.product?.name}</h3>
                                    {getStatusBadge(claim.status)}
                                </div>
                                <p className="mt-2 text-sm text-muted-foreground">
                                    Submitted on {format(new Date(claim.createdAt), 'PPP')}
                                </p>
                                {claim.status === 'REJECTED' && claim.rejectionReason && (
                                    <p className="mt-2 text-sm text-destructive">
                                        Reason: {claim.rejectionReason}
                                    </p>
                                )}
                            </div>
                        ))}
                    </TabsContent>

                    <TabsContent value="pending">
                        {claims
                            .filter((claim) => claim.status.toUpperCase() === 'PENDING')
                            .map((claim) => (
                                <div key={claim.id} className="mb-4 rounded-lg border p-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold">{claim.product?.name}</h3>
                                        {getStatusBadge(claim.status)}
                                    </div>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Submitted on {format(new Date(claim.createdAt), 'PPP')}
                                    </p>
                                </div>
                            ))}
                    </TabsContent>

                    <TabsContent value="approved">
                        {claims
                            .filter((claim) => claim.status.toUpperCase() === 'APPROVED')
                            .map((claim) => (
                                <div key={claim.id} className="mb-4 rounded-lg border p-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold">{claim.product?.name}</h3>
                                        {getStatusBadge(claim.status)}
                                    </div>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Submitted on {format(new Date(claim.createdAt), 'PPP')}
                                    </p>
                                    {claim.reviewedAt && (
                                        <p className="mt-2 text-sm text-muted-foreground">
                                            Approved on {format(new Date(claim.reviewedAt), 'PPP')}
                                        </p>
                                    )}
                                </div>
                            ))}
                    </TabsContent>

                    <TabsContent value="rejected">
                        {claims
                            .filter((claim) => claim.status.toUpperCase() === 'REJECTED')
                            .map((claim) => (
                                <div key={claim.id} className="mb-4 rounded-lg border p-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold">{claim.product?.name}</h3>
                                        {getStatusBadge(claim.status)}
                                    </div>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Submitted on {format(new Date(claim.createdAt), 'PPP')}
                                    </p>
                                    {claim.rejectionReason && (
                                        <p className="mt-2 text-sm text-destructive">
                                            Reason: {claim.rejectionReason}
                                        </p>
                                    )}
                                </div>
                            ))}
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
} 