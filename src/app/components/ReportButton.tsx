'use client';

import React, { useState } from 'react';
import { Flag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@clerk/nextjs';
import ReportModal from './ReportModal';

interface ReportButtonProps {
    reviewId: string;
    authorId: string;
    onReport: (reportId: string) => void;
    className?: string;
}

export default function ReportButton({ reviewId, authorId, onReport, className }: ReportButtonProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const { isSignedIn, userId } = useAuth();

    const handleReport = async (data: { reason: string; additionalNotes?: string }) => {
        try {
            setIsLoading(true);
            const response = await fetch(`/api/reviews/${reviewId}/report`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(data),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to submit report');
            }

            toast.success('Report submitted successfully');
            onReport(result.reportId);
            setIsModalOpen(false);
        } catch (error) {
            toast.error(error instanceof Error ? error.message : 'Failed to submit report');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClick = () => {
        if (!isSignedIn) {
            toast.error('Authentication Required', {
                description: 'You need to sign in to report a review',
            });
            return;
        }

        if (userId === authorId) {
            toast.error("You can't report your own review.");
            return;
        }

        setIsModalOpen(true);
    };

    return (
        <>
            <Button
                variant="ghost"
                size="sm"
                className={`text-gray-500 hover:text-red-500 ${className || ''}`}
                onClick={handleClick}
                disabled={userId === authorId}
            >
                <Flag className="h-4 w-4 mr-1" />
                Report
            </Button>

            <ReportModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSubmit={handleReport}
                reviewId={reviewId}
                isLoading={isLoading}
            />
        </>
    );
}