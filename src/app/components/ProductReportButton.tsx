"use client";

import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { MdReport } from "react-icons/md";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { ProductReportModal } from "./ProductReportModal";

interface ProductReportButtonProps {
    productId: string;
    productName: string;
    variant?: "default" | "outline" | "ghost" | "link";
    size?: "default" | "sm" | "lg" | "icon";
    showText?: boolean;
    className?: string;
}

export function ProductReportButton({
    productId,
    productName,
    variant = "ghost",
    size = "sm",
    showText = true,
    className = "",
}: ProductReportButtonProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { isSignedIn } = useAuth();
    const router = useRouter();

    const handleReportClick = () => {
        if (!isSignedIn) {
            router.push('/sign-in');
            return;
        }
        setIsModalOpen(true);
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    const handleReportSubmitted = () => {
        setIsModalOpen(false);
        // Could add a toast notification here
    };

    return (
        <>
            <Button
                variant={variant}
                size={size}
                onClick={handleReportClick}
                className={`text-red-600 hover:text-red-700 hover:bg-red-50 ${className}`}
                title={`Report ${productName}`}
            >
                <MdReport className="h-4 w-4" />
                {showText && <span className="ml-1">Report</span>}
            </Button>

            {isModalOpen && (
                <ProductReportModal
                    isOpen={isModalOpen}
                    onClose={handleModalClose}
                    onReportSubmitted={handleReportSubmitted}
                    productId={productId}
                    productName={productName}
                />
            )}
        </>
    );
}
