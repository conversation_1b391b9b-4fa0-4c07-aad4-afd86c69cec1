"use client";

import React from 'react';
import AdvertCard from './advert-components/AdvertCard';

const FeaturedProducts = () => {
    // Placeholder data for three featured products
    const featuredProductData = [
        {
            id: "fp1",
            title: "Featured Product 1",
            description: "Amazing product with incredible features. Check it out now!",
            imageUrl: "/your-brand-here.jpg", // Replace with actual image or use a placeholder service
            link: "#product1",
        },
        {
            id: "fp2",
            title: "Top Pick: Product 2",
            description: "Highly rated and recommended by experts. Don't miss this.",
            imageUrl: "/your-brand-here.jpg", // Replace with actual image
            link: "#product2",
        },
        {
            id: "fp3",
            title: "Must-Have Gadget",
            description: "The latest innovation that will change your life. Pre-order today!",
            imageUrl: "/your-brand-here.jpg", // Replace with actual image
            link: "#product3",
        },
    ];

    return (
        <div className="w-full py-12 px-4">
            <div className="container mx-auto">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-800 text-center mb-12 leading-tight">Featured Products</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {featuredProductData.map((product) => (
                        <AdvertCard
                            key={product.id}
                            title={product.title}
                            description={product.description}
                            imageUrl={product.imageUrl}
                            link={product.link}
                            isSponsored={true}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default FeaturedProducts;