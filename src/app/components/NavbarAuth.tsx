"use client";
import { SignInButton, SignedIn, SignedOut, UserButton } from "@clerk/nextjs";
import React, { Suspense } from "react";
import UserButtonComponent from "./UserButton";

const NavbarAuth = () => {
  return (
    <div className="flex justify-center items-center relative">
      <Suspense fallback={
        <div className="w-9 h-9 rounded-full bg-gray-200 animate-pulse"></div>
      }>
        <SignedIn>
          <UserButtonComponent />
        </SignedIn>
      </Suspense>
      <Suspense fallback={
        <div className="h-10 w-24 rounded-md bg-gray-200 animate-pulse"></div>
      }>
        <SignedOut>
          <SignInButton mode="modal">
            <button className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-myTheme-accent hover:text-myTheme-accent/90 bg-transparent transition-colors duration-200 rounded-md">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-4 h-4 mr-2"
              >
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                <polyline points="10 17 15 12 10 7" />
                <line x1="15" y1="12" x2="3" y2="12" />
              </svg>
              Sign In
            </button>
          </SignInButton>
        </SignedOut>
      </Suspense>
    </div>
  );
};

export default NavbarAuth;
