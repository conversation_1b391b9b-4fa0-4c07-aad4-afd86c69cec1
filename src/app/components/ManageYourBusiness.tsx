import React from "react";
import { Building2, ArrowRight } from "lucide-react";
import Link from "next/link";

const ManageYourBusiness = () => {
  return (
    <section className="w-full py-12 bg-gradient-to-br from-myTheme-primary/90 to-myTheme-primary relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.5),transparent)] pointer-events-none" />

      <div className="max-w-6xl mx-auto px-4 relative">
        <div className="flex flex-col md:flex-row items-center justify-between gap-8">
          <div className="flex-1 text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start gap-3 mb-4">
              <Building2 className="w-8 h-8 text-white/90" />
              <h2 className="text-3xl font-bold text-white">
                Manage Your Business
              </h2>
            </div>
            <p className="text-lg text-white/90 max-w-xl">
              Take control of your online presence. Get detailed insights,
              respond to reviews, and grow your business.
            </p>
          </div>

          <Link
            href="/business"
            className="inline-flex items-center gap-2 px-6 py-3 bg-white text-myTheme-primary font-medium rounded-full
              hover:bg-white/90 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl
              group text-base"
          >
            Learn More
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ManageYourBusiness;
