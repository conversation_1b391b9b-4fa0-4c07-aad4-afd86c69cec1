"use client";
import HomeLink from "./HomeLink";
import TopLinks from "./TopLinks";
import SideLinks from "./SideLinks";
import NavbarAuth from "./NavbarAuth";
import { useState, useEffect, useRef } from "react";
import { Toaster } from "@/components/ui/sonner";
import Footer from "./Footer";
import NotificationBell from "./notification-components/OwnerNotification";
import { useAuth } from "@clerk/nextjs";
import NotificationDropdown from "./notification-components/NavNotification";

const Navbar = ({ children }: { children: React.ReactNode }) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const drawerRef = useRef<HTMLDivElement>(null);
  const auth = useAuth();
  const { userId } = auth;

  // Close drawer when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (drawerRef.current && !drawerRef.current.contains(event.target as Node) && isDrawerOpen) {
        setIsDrawerOpen(false);
      }
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isDrawerOpen) {
        setIsDrawerOpen(false);
      }
    };

    // Prevent scrolling when drawer is open
    if (isDrawerOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'auto';
    };
  }, [isDrawerOpen]);

  const handleSideLinkClick = () => {
    setIsDrawerOpen(false);
  };

  return (
    <div className="relative">
      {/* Main content */}
      <div className="flex flex-col">
        {/* Navbar */}
        <header className="w-full bg-white border-b border-gray-200 shadow-sm z-[300] sticky top-0">
          <div className="container mx-auto px-4 flex items-center justify-between h-16">
            {/* Mobile menu button */}
            <div className="flex-none md:hidden">
              <button
                onClick={() => setIsDrawerOpen(!isDrawerOpen)}
                className="p-2 rounded-lg hover:bg-gray-100"
                aria-label={isDrawerOpen ? "Close menu" : "Open menu"}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  className="w-6 h-6 stroke-current"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  ></path>
                </svg>
              </button>
            </div>

            {/* Logo */}
            <div className="flex-none px-2">
              <HomeLink />
            </div>

            {/* Desktop navigation links */}
            <div className="hidden md:flex flex-1 justify-center px-1">
              <TopLinks />
            </div>

            {/* Right side items (notifications, auth) */}
            <div className="flex items-center gap-3">
              {userId && <NotificationDropdown />}
              <NavbarAuth />
            </div>
          </div>
        </header>

        {/* Main content */}
        <div className="flex flex-col w-full flex-grow overflow-y-auto">
          <Toaster position="top-right" />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </div>

      {/* Mobile drawer */}
      <div
        className={`fixed inset-0 bg-gray-900/60 z-[60] transition-opacity ${isDrawerOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
        aria-hidden={!isDrawerOpen}
      >
        <div
          ref={drawerRef}
          className={`fixed top-0 left-0 h-full w-80 bg-white shadow-md overflow-y-auto transform transition-transform ${isDrawerOpen ? 'translate-x-0' : '-translate-x-full'
            }`}
        >
          <div className="flex flex-col h-full p-6">
            {/* Close button */}
            <div className="flex justify-end mb-6">
              <button
                onClick={() => setIsDrawerOpen(false)}
                className="p-2 rounded-lg hover:bg-gray-100"
                aria-label="Close menu"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {/* Mobile logo */}
            <div className="mb-8 px-2">
              <HomeLink />
            </div>

            {/* Mobile links */}
            <div className="flex-1">
              <SideLinks onSideLinkClick={handleSideLinkClick} />
            </div>

            {/* Mobile auth */}
            <div className="mt-auto pt-6 border-t border-gray-100">
              <div className="flex items-center justify-between px-2 py-2">
                {userId && <NotificationDropdown />}
                <NavbarAuth />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
