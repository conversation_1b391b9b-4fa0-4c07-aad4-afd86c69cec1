"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Common cities/areas in Guyana
const COMMON_CITIES = [
  "Georgetown",
  "Linden", 
  "Bartica",
  "New Amsterdam",
  "Anna Regina",
  "Lethem",
  "Mahdia",
  "Mabaruma",
  "Skeldon",
  "Rose Hall"
];

interface CitySelectorProps {
  value?: string | null;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}

export default function CitySelector({ 
  value, 
  onChange, 
  label = "City/Area", 
  placeholder = "Select or enter city/area",
  className = ""
}: CitySelectorProps) {
  const [isCustom, setIsCustom] = React.useState(
    value && !COMMON_CITIES.includes(value)
  );

  return (
    <div className={className}>
      <Label className="text-myTheme-primary font-medium">{label} *</Label>
      
      {!isCustom ? (
        <div className="space-y-2">
          <Select value={value || ""} onValueChange={(val) => {
            if (val === "__custom__") {
              setIsCustom(true);
              onChange("");
            } else {
              onChange(val);
            }
          }}>
            <SelectTrigger className="mt-2 border-gray-200 focus:border-indigo-400 focus:ring-indigo-400/20">
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {COMMON_CITIES.map((city) => (
                <SelectItem key={city} value={city}>
                  {city}
                </SelectItem>
              ))}
              <SelectItem value="__custom__">
                ✏️ Enter custom city/area
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-600">
            Examples: Georgetown, Linden, Bartica, New Amsterdam
          </p>
        </div>
      ) : (
        <div className="space-y-2">
          <Input
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Enter city, town, or area"
            className="mt-2 border-gray-200 focus:border-indigo-400 focus:ring-indigo-400/20"
          />
          <button
            type="button"
            onClick={() => {
              setIsCustom(false);
              onChange("");
            }}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to common cities
          </button>
        </div>
      )}
    </div>
  );
}
