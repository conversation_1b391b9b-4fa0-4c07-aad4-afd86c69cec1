"use client";
import { iReview } from "../util/Interfaces";
import Image from "next/legacy/image";
import DOMPurify from "dompurify";
import Link from "next/link";
import { ReadOnlyRating } from "./RatingSystem";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import ReviewStats from "./ReviewStats";
import { useAuth } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { useAtom } from "jotai";
import { currentReviewAtom } from "../store/store";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, ThumbsUp, Tag } from "lucide-react";
import { motion } from "framer-motion";
import { generateShareMetadata } from "../lib/shareUtils";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { stripSpecificHtmlTags } from "../util/helpers";
import ReportButton from "./ReportButton";
import { toast } from "sonner";
import AdminApprovedBadge from "./AdminApprovedBadge";

dayjs.extend(relativeTime);

interface ReviewBoxProps {
  review: iReview;
}

const ReviewBox: React.FC<ReviewBoxProps> = ({ review }) => {
  // We still need setReview for the ReviewStats component
  const [, setReview] = useAtom(currentReviewAtom);

  // Ensure review has required fields
  if (!review || !review.body || !review.title) {
    return null;
  }

  // Generate metadata for sharing
  const metadata = generateShareMetadata({
    title: `${review.title} - Review for ${review.product?.name}`,
    description: `${stripSpecificHtmlTags(review.body).substring(0, 100)}...`,
    imageUrl: review.product?.display_image,
    url: `/fr?id=${review?.id}&productid=${review?.product?.id}`,
  });

  const truncatedBody =
    review.body.length > 150 ? `${review.body.slice(0, 150)}...` : review.body;

  // Removed animation variants to reduce GPU usage

  return (
    <div className="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300 w-full relative group flex flex-col overflow-hidden">
      {/* Removed motion animations and backdrop-blur for better GPU performance */}
      {/* Verified badge - removed, using AdminApprovedBadge instead */}

      {/* Header Section */}
      <div className="flex items-start space-x-3 mb-3 flex-shrink-0">
        <Link
          href={profileUrl(review.user!)}
          className="flex-shrink-0"
        >
          <div className="relative w-14 h-14 group-hover:ring-2 group-hover:ring-myTheme-primary group-hover:ring-offset-2 transition-all duration-300">
            <Image
              src={review.user?.avatar!}
              alt={`${review.user?.userName}&apos;s avatar`}
              layout="fill"
              className="rounded-full object-cover ring-2 ring-myTheme-primary/30 transition-all duration-300"
            />
          </div>
        </Link>

        <div className="flex-grow min-w-0">
          <div className="flex flex-wrap items-center gap-1 mb-1">
            <Link
              href={profileUrl(review.user!)}
              className="font-semibold text-gray-900 group-hover:text-myTheme-primary transition-colors truncate max-w-[120px] text-sm"
            >
              @{review.user?.userName}
            </Link>
            <AdminApprovedBadge review={review} size="sm" />
            <span className="text-gray-400 text-sm">•</span>
            <span
              className="text-sm text-gray-500"
              title={dayjs(review?.createdDate?.toString()).format(
                "MMMM D, YYYY h:mm A",
              )}
            >
              {dayjs(review?.createdDate).fromNow()}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <ReadOnlyRating
              name={review.id!}
              rating={review.rating}
              size="sm"
            />
            <ReviewStats review={review} setReview={() => setReview(review)} />
          </div>
        </div>
      </div>

      {/* Clickable Review Content */}
      <Link
        href={`/fr?id=${review?.id}&productid=${review?.product?.id}`}
        className="flex-grow block cursor-pointer group/content mb-3"
      >
        <div className="flex-grow overflow-hidden flex flex-col">
          <h3 className="text-sm font-semibold text-gray-900 group-hover/content:text-myTheme-primary transition-colors mb-1 line-clamp-1">
            {review.title}
          </h3>

          <div className="relative">
            <div
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(truncatedBody),
              }}
              className="prose max-w-full text-gray-600 text-xs leading-relaxed mb-1 line-clamp-3 overflow-hidden"
            />
            <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
          </div>

          {review.images && review.images.length > 0 && (
            <div className="flex gap-1 mt-2 mb-1">
              {review.images.slice(0, 3).map((img, i) => (
                <div
                  key={i}
                  className="h-10 w-10 rounded-md overflow-hidden relative"
                >
                  <Image
                    src={img}
                    alt={`Review image ${i + 1}`}
                    layout="fill"
                    objectFit="cover"
                    className="hover:scale-105 transition-transform"
                  />
                </div>
              ))}
              {review.images.length > 3 && (
                <div className="h-10 w-10 rounded-md bg-gray-100 flex items-center justify-center text-xs text-gray-500">
                  +{review.images.length - 3}
                </div>
              )}
            </div>
          )}
        </div>
      </Link>

      {/* Footer Chin */}
      <div className="mt-auto pt-3 border-t border-gray-100 flex-shrink-0">
        {/* Product Info */}
        <div className="mb-2">
          <Link
            href={`/reviews?id=${review?.product?.id}`}
            className="inline-flex items-center gap-2 text-sm font-medium text-gray-900 hover:text-myTheme-primary transition-colors"
          >
            {review.product?.display_image && (
              <div className="relative w-8 h-8 rounded-lg overflow-hidden flex-shrink-0 ring-1 ring-gray-200 bg-white p-0.5">
                <Image
                  src={review.product.display_image}
                  alt={`${review.product.name} thumbnail`}
                  layout="fill"
                  objectFit="contain"
                  className="rounded-md"
                />
              </div>
            )}
            <span className="truncate">{review.product?.name}</span>
          </Link>
          <div className="flex flex-wrap items-center gap-1 mt-0.5">
            <Tag size={10} className="text-gray-400" />
            {review.product?.tags.slice(0, 2).map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="px-1.5 py-0 text-[10px] font-normal bg-myTheme-primary/10 text-myTheme-primary rounded-full"
              >
                {tag}
              </Badge>
            ))}
            {review.product?.tags && review.product.tags.length > 2 && (
              <span className="text-[10px] text-gray-500">
                +{review.product.tags.length - 2}
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <Link
              href={`/fr?id=${review?.id}&productid=${review?.product?.id}`}
              className="inline-flex items-center gap-1 text-xs text-gray-500 hover:text-myTheme-primary transition-colors"
            >
              <MessageSquare
                size={12}
                className="transition-transform group-hover:scale-110"
              />
              <span>{review.comments?.length || 0}</span>
            </Link>

            <div className="inline-flex items-center gap-1 text-xs text-gray-500">
              <ThumbsUp
                size={12}
                className="transition-transform group-hover:scale-110"
              />
              <span>{review.voteCount?.helpfulVotes || 0}</span>
            </div>

            <ShareButtonWrapper metadata={metadata} className="border-0" />
            <ReportButton
              reviewId={review.id!}
              authorId={review.user?.id || ""}
              className="ml-1"
              onReport={(reportId) => {
                toast.success("Review reported successfully");
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewBox;
