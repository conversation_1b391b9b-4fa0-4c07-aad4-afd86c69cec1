import React, { useState, useEffect, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Image from 'next/image';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { FaPlay } from "react-icons/fa";
import { resolveTikTokMobileLink } from '../util/urlResolvers';

type VideoType = 'youtube' | 'tiktok' | 'unknown';

interface VideoEmbedProps {
  url: string;
}

interface EmbedCodeResult {
  error: string;
  embedCode: string;
  thumbnailUrl: string;
  videoType: VideoType;
}

const VideoEmbed: React.FC<VideoEmbedProps> = React.memo(({ url }) => {
  const [embedCode, setEmbedCode] = useState<string>('');
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [videoType, setVideoType] = useState<VideoType>('unknown');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isResolving, setIsResolving] = useState(false);

  const getVideoType = useCallback((url: string): VideoType => {
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
    if (url.includes('tiktok.com')) return 'tiktok';
    return 'unknown';
  }, []);

  const getYouTubeEmbedCode = useCallback((videoId: string): { embedCode: string, thumbnailUrl: string } => {
    const embedCode = `<iframe width="100%" height="100%" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>`;
    const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
    return { embedCode, thumbnailUrl };
  }, []);

  const getTikTokEmbedCode = useCallback(async (url: string): Promise<{ embedCode: string, thumbnailUrl: string }> => {
    try {
      if (url.includes('vm.tiktok.com')) {
        setIsResolving(true);
        try {
          const resolvedUrl = await resolveTikTokMobileLink(url);
          const response = await fetch(`https://www.tiktok.com/oembed?url=${encodeURIComponent(resolvedUrl)}`);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          return { embedCode: data.html, thumbnailUrl: data.thumbnail_url };
        } catch (error) {
          setError('Failed to resolve TikTok mobile link. Please try using the desktop version of the link.');
          return { embedCode: '', thumbnailUrl: '' };
        } finally {
          setIsResolving(false);
        }
      }

      const response = await fetch(`https://www.tiktok.com/oembed?url=${encodeURIComponent(url)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return { embedCode: data.html, thumbnailUrl: data.thumbnail_url };
    } catch (error) {
      throw new Error('Failed to fetch TikTok embed code');
    }
  }, []);

  const generateEmbedCode = useCallback(async (url: string): Promise<EmbedCodeResult> => {
    if (!url) {
      return { error: '', embedCode: '', thumbnailUrl: '', videoType: 'unknown' };
    }

    const type = getVideoType(url);

    try {
      switch (type) {
        case 'youtube': {
          const youtubeId = url.split('v=')[1]?.split('&')[0] || url.split('/').pop();
          if (!youtubeId) {
            return { error: 'Invalid YouTube URL', embedCode: '', thumbnailUrl: '', videoType: type };
          }
          const { embedCode, thumbnailUrl } = getYouTubeEmbedCode(youtubeId);
          return { error: '', embedCode, thumbnailUrl, videoType: type };
        }
        case 'tiktok': {
          const { embedCode, thumbnailUrl } = await getTikTokEmbedCode(url);
          if (!embedCode && url.includes('vm.tiktok.com')) {
            return { error: 'mobile tik tok link not supported, please open this link in your browser and use the expanded link', embedCode: '', thumbnailUrl: '', videoType: type };
          } else if (!embedCode) {
            return { error: 'Invalid TikTok URL', embedCode: '', thumbnailUrl: '', videoType: type };
          }
          return { error: '', embedCode, thumbnailUrl, videoType: type };
        }
        default:
          return { error: 'Unsupported video platform. Please use YouTube, or TikTok.', embedCode: '', thumbnailUrl: '', videoType: 'unknown' };
      }
    } catch (error) {
      return { error: `Error generating embed code: ${error instanceof Error ? error.message : String(error)}`, embedCode: '', thumbnailUrl: '', videoType: type };
    }
  }, [getVideoType, getYouTubeEmbedCode, getTikTokEmbedCode]);

  useEffect(() => {
    generateEmbedCode(url).then((result) => {
      setError(result.error);
      setEmbedCode(result.embedCode);
      setThumbnailUrl(result.thumbnailUrl);
      setVideoType(result.videoType);
    });
  }, [url, generateEmbedCode]);

  useEffect(() => {
    if (videoType === 'tiktok' && isModalOpen) {
      const script = document.createElement('script');
      script.src = "https://www.tiktok.com/embed.js";
      script.async = true;
      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    }
  }, [videoType, embedCode, isModalOpen]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const getPreviewSize = () => {
    switch (videoType) {
      case 'tiktok':
        return {
          width: 180,
          height: 320,
          containerClass: 'sm:w-[180px]',
          aspectRatio: 'aspect-[9/16]'
        };
      case 'youtube':
        return {
          width: 320,
          height: 180,
          containerClass: 'sm:w-[320px]',
          aspectRatio: 'aspect-video'
        };
      default:
        return {
          width: 320,
          height: 180,
          containerClass: 'sm:w-80',
          aspectRatio: 'aspect-video'
        };
    }
  };

  const previewSize = getPreviewSize();

  return (
    <>
      <div className={`w-full ${previewSize.containerClass} mx-auto mt-4`}>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {isResolving && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
            <div className="text-white">Resolving TikTok link...</div>
          </div>
        )}

        {/* Preview/Thumbnail */}
        <div
          className={`relative cursor-pointer rounded-lg overflow-hidden group ${previewSize.aspectRatio}`}
          onClick={handleOpenModal}
        >
          {thumbnailUrl ? (
            <Image
              src={thumbnailUrl}
              alt="Video Thumbnail"
              width={previewSize.width}
              height={previewSize.height}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className={`bg-gray-100 ${previewSize.aspectRatio} w-full flex items-center justify-center`}>
              <FaPlay className="text-gray-400 text-3xl" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center group-hover:bg-black/60 transition-all">
            <FaPlay className="text-white text-3xl" />
          </div>
        </div>
      </div>

      {/* Full Screen Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-screen-lg w-full p-0 h-[90vh] max-h-[90vh] flex items-center justify-center bg-black">
          {embedCode && (
            <div className={`w-full h-full flex items-center justify-center ${videoType === 'tiktok' ? 'max-w-[400px]' : 'max-w-[1024px]'}`}>
              <div
                className={`w-full ${videoType === 'tiktok' ? 'aspect-[9/16]' : 'aspect-video'} relative`}
                dangerouslySetInnerHTML={{ __html: embedCode }}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
});

VideoEmbed.displayName = 'VideoEmbed';

export default VideoEmbed;
