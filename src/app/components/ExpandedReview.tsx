"use client";
import {
  useEffect,
  useMemo,
  useState,
  useCallback,
  lazy,
  Suspense,
} from "react";
import { useAtom } from "jotai";
import { useRouter } from "next/navigation";
import { useAuth } from "@clerk/nextjs";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import ReviewCard from "./ReviewCard";
import { currentReviewAtom, currentUserAtom } from "../store/store";
import { iReview, iComment, iUser, ApiResponse } from "../util/Interfaces";
import {
  createCommentOnReview,
  createReplyOnComment,
  deleteComment,
  editComment,
  getReview,
  getCommentsForReview,
} from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import CommentForm from "./CommentForm";
import DisplayError from "./DisplayError";
import useScrollToComment from "../util/UseScrollToComment";
import SignInToParticipate from "./SignInToParticipate";
import { hasUserData } from "../store/store";

const CommentList = lazy(() => import("./CommentList"));

const ExpandedReview = ({
  reviewId,
  productId,
  cId,
}: {
  reviewId: string;
  productId: string;
  cId: string;
}) => {
  const isCommentLoaded = useScrollToComment(cId, {
    maxAttempts: 10,
    intervalDuration: 500,
  });
  const { userId, isLoaded, isSignedIn } = useAuth();
  const queryClient = useQueryClient();
  const [reviewAtom] = useAtom(currentReviewAtom);
  const [isOpen, setIsOpen] = useState(true);
  const [textAreaValue, setTextAreaValue] = useState("");
  const [currentUser] = useAtom(currentUserAtom);
  const router = useRouter();
  const clerkUserId = userId as string;
  const [commentSortBy, setCommentSortBy] = useState<'best' | 'newest' | 'oldest'>('best');
  const [isLoading, setIsLoading] = useState(true);

  const [comment, setComment] = useState<iComment>({
    reviewId: "",
    body: "",
    createdDate: new Date(),
    user: {} as iUser,
    review: {} as iReview,
    userId: "",
    isDeleted: false,
    upvotes: 0,
    downvotes: 0,
  });

  const commentMutation = useMutation({
    mutationFn: async (comment: iComment) => {
      const data = await createCommentOnReview(comment);
      if (!data.success) {
        throw new Error(data.error || 'Failed to create comment');
      }
      return data;
    },
    onMutate: (newData: iComment) => {
      // Optimistically update comments cache
      queryClient.setQueryData<iComment[]>(
        ["comments", reviewId],
        (oldComments) => {
          if (!oldComments) return [{ ...newData, user: currentUser, isDeleted: false }];
          return [
            { ...newData, user: currentUser, isDeleted: false },
            ...oldComments,
          ];
        }
      );
    },
    onSuccess: () => {
      // Refetch comments to get the latest data from server
      refetchComments();
      toast.success("Comment saved successfully!");
    },
    onError: (error: Error) => {
      // Revert optimistic update on error
      refetchComments();
      toast.error(error.message || "Error saving comment");
    },
  });

  const replyMutation = useMutation({
    mutationFn: async (reply: { parentId: string; body: string }) => {
      const replyData: iComment = {
        body: reply.body,
        parentId: reply.parentId,
        reviewId: reviewId,
        userId: clerkUserId,
        user: currentUser,
        createdDate: new Date(),
        isDeleted: false,
        upvotes: 0,
        downvotes: 0,
      };
      
      const data = await createReplyOnComment(replyData);
      if (!data.success) {
        throw new Error(data.error || 'Failed to create reply');
      }
      return data;
    },
    onSuccess: () => {
      // Refetch comments to get the latest data including the new reply
      refetchComments();
      toast.success("Reply added successfully!");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Error adding reply");
    },
  });

  const handleCommentSubmit = useCallback(
    async (newTextAreaValue: string) => {
      if (isLoaded && !isSignedIn) {
        router.push("https://accounts.reviewit.gy/sign-in");
        return;
      }
      setTextAreaValue(newTextAreaValue);
      setIsOpen(!isOpen);
      commentMutation.mutate({ ...comment, body: newTextAreaValue });
    },
    [isLoaded, isSignedIn, router, isOpen, commentMutation, comment]
  );

  // Check if user is authenticated and has valid data
  const isUserAuthenticated = isLoaded && isSignedIn && hasUserData(currentUser);

  const handleReply = useCallback(
    async (parentId: string, body: string) => {
      try {
        await replyMutation.mutateAsync({ parentId, body });
      } catch (error) {
      }
    },
    [replyMutation]
  );

  const handleEdit = async (commentId: string, body: string) => {
    try {
      const response = await editComment(commentId, body);
      if (response.success) {
        refetchComments(); // Refetch to get updated comment
        toast.success("Comment updated successfully!");
      } else {
        toast.error(response.error || "Failed to update comment");
      }
    } catch (error) {
      toast.error("Failed to update comment");
    }
  };

  const handleDelete = async (commentId: string) => {
    try {
      const deleteResponse = await deleteComment(commentId);
      if (deleteResponse.success) {
        refetchComments(); // Refetch to remove deleted comment
        toast.success("Comment successfully deleted!");
      } else {
        toast.error(deleteResponse.error || "Failed to delete comment");
      }
    } catch (error) {
      toast.error("Failed to delete comment");
    }
  };

  // Separate query for review data (without comments)
  const { data: reviewData, isPending: reviewPending, isError: reviewError, error: reviewErrorMsg } = useQuery({
    queryKey: ["review", reviewId],
    queryFn: async () => {
      console.log("Fetching review with ID:", reviewId);
      setIsLoading(true);
      if (reviewAtom !== null) {
        console.log("Using reviewAtom data:", reviewAtom);
        setIsLoading(false);
        return reviewAtom;
      }
      // Use getReview directly instead of fetching all reviews
      const response = await getReview(reviewId);
      console.log("getReview response:", response);
      if (!response.success || !response.data) {
        console.error("Review fetch failed:", response.error);
        throw new Error(response.error || 'Failed to fetch review');
      }
      console.log("Review data received:", response.data);
      setIsLoading(false);
      return response.data;
    },
    refetchOnWindowFocus: false,
  });

  // Separate query for comments
  const { data: commentsData, isPending: commentsPending, refetch: refetchComments } = useQuery({
    queryKey: ["comments", reviewId],
    queryFn: async () => {
      const response = await getCommentsForReview(reviewId);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch comments');
      }
      return response.data || [];
    },
    refetchOnWindowFocus: false,
    enabled: !!reviewId, // Only run when reviewId is available
  });

  useEffect(() => {
    if (reviewData) {
      setComment((prevComment) => ({
        ...prevComment,
        reviewId: reviewId,
        body: textAreaValue,
        createdDate: new Date(),
        user: currentUser,
        review: reviewAtom || reviewData,
        userId: currentUser?.id || "",
      }));
    }
  }, [reviewData, textAreaValue, currentUser, reviewAtom, reviewId]);

  const sortedComments = useMemo(() => {
    return (
      commentsData
        ?.slice()
        .sort(
          (a: iComment, b: iComment) =>
            new Date(b.createdDate!).valueOf() -
            new Date(a.createdDate!).valueOf()
        ) || []
    );
  }, [commentsData]);

  const finalReviewData = useMemo(() => {
    return reviewAtom || reviewData;
  }, [reviewAtom, reviewData]);

  useEffect(() => {
    if (isCommentLoaded) {
    }
  }, [isCommentLoaded]);

  if (reviewPending || isLoading) return <LoadingSpinner />;
  if (reviewError) return (
    <div className="flex flex-col items-center justify-center p-8">
      <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Review</h2>
      <p className="text-gray-600">{reviewErrorMsg instanceof Error ? reviewErrorMsg.message : 'An error occurred while loading the review'}</p>
    </div>
  );
  if (!finalReviewData) return (
    <div className="flex flex-col items-center justify-center p-8">
      <h2 className="text-xl font-semibold text-gray-600 mb-2">Review Not Found</h2>
      <p className="text-gray-500">The requested review could not be found.</p>
    </div>
  );

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100/50 p-4 transform transition-all duration-500 hover:shadow-xl">
      {/* Review Card */}
      <ReviewCard review={finalReviewData} showFullContent={true} />
      
      {/* Add Comment Form */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        {isUserAuthenticated ? (
          <>
            <h3 className="text-lg font-semibold mb-2 text-gray-900">Leave a comment</h3>
            <CommentForm
              isOpen={isOpen}
              onSubmit={(value) => {
                const newComment: iComment = {
                  body: value,
                  reviewId,
                  userId: clerkUserId,
                  createdDate: new Date(),
                  user: currentUser,
                  review: finalReviewData,
                  isDeleted: false,
                  upvotes: 0,
                  downvotes: 0,
                } as iComment;
                commentMutation.mutate(newComment);
              }}
              product={finalReviewData?.product ?? undefined}
              onClose={(open) => setIsOpen(open)} />
          </>
        ) : (
          <SignInToParticipate />
        )}
      </div>
      
      {/* Comments Section */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <div className="w-2 h-2 bg-myTheme-primary rounded-full"></div>
          Comments ({commentsPending ? '...' : sortedComments.length})
        </h3>
        
        {commentsPending ? (
          <div className="flex justify-center py-4">
            <LoadingSpinner />
          </div>
        ) : sortedComments.length > 0 ? (
          <Suspense fallback={<div className="flex justify-center py-4"><LoadingSpinner /></div>}>
            <CommentList
              comments={sortedComments}
              currentUser={currentUser}
              onReply={handleReply}
              onEdit={handleEdit}
              onDelete={handleDelete}
              clerkUserId={clerkUserId}
              product={finalReviewData?.product || undefined}
              review={finalReviewData || undefined}
              sortBy={commentSortBy}
              onSortChange={setCommentSortBy}
              focusCommentId={cId}
              isUserAuthenticated={isUserAuthenticated}
            />
          </Suspense>
        ) : (
          <div className="text-gray-500 text-center py-4">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p className="text-lg font-medium">No comments yet</p>
            <p className="text-sm text-gray-400 mt-1">Be the first to share your thoughts!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpandedReview;