"use client";
import React from "react";
import { cn } from "@/lib/utils";

type RatingSize = "xs" | "sm" | "md" | "lg" | "xl";
type RatingVariant = "default" | "compact" | "minimal";

interface RatingSystemProps {
  /** Unique identifier for the rating input group */
  name: string;
  /** Current rating value (0-5) */
  rating: number;
  /** Whether the rating is interactive */
  interactive?: boolean;
  /** Callback when rating changes (only for interactive mode) */
  onRatingChange?: (rating: number) => void;
  /** Size of the stars */
  size?: RatingSize;
  /** Visual variant */
  variant?: RatingVariant;
  /** Custom className */
  className?: string;
  /** Show rating as single star with color (for mini displays) */
  singleStar?: boolean;
  /** Disable hover effects */
  disableHover?: boolean;
}

const sizeClasses: Record<RatingSize, string> = {
  xs: "w-3 h-3",
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-6 h-6",
  xl: "w-8 h-8",
};

const variantClasses: Record<RatingVariant, string> = {
  default: "bg-white/10 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20",
  compact: "bg-white/5 backdrop-blur-sm py-1",
  minimal: "",
};

const ratingColors = [
  "text-red-400",
  "text-orange-400",
  "text-yellow-400",
  "text-lime-400",
  "text-green-400",
];

const hoverColors = [
  "hover:text-red-300",
  "hover:text-orange-300",
  "hover:text-yellow-300",
  "hover:text-lime-300",
  "hover:text-green-300",
];

const RatingSystem: React.FC<RatingSystemProps> = ({
  name,
  rating = 0,
  interactive = false,
  onRatingChange,
  size = "lg",
  variant = "default",
  className,
  singleStar = false,
  disableHover = false,
}) => {
  const getRatingColor = (value: number) => {
    const index = Math.max(0, Math.min(4, Math.floor(value) - 1));
    return ratingColors[index] || ratingColors[0];
  };

  const getHoverColor = (value: number) => {
    const index = Math.max(0, Math.min(4, Math.floor(value) - 1));
    return hoverColors[index] || hoverColors[0];
  };

  const handleRatingChange = (newRating: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(newRating);
    }
  };

  // Single star display for compact views
  if (singleStar) {
    return (
      <div className={cn("inline-flex items-center", variantClasses[variant], className)}>
        <svg
          className={cn(
            sizeClasses[size],
            rating > 0 ? getRatingColor(rating) : "text-gray-400",
            "transition-colors duration-200"
          )}
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      </div>
    );
  }

  // Full star rating display
  return (
    <div className={cn("inline-flex items-center", variantClasses[variant], className)}>
      {[1, 2, 3, 4, 5].map((value) => {
        const isActive = value <= rating;
        const starColor = isActive ? getRatingColor(rating) : "text-gray-300";
        const hoverColor = interactive && !disableHover ? getHoverColor(value) : "";
        
        return (
          <label 
            key={value} 
            className={cn(
              interactive ? "cursor-pointer" : "cursor-default",
              "transition-colors duration-150"
            )}
          >
            {interactive && (
              <input
                type="radio"
                name={name}
                value={value}
                checked={rating === value}
                onChange={() => handleRatingChange(value)}
                className="sr-only"
              />
            )}
            <svg
              className={cn(
                sizeClasses[size],
                starColor,
                "transition-all duration-200",
                interactive && !disableHover && !isActive && "hover:text-gray-300 hover:scale-110",
                interactive && !disableHover && isActive && hoverColor,
                interactive && !disableHover && "hover:scale-110"
              )}
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </label>
        );
      })}
    </div>
  );
};

export default RatingSystem;

// Convenience components for backward compatibility and specific use cases
export const InteractiveRating: React.FC<Omit<RatingSystemProps, 'interactive'> & { onRatingChange: (rating: number) => void }> = (props) => (
  <RatingSystem {...props} interactive={true} />
);

export const ReadOnlyRating: React.FC<Omit<RatingSystemProps, 'interactive' | 'onRatingChange'>> = (props) => (
  <RatingSystem {...props} interactive={false} />
);

export const TinyRating: React.FC<Omit<RatingSystemProps, 'size' | 'variant' | 'interactive' | 'onRatingChange'>> = (props) => (
  <RatingSystem {...props} size="xs" variant="compact" interactive={false} />
);

export const MiniRating: React.FC<Omit<RatingSystemProps, 'singleStar' | 'interactive'> & { onRatingChange?: (rating: number) => void }> = (props) => (
  <RatingSystem {...props} singleStar={true} interactive={!!props.onRatingChange} />
);