import React from 'react';
import { iProductClaim } from '../util/Interfaces';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface ClaimStatusProps {
    claim: iProductClaim;
}

export default function ClaimStatus({ claim }: ClaimStatusProps) {
    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    Claim Status
                    {getStatusBadge(claim.status)}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div>
                        <h4 className="text-sm font-medium">Product</h4>
                        <p className="text-sm text-muted-foreground">{claim.product?.name}</p>
                    </div>

                    <div>
                        <h4 className="text-sm font-medium">Submitted</h4>
                        <p className="text-sm text-muted-foreground">
                            {claim.createdAt ? format(new Date(claim.createdAt), 'PPP') : 'Unknown date'}
                        </p>
                    </div>

                    {claim.status === 'APPROVED' && (
                        <div className="bg-green-50 p-4 rounded-md border border-green-200">
                            <div className="flex items-start">
                                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                                <div>
                                    <h4 className="text-sm font-medium text-green-800">Approved</h4>
                                    <p className="text-sm text-green-700">
                                        Your claim has been approved! You now have owner access to this product.
                                    </p>
                                    {claim.reviewedAt && (
                                        <p className="text-xs text-green-600 mt-1">
                                            Approved on {format(new Date(claim.reviewedAt), 'PPP')}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {claim.status === 'REJECTED' && (
                        <div className="bg-red-50 p-4 rounded-md border border-red-200">
                            <div className="flex items-start">
                                <XCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                                <div>
                                    <h4 className="text-sm font-medium text-red-800">Rejected</h4>
                                    {claim.rejectionReason && (
                                        <p className="text-sm text-red-700">
                                            Reason: {claim.rejectionReason}
                                        </p>
                                    )}
                                    {claim.reviewedAt && (
                                        <p className="text-xs text-red-600 mt-1">
                                            Reviewed on {format(new Date(claim.reviewedAt), 'PPP')}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {claim.status === 'PENDING' && (
                        <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
                            <div className="flex items-start">
                                <Clock className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                                <div>
                                    <h4 className="text-sm font-medium text-yellow-800">Under Review</h4>
                                    <p className="text-sm text-yellow-700">
                                        Your claim is currently being reviewed by our team. We&apos;ll notify you once the review is complete.
                                    </p>
                                    <p className="text-xs text-yellow-600 mt-1">
                                        Typical review time: 1-3 business days
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    <div>
                        <h4 className="text-sm font-medium">Claim Images</h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                            {claim.images.map((image, index) => (
                                <img
                                    key={index}
                                    src={image}
                                    alt={`Claim supporting image ${index + 1}`}
                                    className="rounded-md aspect-square object-cover"
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

function getStatusBadge(status: string) {
    switch (status) {
        case 'PENDING':
            return (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100 border-yellow-200">
                    <Clock className="h-3 w-3 mr-1" />
                    Pending
                </Badge>
            );
        case 'APPROVED':
            return (
                <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Approved
                </Badge>
            );
        case 'REJECTED':
            return (
                <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100 border-red-200">
                    <XCircle className="h-3 w-3 mr-1" />
                    Rejected
                </Badge>
            );
        default:
            return (
                <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100 border-gray-200">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Unknown
                </Badge>
            );
    }
} 