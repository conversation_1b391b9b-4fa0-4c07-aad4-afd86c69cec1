'use client';

import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, User, Calendar, Package } from 'lucide-react';
import { toast } from 'sonner';
import { iReview } from '@/app/util/Interfaces';
import { format } from 'date-fns';

interface ReviewModerationQueueProps {
    initialReviews?: iReview[];
    onReviewClick?: (review: iReview) => void;
}

// Export the ref type for TypeScript
export interface ReviewQueueRefType {
    fetchReviews: () => Promise<void>;
}

const ReviewModerationQueue = forwardRef<ReviewQueueRefType, ReviewModerationQueueProps>(({
    initialReviews = [],
    onReviewClick,
}, ref) => {
    const [reviews, setReviews] = useState<iReview[]>(initialReviews);
    const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [status, setStatus] = useState('PENDING');
    const [rating, setRating] = useState<string>('');
    const [search, setSearch] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchReviews = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const params = new URLSearchParams({
                page: page.toString(),
                status,
                ...(rating && rating !== 'all' && { rating }),
                ...(search && { search }),
            });

            const response = await fetch(`/api/admin/reviews/queue?${params}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch reviews');
            }

            if (!data.data?.reviews) {
                throw new Error('No reviews data in response');
            }

            setReviews(data.data.reviews);
            setTotalPages(data.data.pagination.totalPages);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
            setReviews([]);
            setTotalPages(1);
        } finally {
            setLoading(false);
        }
    }, [page, status, rating, search]);

    // Expose the fetchReviews method via ref
    useImperativeHandle(ref, () => ({
        fetchReviews
    }));

    useEffect(() => {
        fetchReviews();
    }, [fetchReviews]);

    const handleBulkAction = async (action: 'APPROVE' | 'REJECT' | 'FLAG') => {
        if (selectedReviews.length === 0) {
            toast.error('Please select at least one review to perform this action.');
            return;
        }

        try {
            const response = await fetch('/api/admin/reviews/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reviewIds: selectedReviews,
                    action,
                }),
            });

            const data = await response.json();

            if (data.success) {
                toast.success(`Successfully processed ${data.data.summary.successCount} reviews`);
                setSelectedReviews([]);
                fetchReviews();
            } else {
                throw new Error(data.error || 'Failed to process bulk action');
            }
        } catch (error) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'An unknown error occurred';
            toast.error(errorMessage);
        }
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedReviews(reviews.map(review => review.id!));
        } else {
            setSelectedReviews([]);
        }
    };

    const handleSelectReview = (reviewId: string) => {
        setSelectedReviews(prev =>
            prev.includes(reviewId)
                ? prev.filter(id => id !== reviewId)
                : [...prev, reviewId]
        );
    };

    const handleRowClick = (review: iReview) => {
        if (onReviewClick) {
            onReviewClick(review);
        }
    };

    return (
        <div className="space-y-4">
            {/* Mobile: Card-based layout */}
            <div className="w-full px-4 block md:hidden space-y-4">
                <div className="grid grid-cols-1 gap-4 w-full">
                    <div className="bg-white p-4 rounded-lg border space-y-3">
                        <h3 className="font-medium text-sm text-gray-700">Filters</h3>
                        <div className="space-y-3">
                            <Select value={status} onValueChange={setStatus}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="PENDING">Pending</SelectItem>
                                    <SelectItem value="APPROVED">Approved</SelectItem>
                                    <SelectItem value="REJECTED">Rejected</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={rating} onValueChange={setRating}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Filter by rating" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Ratings</SelectItem>
                                    {[5, 4, 3, 2, 1].map(r => (
                                        <SelectItem key={r} value={r.toString()}>
                                            {r} Stars
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Input
                                placeholder="Search reviews..."
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                                className="w-full"
                            />
                        </div>
                    </div>

                    <div className="bg-white p-4 rounded-lg border space-y-3">
                        <h3 className="font-medium text-sm text-gray-700">Actions</h3>
                        <div className="grid grid-cols-1 gap-2">
                            <Button
                                variant="outline"
                                onClick={() => handleBulkAction('FLAG')}
                                disabled={selectedReviews.length === 0}
                                className="w-full text-sm"
                            >
                                Flag Selected
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={() => handleBulkAction('REJECT')}
                                disabled={selectedReviews.length === 0}
                                className="w-full text-sm"
                            >
                                Reject Selected
                            </Button>
                            <Button
                                onClick={() => handleBulkAction('APPROVE')}
                                disabled={selectedReviews.length === 0}
                                className="w-full text-sm"
                            >
                                Approve Selected
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Card list for reviews */}
                <div className="space-y-4 mt-4">
                  {loading ? (
                    <div className="text-center text-gray-500 py-8">Loading reviews...</div>
                  ) : reviews.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">No reviews found</div>
                  ) : (
                    reviews.map((review) => (
                      <div
                        key={review.id}
                        className="bg-white rounded-lg border p-4 shadow-sm flex flex-col gap-2 cursor-pointer"
                        onClick={() => handleRowClick(review)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="font-semibold text-base truncate max-w-[70%]">{review.title}</div>
                          <Checkbox
                            checked={selectedReviews.includes(review.id!)}
                            onCheckedChange={() => handleSelectReview(review.id!)}
                            onClick={e => e.stopPropagation()}
                          />
                        </div>
                        <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-600">
                          <span><span className="font-medium">Product:</span> {review.product?.name}</span>
                          <span><span className="font-medium">User:</span> {review.user?.userName}</span>
                          <span><span className="font-medium">Rating:</span> {review.rating}</span>
                          <span><span className="font-medium">Date:</span> {format(new Date(review.createdDate!), 'MMM d, yyyy')}</span>
                        </div>
                        <div className="mt-2">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${review.isPublic && review.isVerified
                              ? 'bg-green-100 text-green-800'
                              : review.isVerified
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                              }`}
                          >
                            {review.isPublic && review.isVerified
                              ? 'Approved'
                              : review.isVerified
                                ? 'Rejected'
                                : 'Pending'}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Mobile pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-end space-x-2 mt-4">
                    <Button
                      variant="outline"
                      onClick={() => setPage(p => Math.max(1, p - 1))}
                      disabled={page === 1 || loading}
                      className="text-sm"
                    >
                      Previous
                    </Button>
                    <div className="text-sm">
                      Page {page} of {totalPages}
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                      disabled={page === totalPages || loading}
                      className="text-sm"
                    >
                      Next
                    </Button>
                  </div>
                )}
            </div>

            {/* Desktop: Two-row layout */}
            <div className="hidden md:block space-y-4">
                {/* First row: Filters */}
                <div className="flex items-center space-x-4">
                    <Select value={status} onValueChange={setStatus}>
                        <SelectTrigger className="w-[160px]">
                            <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="APPROVED">Approved</SelectItem>
                            <SelectItem value="REJECTED">Rejected</SelectItem>
                        </SelectContent>
                    </Select>

                    <Select value={rating} onValueChange={setRating}>
                        <SelectTrigger className="w-[160px]">
                            <SelectValue placeholder="Filter by rating" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Ratings</SelectItem>
                            {[5, 4, 3, 2, 1].map(r => (
                                <SelectItem key={r} value={r.toString()}>
                                    {r} Stars
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Input
                        placeholder="Search reviews..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        className="w-[280px]"
                    />
                </div>

                {/* Second row: Actions */}
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:space-x-2">
                    <div className="text-sm text-gray-600 sm:mr-auto">
                        {selectedReviews.length > 0 && (
                            <span>{selectedReviews.length} review(s) selected</span>
                        )}
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => handleBulkAction('FLAG')}
                            disabled={selectedReviews.length === 0}
                            className="w-full sm:w-auto"
                        >
                            Flag Selected
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={() => handleBulkAction('REJECT')}
                            disabled={selectedReviews.length === 0}
                            className="w-full sm:w-auto"
                        >
                            Reject Selected
                        </Button>
                        <Button
                            onClick={() => handleBulkAction('APPROVE')}
                            disabled={selectedReviews.length === 0}
                            className="w-full sm:w-auto"
                        >
                            Approve Selected
                        </Button>
                    </div>
                </div>

                {/* Desktop Table View */}
                <div className="hidden md:block rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[50px]">
                                    <Checkbox
                                        checked={selectedReviews.length === reviews.length && reviews.length > 0}
                                        onCheckedChange={handleSelectAll}
                                    />
                                </TableHead>
                                <TableHead>Review</TableHead>
                                <TableHead>Product</TableHead>
                                <TableHead>User</TableHead>
                                <TableHead>Rating</TableHead>
                                <TableHead>Date</TableHead>
                                <TableHead>Status</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {reviews.length > 0 ? (
                                reviews.map((review) => (
                                    <TableRow
                                        key={review.id}
                                        className="cursor-pointer hover:bg-gray-50"
                                        onClick={() => handleRowClick(review)}
                                    >
                                        <TableCell onClick={(e) => e.stopPropagation()}>
                                            <Checkbox
                                                checked={selectedReviews.includes(review.id!)}
                                                onCheckedChange={() => handleSelectReview(review.id!)}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <div className="max-w-[300px] truncate">
                                                {review.title}
                                            </div>
                                        </TableCell>
                                        <TableCell>{review.product?.name}</TableCell>
                                        <TableCell>{review.user?.userName}</TableCell>
                                        <TableCell>{review.rating}</TableCell>
                                        <TableCell>
                                            {format(new Date(review.createdDate!), 'MMM d, yyyy')}
                                        </TableCell>
                                        <TableCell>
                                            <span
                                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${review.isPublic && review.isVerified
                                                    ? 'bg-green-100 text-green-800'
                                                    : review.isVerified
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-yellow-100 text-yellow-800'
                                                    }`}
                                            >
                                                {review.isPublic && review.isVerified
                                                    ? 'Approved'
                                                    : review.isVerified
                                                        ? 'Rejected'
                                                        : 'Pending'}
                                            </span>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                                        {loading ? 'Loading reviews...' : 'No reviews found'}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Mobile Card View */}
                <div className="md:hidden space-y-4">
                    {reviews.length > 0 ? (
                        reviews.map((review) => (
                            <Card 
                                key={review.id} 
                                className="cursor-pointer hover:shadow-md transition-shadow"
                                onClick={() => handleRowClick(review)}
                            >
                                <CardContent className="p-4">
                                    <div className="flex items-start gap-3 mb-3">
                                        <Checkbox
                                            checked={selectedReviews.includes(review.id!)}
                                            onCheckedChange={() => handleSelectReview(review.id!)}
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                        <div className="flex-1 min-w-0">
                                            <h3 className="font-medium text-sm leading-tight mb-1">
                                                {review.title}
                                            </h3>
                                            <div className="flex items-center gap-2 mb-2">
                                                <div className="flex items-center">
                                                    <Star className="h-3 w-3 text-yellow-500 fill-yellow-500 mr-1" />
                                                    <span className="text-xs font-medium">{review.rating}</span>
                                                </div>
                                                <Badge
                                                    variant="outline"
                                                    className={`text-xs ${review.isPublic && review.isVerified
                                                        ? 'bg-green-100 text-green-800 border-green-200'
                                                        : review.isVerified
                                                            ? 'bg-red-100 text-red-800 border-red-200'
                                                            : 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                                        }`}
                                                >
                                                    {review.isPublic && review.isVerified
                                                        ? 'Approved'
                                                        : review.isVerified
                                                            ? 'Rejected'
                                                            : 'Pending'}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="space-y-2 text-xs text-muted-foreground">
                                        <div className="flex items-center gap-2">
                                            <Package className="h-3 w-3" />
                                            <span>{review.product?.name}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <User className="h-3 w-3" />
                                            <span>{review.user?.userName}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-3 w-3" />
                                            <span>{format(new Date(review.createdDate!), 'MMM d, yyyy')}</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    ) : (
                        <Card>
                            <CardContent className="p-8 text-center text-gray-500">
                                {loading ? 'Loading reviews...' : 'No reviews found'}
                            </CardContent>
                        </Card>
                    )}
                </div>

                {totalPages > 1 && (
                    <div className="flex items-center justify-end space-x-2 mt-4">
                        <Button
                            variant="outline"
                            onClick={() => setPage(p => Math.max(1, p - 1))}
                            disabled={page === 1 || loading}
                        >
                            Previous
                        </Button>
                        <div className="text-sm">
                            Page {page} of {totalPages}
                        </div>
                        <Button
                            variant="outline"
                            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                            disabled={page === totalPages || loading}
                        >
                            Next
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
});

// Add display name for React DevTools
ReviewModerationQueue.displayName = "ReviewModerationQueue";

export default ReviewModerationQueue;