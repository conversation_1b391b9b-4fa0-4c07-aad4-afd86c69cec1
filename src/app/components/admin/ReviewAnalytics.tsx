'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    BarChart,
    Bar,
} from 'recharts';

interface ReviewAnalyticsData {
    reviewVolume: Array<{
        createdDate: string;
        _count: { id: number };
    }>;
    ratingDistribution: Array<{
        rating: number;
        _count: { rating: number };
    }>;
    moderationStats: Array<{
        isPublic: boolean;
        isVerified: boolean;
        _count: { id: number };
    }>;
    qualityMetrics: {
        _avg: { rating: number };
        _count: { id: number };
    };
    topReviewers: Array<{
        userId: string;
        _count: { id: number };
        user: {
            userName: string;
            firstName: string;
            lastName: string;
        };
    }>;
    timeRange: {
        start: string;
        end: string;
    };
}

export default function ReviewAnalytics() {
    const [data, setData] = useState<ReviewAnalyticsData | null>(null);
    const [timeRange, setTimeRange] = useState('30');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchAnalytics = useCallback(async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/admin/reviews/analytics?days=${timeRange}`);
            const result = await response.json();

            if (result.success) {
                setData(result.data);
            } else {
                throw new Error(result.error || 'Failed to fetch analytics');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setLoading(false);
        }
    }, [timeRange]);

    useEffect(() => {
        fetchAnalytics();
    }, [fetchAnalytics]);

    if (loading) {
        return <div>Loading analytics...</div>;
    }

    if (!data) {
        return <div>No analytics data available</div>;
    }

    // Format review volume data for the line chart
    const volumeData = data.reviewVolume.map(item => ({
        date: new Date(item.createdDate).toLocaleDateString(),
        count: item._count.id,
    }));

    // Format rating distribution data for the bar chart
    const ratingData = data.ratingDistribution.map(item => ({
        rating: `${item.rating} Stars`,
        count: item._count.rating,
    }));

    // Calculate moderation queue stats
    const pendingCount = data.moderationStats.find(
        stat => !stat.isPublic && !stat.isVerified
    )?._count.id || 0;

    const approvedCount = data.moderationStats.find(
        stat => stat.isPublic && stat.isVerified
    )?._count.id || 0;

    const rejectedCount = data.moderationStats.find(
        stat => !stat.isPublic && stat.isVerified
    )?._count.id || 0;

    return (
        <div className="space-y-6 w-full max-w-none px-2 sm:px-4">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">Review Analytics</h2>
                <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select time range" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="7">Last 7 days</SelectItem>
                        <SelectItem value="30">Last 30 days</SelectItem>
                        <SelectItem value="90">Last 90 days</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div className="grid gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4 w-full">
                <Card className="w-full">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Total Reviews
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {data.qualityMetrics._count.id}
                        </div>
                    </CardContent>
                </Card>

                <Card className="w-full">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Average Rating
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {data.qualityMetrics._avg.rating.toFixed(1)}
                        </div>
                    </CardContent>
                </Card>

                <Card className="w-full">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Pending Reviews
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{pendingCount}</div>
                    </CardContent>
                </Card>

                <Card className="w-full">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Rejected Reviews
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{rejectedCount}</div>
                    </CardContent>
                </Card>
            </div>

            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 w-full">
                <Card className="w-full">
                    <CardHeader>
                        <CardTitle>Review Volume Over Time</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="h-[300px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={volumeData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="date" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line
                                        type="monotone"
                                        dataKey="count"
                                        stroke="#8884d8"
                                        name="Reviews"
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </div>
                    </CardContent>
                </Card>

                <Card className="w-full">
                    <CardHeader>
                        <CardTitle>Rating Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="h-[300px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={ratingData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="rating" />
                                    <YAxis />
                                    <Tooltip />
                                    <Bar
                                        dataKey="count"
                                        fill="#8884d8"
                                        name="Reviews"
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <Card className="w-full">
                <CardHeader>
                    <CardTitle>Top Reviewers</CardTitle>
                    <CardDescription>
                        Most active reviewers in the selected time period
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {data.topReviewers.map((reviewer) => (
                            <div
                                key={reviewer.userId}
                                className="flex items-center justify-between"
                            >
                                <div className="flex-1 min-w-0">
                                    <p className="font-medium truncate">
                                        {reviewer.user.userName}
                                    </p>
                                    <p className="text-sm text-gray-500 truncate">
                                        {reviewer.user.firstName} {reviewer.user.lastName}
                                    </p>
                                </div>
                                <div className="text-sm text-gray-500 flex-shrink-0 ml-4">
                                    {reviewer._count.id} reviews
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}