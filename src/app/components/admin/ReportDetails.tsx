'use client';

import { useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { iReviewReport } from '@/app/util/Interfaces';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Loader2, Flag, AlertCircle, Clock, CheckCircle2, UserCircle, FileText, MessageSquare } from 'lucide-react';
import { formatDistance, format } from 'date-fns';
import Link from 'next/link';
import { ReadOnlyRating } from '@/app/components/RatingSystem';
import { Separator } from '@/components/ui/separator';

interface ReportDetailsProps {
    report: iReviewReport;
    isOpen: boolean;
    onClose: () => void;
    onStatusUpdate?: (status: 'PENDING' | 'REVIEWED' | 'RESOLVED', notes?: string, reviewAction?: { action: string, reason?: string }) => Promise<void>;
}

export default function ReportDetails({
    report,
    isOpen,
    onClose,
    onStatusUpdate
}: ReportDetailsProps) {
    const [status, setStatus] = useState<'PENDING' | 'REVIEWED' | 'RESOLVED'>(report.status);
    const [notes, setNotes] = useState<string>(report.notes || '');
    const [reviewAction, setReviewAction] = useState<{ action: string, reason?: string }>({ action: 'NONE' });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async () => {
        if (!onStatusUpdate) return;

        setIsSubmitting(true);
        try {
            await onStatusUpdate(status, notes, reviewAction);
        } catch (error) {
            console.error('Error updating report:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const getStatusBadge = (status: 'PENDING' | 'REVIEWED' | 'RESOLVED') => {
        switch (status) {
            case 'PENDING':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-amber-600 border-amber-200 bg-amber-50">
                        <Clock className="h-3 w-3" />
                        Pending
                    </Badge>
                );
            case 'REVIEWED':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-blue-600 border-blue-200 bg-blue-50">
                        <AlertCircle className="h-3 w-3" />
                        Reviewed
                    </Badge>
                );
            case 'RESOLVED':
                return (
                    <Badge variant="outline" className="flex gap-1 items-center text-green-600 border-green-200 bg-green-50">
                        <CheckCircle2 className="h-3 w-3" />
                        Resolved
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline">{status}</Badge>
                );
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Flag className="h-5 w-5 text-destructive" />
                        Report Details
                    </DialogTitle>
                    <DialogDescription>
                        Review and manage this report
                    </DialogDescription>
                </DialogHeader>

                <Tabs defaultValue="report" className="mt-4">
                    <TabsList className="grid grid-cols-3">
                        <TabsTrigger value="report" className="flex items-center gap-1">
                            <Flag className="h-4 w-4" />
                            Report
                        </TabsTrigger>
                        <TabsTrigger value="review" className="flex items-center gap-1">
                            <FileText className="h-4 w-4" />
                            Reported Review
                        </TabsTrigger>
                        <TabsTrigger value="users" className="flex items-center gap-1">
                            <UserCircle className="h-4 w-4" />
                            Users
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="report" className="space-y-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-muted-foreground">Report Status</h3>
                                <div>{getStatusBadge(report.status)}</div>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-muted-foreground">Report Date</h3>
                                <div className="text-sm">
                                    {report.createdAt ? (
                                        <>
                                            <time dateTime={new Date(report.createdAt).toISOString()}>
                                                {format(new Date(report.createdAt), 'PPP')}
                                            </time>
                                            <span className="text-muted-foreground ml-2">
                                                ({formatDistance(new Date(report.createdAt), new Date(), { addSuffix: true })})
                                            </span>
                                        </>
                                    ) : 'Unknown date'}
                                </div>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <h3 className="text-sm font-medium text-muted-foreground">Reason for Report</h3>
                            <p className="text-sm border rounded-md p-3 bg-muted/30">{report.reason}</p>
                        </div>

                        {report.resolvedAt && (
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-muted-foreground">Resolved</h3>
                                <div className="text-sm">
                                    <time dateTime={new Date(report.resolvedAt).toISOString()}>
                                        {format(new Date(report.resolvedAt), 'PPP p')}
                                    </time>
                                    {report.resolver && (
                                        <span className="text-muted-foreground ml-2">
                                            by {report.resolver.firstName} {report.resolver.lastName}
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}

                        {report.notes && (
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
                                <p className="text-sm border rounded-md p-3 bg-muted/30 whitespace-pre-wrap">{report.notes}</p>
                            </div>
                        )}
                    </TabsContent>

                    <TabsContent value="review" className="space-y-4 mt-4">
                        {report.review ? (
                            <div className="space-y-4">
                                <div className="border rounded-lg p-4 space-y-3">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Link
                                                href={`/reviews?id=${report.review.productId}`}
                                                className="text-sm font-medium hover:underline"
                                            >
                                                {report.review.product?.name || 'Unknown Product'}
                                            </Link>
                                            <ReadOnlyRating
                                name={report.review.id || 'review-rating'}
                                rating={report.review.rating}
                                size="sm"
                            />
                                        </div>
                                        <div>
                                            {report.review.isVerified ? (
                                                <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                                                    Verified
                                                </Badge>
                                            ) : (
                                                <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
                                                    Unverified
                                                </Badge>
                                            )}
                                        </div>
                                    </div>

                                    <div>
                                        <h3 className="font-medium">{report.review.title}</h3>
                                        <div
                                            className="mt-1 text-sm text-muted-foreground prose-sm max-w-full"
                                            dangerouslySetInnerHTML={{ __html: report.review.body }}
                                        />
                                    </div>

                                    <div className="flex gap-2 pt-2">
                                        <Link
                                            href={`/fr?id=${report.review.id}&productid=${report.review.productId}`}
                                            target="_blank"
                                            className="text-xs text-blue-600 hover:underline flex items-center gap-1"
                                        >
                                            <FileText className="h-3 w-3" />
                                            View Full Review
                                        </Link>
                                        {report.review.comments && report.review.comments.length > 0 && (
                                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                                                <MessageSquare className="h-3 w-3" />
                                                {report.review.comments.length} comments
                                            </span>
                                        )}
                                    </div>
                                </div>

                                {report.review.images && report.review.images.length > 0 && (
                                    <div className="space-y-2">
                                        <h3 className="text-sm font-medium text-muted-foreground">Images</h3>
                                        <div className="flex flex-wrap gap-2">
                                            {report.review.images.map((image, index) => (
                                                <a
                                                    key={index}
                                                    href={image}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="block w-20 h-20 rounded-md overflow-hidden bg-muted"
                                                >
                                                    <img src={image} alt={`Review image ${index + 1}`} className="w-full h-full object-cover" />
                                                </a>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-8 text-center text-muted-foreground">
                                <AlertCircle className="h-8 w-8 mx-auto mb-2 text-amber-500" />
                                <p>Review details not available</p>
                            </div>
                        )}
                    </TabsContent>

                    <TabsContent value="users" className="space-y-6 mt-4">
                        <div className="space-y-4">
                            <h3 className="text-sm font-medium">Reporter</h3>
                            {report.user ? (
                                <div className="flex items-start gap-3 p-3 border rounded-lg">
                                    <Avatar className="h-10 w-10">
                                        <AvatarImage src={report.user.avatar || ''} alt={`${report.user.userName}&apos;s avatar`} />
                                        <AvatarFallback>{report.user.firstName?.charAt(0)}{report.user.lastName?.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <Link
                                                href={`/userprofile/${report.user.id}`}
                                                className="font-medium hover:underline"
                                            >
                                                {report.user.firstName} {report.user.lastName}
                                            </Link>
                                            <Badge variant="outline" className="text-xs">@{report.user.userName}</Badge>
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Submitted this report {report.createdAt && formatDistance(new Date(report.createdAt), new Date(), { addSuffix: true })}
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground">User information not available</p>
                            )}
                        </div>

                        <Separator />

                        {report.review?.user && (
                            <div className="space-y-4">
                                <h3 className="text-sm font-medium">Review Author</h3>
                                <div className="flex items-start gap-3 p-3 border rounded-lg">
                                    <Avatar className="h-10 w-10">
                                        <AvatarImage src={report.review.user.avatar || ''} alt={`${report.review.user.userName}&apos;s avatar`} />
                                        <AvatarFallback>{report.review.user.firstName?.charAt(0)}{report.review.user.lastName?.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <Link
                                                href={`/userprofile/${report.review.user.id}`}
                                                className="font-medium hover:underline"
                                            >
                                                {report.review.user.firstName} {report.review.user.lastName}
                                            </Link>
                                            <Badge variant="outline" className="text-xs">@{report.review.user.userName}</Badge>
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Author of the reported review
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>

                {onStatusUpdate && report.status !== 'RESOLVED' && (
                    <>
                        <div className="grid grid-cols-2 gap-4 mt-6">
                            <div className="space-y-2">
                                <label htmlFor="status" className="text-sm font-medium">
                                    Update Status
                                </label>
                                <Select
                                    value={status}
                                    onValueChange={(value: 'PENDING' | 'REVIEWED' | 'RESOLVED') => setStatus(value)}
                                >
                                    <SelectTrigger id="status">
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="PENDING">Pending</SelectItem>
                                        <SelectItem value="REVIEWED">Reviewed</SelectItem>
                                        <SelectItem value="RESOLVED">Resolved</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="notes" className="text-sm font-medium">
                                    Notes
                                </label>
                                <Textarea
                                    id="notes"
                                    placeholder="Add notes about this report"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    className="resize-none h-[80px]"
                                />
                            </div>
                        </div>

                        {status === 'RESOLVED' && (
                            <div className="mt-4 space-y-4">
                                <div className="space-y-2">
                                    <label htmlFor="reviewAction" className="text-sm font-medium">
                                        Review Action
                                    </label>
                                    <Select
                                        value={reviewAction.action}
                                        onValueChange={(value) => setReviewAction({ ...reviewAction, action: value })}
                                    >
                                        <SelectTrigger id="reviewAction">
                                            <SelectValue placeholder="Select action" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="NONE">No Action</SelectItem>
                                            <SelectItem value="HIDE">Hide Review</SelectItem>
                                            <SelectItem value="DELETE">Delete Review</SelectItem>
                                            <SelectItem value="VERIFY">Verify Review</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {reviewAction.action === 'HIDE' && (
                                    <div className="space-y-2">
                                        <label htmlFor="actionReason" className="text-sm font-medium">
                                            Reason for Hiding
                                        </label>
                                        <Textarea
                                            id="actionReason"
                                            placeholder="Provide a reason for hiding this review"
                                            value={reviewAction.reason || ''}
                                            onChange={(e) => setReviewAction({ ...reviewAction, reason: e.target.value })}
                                            className="resize-none h-[80px]"
                                        />
                                    </div>
                                )}
                            </div>
                        )}

                        <DialogFooter className="mt-6">
                            <Button
                                variant="outline"
                                onClick={onClose}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={handleSubmit}
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Updating...
                                    </>
                                ) : (
                                    'Update Report'
                                )}
                            </Button>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    );
}