'use client';

import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { iReview, iModerationEvent } from '@/app/util/Interfaces';
import { format } from 'date-fns';
import { Star } from 'lucide-react';

interface ReviewDetailsProps {
    review: iReview;
    isOpen: boolean;
    onClose: () => void;
    onModerate: (action: 'APPROVE' | 'REJECT' | 'FLAG', reason?: string) => Promise<void>;
}

export default function ReviewDetails({
    review,
    isOpen,
    onClose,
    onModerate,
}: ReviewDetailsProps) {
    const [reason, setReason] = useState('');
    const [loading, setLoading] = useState(false);

    const handleModerate = async (action: 'APPROVE' | 'REJECT' | 'FLAG') => {
        try {
            setLoading(true);
            await onModerate(action, reason);
            setReason('');
            toast.success(`Review ${action.toLowerCase()}ed successfully`);
        } catch (error) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'An unknown error occurred';
            toast.error(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>Review Details</DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Review Content */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">{review.title}</h3>
                            <div className="flex items-center space-x-1">
                                {[...Array(5)].map((_, i) => (
                                    <Star
                                        key={i}
                                        className={`h-4 w-4 ${i < review.rating
                                            ? 'fill-yellow-400 text-yellow-400'
                                            : 'text-gray-300'
                                            }`}
                                    />
                                ))}
                            </div>
                        </div>
                        <p className="text-gray-600">{review.body}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>By {review.user?.userName}</span>
                            <span>•</span>
                            <span>
                                {format(new Date(review.createdDate!), 'MMM d, yyyy')}
                            </span>
                        </div>
                    </div>

                    {/* Product Information */}
                    <div className="space-y-2">
                        <h4 className="font-medium">Product</h4>
                        <div className="flex items-center space-x-4">
                            {review.product?.display_image && (
                                <img
                                    src={review.product.display_image}
                                    alt={review.product.name}
                                    className="h-16 w-16 rounded-md object-cover"
                                />
                            )}
                            <div>
                                <p className="font-medium">{review.product?.name}</p>
                                <p className="text-sm text-gray-500">
                                    {review.product?.description}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Moderation History */}
                    <div className="space-y-2">
                        <h4 className="font-medium">Moderation History</h4>
                        <div className="space-y-2">
                            {review.moderationHistory?.map((event: iModerationEvent) => (
                                <div
                                    key={event.id}
                                    className="rounded-lg border p-3 text-sm"
                                >
                                    <div className="flex items-center justify-between">
                                        <span
                                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${event.action === 'APPROVED'
                                                ? 'bg-green-100 text-green-800'
                                                : event.action === 'REJECTED'
                                                    ? 'bg-red-100 text-red-800'
                                                    : 'bg-yellow-100 text-yellow-800'
                                                }`}
                                        >
                                            {event.action}
                                        </span>
                                        <span className="text-gray-500">
                                            {format(
                                                new Date(event.createdAt!),
                                                'MMM d, yyyy HH:mm'
                                            )}
                                        </span>
                                    </div>
                                    {event.reason && (
                                        <p className="mt-1 text-gray-600">
                                            {event.reason}
                                        </p>
                                    )}
                                    <p className="mt-1 text-gray-500">
                                        By {event.admin?.userName}
                                    </p>
                                </div>
                            ))}
                            {(!review.moderationHistory ||
                                review.moderationHistory.length === 0) && (
                                    <p className="text-sm text-gray-500">
                                        No moderation history available
                                    </p>
                                )}
                        </div>
                    </div>

                    {/* Moderation Actions */}
                    <div className="space-y-4">
                        <h4 className="font-medium">Moderation Actions</h4>
                        <Textarea
                            placeholder="Add a reason for your action (optional)"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            className="min-h-[100px]"
                        />
                        <div className="flex justify-start space-x-4 w-full">
                            <Button
                                onClick={() => handleModerate('APPROVE')}
                                disabled={loading}
                                className="bg-green-600 hover:bg-green-700 text-white font-semibold border-0"
                            >
                                Approve Review
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={() => handleModerate('REJECT')}
                                disabled={loading}
                                className="bg-red-600 hover:bg-red-700 text-white font-semibold border-0"
                            >
                                Reject Review
                            </Button>
                            <Button
                                variant="outline"
                                onClick={() => handleModerate('FLAG')}
                                disabled={loading}
                                className="bg-amber-100 hover:bg-amber-200 text-amber-800 border-amber-300 font-semibold"
                            >
                                Flag Review
                            </Button>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
} 