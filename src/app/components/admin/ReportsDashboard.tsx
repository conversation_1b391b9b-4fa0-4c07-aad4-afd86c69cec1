'use client';

import { useEffect, useState } from 'react';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    Flag,
    AlertCircle,
    Clock,
    CheckCircle2,
    RefreshCcw,
    ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { formatDistance } from 'date-fns';
import { iReviewReport } from '@/app/util/Interfaces';

interface ReportStats {
    counts: {
        pending: number;
        reviewed: number;
        resolved: number;
        total: number;
    };
    recentReports: iReviewReport[];
    dailyReportCounts: {
        date: string;
        count: number;
    }[];
}

interface ReportsDashboardProps {
    initialStats?: {
        totalReports: number;
        reportDistribution: { status: string; _count: { status: number } }[];
        recentReports: any[];
    };
}

export default function ReportsDashboard({ initialStats }: ReportsDashboardProps) {
    const [stats, setStats] = useState<ReportStats | null>(() => {
        if (!initialStats) return null;

        // Transform initialStats into ReportStats format
        const counts = {
            pending: 0,
            reviewed: 0,
            resolved: 0,
            total: initialStats.totalReports
        };

        // Calculate counts from distribution
        initialStats.reportDistribution.forEach(dist => {
            const status = dist.status.toLowerCase();
            if (status in counts) {
                counts[status as keyof typeof counts] = dist._count.status;
            }
        });

        return {
            counts,
            recentReports: initialStats.recentReports,
            dailyReportCounts: [] // This will be populated by the API call
        };
    });
    const [loading, setLoading] = useState(!initialStats);

    useEffect(() => {
        if (!initialStats) {
            fetchStats();
        }
    }, [initialStats]);

    const fetchStats = async () => {
        try {
            const response = await fetch('/api/admin/reports/stats');
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to fetch report statistics');
            }

            setStats(data.metrics as ReportStats);
        } catch (error) {
            toast.error(error instanceof Error ? error.message : 'Failed to fetch report statistics');
        } finally {
            setLoading(false);
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'PENDING':
                return 'text-amber-600 bg-amber-50 border-amber-200';
            case 'REVIEWED':
                return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'RESOLVED':
                return 'text-green-600 bg-green-50 border-green-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'PENDING':
                return <Clock className="h-3 w-3" />;
            case 'REVIEWED':
                return <AlertCircle className="h-3 w-3" />;
            case 'RESOLVED':
                return <CheckCircle2 className="h-3 w-3" />;
            default:
                return <Flag className="h-3 w-3" />;
        }
    };

    if (loading) {
        return <div>Loading statistics...</div>;
    }

    if (!stats) {
        return <div>No statistics available</div>;
    }

    return (
        <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                    <Flag className="h-5 w-5 text-destructive" />
                    Report Statistics
                </h2>

                <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchStats}
                    disabled={loading}
                    className="mt-2 sm:mt-0"
                >
                    <RefreshCcw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-muted-foreground font-medium">Total Reports</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.counts.total || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-muted-foreground font-medium flex items-center gap-1">
                            <Clock className="h-4 w-4 text-amber-500" />
                            Pending
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-amber-600">{stats.counts.pending || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-muted-foreground font-medium flex items-center gap-1">
                            <AlertCircle className="h-4 w-4 text-blue-500" />
                            Reviewed
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-blue-600">{stats.counts.reviewed || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-muted-foreground font-medium flex items-center gap-1">
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                            Resolved
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{stats.counts.resolved || 0}</div>
                    </CardContent>
                </Card>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Recent Reports</CardTitle>
                    <CardDescription>Latest report submissions that require attention</CardDescription>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="space-y-4">
                            {Array.from({ length: 3 }).map((_, i) => (
                                <div key={i} className="flex items-start gap-3">
                                    <Skeleton className="h-10 w-10 rounded-full" />
                                    <div className="space-y-2 flex-1">
                                        <Skeleton className="h-4 w-24" />
                                        <Skeleton className="h-4 w-full" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : stats.recentReports && stats.recentReports.length > 0 ? (
                        <div className="space-y-4">
                            {stats.recentReports.map((report) => (
                                <div key={report.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                    <Avatar className="h-10 w-10">
                                        <AvatarImage src={report.user?.avatar || ''} alt={`${report.user?.userName}&apos;s avatar`} />
                                        <AvatarFallback>{report.user?.firstName?.charAt(0)}{report.user?.lastName?.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium">@{report.user?.userName}</span>
                                                <Badge
                                                    variant="outline"
                                                    className={`${getStatusColor(report.status)} text-xs flex items-center gap-1 px-1.5 py-0`}
                                                >
                                                    {getStatusIcon(report.status)}
                                                    {report.status.charAt(0) + report.status.slice(1).toLowerCase()}
                                                </Badge>
                                            </div>
                                            <span className="text-xs text-muted-foreground">
                                                {report.createdAt && formatDistance(new Date(report.createdAt), new Date(), { addSuffix: true })}
                                            </span>
                                        </div>
                                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                            Reported review: <span className="font-medium">{report.review?.title}</span>
                                        </p>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Reason: {report.reason}
                                        </p>
                                    </div>
                                </div>
                            ))}

                            <div className="flex justify-end">
                                <Link href="/admin/reports">
                                    <Button variant="ghost" size="sm" className="gap-1">
                                        View All Reports
                                        <ArrowRight className="h-4 w-4" />
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    ) : (
                        <div className="py-8 text-center text-muted-foreground">
                            <Flag className="h-8 w-8 mx-auto mb-2 opacity-30" />
                            <p>No reports available</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}