"use client";

import { useState, useEffect } from "react";
import StatusUpdateModal from "./StatusUpdateModal";
import BugReportDetailsModal from "./BugReportDetailsModal";
import { iBugReport } from "@/app/util/Interfaces";
import { toast } from "sonner";

const STATUS_COLORS = {
    OPEN: "bg-yellow-100 text-yellow-800",
    IN_PROGRESS: "bg-blue-100 text-blue-800",
    RESOLVED: "bg-green-100 text-green-800",
    CLOSED: "bg-gray-100 text-gray-800",
    WONT_FIX: "bg-red-100 text-red-800",
} as const;

const ITEMS_PER_PAGE = 10;

export default function BugReportList() {
    const [bugReports, setBugReports] = useState<iBugReport[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedStatus, setSelectedStatus] = useState<string>("ALL");
    const [selectedBugReport, setSelectedBugReport] = useState<iBugReport | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);

    const fetchBugReports = async () => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await fetch("/api/admin/bugreports");
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Failed to fetch bug reports");
            }
            const data = await response.json();
            setBugReports(data);
        } catch (error) {
            console.error("Error fetching bug reports:", error);
            setError(error instanceof Error ? error.message : "Failed to load bug reports");
            toast.error("Failed to load bug reports");
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchBugReports();
    }, []);

    const filteredBugReports = selectedStatus === "ALL"
        ? bugReports
        : bugReports.filter(report => report.status === selectedStatus);

    // Pagination logic
    const totalPages = Math.ceil(filteredBugReports.length / ITEMS_PER_PAGE);
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const paginatedBugReports = filteredBugReports.slice(startIndex, startIndex + ITEMS_PER_PAGE);

    const handleStatusUpdate = () => {
        fetchBugReports();
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center h-64">
                <div className="text-red-600 mb-4">{error}</div>
                <button
                    onClick={fetchBugReports}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                    Retry
                </button>
            </div>
        );
    }

    return (
        <div className="px-4 sm:px-6 lg:px-8">
            <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-xl font-semibold text-gray-900">Bug Reports</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        A list of all bug reports and their current status.
                    </p>
                </div>
                <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                    <select
                        value={selectedStatus}
                        onChange={(e) => {
                            setSelectedStatus(e.target.value);
                            setCurrentPage(1); // Reset to first page when filter changes
                        }}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    >
                        <option value="ALL">All Statuses</option>
                        <option value="OPEN">Open</option>
                        <option value="IN_PROGRESS">In Progress</option>
                        <option value="RESOLVED">Resolved</option>
                        <option value="CLOSED">Closed</option>
                        <option value="WONT_FIX">Won&apos;t Fix</option>
                    </select>
                </div>
            </div>

            <div className="mt-8 flex flex-col">
                {/* Desktop Table View */}
                <div className="hidden md:block">
                    <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                <table className="min-w-full divide-y divide-gray-300">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                                Title
                                            </th>
                                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                Reporter
                                            </th>
                                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                Status
                                            </th>
                                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                Created
                                            </th>
                                            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                                <span className="sr-only">Actions</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {paginatedBugReports.map((report) => (
                                            <tr key={report.id}>
                                                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                    <button
                                                        onClick={() => {
                                                            setSelectedBugReport(report);
                                                            setIsDetailsModalOpen(true);
                                                        }}
                                                        className="text-indigo-600 hover:text-indigo-900 hover:underline"
                                                    >
                                                        {report.title}
                                                    </button>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                    {report.reporter?.userName || 'Anonymous'}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-sm">
                                                    <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${STATUS_COLORS[report.status as keyof typeof STATUS_COLORS]}`}>
                                                        {report.status}
                                                    </span>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                    {new Date(report.created_at).toLocaleDateString()}
                                                </td>
                                                <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                                    <button
                                                        onClick={() => {
                                                            setSelectedBugReport(report);
                                                            setIsModalOpen(true);
                                                        }}
                                                        className="text-indigo-600 hover:text-indigo-900"
                                                    >
                                                        Update Status
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Mobile Card View */}
                <div className="md:hidden space-y-4">
                    {paginatedBugReports.map((report) => (
                        <div key={report.id} className="bg-white rounded-lg shadow p-4 flex flex-col gap-2">
                            <div className="flex items-center justify-between">
                                <h3 className="font-semibold text-lg text-gray-900">{report.title}</h3>
                                <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${STATUS_COLORS[report.status as keyof typeof STATUS_COLORS]}`}>{report.status}</span>
                            </div>
                            <div className="flex items-center justify-between text-sm text-gray-500">
                                <span>Reporter: {report.reporter?.userName || 'Anonymous'}</span>
                                <span>{new Date(report.created_at).toLocaleDateString()}</span>
                            </div>
                            <div className="flex gap-2 mt-2">
                                <button
                                    onClick={() => {
                                        setSelectedBugReport(report);
                                        setIsDetailsModalOpen(true);
                                    }}
                                    className="text-indigo-600 hover:text-indigo-900 hover:underline text-sm font-medium"
                                >
                                    View Details
                                </button>
                                <button
                                    onClick={() => {
                                        setSelectedBugReport(report);
                                        setIsModalOpen(true);
                                    }}
                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                >
                                    Update Status
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
                <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4">
                    <div className="flex flex-1 justify-between sm:hidden">
                        <button
                            onClick={() => setCurrentPage(page => Math.max(1, page - 1))}
                            disabled={currentPage === 1}
                            className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => setCurrentPage(page => Math.min(totalPages, page + 1))}
                            disabled={currentPage === totalPages}
                            className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                        >
                            Next
                        </button>
                    </div>
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700">
                                Showing <span className="font-medium">{startIndex + 1}</span> to{" "}
                                <span className="font-medium">
                                    {Math.min(startIndex + ITEMS_PER_PAGE, filteredBugReports.length)}
                                </span>{" "}
                                of <span className="font-medium">{filteredBugReports.length}</span> results
                            </p>
                        </div>
                        <div>
                            <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                <button
                                    onClick={() => setCurrentPage(page => Math.max(1, page - 1))}
                                    disabled={currentPage === 1}
                                    className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                                >
                                    <span className="sr-only">Previous</span>
                                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                                    </svg>
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                    <button
                                        key={page}
                                        onClick={() => setCurrentPage(page)}
                                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${currentPage === page
                                            ? "z-10 bg-indigo-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                            : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                                            }`}
                                    >
                                        {page}
                                    </button>
                                ))}
                                <button
                                    onClick={() => setCurrentPage(page => Math.min(totalPages, page + 1))}
                                    disabled={currentPage === totalPages}
                                    className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                                >
                                    <span className="sr-only">Next</span>
                                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06.02z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            )}

            {filteredBugReports.length === 0 && (
                <p className="text-center italic text-gray-500 mt-4">
                    No bug reports found{selectedStatus !== "ALL" ? ` with status "${selectedStatus}"` : ""}.
                </p>
            )}

            {selectedBugReport && (
                <>
                    <StatusUpdateModal
                        isOpen={isModalOpen}
                        onClose={() => {
                            setIsModalOpen(false);
                            setSelectedBugReport(null);
                        }}
                        bugReportId={selectedBugReport.id}
                        currentStatus={selectedBugReport.status}
                        onStatusUpdate={handleStatusUpdate}
                    />
                    <BugReportDetailsModal
                        isOpen={isDetailsModalOpen}
                        onClose={() => {
                            setIsDetailsModalOpen(false);
                            setSelectedBugReport(null);
                        }}
                        bugReport={selectedBugReport}
                    />
                </>
            )}
        </div>
    );
}