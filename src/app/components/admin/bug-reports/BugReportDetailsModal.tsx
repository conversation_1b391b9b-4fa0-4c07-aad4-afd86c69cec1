import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { iBugReport } from "@/app/util/Interfaces";

interface BugReportDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    bugReport: iBugReport;
}

const STATUS_COLORS = {
    OPEN: "bg-yellow-100 text-yellow-800",
    IN_PROGRESS: "bg-blue-100 text-blue-800",
    RESOLVED: "bg-green-100 text-green-800",
    CLOSED: "bg-gray-100 text-gray-800",
    WONT_FIX: "bg-red-100 text-red-800",
} as const;

export default function BugReportDetailsModal({
    isOpen,
    onClose,
    bugReport,
}: BugReportDetailsModalProps) {
    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                    id="bug-report-details-title"
                                >
                                    Bug Report Details
                                </Dialog.Title>

                                <div className="mt-4 space-y-4" role="region" aria-labelledby="bug-report-details-title">
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-500" id="bug-report-title">Title</h4>
                                        <p className="mt-1 text-sm text-gray-900" aria-labelledby="bug-report-title">{bugReport.title}</p>
                                    </div>

                                    <div>
                                        <h4 className="text-sm font-medium text-gray-500" id="bug-report-description">Description</h4>
                                        <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap" aria-labelledby="bug-report-description">{bugReport.description}</p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500" id="bug-report-status">Status</h4>
                                            <span
                                                className={`mt-1 inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${STATUS_COLORS[bugReport.status as keyof typeof STATUS_COLORS]}`}
                                                aria-labelledby="bug-report-status"
                                            >
                                                {bugReport.status}
                                            </span>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500" id="bug-report-created">Created</h4>
                                            <p className="mt-1 text-sm text-gray-900" aria-labelledby="bug-report-created">
                                                {new Date(bugReport.created_at).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Reporter</h4>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {bugReport.reporter?.userName || 'Anonymous'}
                                            </p>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Resolver</h4>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {bugReport.resolver?.userName || 'Not resolved yet'}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Browser</h4>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {bugReport.browser || 'Not specified'}
                                            </p>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Device</h4>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {bugReport.device || 'Not specified'}
                                            </p>
                                        </div>
                                    </div>

                                    {bugReport.mobile_os && (
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Mobile OS</h4>
                                            <p className="mt-1 text-sm text-gray-900">{bugReport.mobile_os}</p>
                                        </div>
                                    )}

                                    {bugReport.resolution_notes && (
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Resolution Notes</h4>
                                            <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{bugReport.resolution_notes}</p>
                                        </div>
                                    )}

                                    {bugReport.resolved_at && (
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-500">Resolved At</h4>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {new Date(bugReport.resolved_at).toLocaleString()}
                                            </p>
                                        </div>
                                    )}
                                </div>

                                <div className="mt-6">
                                    <button
                                        type="button"
                                        className="inline-flex justify-center rounded-md border border-transparent bg-indigo-100 px-4 py-2 text-sm font-medium text-indigo-900 hover:bg-indigo-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2"
                                        onClick={onClose}
                                        aria-label="Close bug report details"
                                    >
                                        Close
                                    </button>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
} 