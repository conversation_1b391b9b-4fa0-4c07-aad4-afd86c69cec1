"use client";

import { useState } from "react";
import { iProduct } from "@/app/util/Interfaces";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import EditProductForm from "@/app/components/EditProductForm";

interface ProductActionsProps {
  product: iProduct;
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  setEditDialogOpen: (isOpen: boolean) => void;
  setDeleteDialogOpen: (isOpen: boolean) => void;
  onUpdateProduct: (updatedProduct: iProduct) => Promise<void>;
  onDeleteProduct: (productId: string) => Promise<void>;
}

export default function ProductActions({
  product,
  isEditDialogOpen,
  isDeleteDialogOpen,
  setEditDialogOpen,
  setDeleteDialogOpen,
  onUpdateProduct,
  onDeleteProduct,
}: ProductActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!product.id) return;
    
    setIsDeleting(true);
    try {
      await onDeleteProduct(product.id);
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting product:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Product</DialogTitle>
            <DialogDescription>
              Make changes to the product information below.
            </DialogDescription>
          </DialogHeader>
          <EditProductForm
            initialProduct={product}
            onSubmit={async (updatedProduct: iProduct) => {
              await onUpdateProduct(updatedProduct);
              setEditDialogOpen(false);
            }}
            onCancel={() => setEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Product</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this product? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="font-medium">{product.name}</p>
            <p className="text-sm text-muted-foreground mt-1">
              {product.description && product.description.length > 100
                ? `${product.description.substring(0, 100)}...`
                : product.description}
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
