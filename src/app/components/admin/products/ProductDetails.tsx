"use client";

import { iProduct } from "@/app/util/Interfaces";
import { format } from "date-fns";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ProductDetailsProps {
  product: iProduct;
  isOpen: boolean;
  onClose: () => void;
  onToggleVisibility: (productId: string, currentVisibility: boolean) => Promise<void>;
}

export default function ProductDetails({
  product,
  isOpen,
  onClose,
  onToggleVisibility,
}: ProductDetailsProps) {
  if (!product) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto mt-16">
        <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
          <DialogTitle className="text-xl font-bold">Product Details</DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Product Image */}
            {product.display_image && (
              <div className="aspect-video relative rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={product.display_image}
                  alt={product.name || "Product image"}
                  fill
                  className="object-cover"
                />
              </div>
            )}

            {/* Product Information */}
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-lg">{product.name}</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {product.description}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-sm text-gray-600">Status:</span>
                  <Badge
                    variant={product.isDeleted ? "destructive" : "outline"}
                    className={
                      product.isDeleted ? "bg-red-100 text-red-800" : ""
                    }
                  >
                    {product.isDeleted ? "Deleted" : "Active"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium text-sm text-gray-600">Visibility:</span>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={product.isPublic ?? true}
                      onCheckedChange={() => onToggleVisibility(product.id!, product.isPublic ?? true)}
                      aria-label="Toggle product visibility"
                    />
                    <span className="text-muted-foreground">
                      {product.isPublic ?? true ? "Public" : "Hidden"}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium text-sm text-gray-600">Featured:</span>
                  <Badge variant={product.featuredPosition ? "default" : "secondary"}>
                    {product.featuredPosition ? `Position ${product.featuredPosition}` : "Not Featured"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium text-sm text-gray-600">Has Owner:</span>
                  <Badge variant={product.hasOwner ? "default" : "secondary"}>
                    {product.hasOwner ? "Yes" : "No"}
                  </Badge>
                </div>
                
                {product.createdBy && (
                  <div>
                    <span className="font-medium text-sm text-gray-600 block mb-2">Created By:</span>
                    <div className="flex items-center space-x-3 p-2 bg-white rounded-md border hover:bg-gray-50 transition-colors cursor-pointer"
                      onClick={() => {
                        // Navigate to user profile or show user details
                      }}
                    >
                      <div className="flex-1">
                        <p className="font-medium">{product.createdBy.firstName} {product.createdBy.lastName}</p>
                        <p className="text-sm text-gray-500">{product.createdBy.email}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Metadata */}
            <div className="space-y-4">
              <h3 className="font-medium">Product Metadata</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p className="text-muted-foreground">Created</p>
                  <p>
                    {product.createdDate && format(new Date(product.createdDate), "PPP")}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">Last Updated</p>
                  <p>
                    {product.updatedAt && format(new Date(product.updatedAt), "PPP")}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">Category</p>
                  <p>{(product as any).category || "Uncategorized"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">Brand</p>
                  <p>{(product as any).brand || "Unknown"}</p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-4">
              <h3 className="font-medium">Statistics</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <p className="text-2xl font-bold">{product.rating?.toFixed(1) || "N/A"}</p>
                  <p className="text-sm text-muted-foreground">Average Rating</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-2xl font-bold">{product.viewCount || 0}</p>
                  <p className="text-sm text-muted-foreground">Total Reviews</p>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            {(product as any).additionalInfo && (
              <div className="space-y-2">
                <h3 className="font-medium">Additional Information</h3>
                <div className="text-sm border rounded-md p-3 bg-muted/30">
                  {(product as any).additionalInfo}
                </div>
              </div>
            )}

            {/* Tags */}
            {product.tags && product.tags.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {product.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
