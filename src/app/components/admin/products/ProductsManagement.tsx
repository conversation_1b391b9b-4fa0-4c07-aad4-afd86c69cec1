"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { iProduct } from "@/app/util/Interfaces";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/app/hooks/use-media-query";
import ProductsTable from "./ProductsTable";
import ProductsCards from "./ProductsCards";
import ProductDetails from "./ProductDetails";
import ProductActions from "./ProductActions";
import ProductFilters from "./ProductFilters";
import DeletedProductsList from "./DeletedProductsList";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// API functions
const fetchProducts = async (page = 1, searchTerm = "", statusFilter = "all", sortBy = "name") => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: "10",
    sortBy,
  });

  if (searchTerm) {
    params.append("search", searchTerm);
  }

  if (statusFilter !== "all") {
    if (statusFilter === "active") {
      params.append("isDeleted", "false");
    } else if (statusFilter === "deleted") {
      params.append("isDeleted", "true");
    } else if (statusFilter === "public") {
      params.append("isPublic", "true");
    } else if (statusFilter === "hidden") {
      params.append("isPublic", "false");
    }
  }

  const response = await fetch(`/api/admin/products?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Failed to fetch products");
  }
  return response.json();
};

const updateProductStatus = async (id: string, data: { isDeleted?: boolean; featuredPosition?: number | null; isPublic?: boolean }) => {
  const response = await fetch(`/api/admin/products/${id}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to update product status");
  }

  return response.json();
};

const updateProduct = async (product: iProduct) => {
  const response = await fetch(`/api/admin/products/${product.id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(product),
  });

  if (!response.ok) {
    throw new Error("Failed to update product");
  }

  return response.json();
};

const deleteProduct = async (id: string) => {
  const response = await fetch(`/api/admin/products/${id}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    throw new Error("Failed to delete product");
  }

  return response.json();
};

export default function ProductsManagement() {
  // State
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [selectedProduct, setSelectedProduct] = useState<iProduct | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Responsive layout
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Query client
  const queryClient = useQueryClient();

  // Fetch products
  const { data, isLoading, isError } = useQuery({
    queryKey: ["products", page, searchTerm, statusFilter, sortBy],
    queryFn: () => fetchProducts(page, searchTerm, statusFilter, sortBy),
  });

  // Mutations
  const updateStatusMutation = useMutation({
    mutationFn: (params: { id: string; data: { isDeleted?: boolean; featuredPosition?: number | null; isPublic?: boolean } }) =>
      updateProductStatus(params.id, params.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const updateProductMutation = useMutation({
    mutationFn: updateProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const deleteProductMutation = useMutation({
    mutationFn: deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  // Handlers
  const handleViewDetails = (product: iProduct) => {
    setSelectedProduct(product);
    setIsDetailsOpen(true);
  };

  const handleEditProduct = (product: iProduct) => {
    setSelectedProduct(product);
    setEditDialogOpen(true);
  };

  const handleDeleteProduct = (product: iProduct) => {
    setSelectedProduct(product);
    setDeleteDialogOpen(true);
  };

  const handleToggleVisibility = async (productId: string, currentVisibility: boolean) => {
    await updateStatusMutation.mutateAsync({
      id: productId,
      data: { isPublic: !currentVisibility },
    });
  };

  // Loading and error states
  if (isLoading) {
    return <div>Loading products...</div>;
  }

  if (isError) {
    return <div>Error loading products</div>;
  }

  // Extract products and pagination from the response
  const products = data?.data?.products || [];
  const pagination = data?.data?.pagination || { page: 1, total: 0, totalPages: 1, limit: 10 };

  return (
    <div className="space-y-6 overflow-x-hidden max-w-full">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Products</h1>
          <p className="text-muted-foreground">
            Manage and organize your product catalog
          </p>
        </div>
        <Button>Add New Product</Button>
      </div>

      {/* Filters */}
      <ProductFilters
        onSearch={setSearchTerm}
        onStatusFilterChange={setStatusFilter}
        onSortChange={setSortBy}
        statusFilter={statusFilter}
        sortBy={sortBy}
      />

      {/* Products List */}
      <div className="hidden md:block">
        <ProductsTable
          products={products}
          onViewDetails={handleViewDetails}
          onEditProduct={handleEditProduct}
          onDeleteProduct={handleDeleteProduct}
          onToggleVisibility={handleToggleVisibility}
        />
      </div>

      <div className="md:hidden">
        <ProductsCards
          products={products}
          onViewDetails={handleViewDetails}
          onEditProduct={handleEditProduct}
          onDeleteProduct={handleDeleteProduct}
          onToggleVisibility={handleToggleVisibility}
        />
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-muted-foreground">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
          {pagination.total} products
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page + 1)}
            disabled={page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      </div>

      {/* Product Details Dialog */}
      {selectedProduct && (
        <ProductDetails
          product={selectedProduct}
          isOpen={isDetailsOpen}
          onClose={() => setIsDetailsOpen(false)}
          onToggleVisibility={handleToggleVisibility}
        />
      )}

      {/* Product Actions (Edit/Delete) */}
      {selectedProduct && (
        <ProductActions
          product={selectedProduct}
          isEditDialogOpen={isEditDialogOpen}
          isDeleteDialogOpen={isDeleteDialogOpen}
          setEditDialogOpen={setEditDialogOpen}
          setDeleteDialogOpen={setDeleteDialogOpen}
          onUpdateProduct={updateProductMutation.mutateAsync}
          onDeleteProduct={deleteProductMutation.mutateAsync}
        />
      )}

      {/* Deleted Products Section */}
      <div className="mt-8">
        <DeletedProductsList />
      </div>
    </div>
  );
}
