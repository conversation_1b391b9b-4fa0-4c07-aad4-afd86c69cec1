import React, { useState } from 'react';
import { iProductClaim } from '@/app/util/Interfaces';
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from '@/components/ui/card';
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { format } from 'date-fns';
import {
    Clock,
    CheckCircle,
    XCircle,
    User,
    Calendar,
    Info,
    Mail,
    MessageSquare,
    Image as ImageIcon,
    AlertTriangle,
    Check,
    X
} from 'lucide-react';
import { toast } from 'sonner';
import { formatProductAddress, hasAddressInfo } from '@/app/util/addressUtils';

interface ClaimReviewPanelProps {
    claims: iProductClaim[];
    onRefresh: () => void;
}

export default function ClaimReviewPanel({ claims, onRefresh }: ClaimReviewPanelProps) {
    const pendingClaims = claims.filter(claim => claim.status === 'PENDING');
    const approvedClaims = claims.filter(claim => claim.status === 'APPROVED');
    const rejectedClaims = claims.filter(claim => claim.status === 'REJECTED');

    return (
        <div className="w-full">
            <Tabs defaultValue="pending" className="w-full">
                <TabsList className="flex flex-col xs:flex-row gap-2 mb-4 w-full">
                    <TabsTrigger value="pending" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Pending
                        <Badge variant="secondary" className="ml-1">
                            {pendingClaims.length}
                        </Badge>
                    </TabsTrigger>
                    <TabsTrigger value="approved" className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        Approved
                        <Badge variant="secondary" className="ml-1">
                            {approvedClaims.length}
                        </Badge>
                    </TabsTrigger>
                    <TabsTrigger value="rejected" className="flex items-center gap-2">
                        <XCircle className="h-4 w-4" />
                        Rejected
                        <Badge variant="secondary" className="ml-1">
                            {rejectedClaims.length}
                        </Badge>
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="pending">
                    {pendingClaims.length === 0 ? (
                        <Card>
                            <CardContent className="pt-6 text-center text-muted-foreground">
                                No pending claims to review.
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {pendingClaims.map(claim => (
                                <ClaimReviewCard
                                    key={claim.id}
                                    claim={claim}
                                    onReviewComplete={onRefresh}
                                />
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="approved">
                    {approvedClaims.length === 0 ? (
                        <Card>
                            <CardContent className="pt-6 text-center text-muted-foreground">
                                No approved claims to display.
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {approvedClaims.map(claim => (
                                <ClaimHistoryCard key={claim.id} claim={claim} />
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="rejected">
                    {rejectedClaims.length === 0 ? (
                        <Card>
                            <CardContent className="pt-6 text-center text-muted-foreground">
                                No rejected claims to display.
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {rejectedClaims.map(claim => (
                                <ClaimHistoryCard key={claim.id} claim={claim} />
                            ))}
                        </div>
                    )}
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface ClaimReviewCardProps {
    claim: iProductClaim;
    onReviewComplete: () => void;
}

function ClaimReviewCard({ claim, onReviewComplete }: ClaimReviewCardProps) {
    const [isReviewing, setIsReviewing] = useState(false);
    const [rejectionReason, setRejectionReason] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleApprove = async () => {
        setIsSubmitting(true);
        try {
            const response = await fetch('/api/admin/review-claim', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    claimId: claim.id,
                    status: 'APPROVED',
                }),
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Claim approved successfully');
                onReviewComplete();
            } else {
                toast.error(data.message || 'Failed to approve claim');
            }
        } catch (error) {
            toast.error('An unexpected error occurred');
        } finally {
            setIsSubmitting(false);
            setIsReviewing(false);
        }
    };

    const handleReject = async () => {
        if (!rejectionReason.trim()) {
            toast.error('Please provide a reason for rejection');
            return;
        }

        setIsSubmitting(true);
        try {
            const response = await fetch('/api/admin/review-claim', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    claimId: claim.id,
                    status: 'REJECTED',
                    feedback: rejectionReason.trim(),
                }),
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Claim rejected successfully');
                onReviewComplete();
            } else {
                toast.error(data.message || 'Failed to reject claim');
            }
        } catch (error) {
            toast.error('An unexpected error occurred');
        } finally {
            setIsSubmitting(false);
            setIsReviewing(false);
        }
    };

    const handleStartReview = () => {
        setIsReviewing(true);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex justify-between items-center">
                    <span>Claim for {claim.product?.name}</span>
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100 border-yellow-200">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending Review
                    </Badge>
                </CardTitle>
                <CardDescription>
                    Submitted on {claim.createdAt ? format(new Date(claim.createdAt), 'PPP') : 'Unknown date'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="flex items-center gap-2">
                                <User className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <h4 className="text-sm font-medium">Claimant</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {claim.user?.firstName} {claim.user?.lastName}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <h4 className="text-sm font-medium">Date Submitted</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {claim.createdAt ? format(new Date(claim.createdAt), 'PPP') : 'Unknown date'}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Mail className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <h4 className="text-sm font-medium">Contact Information</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {claim.contactInfo}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start gap-2">
                                <MessageSquare className="h-5 w-5 text-muted-foreground mt-0.5" />
                                <div>
                                    <h4 className="text-sm font-medium">Additional Information</h4>
                                    <p className="text-sm text-muted-foreground whitespace-pre-line">
                                        {claim.additionalInfo}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <h4 className="text-sm font-medium flex items-center gap-1 mb-2">
                                    <ImageIcon className="h-4 w-4" />
                                    Supporting Images
                                </h4>
                                <div className="grid grid-cols-2 gap-2">
                                    {claim.images.map((image, index) => (
                                        <img
                                            key={index}
                                            src={image}
                                            alt={`Supporting evidence for claim ${index + 1}`}
                                            className="w-full h-full object-cover"
                                        />
                                    ))}
                                </div>
                            </div>

                            <div>
                                <h4 className="text-sm font-medium flex items-center gap-1 mb-2">
                                    <Info className="h-4 w-4" />
                                    Product Information
                                </h4>
                                <div className="bg-gray-50 p-3 rounded-md">
                                    <p className="text-sm font-medium">{claim.product?.name}</p>
                                    <p className="text-sm text-muted-foreground truncate">
                                        {claim.product?.description}
                                    </p>
                                    {claim.product && hasAddressInfo(claim.product) && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                            {formatProductAddress(claim.product)}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {isReviewing && (
                        <>
                            <Separator className="my-4" />
                            <div className="space-y-3">
                                <div className="flex items-start gap-2">
                                    <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                                    <div>
                                        <h4 className="text-sm font-medium">Review Decision</h4>
                                        <p className="text-sm text-muted-foreground">
                                            Please review the claim information carefully before making a decision.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">
                                        Rejection Reason (required if rejecting)
                                    </label>
                                    <Textarea
                                        placeholder="Explain why this claim is being rejected..."
                                        value={rejectionReason}
                                        onChange={(e) => setRejectionReason(e.target.value)}
                                        className="min-h-[100px]"
                                    />
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </CardContent>
            <CardFooter className={`flex ${isReviewing ? 'justify-between' : 'justify-end'} gap-2 relative z-10`}>
                {isReviewing ? (
                    <>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setIsReviewing(false);
                            }}
                            disabled={isSubmitting}
                            className="relative z-20"
                        >
                            Cancel
                        </Button>
                        <div className="flex gap-2 relative z-20">
                            <Button
                                variant="destructive"
                                onClick={handleReject}
                                disabled={isSubmitting || !rejectionReason.trim()}
                                className="flex items-center gap-1"
                            >
                                <X className="h-4 w-4" />
                                Reject
                            </Button>
                            <Button
                                variant="default"
                                onClick={handleApprove}
                                disabled={isSubmitting}
                                className="flex items-center gap-1"
                            >
                                <Check className="h-4 w-4" />
                                Approve
                            </Button>
                        </div>
                    </>
                ) : (
                    <Button
                        onClick={handleStartReview}
                        className="flex items-center gap-1 relative z-20"
                    >
                        <Clock className="h-4 w-4" />
                        Review Claim
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
}

interface ClaimHistoryCardProps {
    claim: iProductClaim;
}

function ClaimHistoryCard({ claim }: ClaimHistoryCardProps) {
    const isApproved = claim.status === 'APPROVED';

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex justify-between items-center">
                    <span>Claim for {claim.product?.name}</span>
                    {isApproved ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Approved
                        </Badge>
                    ) : (
                        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100 border-red-200">
                            <XCircle className="h-3 w-3 mr-1" />
                            Rejected
                        </Badge>
                    )}
                </CardTitle>
                <CardDescription>
                    Reviewed on {claim.reviewedAt ? format(new Date(claim.reviewedAt), 'PPP') : 'Unknown date'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-3">
                            <div className="flex items-center gap-2">
                                <User className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <h4 className="text-sm font-medium">Claimant</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {claim.user?.firstName} {claim.user?.lastName}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <h4 className="text-sm font-medium">Dates</h4>
                                    <p className="text-sm text-muted-foreground">
                                        Submitted: {claim.createdAt ? format(new Date(claim.createdAt), 'PPP') : 'Unknown'}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                        Reviewed: {claim.reviewedAt ? format(new Date(claim.reviewedAt), 'PPP') : 'Unknown'}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {!isApproved && claim.rejectionReason && (
                            <div className="bg-red-50 p-4 rounded-md border border-red-200">
                                <div className="flex items-start">
                                    <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                                    <div>
                                        <h4 className="text-sm font-medium text-red-800">Rejection Reason</h4>
                                        <p className="text-sm text-red-700 whitespace-pre-line">
                                            {claim.rejectionReason}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}

                        {isApproved && (
                            <div className="bg-green-50 p-4 rounded-md border border-green-200">
                                <div className="flex items-start">
                                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                                    <div>
                                        <h4 className="text-sm font-medium text-green-800">Claim Approved</h4>
                                        <p className="text-sm text-green-700">
                                            This claim was approved and ownership was granted to the user.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">Supporting Images</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {claim.images.map((image, index) => (
                                <img
                                    key={index}
                                    src={image}
                                    alt={`Supporting evidence for claim ${index + 1}`}
                                    className="rounded-md aspect-square object-cover"
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}