interface iTagViewProps {
  tag: string;
  isSelected: boolean;
  onClick: () => void;
  count: number;
}

const TagView = ({
  tag,
  onClick,
  isSelected,
  count,
}: {
  tag: string;
  onClick: () => void;
  isSelected: boolean;
  count: number;
}) => {
  return (
    <button
      onClick={onClick}
      className={`inline-block px-1 py-0.5 text-xs font-normal transition-all duration-200 whitespace-nowrap ${
        isSelected
          ? "text-myTheme-accent underline decoration-2 underline-offset-2 bg-myTheme-accent/5"
          : "text-myTheme-lightTextBody hover:text-myTheme-accent hover:underline hover:decoration-1 hover:underline-offset-2"
      }`}
    >
      <span className="text-left">
        {tag}
        {isSelected && count > 0 && (
          <span className="ml-1 text-xs text-myTheme-accent/70">
            ({count})
          </span>
        )}
      </span>
    </button>
  );
};

export default TagView;
