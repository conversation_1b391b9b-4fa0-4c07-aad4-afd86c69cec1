import Link from "next/link";
import Image from "next/image";
import BungeeTintText from "../util/BungeeFont";

const HomeLink = () => {
  return (
    <Link
      href="/"
      className="flex items-center gap-2 transition-all duration-300 ease-in-out hover:opacity-90"
    >
      <div className="relative w-8 h-8 md:w-9 md:h-9">
        <Image 
          src="/logo.png" 
          alt="Review It Logo" 
          fill
          sizes="(max-width: 768px) 32px, 36px"
          className="object-contain"
          priority
        />
      </div>
      <div className="font-semibold text-lg md:text-xl transition-colors duration-300 ease-linear hover:text-myTheme-accent">
        <BungeeTintText>[REVIEW IT]</BungeeTintText>
      </div>
    </Link>
  );
};

export default HomeLink;
