"use client";

import Image from 'next/image';

interface QuoteProps {
  userName: string;
  userImage?: string; // Optional: use a placeholder if not provided
  quoteText: string;
}

const Quote: React.FC<QuoteProps> = ({ userName, userImage, quoteText }) => {
  // Truncate to ensure consistent card sizes - shorter limit for better uniformity
  const displayQuote = quoteText.length > 80 ? `${quoteText.substring(0, 80)}...` : quoteText;

  return (
    <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg shadow-md h-40 flex flex-col w-full">
      <div className="flex items-start space-x-4 flex-1">
        <div className="flex-shrink-0">
          <Image
            src={userImage || '/placeholder-avatar.jpg'} // Ensure you have a placeholder avatar in public folder
            alt={`${userName}&apos;s avatar`}
            width={48}
            height={48}
            className="rounded-full"
          />
        </div>
        <div className="flex-1 flex flex-col justify-between h-full">
          <p 
            className="text-gray-700 dark:text-gray-300 italic text-sm leading-relaxed overflow-hidden" 
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              textOverflow: 'ellipsis'
            }}
            dangerouslySetInnerHTML={{ __html: `&quot;${displayQuote}&quot;` }} 
          />
          <p className="text-sm font-semibold text-gray-900 dark:text-white mt-2">- {userName}</p>
        </div>
      </div>
    </div>
  );
};

export default Quote;