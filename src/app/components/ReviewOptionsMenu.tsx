import React from 'react';
import { MoreHorizontal, PencilIcon, TrashIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface ReviewOptionsMenuProps {
  onEdit?: () => void;
  onDelete: () => void;
  setIsEditing?: (editing: boolean) => void;
}

const ReviewOptionsMenu: React.FC<ReviewOptionsMenuProps> = ({ onEdit, onDelete, setIsEditing }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="h-5 w-5 p-0 hover:bg-gray-100 rounded-full"
        >
          <MoreHorizontal className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-32">
        {onEdit && (
          <DropdownMenuItem 
            onClick={() => {
              onEdit();
              setIsEditing?.(true);
            }}
            className="cursor-pointer text-xs"
          >
            <PencilIcon className="mr-2 h-3 w-3" />
            Edit
          </DropdownMenuItem>
        )}
        <DropdownMenuItem 
          onClick={onDelete}
          className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50 text-xs"
        >
          <TrashIcon className="mr-2 h-3 w-3" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ReviewOptionsMenu;