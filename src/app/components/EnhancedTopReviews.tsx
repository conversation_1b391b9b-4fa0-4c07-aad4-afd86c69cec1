"use client";
import { iReview } from "../util/Interfaces";
import ReviewBox from "./ReviewBox";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "../util/serverFunctions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import {
  Clock,
  Flame,
  Star,
  ThumbsUp,
  TrendingUp,
  MapPin,
  Filter,
  Search,
  Calendar,
} from "lucide-react";
import { TopReviewsSkeleton } from "./skeletons";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type FilterType = "latest" | "popular" | "trending" | "verified" | "local";
type TimeFilter = "all" | "today" | "week" | "month";
type CategoryFilter =
  | "all"
  | "restaurants"
  | "services"
  | "retail"
  | "automotive";

const EnhancedTopReviews = () => {
  const [filter, setFilter] = useState<FilterType>("latest");
  const [timeFilter, setTimeFilter] = useState<TimeFilter>("all");
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewsPerPage, setReviewsPerPage] = useState(12);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["enhancedReviews", filter, timeFilter, categoryFilter],
    queryFn: async () => {
      const response = await getLatestReviews(filter);
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch reviews");
      }
      return response.data;
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filter, timeFilter, categoryFilter, searchTerm, reviewsPerPage]);

  // Filter reviews based on search and filters
  const filteredReviews = (data || []).filter((review: iReview) => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        review.title.toLowerCase().includes(searchLower) ||
        review.body.toLowerCase().includes(searchLower) ||
        review.product?.name.toLowerCase().includes(searchLower) ||
        review.user?.userName.toLowerCase().includes(searchLower);

      if (!matchesSearch) return false;
    }

    // Category filter
    if (categoryFilter !== "all") {
      const productTags = review.product?.tags || [];
      const categoryMap = {
        restaurants: ["restaurant", "fast food", "cafe", "bar", "food"],
        services: ["service", "repair", "delivery", "taxi", "healthcare"],
        retail: ["store", "shop", "electronics", "clothing", "supermarket"],
        automotive: ["car rental", "auto repair", "gas station", "mechanic"],
      };

      const categoryTags =
        categoryMap[categoryFilter as keyof typeof categoryMap] || [];
      const hasCategory = productTags.some((tag) =>
        categoryTags.some((catTag) => tag.toLowerCase().includes(catTag)),
      );

      if (!hasCategory) return false;
    }

    // Time filter
    if (timeFilter !== "all") {
      const reviewDate = review.createdDate
        ? new Date(review.createdDate)
        : new Date();
      const now = new Date();
      const timeDiff = now.getTime() - reviewDate.getTime();

      switch (timeFilter) {
        case "today":
          if (timeDiff > 24 * 60 * 60 * 1000) return false;
          break;
        case "week":
          if (timeDiff > 7 * 24 * 60 * 60 * 1000) return false;
          break;
        case "month":
          if (timeDiff > 30 * 24 * 60 * 60 * 1000) return false;
          break;
      }
    }

    return true;
  });

  if (isError) {
    return (
      <div className="text-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-lg font-medium text-red-800">
          Unable to load reviews
        </p>
        <p className="text-red-600 mb-4">
          {error instanceof Error ? error.message : "Unknown error"}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoading) return <TopReviewsSkeleton />;

  const FilterButton = ({
    type,
    icon,
    label,
    count,
    isActive,
  }: {
    type: FilterType;
    icon: React.ReactNode;
    label: string;
    count?: number;
    isActive: boolean;
  }) => (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`flex items-center gap-2 relative px-4 py-3 font-medium text-sm transition-all duration-200 ease-in-out rounded-lg ${
        isActive
          ? "bg-myTheme-primary text-white shadow-md"
          : "bg-white text-myTheme-accent hover:bg-myTheme-primary/10 hover:text-myTheme-primary border border-gray-200"
      }`}
      onClick={() => setFilter(type)}
    >
      {icon}
      <span>{label}</span>
      {count && (
        <Badge
          variant="secondary"
          className={`ml-1 ${isActive ? "bg-white/20 text-white" : "bg-gray-100"}`}
        >
          {count}
        </Badge>
      )}
    </motion.button>
  );

  const activeFiltersCount = [
    timeFilter !== "all" ? 1 : 0,
    categoryFilter !== "all" ? 1 : 0,
    searchTerm ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col w-full justify-center items-center space-y-8 bg-gradient-to-br from-blue-50/50 to-white p-6 md:p-8 rounded-2xl shadow-sm border border-gray-100"
    >
      {/* Header Section */}
      <div className="w-full flex flex-col items-center space-y-6">
        <div className="text-center space-y-2">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-800 leading-tight"
          >
            Latest Reviews from Guyana
          </motion.h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover what fellow Guyanese are saying about local businesses and
            services
          </p>
        </div>

        {/* Search Bar */}
        <div className="relative w-full max-w-md">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <Input
            type="text"
            placeholder="Search reviews, businesses, or reviewers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 py-3 rounded-xl border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
          />
        </div>

        {/* Primary Filter Buttons */}
        <div className="flex gap-3 flex-wrap justify-center">
          <FilterButton
            type="latest"
            icon={<Clock size={18} />}
            label="Latest"
            count={filter === "latest" ? filteredReviews.length : undefined}
            isActive={filter === "latest"}
          />
          <FilterButton
            type="popular"
            icon={<ThumbsUp size={18} />}
            label="Most Helpful"
            count={filter === "popular" ? filteredReviews.length : undefined}
            isActive={filter === "popular"}
          />
          <FilterButton
            type="trending"
            icon={<Flame size={18} />}
            label="Trending"
            count={filter === "trending" ? filteredReviews.length : undefined}
            isActive={filter === "trending"}
          />
          <FilterButton
            type="verified"
            icon={<Star size={18} />}
            label="Verified"
            isActive={filter === "verified"}
          />
          <FilterButton
            type="local"
            icon={<MapPin size={18} />}
            label="Georgetown"
            isActive={filter === "local"}
          />
        </div>

        {/* Advanced Filters Toggle */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            className="flex items-center gap-2"
          >
            <Filter size={16} />
            More Filters
            {activeFiltersCount > 0 && (
              <Badge
                variant="secondary"
                className="bg-myTheme-primary text-white"
              >
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setTimeFilter("all");
                setCategoryFilter("all");
                setSearchTerm("");
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              Clear Filters
            </Button>
          )}
        </div>

        {/* Advanced Filters Panel */}
        <AnimatePresence>
          {isFilterExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="w-full max-w-4xl bg-white rounded-xl border border-gray-200 p-4 shadow-sm"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Time Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar size={16} className="inline mr-2" />
                    Time Period
                  </label>
                  <div className="flex gap-2 flex-wrap">
                    {[
                      { value: "all", label: "All Time" },
                      { value: "today", label: "Today" },
                      { value: "week", label: "This Week" },
                      { value: "month", label: "This Month" },
                    ].map((option) => (
                      <button
                        key={option.value}
                        onClick={() =>
                          setTimeFilter(option.value as TimeFilter)
                        }
                        className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                          timeFilter === option.value
                            ? "bg-myTheme-primary text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <TrendingUp size={16} className="inline mr-2" />
                    Category
                  </label>
                  <div className="flex gap-2 flex-wrap">
                    {[
                      { value: "all", label: "All Categories" },
                      { value: "restaurants", label: "Food & Dining" },
                      { value: "services", label: "Services" },
                      { value: "retail", label: "Shopping" },
                      { value: "automotive", label: "Automotive" },
                    ].map((option) => (
                      <button
                        key={option.value}
                        onClick={() =>
                          setCategoryFilter(option.value as CategoryFilter)
                        }
                        className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                          categoryFilter === option.value
                            ? "bg-myTheme-primary text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results Summary */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Showing{" "}
            <span className="font-semibold">
              {filteredReviews.length === 0
                ? 0
                : (currentPage - 1) * reviewsPerPage + 1}
            </span>
            -
            <span className="font-semibold">
              {Math.min(currentPage * reviewsPerPage, filteredReviews.length)}
            </span>{" "}
            of <span className="font-semibold">{filteredReviews.length}</span>{" "}
            reviews
            {searchTerm && (
              <span>
                {" "}
                matching &ldquo;
                <span className="font-medium">{searchTerm}</span>&rdquo;
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Reviews Grid */}
      {filteredReviews.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center p-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-200"
        >
          <div className="text-6xl mb-4">🔍</div>
          <p className="text-xl font-medium text-gray-800 mb-2">
            No reviews found
          </p>
          <p className="text-gray-600 mb-4">
            {searchTerm || activeFiltersCount > 0
              ? "Try adjusting your search or filters"
              : "Be the first to share your experience!"}
          </p>
          {(searchTerm || activeFiltersCount > 0) && (
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setTimeFilter("all");
                setCategoryFilter("all");
                setFilter("latest");
                setCurrentPage(1);
              }}
            >
              Clear All Filters
            </Button>
          )}
        </motion.div>
      ) : (
        <>
          <motion.div
            layout
            className="w-full grid mx-auto items-start justify-center grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
          >
            <AnimatePresence mode="popLayout">
              {filteredReviews
                .slice(
                  (currentPage - 1) * reviewsPerPage,
                  currentPage * reviewsPerPage,
                )
                .map((review: iReview, index: number) => (
                  <motion.div
                    key={review.id}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                  >
                    <ReviewBox review={review} />
                  </motion.div>
                ))}
            </AnimatePresence>
          </motion.div>

          {/* Pagination Controls */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8"
          >
            {/* Items per page selector */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Show:</span>
              <Select
                value={reviewsPerPage.toString()}
                onValueChange={(value) => setReviewsPerPage(Number(value))}
              >
                <SelectTrigger className="w-20 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                </SelectContent>
              </Select>
              <span>per page</span>
            </div>

            {/* Pagination buttons */}
            {Math.ceil(filteredReviews.length / reviewsPerPage) > 1 && (
              <div className="flex justify-center items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-4 py-2"
                >
                  Previous
                </Button>

                <div className="flex gap-1">
                  {Array.from({
                    length: Math.ceil(filteredReviews.length / reviewsPerPage),
                  })
                    .map((_, index) => index + 1)
                    .filter((page) => {
                      const totalPages = Math.ceil(
                        filteredReviews.length / reviewsPerPage,
                      );
                      if (totalPages <= 7) return true;
                      if (page <= 3) return true;
                      if (page >= totalPages - 2) return true;
                      if (Math.abs(page - currentPage) <= 1) return true;
                      return false;
                    })
                    .map((page, index, array) => {
                      const showEllipsis =
                        index > 0 && page - array[index - 1] > 1;
                      return (
                        <div key={page} className="flex items-center gap-1">
                          {showEllipsis && (
                            <span className="px-2 py-1 text-gray-500">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => setCurrentPage(page)}
                            className={`px-3 py-2 min-w-[40px] ${
                              currentPage === page
                                ? "bg-myTheme-primary text-white"
                                : "hover:bg-gray-100"
                            }`}
                          >
                            {page}
                          </Button>
                        </div>
                      );
                    })}
                </div>

                <Button
                  variant="outline"
                  onClick={() =>
                    setCurrentPage((prev) =>
                      Math.min(
                        Math.ceil(filteredReviews.length / reviewsPerPage),
                        prev + 1,
                      ),
                    )
                  }
                  disabled={
                    currentPage ===
                    Math.ceil(filteredReviews.length / reviewsPerPage)
                  }
                  className="px-4 py-2"
                >
                  Next
                </Button>
              </div>
            )}
          </motion.div>
        </>
      )}

      {/* Real-time Stats */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-center gap-6 text-sm text-gray-500 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full border border-gray-200"
      >
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span>Live updates</span>
        </div>
        <div>Last updated: just now</div>
        <div>{filteredReviews.length} total reviews</div>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedTopReviews;
