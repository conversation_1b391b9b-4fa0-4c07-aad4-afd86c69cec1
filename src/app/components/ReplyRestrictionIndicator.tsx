import React from "react";
import { PermissionResponse } from "@/app/util/commentPermissions";

interface ReplyRestrictionIndicatorProps {
  permission: PermissionResponse;
}

const ReplyRestrictionIndicator: React.FC<ReplyRestrictionIndicatorProps> = ({ permission }) => {
  if (permission.allowed) {
    return null;
  }

  // Define messages for different restriction types
  const messages: Record<string, string> = {
    not_authenticated: "You must be signed in to reply",
    not_reviewer: "Only the original reviewer can reply to the owner",
    default: "You are not allowed to reply to this comment",
  };

  const message = permission.restrictionType ? messages[permission.restrictionType] || messages.default : messages.default;

  return (
    <div className="text-sm text-gray-500 p-2 bg-gray-50 rounded-md border border-gray-200 mt-2">
      {message}
    </div>
  );
};

export default ReplyRestrictionIndicator;
