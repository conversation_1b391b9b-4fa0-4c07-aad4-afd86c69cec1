import React from 'react';
import Image from 'next/image';

interface FullWidthAdvertProps {
    title: string;
    description: string;
    imageUrl: string;
    link: string;
}

const FullWidthAdvert: React.FC<FullWidthAdvertProps> = ({ title, description, imageUrl, link }) => {
    return (
        <div className="w-full bg-white p-6 my-4 shadow-md rounded-xl">
            <a href={link} target="_blank" rel="noopener noreferrer" className="block">
                <div className="flex flex-col md:flex-row items-center gap-6">
                    <div className="relative w-full md:w-1/3 aspect-square max-w-[200px]">
                        <Image
                            src={imageUrl}
                            alt={title}
                            layout="fill"
                            objectFit="cover"
                            className="rounded-lg"
                        />
                    </div>
                    <div className="flex-1 text-center md:text-left">
                        <h3 className="text-2xl font-bold mb-2">{title}</h3>
                        <p className="text-gray-700">{description}</p>
                    </div>
                </div>
            </a>
        </div>
    );
};

export default FullWidthAdvert; 