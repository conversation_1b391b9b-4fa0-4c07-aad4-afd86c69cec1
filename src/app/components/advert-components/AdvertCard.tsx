import React from 'react';
import Image from 'next/image';

interface AdvertCardProps {
    title: string;
    description: string;
    imageUrl: string;
    link: string;
    isSponsored?: boolean;
}

const AdvertCard: React.FC<AdvertCardProps> = ({ title, description, imageUrl, link, isSponsored }) => {
    return (
        <div className="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 relative flex flex-col h-[300px] overflow-hidden backdrop-blur-sm bg-white/90">
            {isSponsored && (
                <div className="absolute top-2 right-2 bg-yellow-400 text-yellow-900 text-xs font-semibold px-2 py-0.5 rounded-full z-10">
                    Sponsored
                </div>
            )}
            <div className="relative w-full h-48 mb-4">
                <Image
                    src={imageUrl}
                    alt={title}
                    layout="fill"
                    objectFit="cover"
                    className="rounded-lg"
                />
            </div>
            <div className="flex-grow">
                <div className="font-bold text-xl mb-2">{title}</div>
                <p className="text-gray-700 text-base line-clamp-2">
                    {description}
                </p>
            </div>
            <div className="mt-auto pt-4">
                <a
                    href={link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full transition-all duration-200"
                >
                    Learn More
                </a>
            </div>
        </div>
    );
};

export default AdvertCard; 