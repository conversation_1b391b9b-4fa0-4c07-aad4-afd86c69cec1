"use client";
import { iReview, iProduct } from "../util/Interfaces";
import { useEffect, useState } from "react";
import { useUser } from "@/app/hooks/useUser";
import { useUser as useClerkUser } from "@clerk/nextjs";
import ReviewAvailabilityMessage from "./ReviewAvailabilityMessage";
import DisplayError from "@/app/components/DisplayError";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getProduct } from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import { useAtom } from "jotai";
import { allProductsAtom } from "../store/store";
import { useRouter, useSearchParams } from "next/navigation";

// Import the extracted components
import OwnershipMessage from "./review/OwnershipMessage";
import ReviewFormHeader from "./review/ReviewFormHeader";
import ReviewRatingSection from "./review/ReviewRatingSection";
import ReviewTitleSection from "./review/ReviewTitleSection";
import ReviewDateSection from "./review/ReviewDateSection";
import ReviewBodySection from "./review/ReviewBodySection";
import ReviewMediaSection from "./review/ReviewMediaSection";
import ReviewSubmitSection from "./review/ReviewSubmitSection";
import ProductCardSection from "./review/ProductCardSection";
import EditorPreview from './EditorPreview';

const ReviewForm = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchRating = searchParams.get("rating");
  const id = searchParams.get("id")!;
  const [disabled, setDisabled] = useState(false);
  const { user, isLoggedIn, isAllowed, securityReason } = useUser();
  const { user: clerkUser, isLoaded, isSignedIn } = useClerkUser();
  const [linksArray, setLinksArray] = useState<string[]>([]);
  const [videosArray, setVideosArray] = useState<string[]>([]);
  const [allUploaded, setAllUploaded] = useState(false);
  const [hasUnuploadedImages, setHasUnuploadedImages] = useState(false);
  // make sure there is an int in searchRating and make sure its between 1 and 5
  const [rating, setRating] = useState(
    searchRating ? parseInt(searchRating) : 2,
  ); // Initial value
  const [startDate, setStartDate] = useState(new Date());
  const [error, setError] = useState<string | null>(null);
  const [reviewEligibility, setReviewEligibility] = useState({
    isEligible: true,
    message: "",
    nextEligibleDate: null,
    daysRemaining: 0
  });
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false);
  const [reviewData, setReviewData] = useState<iReview>({
    body: "",
    createdDate: new Date(),
    rating: rating,
    title: "",
    productId: id,
    userId: clerkUser?.publicMetadata?.id as string || '',
    isVerified: false,
    verifiedBy: undefined,
    isPublic: true,
    images: linksArray,
    videos: videosArray,
    links: linksArray,
    comments: [],
    createdBy: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : '',
    isDeleted: false,
    likedBy: [],
    voteCount: null,
  });

  const [products, setProducts] = useAtom(allProductsAtom);
  const productCardOptions = {
    showLatestReview: false,
    size: "rating-md",
    showWriteReview: false,
    showClaimThisProduct: true,
  };

  // Move useQuery to the top level, before any conditional returns
  const { data: product, isLoading } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      const response = await getProduct(id);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch product');
      }
      return response.data;
    },
    enabled: !!id,
  });

  // All useEffect hooks must be together at the top level
  useEffect(() => {
    if (linksArray.length > 0) {
      setReviewData(
        (prevData): iReview => ({
          ...prevData,
          images: linksArray,
        }),
      );
    }
  }, [linksArray]);
  
  // Track if there are unuploaded images
  useEffect(() => {
    // If we have images in the array but they're not all uploaded yet
    setHasUnuploadedImages(linksArray.length > 0 && !allUploaded);
  }, [linksArray, allUploaded]);

  // Check if the user is eligible to write a review
  useEffect(() => {
    const checkEligibility = async () => {
      // Make sure we have a valid user ID from metadata
      const userId = clerkUser?.publicMetadata?.id as string | undefined;
      if (!userId || !id) return;

      try {
        setIsCheckingEligibility(true);
        const response = await fetch('/api/check-review-eligibility', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            userId,
            productId: id
          })
        });

        if (!response.ok) {
          console.error("Error checking eligibility:", response.statusText);
          return;
        }

        const eligibilityData = await response.json();
        setReviewEligibility(eligibilityData);

        // If not eligible, set the error message
        if (!eligibilityData.isEligible) {
          setError(eligibilityData.message);
        }
      } catch (error) {
        console.error("Error checking review eligibility:", error);
      } finally {
        setIsCheckingEligibility(false);
      }
    };

    if (isLoaded && isSignedIn && clerkUser?.publicMetadata?.id && id) {
      checkEligibility();
    }
  }, [isLoaded, isSignedIn, clerkUser?.publicMetadata?.id, id, setError]);

  // Add authentication check
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('https://accounts.reviewit.gy/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Update user ID in reviewData when it becomes available
  useEffect(() => {
    if (clerkUser?.publicMetadata?.id) {
      setReviewData(prevData => ({
        ...prevData,
        userId: clerkUser.publicMetadata.id as string,
        createdBy: clerkUser ? `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() : ''
      }));
    }
  }, [clerkUser]);

  const handleEditorValue = (value: string) => {
    if (value === "" || value === "<p></p>") {
      setReviewData((prevData): iReview => ({ ...prevData, body: "" }));
      return;
    }
    setReviewData((prevData): iReview => ({ ...prevData, body: value }));
    setError((prevError) => (prevError = null));
  };

  const ratingChanged = (newRating: number) => {
    setRating(newRating);
    function addRating(rating: number) {
      // not sure if this is necessary, but it should be the safest way. test before making simpler
      setReviewData((prevData): iReview => ({ ...prevData, rating: rating }));
    }
    addRating(newRating);
  };

  const sendToServer = async () => {
    try {
      const response = await fetch(`/api/create/review`, {
        method: "POST",
        body: JSON.stringify(reviewData),
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
      });

      const responseData = await response.json();

      if (response.ok) {
          // Invalidate cached latest reviews so home page refetches fresh data
          queryClient.invalidateQueries({ queryKey: ["latestReviews"] });
        router.push(`/reviews?id=${id}`);
      } else {
        setError(responseData.data || "Failed to submit review");
        setDisabled(false);
      }
    } catch (error) {
      let err = error as Error;
      setError((prevError) => (prevError = err.message));
      setDisabled(false);
    }
  };

  // Show loading state while authentication is being checked
  if (!isLoaded || isCheckingEligibility) {
    return <LoadingSpinner />;
  }

  // Don't render the form if not signed in
  if (!isSignedIn) {
    return null;
  }

  // Don't render the form if not eligible
  if (!reviewEligibility.isEligible) {
    return (
      <div className="pt-8 flex flex-col h-full w-full sm:w-3/4 items-center">
        <ReviewAvailabilityMessage
          message={reviewEligibility.message}
          daysRemaining={reviewEligibility.daysRemaining}
          nextEligibleDate={reviewEligibility.nextEligibleDate ? new Date(reviewEligibility.nextEligibleDate).toLocaleDateString() : undefined}
          returnUrl={`/reviews?id=${id}`}
          returnLabel="Back to Reviews"
        />
      </div>
    );
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;

    setReviewData((prevData): iReview => {
      if (name === "videoUrl") {
        return {
          ...prevData,
          videos:
            prevData.videos.length > 0
              ? [value, ...prevData.videos.slice(1)] // Update if array has items
              : [value], // Create new array if empty
        };
      } else {
        // Handle other fields normally
        return { ...prevData, [name]: value };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    setDisabled(true);
    e.preventDefault();
    //disable the form form
    e.currentTarget.disabled = true;

    e.currentTarget.checkValidity();
    if (!e.currentTarget.checkValidity()) {
      setError("Please fill out all fields");
      return;
    }
    if (
      (reviewData.body === "" || reviewData.body === null) &&
      linksArray.length === 0 &&
      reviewData.videos.length === 0
    ) {
      setError((prevError) => (prevError = "Review body is empty"));
      setDisabled(false);
      return;
    }
    // INFO: enable this to send to server
    await sendToServer();
    setDisabled(false);
  };

  if (isLoading) return <LoadingSpinner />;
  if (!product)
    return (
      <p>
        fetch error - cannot give more details cause product variable was taken
      </p>
    );

  const amITheOwner = product.business?.ownerId === user?.id;
  if (amITheOwner) {
    return <OwnershipMessage />;
  }

  return (
    <div className="max-w-4xl w-full mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden my-8">
      <ReviewFormHeader />
      <form onSubmit={handleSubmit} className="p-6 sm:p-8">
        <div className="space-y-8">
          {product && (
            <ProductCardSection
              product={product}
              productCardOptions={productCardOptions}
              userId={user?.id ? user.id : null}
            />
          )}

          <ReviewRatingSection
            rating={rating}
            ratingChanged={ratingChanged}
          />

          <ReviewTitleSection handleChange={handleChange} />

          <ReviewDateSection
            startDate={startDate}
            setStartDate={setStartDate}
          />

          <ReviewBodySection onEditorValue={handleEditorValue} />

          <ReviewMediaSection
            handleChange={handleChange}
            setLinksArray={setLinksArray}
            setAllUploaded={setAllUploaded}
            allUploaded={allUploaded}
          />

          <div className="mt-6">
            <EditorPreview reviewData={reviewData} />
          </div>

          <ReviewSubmitSection
            disabled={disabled}
            hasUnuploadedImages={hasUnuploadedImages}
            isFormValid={!!reviewData.title && (!!reviewData.body || linksArray.length > 0 || reviewData.videos.length > 0)}
          />

          <DisplayError error={error} />
        </div>
      </form>
    </div>
  );
};

export default ReviewForm;
