import React from "react";
import { iProduct } from "../util/Interfaces";
import ProductCardSlim from "@/app/components/ProductCardSlim";
import Link from "next/link";
import { calculateAverageReviewRatingSync } from "../util/calculateAverageReviewRating";
import { WeightedRatingResult } from '../util/calculateWeightedRating';
import { MINIMUM_REVIEWS } from "@/app/config/rating";

interface SearchResultsProps {
  results: iProduct[];
  tags: iProduct[];
}

const SearchResults: React.FC<SearchResultsProps> = ({ results, tags }) => {
  const productCardOptions = {
    size: "rating-sm",
  };

  const getRatingDisplay = (product: iProduct) => {
    const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object' && 'hasMinimumReviews' in product.weightedRating;

    if (hasWeightedRating && product.weightedRating) {
      return {
        display: product.weightedRating.hasMinimumReviews ? product.weightedRating.roundedRatingOneDecimalPlace : null,
        needed: !product.weightedRating.hasMinimumReviews ? MINIMUM_REVIEWS - product.weightedRating.numberOfReviews : 0,
      };
    }

    if (!product.reviews || product.reviews.length === 0) {
      return { display: null, needed: MINIMUM_REVIEWS };
    }

    const rating = calculateAverageReviewRatingSync(product.reviews, true);
    const hasMinimumReviews = 'hasMinimumReviews' in rating ? rating.hasMinimumReviews : rating.numberOfReviews >= MINIMUM_REVIEWS;
    return {
      display: hasMinimumReviews ? rating.roundedRatingOneDecimalPlace : null,
      needed: hasMinimumReviews ? 0 : MINIMUM_REVIEWS - rating.numberOfReviews
    };
  };

  return (
    <div className="mt-2 w-full max-h-[80vh] bg-white shadow-lg rounded-lg overflow-hidden border border-gray-100">
      <div className="p-2 md:p-6 h-full flex flex-col gap-2 md:gap-4">
        {/* Products section */}
        <section className="flex-1 min-h-0 bg-gray-50/50 rounded-lg p-2 md:p-4">
          <div className="flex items-center justify-between mb-2 md:mb-3">
            <h2 className="text-base md:text-xl font-semibold text-gray-800">Products</h2>
            {results.length > 0 && (
              <span className="text-xs md:text-sm text-gray-500">{results.length} found</span>
            )}
          </div>
          <div className="space-y-2 md:space-y-3 overflow-y-auto max-h-[35vh] pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            {results.length > 0 ? (
              results.map((result) => (
                <Link
                  href={`/reviews?id=${result.id}`}
                  key={result.id}
                  className="block p-2 md:p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 cursor-pointer"
                >
                  <div className="flex items-start gap-2 md:gap-3">
                    {result.display_image && (
                      <img
                        src={result.display_image}
                        alt={result.name}
                        className="w-12 h-12 md:w-16 md:h-16 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm md:text-base font-medium text-gray-900 truncate">{result.name}</h3>
                      {result.description && (
                        <p className="text-xs md:text-sm text-gray-600 line-clamp-2">{result.description}</p>
                      )}
                      {result.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-0.5 md:mt-1">
                          {result.tags.slice(0, 3).map((tag, index) => (
                            <span key={index} className="px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs bg-gray-100 text-gray-600 rounded-full">
                              {tag}
                            </span>
                          ))}
                          {result.tags.length > 3 && (
                            <span className="px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs bg-gray-100 text-gray-600 rounded-full">
                              +{result.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    {(() => {
                      const ratingInfo = getRatingDisplay(result);
                      if (ratingInfo.display) {
                        return (
                          <div className="flex items-center gap-0.5 md:gap-1 px-1.5 md:px-2 py-0.5 md:py-1 bg-green-50 rounded-lg">
                            <span className="text-xs md:text-sm font-medium text-green-700">{ratingInfo.display}</span>
                            <svg className="w-3 h-3 md:w-4 md:h-4 text-green-700" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          </div>
                        );
                      } else if (ratingInfo.needed > 0) {
                        return (
                          <div className="text-xs text-gray-400 text-right">
                            ({ratingInfo.needed} more needed)
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                </Link>
              ))
            ) : (
              <div className="text-center py-6">
                <p className="text-xs md:text-sm text-gray-500 italic mb-4">No products found</p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-gray-700 mb-3">Can&apos;t find what you&apos;re looking for?</p>
                  <a
                    href="/submit"
                    className="inline-flex items-center px-4 py-2 bg-myTheme-primary text-white text-sm font-medium rounded-lg hover:bg-myTheme-primary/90 transition-colors duration-200"
                  >
                    Add Your Product
                  </a>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Divider */}
        <div className="w-full h-px bg-gray-200"></div>

        {/* Categories section */}
        <section className="flex-1 min-h-0 bg-gray-50/50 rounded-lg p-2 md:p-4">
          <div className="flex items-center justify-between mb-2 md:mb-3">
            <h2 className="text-base md:text-xl font-semibold text-gray-800">Categories</h2>
            {tags.length > 0 && (
              <span className="text-xs md:text-sm text-gray-500">{tags.length} found</span>
            )}
          </div>
          <div className="space-y-2 md:space-y-3 overflow-y-auto max-h-[35vh] pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            {tags.length > 0 ? (
              tags.map((result) => (
                <Link
                  href={`/reviews?id=${result.id}`}
                  key={result.id}
                  className="block p-2 md:p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100 cursor-pointer"
                >
                  <div className="flex items-start gap-2 md:gap-3">
                    {result.display_image && (
                      <img
                        src={result.display_image}
                        alt={result.name}
                        className="w-12 h-12 md:w-16 md:h-16 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm md:text-base font-medium text-gray-900 truncate">{result.name}</h3>
                      {result.description && (
                        <p className="text-xs md:text-sm text-gray-600 line-clamp-2">{result.description}</p>
                      )}
                      {result.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-0.5 md:mt-1">
                          {result.tags.slice(0, 3).map((tag, index) => (
                            <span key={index} className="px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs bg-gray-100 text-gray-600 rounded-full">
                              {tag}
                            </span>
                          ))}
                          {result.tags.length > 3 && (
                            <span className="px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs bg-gray-100 text-gray-600 rounded-full">
                              +{result.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    {(() => {
                      const ratingInfo = getRatingDisplay(result);
                      if (ratingInfo.display) {
                        return (
                          <div className="flex items-center gap-0.5 md:gap-1 px-1.5 md:px-2 py-0.5 md:py-1 bg-green-50 rounded-lg">
                            <span className="text-xs md:text-sm font-medium text-green-700">{ratingInfo.display}</span>
                            <svg className="w-3 h-3 md:w-4 md:h-4 text-green-700" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          </div>
                        );
                      } else if (ratingInfo.needed > 0) {
                        return (
                          <div className="text-xs text-gray-400 text-right">
                            ({ratingInfo.needed} more needed)
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                </Link>
              ))
            ) : (
              <p className="text-xs md:text-sm text-gray-500 italic">No categories found</p>
            )}
          </div>
        </section>
      </div>
    </div>
  );
};

export default SearchResults;
