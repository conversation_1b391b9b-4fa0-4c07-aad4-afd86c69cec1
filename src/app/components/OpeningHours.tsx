import React from 'react';
import { MdAccessTime, MdCalendarToday } from 'react-icons/md';
import { iProduct } from '../util/Interfaces';
import { cn } from '@/lib/utils';

interface OpeningHoursProps {
    product: iProduct;
    showIcon?: boolean;
    className?: string;
    showDaysOnly?: boolean;
    showHoursOnly?: boolean;
}

const OpeningHours: React.FC<OpeningHoursProps> = ({
    product,
    showIcon = true,
    className = '',
    showDaysOnly = false,
    showHoursOnly = false,
}) => {
    const { openingDays, openingHrs, closingHrs } = product;

    // If no opening days or hours, return null
    if (!openingDays || openingDays.length === 0) {
        return null;
    }

    // Format the days display
    const formatDays = () => {
        if (openingDays.length === 7) {
            return "Every day";
        }
        return openingDays.join(', ');
    };

    // Format the hours display
    const formatHours = () => {
        if (!openingHrs || !closingHrs) {
            return null;
        }
        return `${openingHrs} - ${closingHrs}`;
    };

    // If only showing days
    if (showDaysOnly) {
        return (
            <div className={cn(
                "flex items-center text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md",
                className
            )}>
                {showIcon && <MdCalendarToday className="shrink-0 mr-2 text-blue-500" />}
                <span className="font-medium">{formatDays()}</span>
            </div>
        );
    }

    // If only showing hours
    if (showHoursOnly) {
        const hours = formatHours();
        if (!hours) return null;

        return (
            <div className={cn(
                "flex items-center text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md",
                className
            )}>
                {showIcon && <MdAccessTime className="shrink-0 mr-2 text-blue-500" />}
                <span className="font-medium">{hours}</span>
            </div>
        );
    }

    // Show both days and hours
    const hours = formatHours();

    return (
        <div className={cn(
            "flex flex-col space-y-2 bg-gray-50 p-3 rounded-md",
            className
        )}>
            <div className="flex items-center">
                {showIcon && <MdCalendarToday className="shrink-0 mr-2 text-blue-500" />}
                <span className="font-medium text-gray-700">{formatDays()}</span>
            </div>
            {hours && (
                <div className="flex items-center ml-6">
                    {showIcon && <MdAccessTime className="shrink-0 mr-2 text-blue-500" />}
                    <span className="text-gray-600">{hours}</span>
                </div>
            )}
        </div>
    );
};

export default OpeningHours; 