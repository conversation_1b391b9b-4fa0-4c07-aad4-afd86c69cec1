"use client";
import React, { useState } from "react";
import { iComment, iUser, iReview, iProduct } from "../util/Interfaces";
import dayjs from "dayjs";
import Link from "next/link";
import {
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CheckCircleIcon,
  TrashIcon,
  ReplyIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth, useUser as useClerkUser } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { toast } from "sonner";
import { Tooltip } from "@mantine/core";
import { canReplyToComment } from "@/app/util/commentPermissions";
import ReplyRestrictionIndicator from "./ReplyRestrictionIndicator";
import SignInToParticipate from "./SignInToParticipate";
import { isOwnerComment } from "../util/commentHelpers";

interface OwnerReplyProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  depth: number;
  clerkUserId: string;
  currentUser: iUser;
  productName?: string;
  review?: iReview;
  product?: iProduct;
  isUserAuthenticated?: boolean;
}

const DEPTH_COLORS = [
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
  "border-blue-100",
  "border-blue-50",
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
];

const OwnerReply: React.FC<OwnerReplyProps> = ({
  comment: initialComment,
  onReply,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser,
  productName,
  review,
  product,
  isUserAuthenticated = false,
}) => {
  const { userId } = useAuth();
  const { user: clerkUser } = useClerkUser();
  
  // Use the same ID format that's used throughout the app (publicMetadata.id)
  const userDatabaseId = clerkUser?.publicMetadata?.id as string | undefined;
  const [comment, setComment] = useState(initialComment);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyBody, setReplyBody] = useState("");
  const [replies, setReplies] = useState<iComment[]>(comment.replies || []);
  const [showReplies, setShowReplies] = useState(true);
  // More robust ownership check - try multiple ID fields
  const isCommentOwner = Boolean(userId && (
    userId === comment.user?.clerkUserId || 
    userId === comment.user?.id || 
    userId === comment.userId ||
    clerkUserId === comment.user?.clerkUserId ||
    clerkUserId === comment.user?.id ||
    clerkUserId === comment.userId
  ));
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];

  // Calculate permissions for replying to this owner comment
  let permission: { allowed: boolean; restrictionType: "not_authenticated" | "not_owner_comment" | "not_reviewer" | "no_restriction"; message: string } = { 
    allowed: false, 
    restrictionType: 'not_authenticated', 
    message: 'Unable to determine permissions' 
  };
  try {
    if (review) {
      const permissionResult = canReplyToComment(
        userDatabaseId || clerkUserId, // Use database ID first, fallback to clerk ID
        comment,
        review,
        product
      );
      permission = {
        allowed: permissionResult.allowed,
        restrictionType: permissionResult.restrictionType || 'not_authenticated',
        message: permissionResult.message || 'Permission check completed'
      };
    }
  } catch (error) {
    console.warn('Error calculating reply permissions for owner comment:', comment.id, error);
    permission = { 
      allowed: false, 
      restrictionType: 'not_authenticated', 
      message: 'Unable to determine reply permissions' 
    };
  }



  const handleReply = async () => {
    if (comment.id && permission.allowed) {
      // Create optimistic reply
      const newReply: iComment = {
        id: Date.now().toString(),
        body: replyBody,
        user: currentUser,
        createdDate: new Date(),
        review: comment.review,
        parentId: comment.id,
        userId: userDatabaseId || clerkUserId,
        isDeleted: false,
        reviewId: comment.reviewId,
        replies: [],
        upvotes: 0,
        downvotes: 0,
      };

      // Optimistically update replies
      const updatedReplies = [...replies, newReply];
      setReplies(updatedReplies);
      setIsReplying(false);
      setReplyBody("");
      setShowReplies(true);

      try {
        await onReply(comment.id, replyBody);
        toast.success("Reply added successfully!");
      } catch (error) {
        // Revert on error
        setReplies(replies);
        setIsReplying(true);
        setReplyBody(replyBody);
        toast.error("Failed to add reply. Please try again.");
      }
    }
  };

  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (comment.id && isCommentOwner) {
      // await onEdit(comment.id, editedBody);
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
    }
  };

  const handleDelete = async () => {
    if (comment.id && isCommentOwner) {
      await onDelete(comment.id);
      const updatedComment = { ...comment, isDeleted: true };
      setComment(updatedComment);
    }
  };

  const toggleReplies = () => {
    setShowReplies(!showReplies);
  };

  const renderReplies = () => {
    if (!replies || replies.length === 0) return null;
    return (
      <div className="reply-thread mt-2 relative">
        {/* Visual connection line */}
        <div className="absolute left-6 top-0 w-0.5 h-full bg-gray-200"></div>
        <div className="ml-6 pl-4 space-y-1">
        {replies.map((reply) => {
          try {
            // Check if this is an owner reply
            const isOwner = Boolean(product && isOwnerComment(reply, product));

            if (isOwner) {
              return (
                <OwnerReply
                  clerkUserId={clerkUserId}
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onDelete={onDelete}
                  depth={depth + 1}
                  currentUser={currentUser}
                  productName={product?.name || 'Unknown Product'}
                  review={review}
                  product={product}
                  isUserAuthenticated={isUserAuthenticated}
                />
              );
            } else {
              // Import Comment component at the top and use it here
              const Comment = require('./Comment').default;
              return (
                <Comment
                  clerkUserId={clerkUserId}
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={() => {}} // Owner replies don't support editing regular comments
                  onDelete={onDelete}
                  depth={depth + 1}
                  currentUser={currentUser}
                  product={product}
                  review={review}
                  isUserAuthenticated={isUserAuthenticated}
                />
              );
            }
          } catch (error) {
            // Handle legacy replies that might cause rendering issues
            console.warn('Error rendering reply:', reply.id, error);
            return (
              <div key={reply.id} className="p-2 bg-gray-50 rounded border border-gray-200 text-sm text-gray-500">
                <span className="italic">This comment could not be displayed properly.</span>
              </div>
            );
          }
        })}
        </div>
      </div>
    );
  };

  return (
    <div
      id={comment.id || `reply-${Date.now()}`}
      className="w-full mt-3 ml-6"
    >
      <div className="flex">
        <div className="flex-1">
          <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center text-sm mb-3">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Avatar className="w-8 h-8">
                    <AvatarImage
                      src={comment.user?.avatar || "/default-avatar.png"}
                      alt={`${comment.user?.firstName} ${comment.user?.lastName}`}
                    />
                    <AvatarFallback className="bg-blue-500 text-white text-xs">
                      {comment.user?.firstName?.charAt(0)}
                      {comment.user?.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-blue-500 rounded-full border border-white flex items-center justify-center">
                    <CheckCircleIcon className="w-2 h-2 text-white" />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Owner
                  </span>
                </div>
              </div>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-500 text-sm">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>
              {isCommentOwner && (
                <div className="ml-auto">
                  <button
                    onClick={handleDelete}
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
            <div className="ml-4 pl-4 border-l-2 border-blue-300 bg-blue-50 p-4 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="font-semibold text-blue-700">
                    {comment.user?.userName || "Business Owner"}
                  </div>
                  <div className="text-xs text-blue-500">
                    {dayjs(comment.createdDate).format("MMM D, YYYY")}
                  </div>
                </div>
                <div className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-md">
                  Business Owner
                </div>
              </div>
              <div className="mt-2 text-gray-700">
                {comment.isDeleted ? "[This comment has been deleted]" : comment.body}
              </div>
              
              {/* Reply functionality */}
              {!comment.isDeleted && (
                <div className="mt-3">
                  {permission.allowed && isUserAuthenticated ? (
                    <div className="flex flex-col gap-2">
                      <Button
                        onClick={() => setIsReplying(!isReplying)}
                        size="sm"
                        variant="outline"
                        className="border border-blue-300 text-blue-600 hover:bg-blue-50 px-3 py-1 rounded-md text-xs font-medium w-fit"
                      >
                        <ReplyIcon className="w-3 h-3 mr-1" />
                        Reply to Owner
                      </Button>
                      
                      {isReplying && (
                        <div className="mt-2">
                          <Textarea
                            value={replyBody}
                            onChange={(e) => setReplyBody(e.target.value)}
                            className="w-full p-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[80px]"
                            placeholder="Reply to the business owner..."
                          />
                          <div className="mt-2 flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setIsReplying(false);
                                setReplyBody("");
                              }}
                              className="text-xs px-3 py-1 h-7"
                            >
                              Cancel
                            </Button>
                            <Button
                              className="bg-blue-500 text-white font-medium text-xs px-3 py-1 h-7 rounded hover:bg-blue-600 transition-colors disabled:opacity-50"
                              onClick={handleReply}
                              disabled={!replyBody.trim()}
                            >
                              Reply
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : !permission.allowed && isUserAuthenticated ? (
                    <ReplyRestrictionIndicator permission={permission} />
                  ) : !isUserAuthenticated ? (
                    <div className="mt-2">
                      <SignInToParticipate action="reply" compact />
                    </div>
                  ) : (
                    <div className="mt-2 text-sm text-gray-500">
                      Only the original reviewer can reply to the owner.
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Show replies toggle and replies */}
          {replies.length > 0 && (
            <div className="mt-2 mb-1">
              <button
                className="text-xs text-gray-500 hover:text-blue-500 flex items-center"
                onClick={toggleReplies}
              >
                {showReplies ? (
                  <ChevronUpIcon className="w-3 h-3 mr-1" />
                ) : (
                  <ChevronDownIcon className="w-3 h-3 mr-1" />
                )}
                {showReplies ? "Hide" : "Show"} {replies.length}{" "}
                {replies.length === 1 ? "reply" : "replies"}
              </button>
              {showReplies && renderReplies()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OwnerReply;
