import {
  MdFastfood,
  MdDeliveryDining,
  MdOutlineElectricalServices,
  MdBusinessCenter,
  MdOtherHouses,
  MdOutlineLocalTaxi,
  MdOutlineHomeRepairService,
} from "react-icons/md";
import { IoMdCar } from "react-icons/io";
import { Gi<PERSON>lothes } from "react-icons/gi";
import Link from "next/link";
import { LuExternalLink } from "react-icons/lu";

const QuickTabs = () => {
  const categories = [
    {
      name: "Fast Food",
      icon: <MdFastfood />,
      link: `/browse?tags=${encodeURIComponent("Fast Food".toLowerCase())}`,
      color: "from-orange-400 to-red-500",
    },
    {
      name: "Car Rental",
      icon: <IoMdCar />,
      link: `/browse?tags=${encodeURIComponent("Car Rental".toLowerCase())}`,
      color: "from-blue-400 to-blue-600",
    },
    {
      name: "Delivery",
      icon: <MdDeliveryDining />,
      link: `/browse?tags=${encodeURIComponent("Delivery Service".toLowerCase())}`,
      color: "from-green-400 to-emerald-600",
    },
    {
      name: "Electronics",
      icon: <MdOutlineElectricalServices />,
      link: `/browse?tags=${encodeURIComponent("Electronics".toLowerCase())}`,
      color: "from-purple-400 to-indigo-600",
    },
    {
      name: "Insurance",
      icon: <MdBusinessCenter />,
      link: `/browse?tags=${encodeURIComponent("Insurance Agency".toLowerCase())}`,
      color: "from-teal-400 to-cyan-600",
    },
    {
      name: "Real Estate",
      icon: <MdOtherHouses />,
      link: `/browse?tags=${encodeURIComponent("Real Estate".toLowerCase())}`,
      color: "from-amber-400 to-orange-600",
    },
    {
      name: "Clothing",
      icon: <GiClothes />,
      link: `/browse?tags=${encodeURIComponent("Clothing Store".toLowerCase())}`,
      color: "from-pink-400 to-rose-600",
    },
    {
      name: "Taxi",
      icon: <MdOutlineLocalTaxi />,
      link: `/browse?tags=${encodeURIComponent("Taxi Service".toLowerCase())}`,
      color: "from-yellow-400 to-amber-600",
    },
    {
      name: "Home Services",
      icon: <MdOutlineHomeRepairService />,
      link: `/browse?tags=${encodeURIComponent("Home Services".toLowerCase())}`,
      color: "from-lime-400 to-green-600",
    },
    {
      name: "See All",
      icon: <LuExternalLink />,
      link: "/browse",
      color: "from-gray-400 to-gray-600",
    },
  ];

  return (
    <div className="flex flex-col justify-center items-center w-full bg-gradient-to-b from-gray-50 to-white py-12 sm:py-16 relative z-[1]">
      <div className="flex flex-col sm:flex-row items-center sm:items-center gap-2 sm:gap-3 mb-10 px-4">
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-800 leading-tight">
          Quick Categories
        </h2>
        <span className="text-lg sm:text-xl text-gray-600 font-medium">Find what you need</span>
      </div>
      
      <div className="max-w-6xl mx-auto w-full px-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
          {categories.map((category, index) => (
            <Link
              key={index}
              href={category.link}
              className="group relative overflow-hidden rounded-2xl bg-white shadow-sm hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
              <div className="relative p-6 flex flex-col items-center justify-center text-center">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                  <span className={`text-3xl text-gray-700 group-hover:text-white transition-all duration-300 relative z-10`}>
                    {category.icon}
                  </span>
                </div>
                <p className="text-sm sm:text-base font-semibold text-gray-800 group-hover:text-myTheme-primary transition-colors duration-300">
                  {category.name}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickTabs;
