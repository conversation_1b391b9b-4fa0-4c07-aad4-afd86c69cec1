"use client";

import Image from "next/image";
import SearchBoxAndListener from "./SearchBoxAndListener";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useEffect, useState } from "react";

const HeroSection = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState({
    reviews: 0,
    products: 0,
    categories: 0
  });

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      // Calculate total reviews
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      // Get unique categories from tags
      const uniqueCategories = new Set<string>();
      allProducts.forEach(product => {
        product.tags?.forEach(tag => uniqueCategories.add(tag));
      });

      setStats({
        reviews: totalReviews,
        products: allProducts.length,
        categories: uniqueCategories.size
      });
    }
  }, [allProducts]);

  return (
    <section className="relative h-[550px] sm:h-[500px] md:h-[600px] lg:h-[500px] w-full overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <Image
          src="/hero1.avif" // Pointing to the highly optimized AVIF image
          alt="ReviewIt Guyana - Your trusted review platform"
          fill
          className="object-cover object-center"
          quality={85}
          priority
          sizes="100vw"
        />
        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
      </div>
      
      {/* Static background elements - removed animations and reduced blur for better GPU performance */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-myTheme-primary/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-myTheme-secondary/10 rounded-full blur-xl"></div>
      </div>

      {/* Content Container */}
      <div className="relative flex justify-center items-center h-full w-full text-center text-white">
        <div className="flex flex-1 justify-center flex-col relative max-w-6xl mx-auto px-4">
          <div className="flex flex-col h-full w-full py-4 sm:py-8">
            {/* Main Content - removed animation for better performance */}
            <div className="flex-1 md:mt-4">
              <h1 className="text-4xl sm:text-5xl md:text-4xl lg:text-6xl font-black mb-4 md:mb-6 text-white leading-tight">
                <span className="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                  ReviewIt Guyana
                </span>
                <br />
                <span className="text-3xl sm:text-4xl md:text-3xl lg:text-5xl font-bold">
                  Find & Share Reviews
                </span>
              </h1>
              
              <p className="text-xl sm:text-2xl md:text-xl lg:text-3xl mt-4 font-semibold pb-2 text-gray-100">
                Your trusted platform for honest reviews on businesses and services
              </p>
              
              <p className="text-lg sm:text-xl md:text-lg lg:text-2xl mt-2 font-medium pb-6 md:pb-8 text-gray-200">
                Join thousands of Guyanese sharing their experiences
              </p>
            </div>

            {/* Search Section */}
            <div className="flex flex-col justify-end space-y-6 md:space-y-8 relative z-[200]">
              <form className="flex w-full max-w-2xl mx-auto">
                <div className="relative w-full">
                  <SearchBoxAndListener />
                </div>
              </form>

              {/* Stats with enhanced styling */}
              <div className="flex flex-wrap justify-center gap-4 sm:gap-6 text-sm md:text-base">
                {!allProducts ? (
                  // Loading skeleton
                  <>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                      <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                      <span className="text-gray-200 animate-pulse bg-white/20 rounded w-16 h-4"></span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                      <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                      <span className="text-gray-200 animate-pulse bg-white/20 rounded w-16 h-4"></span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                      <span className="font-bold text-white animate-pulse bg-white/20 rounded w-12 h-5"></span>
                      <span className="text-gray-200 animate-pulse bg-white/20 rounded w-16 h-4"></span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 hover:bg-white/20 transition-all duration-200">
                      <span className="font-bold text-white text-lg">{stats.reviews}+</span>
                      <span className="text-gray-200">Reviews</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 hover:bg-white/20 transition-all duration-200">
                      <span className="font-bold text-white text-lg">{stats.products}+</span>
                      <span className="text-gray-200">Products</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 hover:bg-white/20 transition-all duration-200">
                      <span className="font-bold text-white text-lg">{stats.categories}+</span>
                      <span className="text-gray-200">Categories</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
