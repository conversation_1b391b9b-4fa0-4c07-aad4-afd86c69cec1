import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X } from 'lucide-react';
import { iBugReport } from '@/app/util/Interfaces';

const STATUS_COLORS = {
    OPEN: "bg-yellow-100 text-yellow-800",
    IN_PROGRESS: "bg-blue-100 text-blue-800",
    RESOLVED: "bg-green-100 text-green-800",
    CLOSED: "bg-gray-100 text-gray-800",
    WONT_FIX: "bg-red-100 text-red-800",
} as const;

interface BugReportDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    bugReport: iBugReport | null;
    isLoading?: boolean;
}

export default function BugReportDetailsModal({ isOpen, onClose, bugReport, isLoading = false }: BugReportDetailsModalProps) {
    if (!bugReport && !isLoading) return null;

    return (
        <Transition.Root show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                </Transition.Child>

                <div className="fixed inset-0 z-10 overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        >
                            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                                    <button
                                        type="button"
                                        className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        onClick={onClose}
                                    >
                                        <span className="sr-only">Close</span>
                                        <X className="h-6 w-6" aria-hidden="true" />
                                    </button>
                                </div>
                                <div className="sm:flex sm:items-start">
                                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                                        {isLoading ? (
                                            <div className="flex justify-center items-center h-64">
                                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                            </div>
                                        ) : (
                                            <>
                                                <Dialog.Title as="h3" className="text-2xl font-semibold leading-6 text-gray-900">
                                                    {bugReport?.title}
                                                </Dialog.Title>
                                                <div className="mt-4 space-y-6">
                                                    {/* Status */}
                                                    <div>
                                                        <h4 className="text-sm font-medium text-gray-500">Status</h4>
                                                        <div className="mt-1">
                                                            <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${STATUS_COLORS[bugReport?.status as keyof typeof STATUS_COLORS]}`}>
                                                                {bugReport?.status}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {/* Description */}
                                                    <div>
                                                        <h4 className="text-sm font-medium text-gray-500">Description</h4>
                                                        <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{bugReport?.description}</p>
                                                    </div>

                                                    {/* Environment Info */}
                                                    <div>
                                                        <h4 className="text-sm font-medium text-gray-500">Environment</h4>
                                                        <div className="mt-1 space-y-2">
                                                            {bugReport?.browser && (
                                                                <div>
                                                                    <span className="text-sm font-medium text-gray-700">Browser:</span>
                                                                    <p className="mt-1 text-sm text-gray-900">{bugReport.browser}</p>
                                                                </div>
                                                            )}
                                                            {bugReport?.device && (
                                                                <div>
                                                                    <span className="text-sm font-medium text-gray-700">Device:</span>
                                                                    <p className="mt-1 text-sm text-gray-900">{bugReport.device}</p>
                                                                </div>
                                                            )}
                                                            {bugReport?.mobile_os && (
                                                                <div>
                                                                    <span className="text-sm font-medium text-gray-700">Mobile OS:</span>
                                                                    <p className="mt-1 text-sm text-gray-900">{bugReport.mobile_os}</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Resolution Notes */}
                                                    {bugReport?.resolution_notes && (
                                                        <div>
                                                            <h4 className="text-sm font-medium text-gray-500">Resolution Notes</h4>
                                                            <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{bugReport.resolution_notes}</p>
                                                        </div>
                                                    )}

                                                    {/* Metadata */}
                                                    <div className="border-t border-gray-200 pt-4">
                                                        <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                                                            <div>
                                                                <dt className="text-sm font-medium text-gray-500">Reported by</dt>
                                                                <dd className="mt-1 text-sm text-gray-900">
                                                                    {bugReport?.reporter?.userName || 'Unknown'}
                                                                </dd>
                                                            </div>
                                                            <div>
                                                                <dt className="text-sm font-medium text-gray-500">Reported on</dt>
                                                                <dd className="mt-1 text-sm text-gray-900">
                                                                    {bugReport?.created_at ? new Date(bugReport.created_at).toLocaleString() : 'Unknown'}
                                                                </dd>
                                                            </div>
                                                            {bugReport?.resolver && (
                                                                <div>
                                                                    <dt className="text-sm font-medium text-gray-500">Resolved by</dt>
                                                                    <dd className="mt-1 text-sm text-gray-900">
                                                                        {bugReport.resolver.userName}
                                                                    </dd>
                                                                </div>
                                                            )}
                                                            {bugReport?.resolved_at && (
                                                                <div>
                                                                    <dt className="text-sm font-medium text-gray-500">Resolved on</dt>
                                                                    <dd className="mt-1 text-sm text-gray-900">
                                                                        {new Date(bugReport.resolved_at).toLocaleString()}
                                                                    </dd>
                                                                </div>
                                                            )}
                                                        </dl>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition.Root>
    );
} 