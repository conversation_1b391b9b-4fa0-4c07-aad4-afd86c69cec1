"use client";

import { useState } from "react";
import { toast } from "sonner";

export default function BugReportForm() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [formData, setFormData] = useState({
        title: "",
        description: "",
        browser: "",
        device: "",
        mobile_os: "",
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const response = await fetch("/api/bugreports", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || "Failed to submit bug report");
            }

            toast.success("Bug report submitted successfully");
            setFormData({
                title: "",
                description: "",
                browser: "",
                device: "",
                mobile_os: "",
            });
        } catch (error) {
            toast.error(error instanceof Error ? error.message : "Failed to submit bug report");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700">
                <h2 className="text-xl font-semibold text-white">Submit a Bug Report</h2>
                <p className="mt-1 text-sm text-indigo-100">
                    Help us improve by reporting any issues you encounter. Please provide as much detail as possible.
                </p>
            </div>
            <form onSubmit={handleSubmit} className="space-y-6 p-6">
                <div className="space-y-4">
                    <div>
                        <label
                            htmlFor="title"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Title
                        </label>
                        <div className="mt-1">
                            <input
                                type="text"
                                id="title"
                                name="title"
                                value={formData.title}
                                onChange={handleChange}
                                required
                                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                placeholder="Brief description of the issue (e.g., &apos;Login button not working&apos;)"
                            />
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            A clear, concise title helps us understand the issue quickly.
                        </p>
                    </div>

                    <div>
                        <label
                            htmlFor="description"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Description
                        </label>
                        <div className="mt-1">
                            <textarea
                                id="description"
                                name="description"
                                value={formData.description}
                                onChange={handleChange}
                                required
                                rows={6}
                                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                placeholder="Please include:
1. Steps to reproduce the issue
2. What you expected to happen
3. What actually happened
4. Any error messages you saw"
                            />
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            The more details you provide, the easier it will be for us to fix the issue.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label
                                htmlFor="browser"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Browser
                            </label>
                            <div className="mt-1">
                                <input
                                    type="text"
                                    id="browser"
                                    name="browser"
                                    value={formData.browser}
                                    onChange={handleChange}
                                    className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                    placeholder="e.g., Chrome 91.0, Firefox 89.0"
                                />
                            </div>
                        </div>

                        <div>
                            <label
                                htmlFor="device"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Device
                            </label>
                            <div className="mt-1">
                                <input
                                    type="text"
                                    id="device"
                                    name="device"
                                    value={formData.device}
                                    onChange={handleChange}
                                    className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                    placeholder="e.g., iPhone 12, Windows PC, MacBook Pro"
                                />
                            </div>
                        </div>
                    </div>

                    <div>
                        <label
                            htmlFor="mobile_os"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Mobile OS (if applicable)
                        </label>
                        <div className="mt-1">
                            <input
                                type="text"
                                id="mobile_os"
                                name="mobile_os"
                                value={formData.mobile_os}
                                onChange={handleChange}
                                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                placeholder="e.g., iOS 14.5, Android 11"
                            />
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            Only fill this if you&apos;re using a mobile device.
                        </p>
                    </div>
                </div>

                <div className="flex justify-end space-x-4">
                    <button
                        type="button"
                        onClick={() => {
                            setFormData({
                                title: "",
                                description: "",
                                browser: "",
                                device: "",
                                mobile_os: "",
                            });
                        }}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    >
                        Clear Form
                    </button>
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                        {isSubmitting ? (
                            <>
                                <svg className="w-4 h-4 mr-2 animate-spin" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                </svg>
                                Submitting...
                            </>
                        ) : (
                            'Submit Bug Report'
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
} 
