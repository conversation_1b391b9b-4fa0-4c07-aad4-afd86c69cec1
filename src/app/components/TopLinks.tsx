import { FC, useState, useEffect } from "react";
import Link from "next/link";
import { topLinks } from "@/app/util/links";
import { usePathname } from "next/navigation";
import { useUser } from "@/app/hooks/useUser";
import { iUser } from "@/app/util/Interfaces";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FiChevronDown,
  FiPackage,
  FiPlusSquare,
  FiFileText,
  FiBarChart,
  FiTrendingUp,
} from "react-icons/fi";
import { IoPricetagOutline } from "react-icons/io5";
import { NavSkeleton } from "./Skeleton";

interface LinksProps {
  showHome?: boolean;
}

const Links: FC<LinksProps> = ({ showHome }) => {
  const pathname = usePathname();
  const [isAdmin, setIsAdmin] = useState(false);
  const {
    user,
    isLoading: userIsLoading,
    isLoggedIn,
    isNotLoggedIn,
  } = useUser();
  const links = showHome
    ? [{ name: "Home", link: "/" }, ...topLinks]
    : topLinks;

  useEffect(() => {
    const checkAdminPermission = async () => {
      try {
        const response = await fetch("/api/admin/check-permission");
        const data = await response.json();
        setIsAdmin(data.isAdmin);
      } catch (error) {
        console.error("Error checking admin permission:", error);
        setIsAdmin(false);
      }
    };

    checkAdminPermission();
  }, []);

  return (
    <nav className="flex flex-row items-center justify-center">
      {links.map((link, index) => {
        // Skip admin link if user is not admin
        if (link.adminOnly && !isAdmin) return null;
        
        // Skip "My Profile" link if user is not logged in
        if (link.name === "My Profile" && isNotLoggedIn) return null;

        const isActive = pathname === link.link;

        if (link.name === "My Businesses") {
          // Show skeleton only while actually loading user data
          if (userIsLoading) {
            return (
              <div
                key={index}
                className="inline-flex items-center px-3 md:px-4 py-2.5 text-sm font-medium rounded-lg"
              >
                <span className="animate-pulse text-myTheme-primary font-medium">
                  Checking Biz...
                </span>
              </div>
            );
          }

          // Hide completely if user is not logged in
          if (isNotLoggedIn) {
            return null;
          }

          return (
            <DropdownMenu key={index}>
              <DropdownMenuTrigger
                className={`
                inline-flex items-center px-3 md:px-4 py-2.5 text-sm font-medium rounded-lg whitespace-nowrap group
                ${
                  isActive
                    ? "text-myTheme-primary bg-gray-100 font-semibold"
                    : "text-gray-700 hover:text-myTheme-primary hover:bg-gray-100"
                }
              `}
              >
                {link.name}
                <FiChevronDown className="ml-1.5 w-4 h-4 text-gray-400 group-data-[state=open]:rotate-180" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="min-w-[220px] bg-white rounded-xl shadow-md border border-gray-200 mt-2">
                <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                  <Link
                    href="/business"
                    className="w-full flex items-center py-2 px-3"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiTrendingUp className="mr-3 w-4 h-4 text-myTheme-primary" />
                    <span className="text-gray-700 hover:text-myTheme-primary transition-colors">Business Solutions</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                  <Link
                    href={link.link}
                    className="w-full flex items-center py-2 px-3"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <IoPricetagOutline className="mr-3 w-4 h-4 text-myTheme-primary" />
                    <span className="text-gray-700 hover:text-myTheme-primary transition-colors">My Businesses</span>
                  </Link>
                </DropdownMenuItem>
                {user?.businesses && user.businesses.length > 0 && (
                  <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                    <Link
                      href="/owner-admin"
                      className="w-full flex items-center py-2 px-3"
                      onClick={() =>
                        document.dispatchEvent(
                          new KeyboardEvent("keydown", { key: "Escape" }),
                        )
                      }
                    >
                      <FiBarChart className="mr-3 w-4 h-4 text-myTheme-primary" />
                      <span className="text-gray-700 hover:text-myTheme-primary transition-colors">Advanced Dashboard</span>
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                  <Link
                    href="/claim-product"
                    className="w-full flex items-center py-2 px-3"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiPackage className="mr-3 w-4 h-4 text-myTheme-primary" />
                    <span className="text-gray-700 hover:text-myTheme-primary transition-colors">Claim Product</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                  <Link
                    href="/claims"
                    className="w-full flex items-center py-2 px-3"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiFileText className="mr-3 w-4 h-4 text-myTheme-primary" />
                    <span className="text-gray-700 hover:text-myTheme-primary transition-colors">My Claims</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-gray-100 rounded-lg mx-1 my-0.5">
                  <Link
                    href="/submit"
                    className="w-full flex items-center py-2 px-3"
                    onClick={() =>
                      document.dispatchEvent(
                        new KeyboardEvent("keydown", { key: "Escape" }),
                      )
                    }
                  >
                    <FiPlusSquare className="mr-3 w-4 h-4 text-myTheme-primary" />
                    <span className="text-gray-700 hover:text-myTheme-primary transition-colors">Add Product/Business</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        }

        return (
          <Link
            key={index}
            href={link.link}
            className={`
              inline-flex items-center px-3 md:px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out whitespace-nowrap
              ${
                isActive
                  ? "text-myTheme-primary bg-gradient-to-r from-myTheme-primary/10 to-myTheme-accent/10 font-semibold shadow-sm"
                  : "text-gray-700 hover:text-myTheme-primary hover:bg-white/80 hover:shadow-md hover:scale-105"
              }
            `}
          >
            {link.name}
          </Link>
        );
      })}
    </nav>
  );
};

export default Links;
