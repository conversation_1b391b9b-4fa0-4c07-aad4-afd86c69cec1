'use client';

import { useUser } from '@/app/hooks/useUser';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { useEffect } from 'react';

/**
 * UserStatusGuard component that enforces user status restrictions
 * - Banned users can only access /banned page
 * - Suspended users can only access /suspended page  
 * - Not signed in users can access public pages
 * - Active users can access everything
 */
export default function UserStatusGuard({ children }: { children: React.ReactNode }) {
  const { user, isLoading, isNotLoggedIn } = useUser();
  const { signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't do anything while loading
    if (isLoading) return;

    // If user is not signed in, allow access to public pages
    if (!user) return;

    // If user is banned
    if (user.status === 'BANNED') {
      // Only allow access to /banned page
      if (pathname !== '/banned') {
        router.push('/banned');
        return;
      }
    } else if (pathname === '/banned') {
      // Non-banned users should not access /banned page - log them out
      signOut();
      return;
    }

    // If user is suspended
    if (user.status === 'SUSPENDED') {
      // Only allow access to /suspended page
      if (pathname !== '/suspended') {
        const params = new URLSearchParams();
        
        if (user.suspendedUntil) {
          try {
            const suspendedDate = new Date(user.suspendedUntil);
            if (!isNaN(suspendedDate.getTime())) {
              params.set('until', suspendedDate.toISOString());
            }
          } catch (error) {
            console.error('Error parsing suspension date:', error);
          }
        }

        if (user.suspendedReason) {
          params.set('reason', encodeURIComponent(user.suspendedReason));
        }

        const redirectUrl = `/suspended${params.toString() ? `?${params.toString()}` : ''}`;
        router.push(redirectUrl);
        return;
      }
    } else if (pathname === '/suspended') {
      // Non-suspended users should not access /suspended page - log them out
      signOut();
      return;
    }

    // If user is active, allow access to everything
    // (No restrictions for ACTIVE users)
  }, [user, isLoading, pathname, router, signOut]);

  // Show loading state only when we're still checking user status
  // Don't block non-authenticated users from seeing public content
  if (isLoading && !isNotLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}