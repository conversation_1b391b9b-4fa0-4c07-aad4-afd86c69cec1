import React from 'react';

interface Review {
  author: string;
  reviewRating: number;
  reviewBody: string;
  datePublished: string;
}

interface SchemaProps {
  productName: string;
  description: string;
  rating: number;
  reviewCount: number;
  reviews: Review[];
  price?: string;
  availability?: string;
  brand?: string;
  sku?: string;
  image?: string;
  category?: string;
}

const Schema = ({
  productName,
  description,
  rating,
  reviewCount,
  reviews,
  price,
  availability = "https://schema.org/InStock",
  brand,
  sku,
  image,
  category
}: SchemaProps) => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://reviewit.gy';

  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productName,
    "description": description,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": Math.max(1, Math.min(5, rating || 0)),
      "reviewCount": Math.max(0, reviewCount || 0),
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": reviews.map(review => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author || "Anonymous" // Ensure author has a fallback
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.reviewRating,
        "bestRating": "5",
        "worstRating": "1"
      },
      "reviewBody": review.reviewBody,
      // Ensure ISO 8601 format for dates
      "datePublished": review.datePublished ? new Date(review.datePublished).toISOString() : new Date().toISOString()
    })),
    ...(brand && {
      "brand": {
        "@type": "Brand",
        "name": brand
      }
    }),
    ...(sku && { "sku": sku }),
    ...(price && {
      "offers": {
        "@type": "Offer",
        "price": price,
        "priceCurrency": "GYD",
        "availability": availability,
        "url": `${baseUrl}/reviews?id=${sku}`
      }
    }),
    ...(image && { "image": image }),
    ...(category && { "category": category }) // Keep category for Product schema
  };

  // Removed the organizationSchema (LocalBusiness) block

  return (
    <script // Return only the Product schema script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(productSchema)
      }}
    />
  );
};

export default Schema;
