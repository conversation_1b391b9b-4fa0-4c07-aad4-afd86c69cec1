"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { FaArrowUpRightFromSquare } from "react-icons/fa6";
import { MdCategory } from "react-icons/md";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { iProduct } from "@/app/util/Interfaces";

interface Category {
  count: number;
  name: string;
}

const CompanyCategories: React.FC = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      const tagCounts: Record<string, number> = {};
      allProducts.forEach((product: iProduct) => {
        product.tags?.forEach((tag) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      });

      const sortedTags = Object.entries(tagCounts)
        .sort(([, countA], [, countB]) => countB - countA)
        .slice(0, 8)
        .map(([name, count]) => ({ name, count }));

      setCategories(sortedTags);
      setIsLoading(false);
    }
  }, [allProducts]);

  // Loading skeleton
  if (isLoading || categories.length === 0) {
    return (
      <div className="w-full bg-gradient-to-b from-white to-gray-50 py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded-lg w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded-lg w-48 mx-auto"></div>
            </div>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-xl h-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <section className="w-full py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-3">
            <MdCategory className="text-3xl text-myTheme-primary" />
            <h2 className="text-3xl font-bold text-myTheme-lightTextHeading">Company Categories</h2>
          </div>
          <p className="text-myTheme-lightTextBody text-lg max-w-2xl mx-auto">
            Discover businesses by category and find exactly what you&apos;re looking for
          </p>
        </div>
        
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {categories.map((category, index) => (
            <Link
              href={`/browse?tags=${encodeURIComponent(category.name)}`}
              key={index}
              className="group bg-white rounded-xl p-6 shadow-sm hover:shadow-md border border-gray-100 transition-all duration-300 ease-in-out hover:-translate-y-1"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="rounded-full bg-myTheme-primary/10 p-3 group-hover:bg-myTheme-primary/20 transition-colors">
                  <div className="rounded-full bg-myTheme-primary p-2">
                    <MdCategory className="h-4 w-4 text-white" />
                  </div>
                </div>
                <FaArrowUpRightFromSquare
                  className="text-gray-400 group-hover:text-myTheme-primary transition-colors duration-200"
                  size={16}
                />
              </div>
              
              <h3 className="text-lg font-semibold mb-2 text-myTheme-lightTextHeading group-hover:text-myTheme-primary transition-colors">
                {category.name}
              </h3>
              
              <div className="flex items-center gap-2">
                <span className="px-2.5 py-0.5 bg-myTheme-primary/10 text-myTheme-primary rounded-full text-sm font-medium">
                  {category.count.toLocaleString()}
                </span>
                <span className="text-sm text-myTheme-lightTextBody">businesses</span>
              </div>
            </Link>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Link
            href="/browse"
            className="inline-flex items-center gap-2 px-6 py-3 bg-myTheme-primary text-white rounded-lg hover:bg-myTheme-secondary transition-colors duration-200 font-medium"
          >
            View All Categories
            <FaArrowUpRightFromSquare size={14} />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CompanyCategories;
