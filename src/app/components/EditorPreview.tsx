"use client";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, Suspense, useState } from "react";
import parse from "html-react-parser";
import { ReadOnlyRating } from "./RatingSystem";
import { iReview } from "../util/Interfaces";
import { useUser } from "@/app/hooks/useUser";
import { useUser as useClerkUser } from "@clerk/nextjs";

interface editorPreviewProps {
  reviewData: iReview;
}

const EditorPreview = ({ reviewData }: editorPreviewProps) => {
  const { user, isAllowed, securityReason } = useUser();
  const { user: clerkUser } = useClerkUser();

  const handleParse = (node: any): any => {
    //in the html i get two p tags when should be <br> so i manually replace the empty p tags
    if (
      node.type === "tag" &&
      node.name === "p" &&
      (!node.children || node.children.length === 0)
    ) {
      // If empty <p> tag, replace it with a newline
      return <br />;
    }
    return undefined; // Return undefined to keep the default behavior
  };

  const options = {
    replace: handleParse,
  };

  let [isOpen, setIsOpen] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  return (
    <div className="flex flex-col w-full">
      <button
        type="button"
        onClick={openModal}
        className="bg-myTheme-primary hover:bg-myTheme-secondary text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
        </svg>
        Preview Review
      </button>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog onClose={closeModal} className="relative z-50">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="div" className="border-b border-gray-100 pb-4 mb-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-myTheme-primary">Review Preview</h3>
                      <button
                        onClick={closeModal}
                        className="text-gray-400 hover:text-gray-500 rounded-full p-1 hover:bg-gray-100 transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </Dialog.Title>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 shadow-inner">
                    <h1 className="text-2xl font-bold text-myTheme-primary text-center mb-2">
                      {parse(reviewData.title)}
                    </h1>

                    {reviewData.title && (
                      <div className="text-sm text-gray-600 text-center mb-4 flex items-center justify-center gap-2">
                        <span>by</span>
                        <span className="font-medium text-myTheme-primary">{user?.userName}</span>
                      </div>
                    )}

                    {reviewData.title && (
                      <div className="flex justify-center mb-6">
                        <Suspense fallback={<div className="text-sm text-gray-500">Loading rating...</div>}>
                          <ReadOnlyRating
                            name="rating"
                            rating={reviewData.rating}
                            size="lg"
                          />
                        </Suspense>
                      </div>
                    )}

                    {reviewData.body && (
                      <div className="prose prose-green max-w-none mb-6 text-gray-700">
                        {parse(reviewData.body, options)}
                      </div>
                    )}

                    {(reviewData.images.length > 0 || reviewData.videos.length > 0) && (
                      <div className="space-y-6">
                        {reviewData.images.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-3">Photos</h4>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                              {reviewData.images.map((image, index) => (
                                <div key={index} className="relative aspect-square rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                                  <img
                                    src={image}
                                    alt={`Review image ${index + 1}`}
                                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {reviewData.videos.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-3">Videos</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {reviewData.videos.map((video, index) => (
                                <div key={index} className="aspect-video rounded-lg overflow-hidden shadow-md bg-gray-50">
                                  {video}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="mt-4">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-transparent px-2 py-1 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 "
                      onClick={closeModal}
                    >
                      Close
                    </button>
                    <div className="z-50"></div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default EditorPreview;
