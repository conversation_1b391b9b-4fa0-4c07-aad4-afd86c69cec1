"use client";

import React from "react";
import Link from "next/link";
import { Building2, ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const BusinessCTA: React.FC = () => {
  return (
    <section className="py-20 bg-gradient-to-r from-myTheme-primary to-myTheme-secondary px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          Ready to Transform Your Business?
        </h2>
        <p className="text-xl text-white/90 mb-8">
          Join the growing community of successful businesses on ReviewIt.
          Start building trust and growing your customer base today.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/claim-product">
            <Button
              size="lg"
              className="bg-white text-myTheme-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            >
              <Building2 className="w-5 h-5 mr-2" />
              Claim Your Business
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </Link>

          <Link href="/about">
            <Button
              variant="outline"
              size="lg"
              className="border-white border-2 text-white hover:bg-white/20 font-semibold px-8 py-4 text-lg rounded-full shadow-md"
            >
              Learn More
            </Button>
          </Link>
        </div>

        <div className="mt-8 text-white/80 text-sm">
          🚀 Get started in less than 5 minutes • No setup fees • Cancel
          anytime
        </div>
      </div>
    </section>
  );
};

export default BusinessCTA;
