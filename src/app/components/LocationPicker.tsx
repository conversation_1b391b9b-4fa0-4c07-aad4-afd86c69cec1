"use client";
import { useState, useCallback } from "react";
import { GoogleMap, LoadScript, Marker } from "@react-google-maps/api";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";

interface LocationPickerProps {
  onLocationSelect: (location: {
    lat: number;
    lng: number;
    address: string;
  }) => void;
  initialLocation?: { lat: number; lng: number };
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  onLocationSelect,
  initialLocation,
}) => {
  const [marker, setMarker] = useState(initialLocation || { lat: 0, lng: 0 });
  const [address, setAddress] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleMapClick = useCallback(
    (e: google.maps.MapMouseEvent) => {
      if (e.latLng) {
        const newLocation = {
          lat: e.latLng.lat(),
          lng: e.latLng.lng(),
        };
        setMarker(newLocation);

        // Reverse geocoding to get address
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ location: newLocation }, (results, status) => {
          if (status === "OK" && results && results[0]) {
            // Extract address components
            const addressComponents = results[0].address_components;
            let streetNumber = "";
            let route = "";
            let locality = "";
            let administrativeArea = "";
            let country = "";

            if (addressComponents) {
              for (const component of addressComponents) {
                const types = component.types;
                if (types.includes("street_number")) {
                  streetNumber = component.long_name;
                } else if (types.includes("route")) {
                  route = component.long_name;
                } else if (types.includes("locality")) {
                  locality = component.long_name;
                } else if (types.includes("administrative_area_level_1")) {
                  administrativeArea = component.long_name;
                } else if (types.includes("country")) {
                  country = component.long_name;
                }
              }
            }

            // Construct a more detailed address
            let formattedAddress = "";
            if (streetNumber && route) {
              formattedAddress = `${streetNumber} ${route}`;
            } else if (route) {
              formattedAddress = route;
            }

            if (locality) {
              formattedAddress += formattedAddress ? `, ${locality}` : locality;
            }
            if (administrativeArea) {
              formattedAddress += formattedAddress
                ? `, ${administrativeArea}`
                : administrativeArea;
            }
            if (country) {
              formattedAddress += formattedAddress ? `, ${country}` : country;
            }

            // Fallback to formatted_address if no detailed address could be constructed
            if (!formattedAddress) {
              formattedAddress = results[0].formatted_address;
            }

            setAddress(formattedAddress);
            onLocationSelect({ ...newLocation, address: formattedAddress });
          } else {
            setError("Could not find address for this location");
          }
        });
      }
    },
    [onLocationSelect],
  );

  const handleUseCurrentLocation = () => {
    setIsLoading(true);
    setError(null);

    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser");
      setIsLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setMarker(newLocation);

        // Reverse geocoding to get address
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ location: newLocation }, (results, status) => {
          setIsLoading(false);
          if (status === "OK" && results && results[0]) {
            // Extract address components
            const addressComponents = results[0].address_components;
            let streetNumber = "";
            let route = "";
            let locality = "";
            let administrativeArea = "";
            let country = "";

            if (addressComponents) {
              for (const component of addressComponents) {
                const types = component.types;
                if (types.includes("street_number")) {
                  streetNumber = component.long_name;
                } else if (types.includes("route")) {
                  route = component.long_name;
                } else if (types.includes("locality")) {
                  locality = component.long_name;
                } else if (types.includes("administrative_area_level_1")) {
                  administrativeArea = component.long_name;
                } else if (types.includes("country")) {
                  country = component.long_name;
                }
              }
            }

            // Construct a more detailed address
            let formattedAddress = "";
            if (streetNumber && route) {
              formattedAddress = `${streetNumber} ${route}`;
            } else if (route) {
              formattedAddress = route;
            }

            if (locality) {
              formattedAddress += formattedAddress ? `, ${locality}` : locality;
            }
            if (administrativeArea) {
              formattedAddress += formattedAddress
                ? `, ${administrativeArea}`
                : administrativeArea;
            }
            if (country) {
              formattedAddress += formattedAddress ? `, ${country}` : country;
            }

            // Fallback to formatted_address if no detailed address could be constructed
            if (!formattedAddress) {
              formattedAddress = results[0].formatted_address;
            }

            setAddress(formattedAddress);
            onLocationSelect({ ...newLocation, address: formattedAddress });
          } else {
            setError("Could not find address for your current location");
          }
        });
      },
      (error) => {
        setIsLoading(false);
        switch (error.code) {
          case error.PERMISSION_DENIED:
            setError("Please allow location access to use this feature");
            break;
          case error.POSITION_UNAVAILABLE:
            setError("Location information is unavailable");
            break;
          case error.TIMEOUT:
            setError("The request to get your location timed out");
            break;
          default:
            setError("An unknown error occurred");
            break;
        }
      },
    );
  };

  return (
    <div className="w-full">
      <div className="mb-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleUseCurrentLocation}
          disabled={isLoading}
          className="flex items-center gap-1"
        >
          <MapPin size={16} />
          {isLoading ? "Getting location..." : "Use My Current Location"}
        </Button>
      </div>

      <div className="h-[400px] rounded-lg overflow-hidden border border-gray-200">
        <LoadScript
          googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ""}
        >
          <GoogleMap
            mapContainerStyle={{ width: "100%", height: "100%" }}
            center={marker}
            zoom={10}
            onClick={handleMapClick}
          >
            <Marker position={marker} />
          </GoogleMap>
        </LoadScript>
      </div>

      {error && (
        <div className="mt-2 p-2 bg-red-50 text-red-600 rounded text-sm">
          {error}
        </div>
      )}

      {address && (
        <div className="mt-2 p-2 bg-white border border-gray-200 rounded">
          <p className="text-sm text-gray-600">{address}</p>
        </div>
      )}
    </div>
  );
};

export default LocationPicker;
