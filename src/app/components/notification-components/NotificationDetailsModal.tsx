"use client";
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Eye,
  MessageSquare,
  User,
  Calendar,
  Package,
  Star,
  CheckCircle,
  X,
  ExternalLink,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { shouldHavePremiumStyling } from "@/app/util/notificationHelpers";
import { iProductOwnerNotification, iUserNotification, LikeNotification } from "@/app/util/Interfaces";
import { Tooltip } from "@mantine/core";

interface NotificationDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  notification: iProductOwnerNotification | iUserNotification | LikeNotification | null;
  notificationType: "owner" | "user" | "like";
  onViewItem: () => void;
  onMarkAsRead?: () => void;
}

const NotificationDetailsModal: React.FC<NotificationDetailsModalProps> = ({
  isOpen,
  onClose,
  notification,
  notificationType,
  onViewItem,
  onMarkAsRead,
}) => {
  if (!notification || notificationType === 'like') return null;

  const safeNotification = notification as iProductOwnerNotification | iUserNotification;
  const isPremium = shouldHavePremiumStyling(safeNotification);
  const isRead = safeNotification.read === true;
  const isOwnerNotification = notificationType === "owner";

  // Type-safe access to notification properties
  const getNotificationTitle = () => {
    if (isOwnerNotification) {
      const ownerNotif = notification as iProductOwnerNotification;
      return ownerNotif.review_title || "Review Notification";
    } else {
      const userNotif = notification as iUserNotification;
      return userNotif.content || "User Notification";
    }
  };

  const getNotificationContent = () => {
    if (isOwnerNotification) {
      const ownerNotif = notification as iProductOwnerNotification;
      return {
        type: ownerNotif.notification_type,
        productName: ownerNotif.product_name,
        reviewTitle: ownerNotif.review_title,
        hasComment: !!ownerNotif.comment_id,
      };
    } else {
      const userNotif = notification as iUserNotification;
      return {
        type: userNotif.notification_type,
        content: userNotif.content,
        hasComment: !!userNotif.comment_id,
        productId: userNotif.product_id,
      };
    }
  };

  const getNotificationTypeDisplay = (type: string) => {
    const typeMap: Record<string, { label: string; icon: React.ReactNode; color: string }> = {
      owner_comment: {
        label: "Owner Comment",
        icon: <CheckCircle className="w-4 h-4" />,
        color: "bg-blue-100 text-blue-800",
      },
      owner_reply: {
        label: "Owner Reply",
        icon: <MessageSquare className="w-4 h-4" />,
        color: "bg-blue-100 text-blue-800",
      },
      comment: {
        label: "New Comment",
        icon: <MessageSquare className="w-4 h-4" />,
        color: "bg-green-100 text-green-800",
      },
      reply: {
        label: "Reply",
        icon: <MessageSquare className="w-4 h-4" />,
        color: "bg-purple-100 text-purple-800",
      },
      review: {
        label: "New Review",
        icon: <Star className="w-4 h-4" />,
        color: "bg-yellow-100 text-yellow-800",
      },
      default: {
        label: "Notification",
        icon: <User className="w-4 h-4" />,
        color: "bg-gray-100 text-gray-800",
      },
    };

    return typeMap[type] || typeMap.default;
  };

  const content = getNotificationContent();
  const typeDisplay = getNotificationTypeDisplay(content.type);

  const handleViewAndClose = () => {
    onViewItem();
    onClose();
  };

  const handleMarkAsReadAndClose = () => {
    if (onMarkAsRead) {
      onMarkAsRead();
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <DialogTitle className="text-xl font-semibold text-gray-900 pr-4">
              Notification Details
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Type Section */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge
                variant="outline"
                className={cn(
                  "flex items-center gap-1 px-3 py-1",
                  typeDisplay.color
                )}
              >
                {typeDisplay.icon}
                {typeDisplay.label}
              </Badge>
              
              <Badge
                variant={isRead ? "secondary" : "default"}
                className={cn(
                  "px-3 py-1",
                  isRead
                    ? "bg-green-100 text-green-800"
                    : "bg-blue-100 text-blue-800 animate-pulse-subtle"
                )}
              >
                {isRead ? "Read" : "Unread"}
              </Badge>
            </div>

            {isPremium && (
              <Tooltip label="Verified Business Owner" withArrow>
                <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 rounded-full">
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium text-blue-700">
                    Verified Owner
                  </span>
                </div>
              </Tooltip>
            )}
          </div>

          <Separator />

          {/* Main Content Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {getNotificationTitle()}
              </h3>
              
              {isOwnerNotification && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Package className="w-4 h-4" />
                    <span className="font-medium">Product:</span>
                    <span>{(notification as iProductOwnerNotification).product_name}</span>
                  </div>
                  
                  {content.hasComment && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MessageSquare className="w-4 h-4" />
                      <span>This notification includes a comment</span>
                    </div>
                  )}
                </div>
              )}

              {!isOwnerNotification && (
                <div className="space-y-2">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-700">
                      {(notification as iUserNotification).content}
                    </p>
                  </div>
                  
                  {content.hasComment && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MessageSquare className="w-4 h-4" />
                      <span>This notification includes a comment</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Sender Information */}
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">From</h4>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center",
                  isPremium ? "bg-blue-100" : "bg-gray-100"
                )}>
                  {isPremium ? (
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                  ) : (
                    <User className="w-4 h-4 text-gray-600" />
                  )}
                </div>
                <div>
                  <p className={cn(
                    "font-medium",
                    isPremium ? "text-blue-700" : "text-gray-900"
                  )}>
                    {notification.from_name}
                  </p>
                  {isPremium && (
                    <p className="text-xs text-blue-600">Business Owner</p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* Timestamp */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4" />
              <span>
                {notification.created_at
                  ? new Date(notification.created_at).toLocaleString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : "Date unknown"}
              </span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 pt-6">
          <div className="flex flex-1 gap-2">
            {!isRead && onMarkAsRead && (
              <Button
                variant="outline"
                onClick={handleMarkAsReadAndClose}
                className="flex items-center gap-2"
              >
                <CheckCircle className="w-4 h-4" />
                Mark as Read
              </Button>
            )}
            
            <Button
              onClick={handleViewAndClose}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
            >
              <ExternalLink className="w-4 h-4" />
              View Item
            </Button>
          </div>
          
          <Button variant="ghost" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationDetailsModal;
