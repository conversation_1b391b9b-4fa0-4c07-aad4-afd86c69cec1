"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Bell,
  CheckCircle,
  Eye,
  MessageSquare,
  User,
  X,
  Wifi,
  ThumbsUp,
  WifiOff,
  Info,
} from "lucide-react";
import { getNotificationIcon, getNotificationTypeLabel } from "@/app/util/notificationIcons";
import { useRouter } from "next/navigation";
import { useAtom } from "jotai";
import {
  ownerNotificationsAtom,
  userNotificationsAtom,
  likeNotificationsAtom
} from "@/app/store/store";
import { cn } from "@/lib/utils";
import { shouldHavePremiumStyling } from "@/app/util/notificationHelpers";
import { Tooltip } from "@mantine/core";
import LikeNotificationsList from "./LikeNotificationsList";
import OwnerNotificationsList from "./OwnerNotificationsList";
import UserNotificationsList from "./UserNotificationsList";
import useSSENotifications from '@/app/hooks/useSSENotifications';
import { useAuth } from "@clerk/nextjs";
import { markNotificationAsRead, markAllNotificationsAsRead } from "@/app/util/NotificationFunctions";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import NotificationDetailsModal from "./NotificationDetailsModal";
import { iProductOwnerNotification, iUserNotification, LikeNotification } from "@/app/util/Interfaces";

interface NotificationsPageProps {
  productId?: string | null;
}

const AllNotifications = ({
  productId = null,
}: NotificationsPageProps): React.ReactElement => {
  const router = useRouter();
  const [filter, setFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"owner" | "user" | "like">(
    productId ? "owner" : "owner"
  );
  
  // Modal state
  const [selectedNotification, setSelectedNotification] = useState<iProductOwnerNotification | iUserNotification | LikeNotification | null>(null);
  const [selectedNotificationType, setSelectedNotificationType] = useState<"owner" | "user" | "like">("owner");
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const auth = useAuth();
  const [ONA] = useAtom(ownerNotificationsAtom);
  const [UNA] = useAtom(userNotificationsAtom);
  const [LNA] = useAtom(likeNotificationsAtom);

  // Use SSE notifications hook
  const { isSSEConnected, isLoading, refetch } = useSSENotifications();

  // Fix for notification loading issue when navigating from reloaded home page
  useEffect(() => {
    // Check if all notification atoms are empty (indicating they were cleared on reload)
    const allAtomsEmpty = (!ONA || ONA.length === 0) && 
                         (!UNA || UNA.length === 0) && 
                         (!LNA || LNA.length === 0);
    
    // If atoms are empty and we're not currently loading, trigger a refetch
    if (allAtomsEmpty && !isLoading) {
      refetch();
    }
  }, [ONA, UNA, LNA, isLoading, refetch]);

  // Use the atoms directly as they're updated by the SSE hook
  const effectiveOwnerNotifications = ONA || [];
  const effectiveUserNotifications = UNA || [];
  const effectiveLikeNotifications = LNA || [];

  const markAsRead = async (notificationId: string, notificationType: "owner" | "user" | "like") => {
    try {
      await markNotificationAsRead(notificationId, auth.userId || '', notificationType);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const handleMarkAsRead = async (notificationId: string, notificationType: "owner" | "user" | "like") => {
    try {
      await markAsRead(notificationId, notificationType);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Modal handlers
  const handleShowDetails = (
    notification: iProductOwnerNotification | iUserNotification | LikeNotification,
    type: "owner" | "user" | "like"
  ) => {
    setSelectedNotification(notification);
    setSelectedNotificationType(type);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedNotification(null);
  };

  const handleViewItem = () => {
    if (!selectedNotification) return;
    if (selectedNotificationType === "like") {
      // For like notifications, navigate to review or comment depending on target_type
      const likeNotif = selectedNotification as LikeNotification;
      if (likeNotif.target_type === 'review') {
        const reviewId = likeNotif.review_id || likeNotif.target_id;
        router.push(`/review/${reviewId}`);
      } else {
        // comment
        const reviewId = likeNotif.review_id || '';
        const commentId = likeNotif.comment_id || likeNotif.target_id;
        router.push(`/review/${reviewId}?commentId=${commentId}`);
      }
      return;
    }
    const reviewId = (selectedNotification as any).review_id;
    const productId = selectedNotificationType === "owner" 
      ? (selectedNotification as iProductOwnerNotification).product_id
      : (selectedNotification as iUserNotification).product_id;
    router.push(`/fr?id=${reviewId}&productid=${productId || ""}${(selectedNotification as any).comment_id ? `&cid=${(selectedNotification as any).comment_id}` : ""}`);
  };

  const handleMarkAsReadFromModal = async () => {
    if (selectedNotification) {
      await handleMarkAsRead(selectedNotification.id!, selectedNotificationType);
    }
  };

  const filteredOwnerNotifications = effectiveOwnerNotifications.filter((notification) => {
    if (!notification) return false;
    const matchesFilter =
      filter === "all" || (filter === "unread" && !notification.read);
    const matchesSearch =
      (notification.review_title || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.from_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.product_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    const matchesProduct = productId
      ? notification.product_id === productId
      : true;
    return matchesFilter && matchesSearch && matchesProduct;
  });

  const filteredLikeNotifications = effectiveLikeNotifications.filter((notification) => {
    if (!notification) return false;
    const matchesFilter = filter === "all" || (filter === "unread" && !notification.read);
    const matchesSearch = (notification.from_name || "").toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const filteredUserNotifications = effectiveUserNotifications.filter((notification) => {
    if (!notification) return false;
    const matchesFilter =
      filter === "all" || (filter === "unread" && !notification.read);
    const matchesSearch =
      (notification.content || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.from_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const ownerUnreadCount = effectiveOwnerNotifications.filter((n) => n && !n.read).length;
  const userUnreadCount = effectiveUserNotifications.filter((n) => n && !n.read).length;
  const likeUnreadCount = effectiveLikeNotifications.filter((n) => n && !n.read).length;
  const totalOwnerCount = effectiveOwnerNotifications.length;
  const totalUserCount = effectiveUserNotifications.length;
  const totalLikeCount = effectiveLikeNotifications.length;

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="text-center py-8">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Bell className="h-6 w-6 text-myTheme-primary" />
          <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          <div className="flex items-center gap-2">
            {isSSEConnected ? (
              <div className="flex items-center gap-1 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-xs font-medium">Live</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-xs font-medium">Offline</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex gap-2">
          <Button
            variant={filter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter("all")}
          >
            All
          </Button>
          <Button
            variant={filter === "unread" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter("unread")}
          >
            Unread
          </Button>
        </div>
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search notifications..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-myTheme-primary focus:border-transparent"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "owner" | "user" | "like")}>
          <TabsList className="grid w-full grid-cols-3 bg-gray-50 p-1 rounded-t-lg">
            <TabsTrigger
              value="owner"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <div className="flex items-center justify-center gap-2">
                <User className="h-4 w-4 text-blue-500" />
                <span>Owner</span>
                {ownerUnreadCount > 0 && (
                  <span className="ml-1 text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2 py-0.5 rounded-full animate-pulse-subtle">
                    {ownerUnreadCount}/{totalOwnerCount}
                  </span>
                )}
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="user"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <div className="flex items-center justify-center gap-2">
                <MessageSquare className="h-4 w-4 text-green-500" />
                <span>User</span>
                {userUnreadCount > 0 && (
                  <span className="ml-1 text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2 py-0.5 rounded-full animate-pulse-subtle">
                    {userUnreadCount}/{totalUserCount}
                  </span>
                )}
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="like"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              <div className="flex items-center justify-center gap-2">
                <ThumbsUp className="h-4 w-4 text-red-500" />
                <span>Like</span>
                {likeUnreadCount > 0 && (
                  <span className="ml-1 text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2 py-0.5 rounded-full animate-pulse-subtle">
                    {likeUnreadCount}/{totalLikeCount}
                  </span>
                )}
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="owner" className="mt-4 animate-fadeIn">
            <OwnerNotificationsList
              notifications={filteredOwnerNotifications}
              onMarkAsRead={(id: string) => handleMarkAsRead(id, "owner")}
              onShowDetails={(notification: iProductOwnerNotification) => handleShowDetails(notification, "owner")}
            />
          </TabsContent>

          <TabsContent value="user" className="mt-4 animate-fadeIn">
            <UserNotificationsList
              notifications={filteredUserNotifications}
              onMarkAsRead={(id: string) => handleMarkAsRead(id, "user")}
              onShowDetails={(notification: iUserNotification) => handleShowDetails(notification, "user")}
            />
          </TabsContent>

          <TabsContent value="like" className="mt-4 animate-fadeIn">
            <LikeNotificationsList
              notifications={filteredLikeNotifications}
              onMarkAsRead={(id: string) => handleMarkAsRead(id, "like")}
              onViewItem={handleViewItem}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Notification Details Modal */}
      <NotificationDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        notification={selectedNotification}
        notificationType={selectedNotificationType}
        onViewItem={handleViewItem}
        onMarkAsRead={selectedNotification && !selectedNotification.read ? handleMarkAsReadFromModal : undefined}
      />
    </div>
  );
};

export default AllNotifications;