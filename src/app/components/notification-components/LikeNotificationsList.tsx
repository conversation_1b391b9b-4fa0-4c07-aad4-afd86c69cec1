"use client";
import React from "react";
import { ThumbsUp, <PERSON>, X } from "lucide-react";
import { LikeNotification } from "@/app/util/Interfaces";
import { cn } from "@/lib/utils";

interface Props {
  notifications: LikeNotification[];
  onMarkAsRead: (id: string) => void;
  onViewItem: (notification: LikeNotification) => void;
}

const LikeNotificationsList: React.FC<Props> = ({ notifications, onMarkAsRead, onViewItem }) => {
  if (!notifications) return null;

  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="bg-red-50 p-4 rounded-full mb-4">
          <ThumbsUp className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">No like notifications</h3>
        <p className="text-gray-500 text-center max-w-md">You don't have any like notifications at the moment.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification, index) => {
        const isRead = notification.read === true;
        return (
          <div
            key={notification.id}
            className={cn(
              "rounded-lg border p-5 hover:shadow-md transition-all relative overflow-hidden border-l-4 animate-fadeIn",
              "border-red-200 border-l-red-500",
              !isRead && "bg-red-50"
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {!isRead && (
              <div className="absolute top-4 right-4">
                <div className="unread-dot bg-red-500" />
              </div>
            )}
            <div className="flex items-start gap-3 mb-2">
              <div className="flex-shrink-0 mt-1">
                <ThumbsUp className="h-5 w-5 text-red-500" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h3 className={cn("font-medium text-gray-900", !isRead && "font-bold")}>New Like</h3>
                  <span className="text-sm text-gray-500">
                    {notification.created_at ? new Date(notification.created_at).toLocaleString() : ''}
                  </span>
                </div>
                <p className="text-sm text-gray-700">
                  {notification.from_name} liked your {notification.target_type}.
                </p>
                <div className="flex items-center gap-4 mt-3 text-sm">
                  <button
                    onClick={() => onMarkAsRead(notification.id!)}
                    className="flex items-center gap-1 text-gray-600 hover:text-gray-800 transition-colors"
                    title="Mark as read"
                  >
                    <X className="w-4 h-4" />
                    Mark read
                  </button>
                  <button
                    onClick={() => onViewItem(notification)}
                    className="flex items-center gap-1 transition-colors cursor-pointer px-2 py-1 rounded hover:bg-gray-100 text-myTheme-primary"
                    title="View item"
                  >
                    <Eye className="w-4 h-4" />
                    View
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default LikeNotificationsList;
