"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Bell,
  CheckCircle,
  Eye,
  MessageSquare,
  Trash2,
  User,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import {
  iProductOwnerNotification,
  iUserNotification,
} from "@/app/util/Interfaces";
import { markNotificationAsRead } from "@/app/util/NotificationFunctions";
import { useAtom } from "jotai";
import {
  ownerNotificationsAtom,
  userNotificationsAtom,
} from "@/app/store/store";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { shouldHavePremiumStyling } from "@/app/util/notificationHelpers";
import { Tooltip } from "@mantine/core";

interface NotificationsPageProps {
  ONA: iProductOwnerNotification[];
  UNA: iUserNotification[];
  productId?: string | null;
}

const AllNotifications = ({
  ONA: initialONA,
  UNA: initialUNA,
  productId = null,
}: NotificationsPageProps): React.ReactElement => {
  const router = useRouter();
  const [filter, setFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"owner" | "user">(
    productId ? "owner" : "owner" // Default to owner tab if productId is provided
  );

  const [ONA, setONA] = useAtom(ownerNotificationsAtom);
  const [UNA, setUNA] = useAtom(userNotificationsAtom);

  useEffect(() => {
    if (initialONA || initialUNA) {
      setONA(initialONA || []);
      setUNA(initialUNA || []);
    }
  }, [initialONA, initialUNA, setONA, setUNA]);

  // Removed automatic redirect to /mybusinesses when no notifications
  // Users should be able to view the notifications page even without notifications or businesses

  // If productId is provided, set a product filter
  useEffect(() => {
    if (productId) {
      setActiveTab("owner"); // Switch to owner tab when filtering by product
    }
  }, [productId]);

  const handleMarkAsRead = async (
    notificationId: string,
    notificationType: "owner" | "user"
  ) => {
    try {
      const result = await markNotificationAsRead(
        notificationId,
        'user_placeholder', // TODO: Get actual user ID
        notificationType
      );

      if (notificationType === "owner") {
        setONA((prevNotifications: iProductOwnerNotification[]) => {
          if (!prevNotifications) return [];
          const updatedNotifications = prevNotifications.map((n) =>
            n.id === notificationId ? { ...n, read: true } : n
          );
          return updatedNotifications;
        });
      } else {
        setUNA((prevNotifications: iUserNotification[]) => {
          if (!prevNotifications) return [];
          const updatedNotifications = prevNotifications.map((n) =>
            n.id === notificationId ? { ...n, read: true } : n
          );
          return updatedNotifications;
        });
      }
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const filteredOwnerNotifications = (ONA || []).filter((notification) => {
    if (!notification) return false;
    const matchesFilter =
      filter === "all" || (filter === "unread" && !notification.read);
    const matchesSearch =
      (notification.review_title || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.from_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.product_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    const matchesProduct = productId
      ? notification.product_id === productId
      : true;
    return matchesFilter && matchesSearch && matchesProduct;
  });

  const filteredUserNotifications = (UNA || []).filter((notification) => {
    if (!notification) return false;
    const matchesFilter =
      filter === "all" || (filter === "unread" && !notification.read);
    const matchesSearch =
      (notification.content || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (notification.from_name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const ownerUnreadCount = (ONA || []).filter((n) => n && !n.read).length;
  const userUnreadCount = (UNA || []).filter((n) => n && !n.read).length;
  const totalOwnerCount = ONA?.length || 0;
  const totalUserCount = UNA?.length || 0;

  if (!ONA?.length && !UNA?.length) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="text-center py-8">
          <p className="text-lg text-gray-500">No notifications yet.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3 sm:mb-0">
          Notifications Center
          {productId && (
            <span className="ml-2 text-base sm:text-lg font-normal text-gray-500">
              (Filtered by product)
            </span>
          )}
        </h1>

        {/* Add a clear filter button if productId is present */}
        {productId && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/notifications")}
            className="flex items-center gap-1 hover:bg-gray-100 transition-colors duration-300 shadow-sm"
            aria-label="Clear product filter"
          >
            <X className="h-4 w-4" />
            Clear product filter
          </Button>
        )}
      </div>

      <div className="flex flex-col gap-6">
        <div className="stats grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="stat bg-white rounded-xl border border-gray-200 p-5 flex items-center justify-between shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div>
              <div className="text-sm text-gray-500 mb-1">Total Notifications</div>
              <div className="text-2xl font-bold text-gray-900">
                {totalOwnerCount + totalUserCount}
              </div>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Bell className="w-5 h-5 text-blue-600" aria-hidden="true" />
            </div>
          </div>

          <div className="stat bg-white rounded-xl border border-gray-200 p-5 flex items-center justify-between shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div>
              <div className="text-sm text-gray-500 mb-1">Unread Notifications</div>
              <div className="text-2xl font-bold text-gray-900">
                {ownerUnreadCount + userUnreadCount}
              </div>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Eye className="w-5 h-5 text-purple-600" aria-hidden="true" />
            </div>
          </div>

          <div className="stat bg-white rounded-xl border border-gray-200 p-5 flex items-center justify-between shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div>
              <div className="text-sm text-gray-500 mb-1">Read Rate</div>
              <div className="text-2xl font-bold text-gray-900">
                {(
                  ((totalOwnerCount +
                    totalUserCount -
                    (ownerUnreadCount + userUnreadCount)) /
                    (totalOwnerCount + totalUserCount)) *
                  100
                ).toFixed(1)}
                %
              </div>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <MessageSquare className="w-5 h-5 text-green-600" aria-hidden="true" />
            </div>
          </div>
        </div>

        <Tabs defaultValue="user" className="w-full">
          <TabsList className="w-full grid grid-cols-2 bg-gray-100/80 p-1.5 rounded-lg sticky top-0 z-10 shadow-sm mb-4">
            <TabsTrigger
              value="user"
              className="rounded-md py-2.5 px-4 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm transition-all font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              aria-label="User notifications tab"
            >
              <div className="flex items-center justify-center gap-2">
                <User className="h-4 w-4" />
                <span>User Notifications</span>
                {userUnreadCount > 0 && (
                  <span className="ml-1 text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2 py-0.5 rounded-full animate-pulse-subtle">
                    {userUnreadCount}/{totalUserCount}
                  </span>
                )}
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="owner"
              className="rounded-md py-2.5 px-4 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm transition-all font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              aria-label="Owner notifications tab"
            >
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Owner Notifications</span>
                {ownerUnreadCount > 0 && (
                  <span className="ml-1 text-xs bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2 py-0.5 rounded-full animate-pulse-subtle">
                    {ownerUnreadCount}/{totalOwnerCount}
                  </span>
                )}
              </div>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="user" className="mt-4 animate-fadeIn">
            {filteredUserNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="bg-blue-50 p-4 rounded-full mb-4">
                  <Bell className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">No user notifications</h3>
                <p className="text-gray-500 text-center max-w-md">You don't have any user notifications at the moment.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredUserNotifications.map((notification, index) => {
                  // Check if this notification is from a business owner
                  const isPremium = shouldHavePremiumStyling(notification);
                  const isRead = notification.read === true;

                  return (
                    <div
                      key={index}
                      className={cn(
                        "rounded-lg border p-5 hover:shadow-md transition-all relative overflow-hidden border-l-4 animate-fadeIn",
                        isPremium
                          ? "owner-comment border-blue-200 border-l-blue-500"
                          : "bg-white border-gray-200 border-l-transparent",
                        !isRead && "bg-blue-50"
                      )}
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      {!isRead && (
                        <div className="absolute top-4 right-4">
                          <div className="unread-dot"></div>
                        </div>
                      )}
                      <div className="flex items-center justify-between mb-2">
                        <h3
                          className={cn(
                            "font-medium",
                            isPremium ? "owner-text" : "text-gray-900",
                            !isRead && "font-bold"
                          )}
                        >
                          {notification.content.length > 120
                            ? `${notification.content.substring(0, 120)}...`
                            : notification.content}
                        </h3>
                        <span
                          className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            isRead
                              ? "bg-green-50 text-green-700"
                              : "bg-blue-100 text-blue-700"
                          )}
                        >
                          {isRead ? "Read" : "New"}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2 flex items-center">
                        {isPremium ? (
                          <>
                            <span className="mr-1">From:</span>
                            <Tooltip label="Business Owner" withArrow>
                              <div className="flex items-center">
                                <CheckCircle className="h-4 w-4 mr-1 text-blue-500" />
                                <span className="font-medium text-blue-700">
                                  {notification.from_name}
                                </span>
                              </div>
                            </Tooltip>
                            <span className="notification-owner-badge ml-2">
                              Verified Owner
                            </span>
                          </>
                        ) : (
                          <>
                            <span>From: {notification.from_name}</span>
                          </>
                        )}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          {notification.created_at
                            ? new Date(notification.created_at).toLocaleString()
                            : "Date unknown"}
                        </span>
                        <div
                          onClick={() => {
                            handleMarkAsRead(notification.id, "user");
                            router.push(
                              `/fr?id=${notification.review_id}&productid=${notification.product_id || ""}`
                            );
                          }}
                          className={cn(
                            "flex items-center gap-1 transition-colors cursor-pointer",
                            isPremium
                              ? "text-blue-600 hover:text-blue-800"
                              : "text-myTheme-primary hover:text-myTheme-primary/80"
                          )}
                        >
                          <Eye className="w-4 h-4" />
                          View
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
          <TabsContent value="owner" className="mt-4 animate-fadeIn">
            {filteredOwnerNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="bg-purple-50 p-4 rounded-full mb-4">
                  <CheckCircle className="h-8 w-8 text-purple-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">No owner notifications</h3>
                <p className="text-gray-500 text-center max-w-md">You don't have any owner notifications at the moment.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOwnerNotifications.map((notification, index) => {
                  const isPremium = shouldHavePremiumStyling(notification);
                  const isRead = notification.read === true;

                  return (
                    <div
                      key={index}
                      className={cn(
                        "rounded-lg border p-5 hover:shadow-md transition-all relative overflow-hidden border-l-4 animate-fadeIn",
                        isPremium
                          ? "owner-comment border-blue-200 border-l-blue-500"
                          : "bg-white border-gray-200 border-l-transparent",
                        !isRead && "bg-blue-50"
                      )}
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      {!isRead && (
                        <div className="absolute top-4 right-4">
                          <div className="unread-dot"></div>
                        </div>
                      )}
                      <div className="flex items-center justify-between mb-2">
                        <h3
                          className={cn(
                            "font-medium",
                            isPremium ? "owner-text" : "text-gray-900",
                            !isRead && "font-bold"
                          )}
                        >
                          {notification.review_title}
                        </h3>
                        <span
                          className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            isRead
                              ? "bg-green-50 text-green-700"
                              : "bg-blue-100 text-blue-700"
                          )}
                        >
                          {isRead ? "Read" : "New"}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        <span className="mr-1">From:</span>{" "}
                        {notification.from_name}
                        <span className="mx-1">|</span>
                        <span className="font-medium">Product:</span>{" "}
                        {notification.product_name}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          {notification.created_at
                            ? new Date(notification.created_at).toLocaleString()
                            : "Date unknown"}
                        </span>
                        <div
                          onClick={() => {
                            handleMarkAsRead(notification.id, "owner");
                            router.push(
                              `/fr?id=${notification.review_id}&productid=${notification.product_id}`
                            );
                          }}
                          className={cn(
                            "flex items-center gap-1 transition-colors cursor-pointer",
                            isPremium
                              ? "text-blue-600 hover:text-blue-800"
                              : "text-myTheme-primary hover:text-myTheme-primary/80"
                          )}
                        >
                          <Eye className="w-4 h-4" />
                          View
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AllNotifications;
