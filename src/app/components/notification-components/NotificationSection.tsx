import React from "react";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip } from "@mantine/core";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {
  Clock,
  CheckCircleIcon,
  Users,
  User,
  ExternalLink,
} from "lucide-react";

import { getNotificationIcon, getNotificationTypeLabel } from "@/app/util/notificationIcons";
import { cn } from "@/lib/utils";
import {
  isOwnerNotification,
  shouldHavePremiumStyling,
} from "@/app/util/notificationHelpers";
import {
  iProductOwnerNotification,
  iUserNotification,
  LikeNotification,
} from "@/app/util/Interfaces";

dayjs.extend(relativeTime);

type NotificationType = iUserNotification | iProductOwnerNotification | LikeNotification;

interface NotificationSectionProps {
  title: string;
  notifications: NotificationType[];
  onClick: (notification: NotificationType) => void;
  onMarkAsRead?: (notification: NotificationType) => void;
  icon: React.ReactNode;
}

const NotificationSection: React.FC<NotificationSectionProps> = ({
  title,
  notifications,
  onClick,
  onMarkAsRead,
  icon,
}) => {
  if (notifications.length === 0) return null;

  return (
    <>
      <div className="px-3 py-2 bg-gray-50 border-b border-gray-100">
        <div className="flex items-center">
          {icon}
          <span className="ml-2 font-medium text-sm text-gray-700">{title}</span>
        </div>
      </div>
      {notifications.map((notification, index) => {
        const isPremium = shouldHavePremiumStyling(notification);
        const isRead = notification.read === true;

        return (
          <DropdownMenuItem
            key={`${title.toLowerCase()}-${index}`}
            className={cn(
              "flex flex-col items-start py-2 px-3 cursor-pointer focus:bg-gray-50 relative overflow-hidden border-l-4",
              isPremium
                ? "bg-gradient-to-r from-slate-50 to-blue-50/30 hover:bg-blue-50 border-l-blue-500"
                : "hover:bg-gray-50 border-l-transparent",
              !isRead && "bg-blue-50 font-semibold"
            )}
            onClick={() => onClick(notification)}
          >
            {!isRead && (
              <div className="absolute top-2 right-2">
                <div className="unread-dot"></div>
              </div>
            )}
            <div className="flex items-start w-full">
              <div className="flex-shrink-0 mr-3 mt-0.5">
                {getNotificationIcon(notification)}
              </div>
              <div className="flex-1 min-w-0">
                <p
                  className={cn(
                    "font-medium text-sm truncate",
                    isPremium ? "owner-text" : "text-gray-900",
                    !isRead && "font-bold"
                  )}
                >
                  {'target_type' in notification
                    ? `${notification.from_name} liked your ${notification.target_type}`
                    : isOwnerNotification(notification as iUserNotification)
                      ? (notification as iProductOwnerNotification).review_title
                      : (notification as any).content}
                </p>
                <div className="flex items-center justify-between mt-1">
                  <div className="flex items-center text-xs text-gray-500">
                    {isPremium ? (
                      <>
                        <Tooltip label="Business Owner" withArrow>
                          <div className="flex items-center mr-1">
                            <CheckCircleIcon className="h-3 w-3 mr-1 text-blue-500" />
                            <span className="truncate font-medium text-blue-700">
                              {(notification as any).from_name}
                            </span>
                          </div>
                        </Tooltip>
                        <span className="notification-owner-badge">Verified Owner</span>
                      </>
                    ) : (
                      <>
                        {(notification as any).additional_users ? (
                          <>
                            <Users className="h-3 w-3 mr-1 text-gray-600" />
                            <span className="truncate">
                              {(notification as any).from_name} and {(notification as any).additional_users.length} others
                            </span>
                          </>
                        ) : (
                          <>
                            <User className="h-3 w-3 mr-1" />
                            <span className="truncate">{(notification as any).from_name}</span>
                          </>
                        )}
                      </>
                    )}
                  </div>
                  <div className="flex items-center ml-2 text-xs text-gray-400">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{dayjs(notification.created_at).fromNow()}</span>
                  </div>
                </div>
                {/* Type badge and view button */}
                <div className="mt-1 flex items-center justify-between w-full">
                  <Badge variant="secondary" className="text-xxs bg-gray-100 text-gray-600 border-0">
                    {getNotificationTypeLabel(notification)}
                  </Badge>
                  <div className="flex items-center gap-1">
                    {!isRead && onMarkAsRead && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent parent click handler
                          onMarkAsRead(notification);
                        }}
                      >
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Mark read
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent parent click handler
                        onClick(notification);
                      }}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </DropdownMenuItem>
        );
      })}
    </>
  );
};

export default NotificationSection;
