import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  BellIcon,
  Check,
  CheckCircleIcon,
  Clock,
  MessageSquare,
  Star,
  User,
  Wifi,
  WifiOff,
  ThumbsUp,
  ThumbsDown,
  Heart,
  Users,
  RefreshCw,
} from "lucide-react";
import { getNotificationIcon, getNotificationTypeLabel } from "@/app/util/notificationIcons";
import NotificationSection from "./NotificationSection";
import { useAtom, useSetAtom } from "jotai";
import { useAuth } from "@clerk/nextjs";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

import {
  iProductOwnerNotification,
  iUserNotification,
  LikeNotification
} from "@/app/util/Interfaces";
import {
  ownerNotificationsAtom,
  userNotificationsAtom,
  likeNotificationsAtom
} from "@/app/store/store";
import {
  isOwnerNotification,
  shouldHavePremiumStyling,
} from "@/app/util/notificationHelpers";
import { Tooltip } from "@mantine/core";
import useSSENotifications from '@/app/hooks/useSSENotifications';
import { markAsRead, markAllAsRead, navigateToNotificationTarget } from "@/app/util/notificationService";

dayjs.extend(relativeTime);

type NotificationType = iUserNotification | iProductOwnerNotification | LikeNotification;

export default function NotificationDropdown() {
  const auth = useAuth();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const setUserNotificationsAtom = useSetAtom(userNotificationsAtom);
  const setOwnerNotificationsAtom = useSetAtom(ownerNotificationsAtom);
  const setLikeNotificationsAtom = useSetAtom(likeNotificationsAtom);
  const [userNotifications] = useAtom(userNotificationsAtom);
  const [ownerNotifications] = useAtom(ownerNotificationsAtom);
  const [likeNotifications] = useAtom(likeNotificationsAtom);
  
  // Use SSE notifications hook
  const {
    notifications: sseNotifications,
    unreadCount,
    isSSEConnected,
    isLoading,
    error,
    isUsingPolling,
    reconnect,
    refetch
  } = useSSENotifications(auth.userId || '', { enabled: true, debug: true });

  // Update atoms when SSE notifications change
  useEffect(() => {
    if (sseNotifications) {
      setUserNotificationsAtom(sseNotifications.userNotifications || []);
      setOwnerNotificationsAtom(sseNotifications.ownerNotifications || []);
      setLikeNotificationsAtom(sseNotifications.likeNotifications || []);
    }
  }, [sseNotifications, setUserNotificationsAtom, setOwnerNotificationsAtom, setLikeNotificationsAtom]);

  // Handle click on the bell icon when there are no notifications
  const handleBellClick = () => {
    if (unreadCount === 0) {
      router.push("/notifications");
      return;
    }
    setIsOpen(!isOpen);
  };

  // Filter unread notifications and calculate counts
  const unreadUserNotifications = userNotifications.filter(n => !n.read);
  const unreadOwnerNotifications = ownerNotifications.filter(n => !n.read);
  const unreadLikeNotifications = likeNotifications.filter(n => !n.read);
  
  // Use the unreadCount from SSE hook if available, otherwise calculate locally
  const totalCount = unreadCount !== undefined ? unreadCount : 
    unreadUserNotifications.length + unreadOwnerNotifications.length + unreadLikeNotifications.length;

  const latestUserNotifications = unreadUserNotifications.slice(0, 3);
  const latestOwnerNotifications = unreadOwnerNotifications.slice(0, 3);
  const latestLikeNotifications = unreadLikeNotifications.slice(0,3);

  // Handle marking a notification as read and updating local state
  const handleMarkAsReadLocal = async (notificationId: string | undefined, type: 'user' | 'owner' | 'like') => {
    if (!notificationId) return;
    try {
      // Optimistically update UI
      if (type === 'user') {
        setUserNotificationsAtom(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
      } else if (type === 'owner') {
        setOwnerNotificationsAtom(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
      } else if (type === 'like') {
        setLikeNotificationsAtom(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
      }
      
      // Call the API to mark as read
      const response = await markAsRead(notificationId, auth.userId || '', type);
      
      if (!response.success) {
        // Revert optimistic update if API call fails
        if (type === 'user') {
          setUserNotificationsAtom(prev => 
            prev.map(n => n.id === notificationId ? { ...n, read: false } : n)
          );
        } else if (type === 'owner') {
          setOwnerNotificationsAtom(prev => 
            prev.map(n => n.id === notificationId ? { ...n, read: false } : n)
          );
        } else if (type === 'like') {
          setLikeNotificationsAtom(prev => 
            prev.map(n => n.id === notificationId ? { ...n, read: false } : n)
          );
        }
        
        // Show error toast
        toast.error('Failed to mark notification as read', {
          description: response.message || 'Please try again later',
          duration: 3000,
        });
        console.error('Failed to mark notification as read:', response);
      }
    } catch (error) {
      // Show error toast
      toast.error('Error marking notification as read', {
        description: 'Please check your connection and try again',
        duration: 3000,
      });
      console.error('Error marking notification as read:', error);
    }
  };
  
  // Handle clicking on a like notification
  const handleLikeNotiClick = async (notification: NotificationType) => {
    if ('target_type' in notification) {
      try {
        // Mark as read first
        await handleMarkAsReadLocal(notification.id, 'like');
        
        // Then navigate to the target using the proper URL format
        let targetUrl = '/notifications'; // fallback
        
        if (notification.target_type === 'review' && notification.review_id) {
          targetUrl = `/fr?id=${notification.review_id}`;
        } else if (notification.target_type === 'comment' && notification.review_id && notification.comment_id) {
          targetUrl = `/fr?id=${notification.review_id}&cid=${notification.comment_id}`;
        }
        
        router.push(targetUrl);
        setIsOpen(false);
      } catch (error) {
        console.error('Error handling like notification click:', error);
        toast.error('Error navigating to notification target');
      }
    }
  };

  // Show error state with retry option
  if (error) {
    return (
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          onClick={() => router.push("/notifications")}
        >
          <BellIcon className="h-5 w-5 text-gray-400" />
          <Badge variant="destructive" className="absolute -top-1 -right-1 px-1 min-w-[1.25rem] h-5">
            !
          </Badge>
        </Button>
        <Tooltip
          label="Connection error. Click to retry."
          position="bottom"
          withArrow
        >
          <Button
            variant="ghost"
            size="icon"
            className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-amber-100 p-0"
            onClick={(e) => {
              e.stopPropagation();
              if (isUsingPolling) {
                refetch();
              } else {
                reconnect();
              }
            }}
          >
            <RefreshCw className="h-3 w-3 text-amber-600" />
          </Button>
        </Tooltip>
      </div>
    );
  }

  // Handle clicking on an owner notification
  const handleOwnerNotiClick = async (notification: NotificationType) => {
    if ('owner_id' in notification) {
      try {
        // Mark as read first
        await handleMarkAsReadLocal(notification.id, 'owner');
        
        // Then navigate to the target using the proper URL format
        let targetUrl = '/notifications'; // fallback
        
        if (notification.review_id) {
          targetUrl = `/fr?id=${notification.review_id}`;
          if (notification.product_id) {
            targetUrl += `&productid=${notification.product_id}`;
          }
        }
        
        router.push(targetUrl);
        setIsOpen(false);
      } catch (error) {
        console.error('Error handling owner notification click:', error);
        toast.error('Error navigating to notification target');
      }
    }
  };

  // Handle clicking on a user notification
  const handleUserNotiClick = async (notification: NotificationType) => {
    if ('content' in notification) {
      try {
        // Mark as read first
        await handleMarkAsReadLocal(notification.id, 'user');
        
        // Then navigate to the target using the proper URL format
        let targetUrl = '/notifications'; // fallback
        
        if (notification.review_id) {
          targetUrl = `/fr?id=${notification.review_id}`;
          if (notification.comment_id) {
            targetUrl += `&cid=${notification.comment_id}`;
          }
          if (notification.product_id) {
            targetUrl += `&productid=${notification.product_id}`;
          }
        }
        
        router.push(targetUrl);
        setIsOpen(false);
      } catch (error) {
        console.error('Error handling user notification click:', error);
        toast.error('Error navigating to notification target');
      }
    }
  };
  
  // Handle marking all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      // Store original notification states for potential rollback
      const originalUserNotifications = [...userNotifications];
      const originalOwnerNotifications = [...ownerNotifications];
      const originalLikeNotifications = [...likeNotifications];
      
      // Optimistically update UI
      setUserNotificationsAtom(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
      setOwnerNotificationsAtom(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
      setLikeNotificationsAtom(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
      
      // Call the API to mark all notifications as read using the corrected function
      const response = await markAllAsRead(auth.userId || '');
      
      if (!response.success) {
        // Revert optimistic updates if API call fails
        setUserNotificationsAtom(originalUserNotifications);
        setOwnerNotificationsAtom(originalOwnerNotifications);
        setLikeNotificationsAtom(originalLikeNotifications);
        
        // Show informative toast about missing backend feature
        toast.error('Mark all not available yet', {
          description: 'Please mark notifications individually for now',
          duration: 4000,
        });
        console.warn('Mark all endpoint not implemented on backend yet');
      } else {
        // Show success toast
        toast.success('All notifications marked as read', {
          duration: 2000,
        });
      }
    } catch (error) {
      // Show error toast
      toast.error('Error marking all notifications as read', {
        description: 'Please check your connection and try again',
        duration: 3000,
      });
      console.error('Error marking all notifications as read:', error);
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "relative",
              isHovered ? "bg-gray-100" : "",
              isLoading ? "opacity-70" : ""
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={(e) => {
              if (unreadCount === 0) {
                e.preventDefault();
                handleBellClick();
              }
            }}
          >
            <BellIcon
              className={cn(
                "h-5 w-5",
                isHovered ? "text-myTheme-accent" : "text-gray-700"
              )}
            />
            {unreadCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 px-1 min-w-[1.25rem] h-5 bg-red-500 border border-white"
              >
                {unreadCount > 99 ? "99+" : unreadCount}
              </Badge>
            )}
          </Button>
          
          {/* Connection status indicator */}
          <Tooltip
            label={isSSEConnected ? "Real-time connected" : "Real-time disconnected"}
            position="bottom"
            withArrow
            className="absolute bottom-0 right-0"
          >
            <div className="absolute bottom-1 right-1 w-2 h-2 rounded-full">
              {isSSEConnected ? (
                <Wifi className="w-3 h-3 text-green-500" />
              ) : (
                <WifiOff className="w-3 h-3 text-amber-500" />
              )}
            </div>
          </Tooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 max-h-[80vh] overflow-y-auto p-0 rounded-xl shadow-lg border border-gray-200 bg-white"
        sideOffset={8}
      >
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-gray-900">Notifications</h3>
              {isSSEConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
            </div>
            {totalCount > 0 && (
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                  onClick={handleMarkAllAsRead}
                >
                  Mark all as read
                </Button>
                <Badge variant="outline" className="bg-blue-500 text-white border-blue-600">
                  {totalCount} new
                </Badge>
              </div>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="p-6 flex justify-center">
            <div className="rounded-full h-8 w-8 border-b-2 border-myTheme-accent opacity-70"></div>
          </div>
        ) : unreadCount === 0 ? (
          <>
            <div className="p-8 text-center">
              <BellIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-700 font-medium">No new notifications</p>
              <p className="text-sm text-gray-500 mt-2">
                We will notify you when something new arrives
              </p>
            </div>
            <DropdownMenuSeparator className="my-1 border-gray-200" />
            <DropdownMenuItem
              asChild
              className="px-4 py-3 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 transition-colors"
            >
              <Link
                href="/notifications"
                className="flex items-center justify-between w-full text-blue-600 hover:text-blue-700"
              >
                <span>See All Notifications</span>
                <MessageSquare className="h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </>
        ) : (
          <>
            <NotificationSection
              title="User Notifications"
              notifications={latestUserNotifications}
              onClick={handleUserNotiClick}
              onMarkAsRead={(notification) => handleMarkAsReadLocal(notification.id, 'user')}
              icon={<MessageSquare className="h-4 w-4 text-green-500" />}
            />
            {unreadUserNotifications.length > 0 && unreadOwnerNotifications.length > 0 && (
              <DropdownMenuSeparator className="my-1 border-gray-200" />
            )}
            <NotificationSection
              title="Product Reviews"
              notifications={latestOwnerNotifications}
              onClick={handleOwnerNotiClick}
              onMarkAsRead={(notification) => handleMarkAsReadLocal(notification.id, 'owner')}
              icon={<Star className="h-4 w-4 text-amber-500" />}
            />
            {latestLikeNotifications.length > 0 && (
              <>
                <DropdownMenuSeparator className="my-1 border-gray-200" />
                <NotificationSection
                  title="Likes"
                  notifications={latestLikeNotifications}
                  onClick={handleLikeNotiClick}
                  onMarkAsRead={(notification) => handleMarkAsReadLocal(notification.id, 'like')}
                  icon={<ThumbsUp className="h-4 w-4 text-red-500" />}
                />
              </>
            )}
            <DropdownMenuSeparator className="my-1 border-gray-200" />
            <DropdownMenuItem
              asChild
              className="px-4 py-3 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 transition-colors"
            >
              <Link
                href="/notifications"
                className="flex items-center justify-between w-full text-blue-600 hover:text-blue-700"
              >
                <span>See All Notifications</span>
                <MessageSquare className="h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
