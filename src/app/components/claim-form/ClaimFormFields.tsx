"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { toast } from "sonner";

interface ClaimFormFieldsProps {
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  setContactInfo: (
    info: React.SetStateAction<{
      name: string;
      email: string;
      phone: string;
    }>
  ) => void;
  additionalInfo: string;
  setAdditionalInfo: (info: string) => void;
  uploadedImages: string[];
  setUploadedImages: (images: React.SetStateAction<string[]>) => void;
  handleFileUpload: (files: File[]) => Promise<void>;
  isUploading: boolean;
  isDisabled: boolean;
  maxImages: number;
}

const ClaimFormFields: React.FC<ClaimFormFieldsProps> = ({
  contactInfo,
  setContactInfo,
  additionalInfo,
  setAdditionalInfo,
  uploadedImages,
  setUploadedImages,
  handleFileUpload,
  isUploading,
  isDisabled,
  maxImages,
}) => {
  return (
    <fieldset disabled={isDisabled}>
      <div className="space-y-4">
        <h3 className="font-medium">Contact Information</h3>
        <div className="grid gap-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            value={contactInfo.name}
            onChange={(e) =>
              setContactInfo((prev) => ({ ...prev, name: e.target.value }))
            }
            required
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            value={contactInfo.email}
            onChange={(e) =>
              setContactInfo((prev) => ({ ...prev, email: e.target.value }))
            }
            required
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            value={contactInfo.phone}
            onChange={(e) =>
              setContactInfo((prev) => ({ ...prev, phone: e.target.value }))
            }
          />
        </div>
      </div>

      <div className="space-y-4 mt-6">
        <h3 className="font-medium">Additional Information</h3>
        <div className="grid gap-2">
          <Label htmlFor="additionalInfo">
            Why should you be the owner of this product? *
          </Label>
          <Textarea
            id="additionalInfo"
            value={additionalInfo}
            onChange={(e) => setAdditionalInfo(e.target.value)}
            placeholder="Explain your connection to this product and why you should be recognized as its owner..."
            rows={4}
            required
          />
        </div>
      </div>

      <div className="space-y-4 mt-6">
        <h3 className="font-medium">Supporting Images</h3>
        <div className="grid gap-2">
          <Label htmlFor="claimImages">
            Upload Images (Max 5MB each, up to {maxImages} images)
          </Label>
          <Input
            id="claimImages"
            type="file"
            multiple
            accept="image/*"
            onChange={(e) => {
              if (e.target.files) {
                // Limit to maxImages total including already uploaded ones
                const currentImageCount = uploadedImages.length;
                const filesToUpload = Array.from(e.target.files).slice(
                  0,
                  maxImages - currentImageCount
                );
                if (filesToUpload.length > 0) {
                  handleFileUpload(filesToUpload);
                }
                if (
                  Array.from(e.target.files).length + currentImageCount >
                  maxImages
                ) {
                  toast.info(
                    `You can upload a maximum of ${maxImages} images. ${
                      maxImages - currentImageCount
                    } more can be added.`
                  );
                }
              }
            }}
            disabled={isUploading || uploadedImages.length >= maxImages}
            className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"
          />
          {uploadedImages.length >= maxImages && (
            <p className="text-xs text-red-500 mt-1">
              Maximum of {maxImages} images already uploaded.
            </p>
          )}
        </div>
        {isUploading && (
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Uploading images...</span>
          </div>
        )}
        {uploadedImages.length > 0 && (
          <div className="mt-2 space-y-2">
            <p className="text-sm font-medium">Uploaded Image Previews:</p>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
              {uploadedImages.map((url, index) => (
                <div key={index} className="relative group">
                  <img
                    src={url}
                    alt={`Uploaded preview ${index + 1}`}
                    className="rounded-md object-cover aspect-square"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => {
                      const newUploadedImages = uploadedImages.filter(
                        (_, i) => i !== index
                      );
                      setUploadedImages(newUploadedImages);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </fieldset>
  );
};

export default ClaimFormFields;
