"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Loader2, AlertCircle, X } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import ProductCardSlim from "../ProductCardSlim";

interface ProductSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  handleSearch: () => Promise<void>;
  handleKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  isSearching: boolean;
  error: string | null;
  searchResults: iProduct[];
  onProductSelect: (product: iProduct) => void;
}

const ProductSearch: React.FC<ProductSearchProps> = ({
  searchQuery,
  setSearchQuery,
  handleSearch,
  handleKeyPress,
  isSearching,
  error,
  searchResults,
  onProductSelect,
}) => {
  return (
    <div className="space-y-4">
      <div className="flex space-x-2">
        <Input
          type="search"
          placeholder="Search product by name or ID..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isSearching}
          className="flex-grow"
        />
        <Button onClick={handleSearch} disabled={isSearching || !searchQuery.trim()}>
          {isSearching ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Searching...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search
            </>
          )}
        </Button>
      </div>

      {error && (
        <div className="text-sm text-red-600 flex items-center gap-2 p-3 border border-red-300 bg-red-50 rounded-md">
          <AlertCircle className="h-4 w-4" />
          {error}
        </div>
      )}

      {searchResults.length > 0 && (
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">
            Select a product to claim:
          </p>
          <div className="grid gap-3">
            {searchResults.map((product) => (
              <div
                key={product.id}
                className="cursor-pointer hover:bg-gray-50 rounded-lg transition-colors"
                onClick={() => onProductSelect(product)}
              >
                <ProductCardSlim product={product} options={{ size: "small" }} />
              </div>
            ))}
          </div>
        </div>
      )}

      {searchResults.length === 0 && searchQuery && !isSearching && !error && (
        <p className="text-sm text-muted-foreground">
          No available products found matching your search.
        </p>
      )}
    </div>
  );
};

export default ProductSearch;
