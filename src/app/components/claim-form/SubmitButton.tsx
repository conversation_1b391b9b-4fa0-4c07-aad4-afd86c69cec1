"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface SubmitButtonProps {
  isSubmitting: boolean;
  isDisabled: boolean;
}

const SubmitButton: React.FC<SubmitButtonProps> = ({
  isSubmitting,
  isDisabled,
}) => {
  return (
    <Button
      type="submit"
      disabled={isDisabled || isSubmitting}
      className="w-full"
    >
      {isSubmitting ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Submitting Claim...
        </>
      ) : (
        "Submit Claim"
      )}
    </Button>
  );
};

export default SubmitButton;
