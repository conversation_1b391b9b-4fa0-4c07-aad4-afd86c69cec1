"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface AuthCheckProps {
  isSignedIn: boolean;
  children: React.ReactNode;
}

const AuthCheck: React.FC<AuthCheckProps> = ({ isSignedIn, children }) => {
  const router = useRouter();

  if (!isSignedIn) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sign in Required</CardTitle>
          <CardDescription>Please sign in to claim a product.</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => router.push("https://accounts.reviewit.gy/sign-in")}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    );
  }

  return <>{children}</>;
};

export default AuthCheck;
