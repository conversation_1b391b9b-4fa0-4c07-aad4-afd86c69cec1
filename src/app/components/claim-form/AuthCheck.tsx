"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SignInButton } from "@clerk/nextjs";

interface AuthCheckProps {
  isSignedIn: boolean;
  children: React.ReactNode;
}

const AuthCheck: React.FC<AuthCheckProps> = ({ isSignedIn, children }) => {
  const router = useRouter();

  if (!isSignedIn) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sign in Required</CardTitle>
          <CardDescription>Please sign in to claim a product.</CardDescription>
        </CardHeader>
        <CardContent>
          <SignInButton mode="redirect">
            <Button>
              Sign In
            </Button>
          </SignInButton>
        </CardContent>
      </Card>
    );
  }

  return <>{children}</>;
};

export default AuthCheck;
