"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Loader2 } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import ProductCardSlim from "../ProductCardSlim";
import { format } from "date-fns";

interface SelectedProductDisplayProps {
  selectedProduct: iProduct;
  onChangeProduct: () => void;
  isCheckingClaim: boolean;
  userHasExistingClaim: boolean;
  existingClaimDetails: {
    id: string;
    status: string;
    createdAt: string;
    rejectionReason?: string;
  } | null;
}

const SelectedProductDisplay: React.FC<SelectedProductDisplayProps> = ({
  selectedProduct,
  onChangeProduct,
  isCheckingClaim,
  userHasExistingClaim,
  existingClaimDetails,
}) => {
  return (
    <div className="space-y-4">
      <ProductCardSlim product={selectedProduct} options={{ size: "medium" }} />
      
      <Button
        variant="outline"
        onClick={onChangeProduct}
        className="my-4"
      >
        Change Product
      </Button>

      {isCheckingClaim && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Checking your claim status...</span>
        </div>
      )}

      {userHasExistingClaim && existingClaimDetails && (
        <div className="p-4 border border-amber-200 bg-amber-50 rounded-md">
          <h4 className="font-medium text-amber-800 mb-2 flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Existing Claim Information
          </h4>
          <div className="space-y-1 text-sm">
            <p>
              <span className="font-medium">Status:</span>{" "}
              <span
                className={`${
                  existingClaimDetails.status === "PENDING"
                    ? "text-amber-600"
                    : existingClaimDetails.status === "APPROVED"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {existingClaimDetails.status}
              </span>
            </p>
            <p>
              <span className="font-medium">Submitted:</span>{" "}
              {format(
                new Date(existingClaimDetails.createdAt),
                "MMM d, yyyy 'at' h:mm a"
              )}
            </p>
            {existingClaimDetails.status === "REJECTED" &&
              existingClaimDetails.rejectionReason && (
                <p>
                  <span className="font-medium">Reason:</span>{" "}
                  {existingClaimDetails.rejectionReason}
                </p>
              )}
            <p className="mt-2 text-amber-700">
              You cannot submit another claim for this product while you have an
              existing claim.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedProductDisplay;
