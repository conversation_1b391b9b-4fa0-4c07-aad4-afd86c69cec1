"use client";
import { useRouter } from "next/navigation";

interface ReviewAvailabilityMessageProps {
  message: string;
  daysRemaining?: number;
  nextEligibleDate?: string;
  returnUrl?: string;
  returnLabel?: string;
}

/**
 * A friendly message component for when a user cannot submit a review
 */
export default function ReviewAvailabilityMessage({
  message,
  daysRemaining,
  nextEligibleDate,
  returnUrl = "/",
  returnLabel = "Back to Reviews",
}: ReviewAvailabilityMessageProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 text-center max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-4">Review Not Available</h2>
      <p className="text-blue-600 font-medium text-center mb-2">{message}</p>
      {nextEligibleDate && daysRemaining !== undefined && (
        <p className="text-sm mb-4 text-center">
          You can review this product again in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}
          {nextEligibleDate ? ` (after ${nextEligibleDate})` : ''}
        </p>
      )}
      <button
        onClick={() => router.push(returnUrl)}
        className="px-4 py-2 bg-myTheme-primary text-white rounded-md hover:bg-myTheme-secondary transition-colors duration-300 mt-4"
      >
        {returnLabel}
      </button>
    </div>
  );
}
