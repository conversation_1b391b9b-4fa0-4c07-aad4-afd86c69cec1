"use client";

import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertTriangle } from "lucide-react";

interface ProductReportModalProps {
    isOpen: boolean;
    onClose: () => void;
    onReportSubmitted: () => void;
    productId: string;
    productName: string;
}

const PRODUCT_REPORT_REASONS = [
    { value: 'inappropriate_content', label: 'Inappropriate Content' },
    { value: 'spam', label: 'Spam or Fake Listing' },
    { value: 'copyright', label: 'Copyright Violation' },
    { value: 'misleading_info', label: 'Misleading Information' },
    { value: 'duplicate', label: 'Duplicate Listing' },
    { value: 'closed_business', label: 'Business is Closed/Doesn\'t Exist' },
    { value: 'other', label: 'Other (please specify)' },
];

export function ProductReportModal({
    isOpen,
    onClose,
    onReportSubmitted,
    productId,
    productName,
}: ProductReportModalProps) {
    const [selectedReason, setSelectedReason] = useState('');
    const [additionalNotes, setAdditionalNotes] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!selectedReason) {
            setError('Please select a reason for reporting this product.');
            return;
        }

        // If "Other" is selected, require additional notes
        if (selectedReason === 'other' && !additionalNotes.trim()) {
            setError('Please provide additional details when selecting "Other".');
            return;
        }

        setIsSubmitting(true);
        setError(null);

        try {
            const response = await fetch(`/api/products/${productId}/report`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reason: selectedReason,
                    additionalNotes: additionalNotes.trim() || undefined,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to submit report');
            }

            setSuccess(true);
            setTimeout(() => {
                onReportSubmitted();
                resetForm();
            }, 2000);

        } catch (err) {
            console.error('Error submitting report:', err);
            setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        } finally {
            setIsSubmitting(false);
        }
    };

    const resetForm = () => {
        setSelectedReason('');
        setAdditionalNotes('');
        setError(null);
        setSuccess(false);
    };

    const handleClose = () => {
        if (!isSubmitting) {
            resetForm();
            onClose();
        }
    };

    if (success) {
        return (
            <Dialog open={isOpen} onOpenChange={handleClose}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="text-green-600">Report Submitted</DialogTitle>
                        <DialogDescription>
                            Thank you for reporting this product. Our team will review your report and take appropriate action.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-center py-4">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                        Report Product
                    </DialogTitle>
                    <DialogDescription>
                        Report "{productName}" for inappropriate content or policy violations.
                        Your report will be reviewed by our moderation team.
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit}>
                    <div className="space-y-4">
                        <div>
                            <Label className="text-base font-medium">
                                Why are you reporting this product? *
                            </Label>
                            <RadioGroup
                                value={selectedReason}
                                onValueChange={setSelectedReason}
                                className="mt-2"
                            >
                                {PRODUCT_REPORT_REASONS.map((reason) => (
                                    <div key={reason.value} className="flex items-center space-x-2">
                                        <RadioGroupItem value={reason.value} id={reason.value} />
                                        <Label htmlFor={reason.value} className="font-normal">
                                            {reason.label}
                                        </Label>
                                    </div>
                                ))}
                            </RadioGroup>
                        </div>

                        <div>
                            <Label htmlFor="additionalNotes" className="text-base font-medium">
                                Additional Details
                                {selectedReason === 'other' && <span className="text-red-500"> *</span>}
                            </Label>
                            <Textarea
                                id="additionalNotes"
                                placeholder="Please provide any additional information that would help us review this report..."
                                value={additionalNotes}
                                onChange={(e) => setAdditionalNotes(e.target.value)}
                                maxLength={1000}
                                className="mt-1 min-h-[100px]"
                                disabled={isSubmitting}
                            />
                            <div className="text-sm text-gray-500 mt-1">
                                {additionalNotes.length}/1000 characters
                            </div>
                        </div>

                        {error && (
                            <Alert variant="destructive">
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <DialogFooter className="mt-6">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleClose}
                            disabled={isSubmitting}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            variant="destructive"
                            disabled={isSubmitting || !selectedReason}
                        >
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Submitting...
                                </>
                            ) : (
                                'Submit Report'
                            )}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
