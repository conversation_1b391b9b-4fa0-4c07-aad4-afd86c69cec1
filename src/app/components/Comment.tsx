import React, { useState, useEffect, ReactNode } from "react";
import { iComment, iUser, iCommentVote, iProduct, iReview } from "../util/Interfaces";
import { sendLikeNotification } from "../util/serverFunctions";
import dayjs from "dayjs";
import { cn } from "@/lib/utils";
import { profileUrl } from "@/app/util/userHelpers";
import Link from "next/link";
import {
  ReplyIcon,
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MessageSquareIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import OptionsMenu from "./CommentOptionsMenu";
import { useAuth, useUser as useClerkUser } from "@clerk/nextjs";
import { toast } from "sonner";
import { Avatar as MantineAvatar, Tooltip } from "@mantine/core";
import OwnerComment from "./OwnerComment";
import OwnerReply from "./OwnerReply";
import { isOwnerComment } from "../util/commentHelpers";
import SignInToParticipate from "./SignInToParticipate";
import { canReplyToComment } from "@/app/util/commentPermissions";
import ReplyRestrictionIndicator from "./ReplyRestrictionIndicator";

interface CommentProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  depth: number;
  children?: ReactNode;
  clerkUserId: string;
  currentUser: iUser;
  product?: iProduct;
  review?: iReview;
  isUserAuthenticated?: boolean;
}

const MAX_VISIBLE_DEPTH = 5;
// Colors for the vertical lines indicating comment depth
const DEPTH_COLORS = [
  "border-blue-400",
  "border-green-400",
  "border-purple-400",
  "border-orange-400",
  "border-pink-400",
  "border-teal-400",
  "border-red-400",
  "border-indigo-400",
];

const Comment: React.FC<CommentProps> = ({
  comment: initialComment,
  onReply,
  onEdit,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser,
  product,
  review,
  isUserAuthenticated = false,
}) => {
  const { userId } = useAuth();
  const { user: clerkUser } = useClerkUser();
  
  // Use the same ID format that's used throughout the app (publicMetadata.id)
  const userDatabaseId = clerkUser?.publicMetadata?.id as string | undefined;
  const [comment, setComment] = useState(initialComment);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyBody, setReplyBody] = useState("");
  const [replies, setReplies] = useState<iComment[]>(comment.replies || []);
  const [showReplies, setShowReplies] = useState(depth < MAX_VISIBLE_DEPTH);
  const [upvoted, setUpvoted] = useState(false);
  const [downvoted, setDownvoted] = useState(false);
  const [voteCount, setVoteCount] = useState(
    comment.upvotes - comment.downvotes
  );

  // More robust ownership check - try multiple ID fields
  const currentUserId = userDatabaseId || clerkUserId;
  const isCommentOwner = Boolean(currentUserId && (
    currentUserId === comment.user?.clerkUserId || 
    currentUserId === comment.user?.id || 
    currentUserId === comment.userId ||
    userId === comment.user?.clerkUserId ||
    userId === comment.user?.id ||
    userId === comment.userId
  ));

  // Debug logging (remove this after testing)
  if (process.env.NODE_ENV === 'development') {
    console.log('Comment ownership check:', {
      userId,
      clerkUserId,
      commentUserId: comment.user?.id,
      commentClerkUserId: comment.user?.clerkUserId,
      commentUserId2: comment.userId,
      isCommentOwner,
      isDeleted: comment.isDeleted,
      isEditing
    });
  }

  // Calculate permissions for replying to this comment with error handling
  let permission: { allowed: boolean; restrictionType: "not_authenticated" | "not_owner_comment" | "not_reviewer" | "no_restriction"; message: string } = { 
    allowed: false, 
    restrictionType: 'not_authenticated', 
    message: 'Unable to determine permissions' 
  };
  try {
    const permissionResult = canReplyToComment(
      userDatabaseId || clerkUserId, // Use database ID first, fallback to clerk ID
      comment,
      comment.review as iReview,
      product
    );
    permission = {
      allowed: permissionResult.allowed,
      restrictionType: permissionResult.restrictionType || 'not_authenticated',
      message: permissionResult.message || 'Permission check completed'
    };
  } catch (error) {
    console.warn('Error calculating reply permissions for comment:', comment.id, error);
    // Default to not allowing replies if permission calculation fails
    permission = { 
      allowed: false, 
      restrictionType: 'not_authenticated', 
      message: 'Unable to determine reply permissions' 
    };
  }

  // Get border color for the current depth
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];

  useEffect(() => {
    setComment(initialComment);
    setEditedBody(initialComment.body);
    setReplies(initialComment.replies || []);
    setVoteCount(initialComment.upvotes - initialComment.downvotes);

    // Initialize vote states based on user's previous votes
    if (userId && initialComment.votes) {
      const userVote = initialComment.votes.find(
        (vote: iCommentVote) => vote.clerkUserId === userId
      );

      if (userVote) {
        setUpvoted(userVote.voteType === "UP");
        setDownvoted(userVote.voteType === "DOWN");
      } else {
        // Reset vote states if no vote found
        setUpvoted(false);
        setDownvoted(false);
      }
    } else {
      // Reset vote states if no votes array or no user ID
      setUpvoted(false);
      setDownvoted(false);
    }
  }, [initialComment, userId]);


  const handleReply = async () => {
    if (comment.id && permission.allowed) {

      const newReply: iComment = {
        id: Date.now().toString(),
        body: replyBody,
        user: currentUser,
        createdDate: new Date(),
        review: comment.review,
        parentId: comment.id,
        userId: userId as string,
        isDeleted: false,
        reviewId: comment.reviewId,
        replies: [],
        upvotes: 0,
        downvotes: 0,
      };

      // Optimistically update both local and parent comment state
      const updatedReplies = [...replies, newReply];
      setReplies(updatedReplies);

      // Update the parent comment state to include the new reply
      const updatedComment = {
        ...comment,
        replies: updatedReplies,
      };
      setComment(updatedComment);

      setIsReplying(false);
      setReplyBody("");
      setShowReplies(true);

      try {
        // Call the API
        await onReply(comment.id, replyBody);
      } catch (error) {
        // If the API call fails, revert both states
        setReplies(replies);
        setComment(comment);
        setIsReplying(true);
        setReplyBody(replyBody);
        toast.error("Failed to add reply. Please try again.");
      }
    }
  };

  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (comment.id && isCommentOwner) {
      await onEdit(comment.id, editedBody);
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
    }
  };

  const handleDelete = async () => {
    if (
      comment.id &&
      isCommentOwner &&
      window.confirm("Are you sure you want to delete this comment?")
    ) {
      await onDelete(comment.id);
      const deletedComment = {
        ...comment,
        isDeleted: true,
        body: "This comment has been deleted",
        user: {
          ...(comment.user || {}),
          id: comment.user?.id || comment.userId,
          userName: "Deleted User",
          avatar: "/deleted-user.svg",
        },
      };
      setComment(deletedComment as iComment);
    }
  };

  const toggleReplies = () => {
    setShowReplies(!showReplies);
  };

  const handleVote = async (voteType: "UP" | "DOWN") => {
    if (isCommentOwner) {
      toast.info("You cannot vote on your own comment.");
      return;
    }
    if (!userId) {
      toast.error("Please sign in to vote");
      return;
    }

    // Get existing vote if any
    const existingVote = comment.votes?.find(
      (vote: iCommentVote) => vote.clerkUserId === userId
    );
    const isRemovingVote = existingVote?.voteType === voteType;

    // Optimistic update
    const optimisticComment = { ...comment };
    if (!optimisticComment.votes) {
      optimisticComment.votes = [];
    }

    // Remove any existing vote by this user
    optimisticComment.votes = optimisticComment.votes.filter(
      (vote) => vote.clerkUserId !== userId
    );

    if (isRemovingVote) {
      // If clicking the same vote type, remove the vote
      if (voteType === "UP") {
        optimisticComment.upvotes--;
        setUpvoted(false);
      } else {
        optimisticComment.downvotes--;
        setDownvoted(false);
      }
    } else {
      // Add new vote or change vote
      if (voteType === "UP") {
        // If changing from downvote to upvote
        if (existingVote?.voteType === "DOWN") {
          optimisticComment.downvotes--;
          optimisticComment.upvotes++;
        } else {
          // New upvote
          optimisticComment.upvotes++;
        }
        setUpvoted(true);
        setDownvoted(false);
        optimisticComment.votes.push({
          id: Date.now().toString(),
          commentId: comment.id!,
          userId,
          clerkUserId: userId,
          voteType: "UP",
          createdAt: new Date(),
          user: currentUser,
        });
      } else {
        // If changing from upvote to downvote
        if (existingVote?.voteType === "UP") {
          optimisticComment.upvotes--;
          optimisticComment.downvotes++;
        } else {
          // New downvote
          optimisticComment.downvotes++;
        }
        setUpvoted(false);
        setDownvoted(true);
        optimisticComment.votes.push({
          id: Date.now().toString(),
          commentId: comment.id!,
          userId,
          clerkUserId: userId,
          voteType: "DOWN",
          createdAt: new Date(),
          user: currentUser,
        });
      }
    }

    setComment(optimisticComment);
    setVoteCount(optimisticComment.upvotes - optimisticComment.downvotes);

    try {
      // Send vote to server
      const response = await fetch("/api/vote/comment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
        body: JSON.stringify({
          commentId: comment.id,
          voteType,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Notification is now handled directly in the vote API endpoint

        // Update with server data
        const updatedComment = data.data.comment;

        if (updatedComment) {
          setComment(updatedComment);
          setVoteCount(updatedComment.upvotes - updatedComment.downvotes);

          // Update vote states based on the user's vote in the updated comment
          if (updatedComment.votes) {
            const userVote = updatedComment.votes.find(
              (vote: iCommentVote) => vote.clerkUserId === userId
            );
            setUpvoted(userVote?.voteType === "UP");
            setDownvoted(userVote?.voteType === "DOWN");
          }
        }
      } else {
        // Revert optimistic update on failure
        setComment(comment);
        setVoteCount(comment.upvotes - comment.downvotes);
        if (comment.votes) {
          const userVote = comment.votes.find(
            (vote: iCommentVote) => vote.userId === userId
          );
          setUpvoted(userVote?.voteType === "UP");
          setDownvoted(userVote?.voteType === "DOWN");
        }
        toast.error("Failed to process vote");
      }
    } catch (error) {
      // Revert optimistic update on error
      setComment(comment);
      setVoteCount(comment.upvotes - comment.downvotes);
      if (comment.votes) {
        const userVote = comment.votes.find(
          (vote: iCommentVote) => vote.userId === userId
        );
        setUpvoted(userVote?.voteType === "UP");
        setDownvoted(userVote?.voteType === "DOWN");
      }
      toast.error("Failed to process vote");
    }
  };

  const handleUpvote = () => handleVote("UP");
  const handleDownvote = () => handleVote("DOWN");

  const getUpvoters = () => {
    if (!comment.votes) return [];

    // Get all upvoters and sort by newest first
    let upvoters = comment.votes
      .filter((vote) => vote.voteType === "UP")
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

    // If current user upvoted, put them last (will be most visible)
    if (userId) {
      const currentUserVoteIndex = upvoters.findIndex(
        (vote) => vote.clerkUserId === userId
      );
      if (currentUserVoteIndex >= 0) {
        const currentUserVote = upvoters.splice(currentUserVoteIndex, 1)[0];
        upvoters.push(currentUserVote);
      }
    }

    // Reverse the array so newest votes (including current user) appear on top
    return upvoters.reverse();
  };

  const renderReplies = () => {
    if (!replies || replies.length === 0) return null;
    return (
      <div className="reply-thread mt-2 relative">
        {/* Visual connection line */}
        <div className="absolute left-6 top-0 w-0.5 h-full bg-gray-200"></div>
        <div className="ml-6 pl-4 space-y-1">
        {replies.map((reply) => {
          try {
            // Check if this is an owner reply
            const isOwner = Boolean(product && isOwnerComment(reply, product));

            if (isOwner) {
              return (
                <OwnerReply
                  clerkUserId={clerkUserId}
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onDelete={onDelete}
                  depth={depth + 1}
                  currentUser={currentUser}
                  productName={product?.name || 'Unknown Product'}
                  review={review}
                  product={product}
                  isUserAuthenticated={isUserAuthenticated}
                />
              );
            } else {
              return (
                <Comment
                  clerkUserId={clerkUserId}
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  depth={depth + 1}
                  currentUser={currentUser}
                  product={product}
                  review={review}
                  isUserAuthenticated={isUserAuthenticated}
                />
              );
            }
          } catch (error) {
            // Handle legacy replies that might cause rendering issues
            console.warn('Error rendering reply:', reply.id, error);
            return (
              <div key={reply.id} className="p-2 bg-gray-50 rounded border border-gray-200 text-sm text-gray-500">
                <span className="italic">This comment could not be displayed properly.</span>
              </div>
            );
          }
        })}
        </div>
      </div>
    );
  };

  // Check if this is an owner comment with error handling
  let isOwner: boolean = false;
  try {
    isOwner = Boolean(product && isOwnerComment(comment, product));
  } catch (error) {
    console.warn('Error checking if comment is from owner:', comment.id, error);
    isOwner = false;
  }

  // If this is an owner comment and it's a top-level comment (not a reply), use OwnerComment
  if (isOwner && depth === 0) {
    try {
      return (
        <OwnerComment
          comment={comment}
          onDelete={onDelete}
          depth={depth}
          clerkUserId={clerkUserId}
          currentUser={currentUser}
          productName={product?.name || 'Unknown Product'}
          review={review}
        />
      );
    } catch (error) {
      console.warn('Error rendering OwnerComment:', comment.id, error);
      // Fall through to regular comment rendering
    }
  }

  // If this is an owner comment and it's a reply, use OwnerReply
  if (isOwner && depth > 0) {
    try {
      return (
        <OwnerReply
          comment={comment}
          onReply={onReply}
          onDelete={onDelete}
          depth={depth}
          clerkUserId={clerkUserId}
          currentUser={currentUser}
          productName={product?.name || 'Unknown Product'}
          review={review}
          product={product}
          isUserAuthenticated={isUserAuthenticated}
        />
      );
    } catch (error) {
      console.warn('Error rendering OwnerReply:', comment.id, error);
      // Fall through to regular comment rendering
    }
  }

  // Otherwise, use the regular comment component
  return (
    <div
      id={comment.id}
      className={`w-full ${depth === 0 ? "border-b border-gray-200 pb-2" : "mt-1"}`}
    >
      <div className="flex">
        {/* Left border line for depth indication */}
        {depth > 0 && (
          <div className="flex flex-col items-center mr-2">
            <div className={`w-0.5 h-full ${depthColor}`}></div>
          </div>
        )}

        {/* Main comment content - now uses full width */}
        <div className="flex-1">
          <div className="bg-white rounded-md px-3 py-2">
            <div className="flex items-center text-xs text-gray-500 mb-1">
              <Avatar className="w-5 h-5 mr-1">
                <AvatarImage
                  src={comment.user?.avatar || "/default-avatar.png"}
                  alt={`${comment.user?.firstName} ${comment.user?.lastName}`}
                />
                <AvatarFallback>
                  {comment.user?.firstName?.charAt(0)}
                  {comment.user?.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <Link
                href={profileUrl(comment.user!)}
                className="font-semibold text-gray-900 hover:underline mr-1"
              >
                @{comment.user?.userName}
              </Link>
              <span className="mx-1">•</span>
              <span className="text-gray-500">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>

              {!comment.isDeleted && isCommentOwner && !isEditing && (
                <div className="ml-auto">
                  <OptionsMenu
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setIsEditing={setIsEditing}
                  />
                </div>
              )}
            </div>

            <div className="text-sm leading-relaxed break-words">
              {isEditing ? (
                <Textarea
                  value={editedBody}
                  onChange={(e) => setEditedBody(e.target.value)}
                  className="w-full p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[100px]"
                />
              ) : comment.isDeleted ? (
                <span className="italic text-gray-400 text-sm">
                  {comment.body}
                </span>
              ) : showFullComment || comment.body.length <= 100 ? (
                <div className="whitespace-pre-line">{comment.body}</div>
              ) : (
                <>
                  <div className="whitespace-pre-line">
                    {comment.body.slice(0, 100)}...
                  </div>
                  <button
                    onClick={() => setShowFullComment(true)}
                    className="text-gray-500 hover:text-blue-500 text-xs font-medium mt-1"
                  >
                    Read more
                  </button>
                </>
              )}
            </div>

            {/* Action buttons and voting section */}
            <div className="mt-2 flex items-center justify-between">
              <div className="flex flex-wrap gap-2 text-xs">
                {!isEditing && permission.allowed && (
                  <div className="mt-4">
                    <Button
                      onClick={() => setIsReplying(!isReplying)}
                      size="sm"
                      variant="outline"
                      className="border border-blue-300 text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-md text-sm font-medium"
                    >
                      <ReplyIcon className="w-4 h-4 mr-1" />
                      Reply
                    </Button>
                  </div>
                )}
                {!isEditing && !permission.allowed && (
                  <ReplyRestrictionIndicator permission={permission} />
                )}
              </div>
              
              {/* Voting buttons moved to bottom right */}
              {!comment.isDeleted && (
                <div className="flex items-center gap-1">
                  {isUserAuthenticated ? (
                    <>
                      <button
                        onClick={handleUpvote}
                        disabled={isCommentOwner}
                        className={`p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
                        title={isCommentOwner ? "You cannot vote on your own comment" : "Upvote"}
                      >
                        <ArrowUpIcon className={`w-4 h-4 ${upvoted ? "text-green-500" : "text-gray-400"}`} />
                      </button>
                      <span
                        className={`text-xs font-medium min-w-[20px] text-center ${upvoted ? "text-green-500" : downvoted ? "text-red-500" : "text-gray-600"}`}
                      >
                        {voteCount || 0}
                      </span>
                      <button
                        onClick={handleDownvote}
                        disabled={isCommentOwner}
                        className={`p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
                        title={isCommentOwner ? "You cannot vote on your own comment" : "Downvote"}
                      >
                        <ArrowDownIcon className={`w-4 h-4 ${downvoted ? "text-red-500" : "text-gray-400"}`} />
                      </button>
                    </>
                  ) : (
                    <div className="flex items-center gap-1">
                      <div className="p-1 text-gray-300">
                        <ArrowUpIcon className="w-4 h-4" />
                      </div>
                      <span className="text-xs font-medium text-gray-600 min-w-[20px] text-center">
                        {voteCount || 0}
                      </span>
                      <div className="p-1 text-gray-300">
                        <ArrowDownIcon className="w-4 h-4" />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Show sign-in prompt for reply if not authenticated */}
            {!isUserAuthenticated && !comment.isDeleted && !isCommentOwner && !isEditing && (
              <div className="mt-2">
                <SignInToParticipate action="reply" compact />
              </div>
            )}

            {isReplying && (
              <div className="mt-2">
                <Textarea
                  value={replyBody}
                  onChange={(e) => setReplyBody(e.target.value)}
                  className="w-full p-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[80px]"
                  placeholder="What are your thoughts?"
                />
                <div className="mt-2 flex justify-end">
                  <Button
                    className="bg-blue-500 text-white font-medium text-xs px-3 py-1 h-7 rounded hover:bg-blue-600 transition-colors disabled:opacity-50"
                    onClick={handleReply}
                    disabled={!replyBody.trim()}
                  >
                    Reply
                  </Button>
                </div>
              </div>
            )}

            {comment.votes &&
              comment.votes.some((vote) => vote.voteType === "UP") && (
                <Tooltip.Group openDelay={300} closeDelay={100}>
                  <div className="flex items-center justify-end gap-2 mt-2">
                    <span className="text-xs text-gray-500">Liked by</span>
                    <MantineAvatar.Group spacing="sm">
                      {getUpvoters()
                        .slice(0, 5)
                        .map((vote) => (
                          <Tooltip
                            key={vote.id}
                            label={vote.user?.userName || "Unknown user"}
                            withArrow
                          >
                            <MantineAvatar
                              src={vote.user?.avatar || "/default-avatar.png"}
                              radius="xl"
                              size="sm"
                              alt={vote.user?.userName || "Unknown user"}
                            >
                              {vote.user?.firstName?.[0]}
                              {vote.user?.lastName?.[0]}
                            </MantineAvatar>
                          </Tooltip>
                        ))}
                      {getUpvoters().length > 5 && (
                        <Tooltip
                          label={`${getUpvoters().length - 5} more ${getUpvoters().length - 5 === 1 ? "person" : "people"} liked this`}
                          withArrow
                        >
                          <MantineAvatar radius="xl" size="sm">
                            +{getUpvoters().length - 5}
                          </MantineAvatar>
                        </Tooltip>
                      )}
                    </MantineAvatar.Group>
                  </div>
                </Tooltip.Group>
              )}
          </div>

          {replies.length > 0 && (
            <div className="mt-2 mb-1">
              <button
                className="text-xs text-gray-500 hover:text-blue-500 flex items-center"
                onClick={toggleReplies}
              >
                {showReplies ? (
                  <ChevronUpIcon className="w-3 h-3 mr-1" />
                ) : (
                  <ChevronDownIcon className="w-3 h-3 mr-1" />
                )}
                {showReplies ? "Hide" : "Show"} {replies.length}{" "}
                {replies.length === 1 ? "reply" : "replies"}
              </button>
              {showReplies && renderReplies()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Comment;
