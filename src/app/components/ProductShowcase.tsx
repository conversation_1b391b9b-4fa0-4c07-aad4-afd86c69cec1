import { iProduct } from "../util/Interfaces";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ImageModal from "./ImageModal";

interface ProductShowcaseProps {
    product: iProduct;
}

// Function to normalize and validate image URLs from various sources
const normalizeImageUrl = (url: string): string => {
    try {
        const urlObj = new URL(url);
        
        // Handle Unsplash URLs
        if (url.includes('unsplash.com')) {
            if (!urlObj.searchParams.has('fm')) {
                urlObj.searchParams.set('fm', 'jpg');
            }
            if (!urlObj.searchParams.has('q')) {
                urlObj.searchParams.set('q', '80');
            }
            if (!urlObj.searchParams.has('w')) {
                urlObj.searchParams.set('w', '400');
            }
            if (!urlObj.searchParams.has('h')) {
                urlObj.searchParams.set('h', '400');
            }
            return urlObj.toString();
        }
        
        // Handle Pexels URLs
        if (url.includes('pexels.com')) {
            // Pexels URLs work well as-is, but we can add size optimization
            if (url.includes('/photos/') && !url.includes('?')) {
                return `${url}?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1`;
            }
            return url;
        }
        
        // Handle Pixabay URLs
        if (url.includes('pixabay.com')) {
            // Pixabay URLs usually work well as-is
            return url;
        }
        
        // Handle Cloudinary URLs (already optimized usually)
        if (url.includes('cloudinary.com')) {
            return url;
        }
        
        // Handle AWS S3 URLs
        if (url.includes('amazonaws.com') || url.includes('s3.')) {
            return url;
        }
        
        // Handle Imgur URLs
        if (url.includes('imgur.com')) {
            // Ensure we're using the direct image URL
            if (!url.includes('i.imgur.com')) {
                return url.replace('imgur.com', 'i.imgur.com');
            }
            return url;
        }
        
        // For other URLs, return as-is
        return url;
        
    } catch (error) {
        console.warn('Invalid image URL:', url);
        return url; // Return original URL if parsing fails
    }
};

// Function to check if an image URL is likely to work
const isReliableImageSource = (url: string): boolean => {
    const reliableDomains = [
        'cloudinary.com',
        'amazonaws.com',
        's3.',
        'imgur.com',
        'unsplash.com',
        'pexels.com',
        'pixabay.com',
        'storage.googleapis.com',
        'blob.core.windows.net'
    ];
    
    const problematicDomains = [
        'instagram.com',
        'facebook.com',
        'twitter.com',
        'x.com',
        'linkedin.com',
        'drive.google.com',
        'dropbox.com',
        'onedrive.com'
    ];
    
    const lowerUrl = url.toLowerCase();
    
    // Check for problematic domains
    if (problematicDomains.some(domain => lowerUrl.includes(domain))) {
        return false;
    }
    
    // Check for reliable domains
    if (reliableDomains.some(domain => lowerUrl.includes(domain))) {
        return true;
    }
    
    // Check if URL has a proper image extension
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.svg'];
    if (imageExtensions.some(ext => lowerUrl.includes(ext))) {
        return true;
    }
    
    // Default to potentially unreliable
    return false;
};
const convertToEmbedUrl = (url: string): string => {
    // YouTube patterns
    const youtubeWatchRegex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/;
    const youtubeShortRegex = /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]+)/;
    const youtubeEmbedRegex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/;
    
    // TikTok patterns
    const tiktokRegex = /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/@[^\/]+\/video\/(\d+)/;
    const tiktokShortRegex = /(?:https?:\/\/)?(?:vm\.)?tiktok\.com\/([a-zA-Z0-9]+)/;
    const tiktokEmbedRegex = /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/embed\/v2\/(\d+)/;
    
    // Vimeo patterns
    const vimeoRegex = /(?:https?:\/\/)?(?:www\.)?vimeo\.com\/(\d+)/;
    const vimeoEmbedRegex = /(?:https?:\/\/)?player\.vimeo\.com\/video\/(\d+)/;
    
    // Check YouTube URLs
    if (youtubeEmbedRegex.test(url)) {
        return url;
    }
    
    const youtubeWatchMatch = url.match(youtubeWatchRegex);
    if (youtubeWatchMatch) {
        return `https://www.youtube.com/embed/${youtubeWatchMatch[1]}`;
    }
    
    const youtubeShortMatch = url.match(youtubeShortRegex);
    if (youtubeShortMatch) {
        return `https://www.youtube.com/embed/${youtubeShortMatch[1]}`;
    }
    
    // Check TikTok URLs
    if (tiktokEmbedRegex.test(url)) {
        return url;
    }
    
    const tiktokMatch = url.match(tiktokRegex);
    if (tiktokMatch) {
        return `https://www.tiktok.com/embed/v2/${tiktokMatch[1]}`;
    }
    
    // Handle TikTok short URLs (these are trickier and might need the full URL)
    const tiktokShortMatch = url.match(tiktokShortRegex);
    if (tiktokShortMatch) {
        // For TikTok short URLs, we'll return the original URL with a note
        // These typically redirect to the full URL, but embedding might be limited
        console.warn('TikTok short URL detected. Full video ID needed for embedding:', url);
        return url; // Return original URL - user might need to use the full TikTok URL
    }
    
    // Check Vimeo URLs
    if (vimeoEmbedRegex.test(url)) {
        return url;
    }
    
    const vimeoMatch = url.match(vimeoRegex);
    if (vimeoMatch) {
        return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
    }
    
    // For other video platforms or already properly formatted URLs, return as-is
    return url;
};

const ProductShowcase = ({ product }: ProductShowcaseProps) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);

    const hasContent = product.images.length > 0 ||
        product.videos.length > 0 ||
        product.links.length > 0 ||
        product.website.length > 0;

    if (!hasContent) return null;

    return (
        <>
            <div className="w-full max-w-4xl mx-auto mt-4 sm:mt-8 p-3 sm:p-4 bg-white rounded-lg shadow-md">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Product Showcase</h2>

                {/* Images Section */}
                {product.images.length > 0 && (
                    <div className="mb-6 sm:mb-8">
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Images</h3>
                        <div className="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 gap-1 sm:gap-2">
                            {product.images.map((image, index) => {
                                const normalizedUrl = normalizeImageUrl(image);
                                const isReliable = isReliableImageSource(image);
                                
                                return (
                                    <div
                                        key={index}
                                        className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer shadow-sm hover:shadow-md transition-shadow duration-200 max-w-[80px] sm:max-w-[90px] mx-auto bg-gray-100 ${!isReliable ? 'ring-2 ring-yellow-300' : ''}`}
                                        onClick={() => setSelectedImage(image)}
                                        title={!isReliable ? 'This image source may not load reliably' : ''}
                                    >
                                        <img
                                            src={normalizedUrl}
                                            alt={`${product.name} image ${index + 1}`}
                                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                                            loading="lazy"
                                            onError={(e) => {
                                                console.error('Failed to load image:', normalizedUrl);
                                                const target = e.target as HTMLImageElement;
                                                target.style.display = 'none';
                                                const container = target.parentElement;
                                                if (container) {
                                                    container.innerHTML = `
                                                        <div class="w-full h-full flex flex-col items-center justify-center bg-gray-200 text-gray-500 p-2">
                                                            <svg class="w-4 h-4 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                            </svg>
                                                            <span class="text-xs">Failed</span>
                                                        </div>
                                                    `;
                                                }
                                            }}
                                        />
                                        {!isReliable && (
                                            <div className="absolute top-1 right-1">
                                                <div className="w-3 h-3 bg-yellow-400 rounded-full" title="Potentially unreliable source"></div>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                        
                    </div>
                )}

                {/* Videos Section */}
                {product.videos.length > 0 && (
                    <div className="mb-6 sm:mb-8">
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Videos</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                            {product.videos.map((video, index) => {
                                const embedUrl = convertToEmbedUrl(video);
                                const isTikTok = video.includes('tiktok.com');
                                const isYouTube = video.includes('youtube.com') || video.includes('youtu.be');
                                const isVimeo = video.includes('vimeo.com');
                                
                                // For TikTok, show a custom card instead of trying to embed
                                if (isTikTok) {
                                    return (
                                        <div key={index} className="relative aspect-video rounded-lg overflow-hidden shadow-sm bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center">
                                            <div className="text-center text-white p-6">
                                                <div className="mb-4">
                                                    <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z"/>
                                                    </svg>
                                                    <p className="text-lg font-semibold">TikTok Video</p>
                                                    <p className="text-sm opacity-90">Click to watch on TikTok</p>
                                                </div>
                                                <Link
                                                    href={video}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors font-medium"
                                                >
                                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                    </svg>
                                                    Watch Video
                                                </Link>
                                            </div>
                                        </div>
                                    );
                                }
                                
                                // For other platforms, try to embed normally
                                return (
                                    <div key={index} className="relative aspect-video rounded-lg overflow-hidden shadow-sm bg-gray-100">
                                        <iframe
                                            src={embedUrl}
                                            className="absolute inset-0 w-full h-full"
                                            allowFullScreen
                                            title={`${product.name} video ${index + 1}`}
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            onError={() => {
                                                console.error('Failed to load video:', embedUrl);
                                            }}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                        
                        {/* Info note about video platforms */}
                        <div className="mt-3 text-xs text-gray-500">
                            <p>YouTube and Vimeo videos embed directly. TikTok videos open on TikTok due to platform restrictions.</p>
                        </div>
                    </div>
                )}

                {/* Links Section */}
                {(product.links.length > 0 || product.website.length > 0) && (
                    <div>
                        <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Related Links</h3>
                        <div className="space-y-2 sm:space-y-3">
                            {product.website.map((site, index) => (
                                <div key={`website-${index}`} className="flex items-center">
                                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                    </svg>
                                    <Link
                                        href={site.startsWith('http') ? site : `https://${site}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 hover:underline text-sm sm:text-base break-all"
                                    >
                                        {site}
                                    </Link>
                                </div>
                            ))}
                            {product.links.map((link, index) => (
                                <div key={`link-${index}`} className="flex items-center">
                                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                    </svg>
                                    <Link
                                        href={link.startsWith('http') ? link : `https://${link}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 hover:underline text-sm sm:text-base break-all"
                                    >
                                        {link}
                                    </Link>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Image Modal */}
            {selectedImage && (
                <ImageModal
                    src={selectedImage}
                    alt={product.name}
                    onClose={() => setSelectedImage(null)}
                />
            )}
        </>
    );
};

export default ProductShowcase; 