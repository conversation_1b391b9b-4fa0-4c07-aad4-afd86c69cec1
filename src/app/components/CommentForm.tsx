"use client";
import React, { useState } from "react";
import { useAtom } from "jotai";
import { currentUserAtom } from "@/app/store/store";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { iProduct, iUser } from "../util/Interfaces";
import { isCurrentUserProductOwner } from "../util/commentHelpers";
import { CheckCircleIcon, SendHorizontalIcon } from "lucide-react";

interface Props {
  isOpen: boolean;
  onClose: (isOpen: any) => void;
  onSubmit: (textAreaValue: string) => void;
  product?: iProduct;
}

const CommentForm = ({ isOpen, onSubmit, product }: Props) => {
  const [textAreaValue, setTextAreaValue] = useState("");
  const [currentUser] = useAtom(currentUserAtom);

  // Check if the current user is the owner of the product
  const isOwner =
    currentUser && product && isCurrentUserProductOwner(currentUser, product);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onSubmit(textAreaValue);
    setTextAreaValue("");
  };

  return (
    <div
      className={`${isOpen ? "block" : "hidden"} w-full rounded-lg mt-6 mb-8`}
    >
      <div className="w-full p-0">
        <form onSubmit={handleSubmit} className="flex items-start space-x-3">
          <Avatar
            className={`w-9 h-9 flex-shrink-0 mt-1 ${isOwner ? "ring-2 ring-amber-400" : ""}`}
          >
            <AvatarImage
              src={currentUser?.avatar || "/default-avatar.png"}
              alt={currentUser?.firstName || "Guest"}
            />
            <AvatarFallback>
              {currentUser?.firstName?.charAt(0) || "G"}
            </AvatarFallback>
          </Avatar>

          <div className={
            "flex-grow flex items-center border rounded-xl shadow-sm " +
            (isOwner
              ? "border-amber-200 bg-amber-50/50 focus-within:border-amber-300 focus-within:ring-1 focus-within:ring-amber-300"
              : "border-gray-200 bg-gray-50 focus-within:border-gray-300 focus-within:ring-1 focus-within:ring-gray-300")
          }>
            <textarea
              className={`w-full p-3 text-[15px] leading-relaxed resize-y bg-transparent focus:outline-none placeholder-gray-500 overflow-y-hidden`}
              placeholder={
                isOwner
                  ? "Share your response as the business owner..."
                  : currentUser?.firstName
                    ? `Comment as ${currentUser.firstName}...`
                    : "Write a comment..."
              }
              name="body"
              value={textAreaValue}
              onChange={(e) => {
                setTextAreaValue(e.target.value);
                // Auto-resize logic
                e.target.style.height = 'auto'; // Reset height
                const maxHeight = 150; // Define a max height in pixels
                const scrollHeight = e.target.scrollHeight;
                e.target.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
                if (scrollHeight > maxHeight) {
                  e.target.style.overflowY = 'auto'; // Show scrollbar if content exceeds max height
                } else {
                  e.target.style.overflowY = 'hidden'; // Hide scrollbar if content is less than max height
                }
              }}
              rows={1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e as any);
                }
              }}
            ></textarea>
            <button
              type="submit"
              className={`p-2 text-gray-500 hover:text-amber-500 disabled:opacity-50 disabled:cursor-not-allowed mr-2`}
              disabled={!textAreaValue.trim()}
              aria-label={isOwner ? "Post Response" : "Post Comment"}
            >
              <SendHorizontalIcon className="w-5 h-5" />
            </button>
          </div>
        </form>
        {
          isOwner && (
            <div className="flex items-center text-xs px-2 py-1 mt-2 ml-12 rounded-full owner-badge bg-amber-400 text-amber-900 border border-amber-300 shadow-sm w-fit">
              <CheckCircleIcon className="w-3 h-3 mr-1" />
              <span className="font-medium">Verified Owner</span>
            </div>
          )
        }
      </div >
    </div >
  );
};

export default CommentForm;
