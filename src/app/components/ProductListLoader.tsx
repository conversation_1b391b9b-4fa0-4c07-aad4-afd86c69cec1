"use client";
import { iProduct } from "@/app/util/Interfaces";
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "../util/serverFunctions";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useEffect } from "react";

const ProductListLoader = () => {
  const [_, setAllProducts] = useAtom(allProductsStore);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["allProducts"],
    queryFn: () => getProducts(),
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (!isLoading && !isError && data?.success && data?.data) {
      setAllProducts(data.data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isLoading, isError]);

  if (isLoading) {
    return (
      <div className="flex font-extralight text-xs justify-center bg-transparent">
        Loading search engine...
      </div>
    );
  }
  if (isError) {
    return (
      <div className="text-red-500">
        {error instanceof Error ? error.message : "Failed to load products"}
      </div>
    );
  }

  if (data && !data.success) {
    return (
      <div className="text-red-500">
        {data.error || "Failed to load products"}
      </div>
    );
  }

  return <></>;
};
export default ProductListLoader;
