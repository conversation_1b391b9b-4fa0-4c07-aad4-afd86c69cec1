import React from 'react';
import ProductCard from '../ProductCard';
import { iProduct } from '../../util/Interfaces';

interface ProductCardSectionProps {
    product: iProduct;
    productCardOptions: any;
    userId: string | null;
}

const ProductCardSection: React.FC<ProductCardSectionProps> = ({
    product,
    productCardOptions,
    userId
}) => {
    return (
        <div className="flex justify-center items-center mb-4">
            <ProductCard
                options={productCardOptions}
                product={product}
                currentUserId={userId}
            />
        </div>
    );
};

export default ProductCardSection; 