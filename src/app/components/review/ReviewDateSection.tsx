import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Label } from '@/components/ui/label';

interface ReviewDateSectionProps {
    startDate: Date;
    setStartDate: (date: Date) => void;
}

const ReviewDateSection: React.FC<ReviewDateSectionProps> = ({
    startDate,
    setStartDate,
}) => {
    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                Date of Experience
            </h2>
            <div>
                <Label htmlFor="dateItHappened" className="text-myTheme-primary font-medium">Date *</Label>
                <DatePicker
                    id="dateItHappened"
                    name="dateItHappened"
                    selected={startDate}
                    onChange={(date) => setStartDate(date!)}
                    className="mt-2 w-full px-4 py-2 border border-gray-200 rounded-md bg-white focus:ring-2 focus:ring-myTheme-primary focus:border-transparent"
                />
            </div>
        </div>
    );
};

export default ReviewDateSection; 