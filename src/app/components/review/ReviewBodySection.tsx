import React from 'react';
import Editor from '../Editor';
import { Label } from '@/components/ui/label';

interface ReviewBodySectionProps {
    onEditorValue: (value: string) => void;
}

const ReviewBodySection: React.FC<ReviewBodySectionProps> = ({ onEditorValue }) => {
    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                Your Review
            </h2>
            <div>
                <Label htmlFor="body" className="text-myTheme-primary font-medium">Review Body *</Label>
                <div className="mt-2">
                    <Editor onEditorValue={onEditorValue} />
                </div>
            </div>
        </div>
    );
};

export default ReviewBodySection;