import React from 'react';
import MultiFileUpload from '../fileUpload/MultiFileUpload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReviewMediaSectionProps {
    handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    setLinksArray: React.Dispatch<React.SetStateAction<string[]>>;
    setAllUploaded: React.Dispatch<React.SetStateAction<boolean>>;
    allUploaded: boolean;
}

const ReviewMediaSection: React.FC<ReviewMediaSectionProps> = ({
    handleChange,
    setLinksArray,
    setAllUploaded,
    allUploaded
}) => {
    return (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Media (Optional)
            </h2>
            <div className="space-y-6">
                <div>
                    <Label htmlFor="videoUrl" className="text-myTheme-primary font-medium">Video Link</Label>
                    <Input
                        id="videoUrl"
                        name="videoUrl"
                        type="url"
                        placeholder="https://youtube.com/watch?v=..."
                        onChange={handleChange}
                        className="mt-2 border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                </div>
                <div>
                    <Label className="text-myTheme-primary font-medium">Add Photos</Label>
                    {!allUploaded && (
                        <Alert className="mt-2 mb-2 bg-amber-50 border-amber-200 text-amber-800">
                            <AlertDescription>
                                Please upload your images before submitting the review. The submit button will be disabled until all images are uploaded.
                            </AlertDescription>
                        </Alert>
                    )}
                    <div className="mt-2">
                        <MultiFileUpload
                            setLinksArray={setLinksArray}
                            setAllUploaded={setAllUploaded}
                            allUploaded={allUploaded}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReviewMediaSection;