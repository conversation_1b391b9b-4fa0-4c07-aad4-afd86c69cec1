import React from 'react';
import { InteractiveRating } from '../RatingSystem';

interface ReviewRatingSectionProps {
    rating: number;
    ratingChanged: (newRating: number) => void;
}

const ReviewRatingSection: React.FC<ReviewRatingSectionProps> = ({ rating, ratingChanged }) => {
    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                Your Rating
            </h2>
            <div className="flex items-center space-x-2">
                <InteractiveRating
                    name="rating"
                    rating={rating}
                    onRatingChange={ratingChanged}
                    size="lg"
                />
                <span className="font-semibold text-myTheme-primary">{rating} out of 5</span>
            </div>
        </div>
    );
};

export default ReviewRatingSection;