import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface ReviewTitleSectionProps {
    handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const ReviewTitleSection: React.FC<ReviewTitleSectionProps> = ({ handleChange }) => {
    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                Review Title
            </h2>
            <div>
                <Label htmlFor="title" className="text-myTheme-primary font-medium">Title *</Label>
                <Input
                    id="title"
                    name="title"
                    onChange={handleChange}
                    required
                    className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
                    placeholder="e.g. Best experience ever!"
                />
            </div>
        </div>
    );
};

export default ReviewTitleSection;