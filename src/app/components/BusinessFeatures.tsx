"use client";

import React from "react";
import {
  Star,
  BarChart3,
  MessageSquare,
  Shield,
  TrendingUp,
  Award,
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: string;
}

const BusinessFeatures: React.FC = () => {
  const features: Feature[] = [
    {
      icon: <Star className="w-6 h-6" />,
      title: "Review Management",
      description: "Monitor and respond to customer reviews in real-time. Build trust by engaging with feedback professionally.",
      color: "bg-yellow-500",
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Analytics Dashboard",
      description: "Track performance metrics, customer sentiment, and review trends with our comprehensive analytics tools.",
      color: "bg-blue-500",
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "Customer Engagement",
      description: "Build lasting relationships through direct communication. Reply to reviews and show customers you care.",
      color: "bg-green-500",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Trust Building",
      description: "Showcase verified reviews and business credentials. Establish credibility in the Guyanese market.",
      color: "bg-purple-500",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Growth Insights",
      description: "Understand market trends and customer preferences with detailed reports and actionable insights.",
      color: "bg-orange-500",
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Recognition Program",
      description: "Earn badges and recognition for excellent service. Stand out from competitors with quality indicators.",
      color: "bg-pink-500",
    },
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need to Succeed
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive tools designed specifically for Guyanese businesses
            to thrive in the digital age
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                className="group hover:shadow-lg transition-all duration-300 border-0 shadow-sm hover:-translate-y-1 h-full"
              >
                <CardHeader>
                  <div
                    className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-200`}
                  >
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900 group-hover:text-myTheme-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BusinessFeatures;
