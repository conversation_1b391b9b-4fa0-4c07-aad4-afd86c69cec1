"use client";
import React from "react";

const RatingModuleMini: React.FC<{
  name: string;
  rating: number;
  ratingChanged: (rating: number) => void;
  size: string;
}> = ({ name, rating = 1, ratingChanged, size = "rating-lg" }) => {
  const getRatingColor = (rating: number) => {
    const colors = {
      1: "text-red-500",
      2: "text-orange-500",
      3: "text-yellow-500",
      4: "text-lime-500",
      5: "text-green-500",
    };
    return colors[rating as keyof typeof colors] || colors[1];
  };

  return (
    <div className="inline-flex items-center">
      <label className={`cursor-pointer ${size}`}>
        <input
          type="radio"
          name={name}
          value={5}
          checked={rating === 5}
          onChange={() => ratingChanged(5)}
          className="hidden"
        />
        <svg
          className={`w-6 h-6 ${getRatingColor(rating)}`}
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      </label>
    </div>
  );
};

export default RatingModuleMini;
