import React, { useState, useEffect, useCallback } from "react";
import { genTags } from "../util/serverFunctions";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { iProduct } from "../util/Interfaces";

interface Props {
  description: string;
  handleArrayInput: (field: keyof iProduct, value: string) => void;
  handleRemoveArrayItem: (field: keyof iProduct, index: number) => void;
  field: keyof iProduct;
}

interface TagsResponse {
  success: boolean;
  data: {
    tags: string[];
  } | string[] | string | Record<string, unknown>;
  error?: string;
}

const SmartTags = ({ handleArrayInput, description, field }: Props) => {
  const [allAiTags, setAllAiTags] = useState<string[]>([]);
  const [displayedTags, setDisplayedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isDescriptionValid, setIsDescriptionValid] = useState(false);

  useEffect(() => {
    const wordCount = description.trim().split(/\s+/).length;
    setIsDescriptionValid(wordCount >= 10);
  }, [description]);

  const updateDisplayedTags = useCallback(() => {
    setDisplayedTags(allAiTags.slice(0, 5));
  }, [allAiTags]);

  useEffect(() => {
    updateDisplayedTags();
  }, [updateDisplayedTags]);

  const onClickHandle = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await genTags(description) as TagsResponse;

      if (!response.success) {
        throw new Error(response.error || "Failed to generate tags");
      }

      if (!response.data) {
        throw new Error("No tags were generated");
      }

      // Extract tags from the nested structure
      let tagsArray: unknown[] = [];

      if (typeof response.data === 'object' && response.data !== null) {
        if ('tags' in response.data && Array.isArray(response.data.tags)) {
          tagsArray = response.data.tags;
        } else if (Array.isArray(response.data)) {
          tagsArray = response.data;
        } else {
          tagsArray = Object.values(response.data);
        }
      } else if (typeof response.data === 'string') {
        tagsArray = [response.data];
      }

      const validTags = tagsArray
        .filter((tag: unknown): tag is string => typeof tag === 'string' && tag.trim().length > 0)
        .map((tag: string) => tag.trim());

      if (validTags.length === 0) {
        throw new Error("No valid tags were generated");
      }

      setAllAiTags(validTags);
    } catch (error) {
      console.error("Error generating tags:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to generate tags. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleTagClick = (
    e: React.MouseEvent<HTMLParagraphElement>,
    tag: string
  ) => {
    e.preventDefault();
    handleArrayInput(field, tag);
    setAllAiTags((prevTags) => {
      const newTags = prevTags.filter((t) => t !== tag);
      setDisplayedTags(newTags.slice(0, 5));
      return newTags;
    });
  };

  return (
    <div className="container py-2">
      {loading ? (
        <div className="flex items-center justify-center p-4">
          <p className="text-sm text-gray-600">Loading tags...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center p-4 gap-2">
          <p className="text-sm text-red-500">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={onClickHandle}
            className="bg-myTheme-secondary text-white hover:bg-myTheme-secondary/90"
          >
            Retry
          </Button>
        </div>
      ) : displayedTags.length === 0 ? (
        <div className="flex items-center justify-center p-4 gap-2">
          <p className="text-sm text-gray-600 mb-2">
            Having trouble thinking of categories? Let our AI help you!
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={onClickHandle}
            disabled={!isDescriptionValid}
            className="bg-myTheme-primary text-white hover:bg-myTheme-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ✨ Suggest Categories with AI
          </Button>
          {!isDescriptionValid && (
            <p className="text-xs text-gray-500 ml-2">
              Please enter at least 10 words in the description.
            </p>
          )}
        </div>
      ) : (
        <div className="flex flex-wrap gap-2">
          {displayedTags.map((tag, index) => (
            <div key={index}>
              <p
                className="bg-white rounded-full px-3 py-1.5 text-sm border border-gray-100 cursor-pointer hover:bg-myTheme-primary hover:text-white transition-all duration-200 ease-in-out flex items-center gap-1"
                onClick={(e) => handleTagClick(e, tag)}
                aria-label={`Add tag ${tag}`}
              >
                <span className="text-lg">+</span> {tag}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SmartTags;
