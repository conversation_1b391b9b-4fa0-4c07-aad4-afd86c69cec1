"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MessageSquare, Heart, Reply, UserPlus } from "lucide-react";
import Link from "next/link";
import { SignInButton, SignUpButton } from "@clerk/nextjs";

interface SignInToParticipateProps {
  action?: "comment" | "reply" | "vote" | "general";
  className?: string;
  compact?: boolean;
}

const SignInToParticipate: React.FC<SignInToParticipateProps> = ({
  action = "general",
  className = "",
  compact = false,
}) => {
  const getActionText = () => {
    switch (action) {
      case "comment":
        return "leave a comment";
      case "reply":
        return "reply to comments";
      case "vote":
        return "vote on comments";
      default:
        return "participate in the discussion";
    }
  };

  const getIcon = () => {
    switch (action) {
      case "comment":
        return <MessageSquare className="w-5 h-5" />;
      case "reply":
        return <Reply className="w-5 h-5" />;
      case "vote":
        return <Heart className="w-5 h-5" />;
      default:
        return <UserPlus className="w-5 h-5" />;
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center justify-center p-3 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 text-gray-600">
          {getIcon()}
          <span className="text-sm">
            <SignInButton mode="redirect">
              <button className="text-blue-600 hover:text-blue-800 font-medium">
                Sign in
              </button>
            </SignInButton>
            {" "}to {getActionText()}
          </span>
        </div>
      </div>
    );
  }

  return (
    <Card className={`border-blue-200 bg-blue-50 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 p-2 bg-blue-100 rounded-full">
            {getIcon()}
          </div>
          <div className="flex-grow">
            <h3 className="font-semibold text-blue-900 mb-2">
              Join the conversation
            </h3>
            <p className="text-blue-700 mb-4">
              Sign in to {getActionText()} and connect with other users.
            </p>
            <div className="flex gap-3">
              <SignInButton mode="redirect">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Sign In
                </Button>
              </SignInButton>
              <SignUpButton mode="redirect">
                <Button variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                  Create Account
                </Button>
              </SignUpButton>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SignInToParticipate;