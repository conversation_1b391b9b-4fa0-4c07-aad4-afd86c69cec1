"use client";

import { useEffect, useRef } from "react";
import { useAuth } from "@clerk/nextjs";
import { NotificationManager } from "../util/NotificationManager";

/**
 * Component that initializes the notification system when a user logs in.
 * Place this in your app shell or layout to ensure notifications work globally.
 */
export function NotificationInitializer() {
  const { userId, isSignedIn } = useAuth();
  const notificationManagerRef = useRef<NotificationManager | null>(null);

  useEffect(() => {
    // Clean up previous connection if any
    if (notificationManagerRef.current) {
      notificationManagerRef.current.disconnect();
      notificationManagerRef.current = null;
    }

    // Only connect if user is signed in
    if (isSignedIn && userId) {
      console.log("Initializing notification stream for user:", userId);
      notificationManagerRef.current = new NotificationManager(userId);
      notificationManagerRef.current.connect();
    }

    // Clean up on unmount
    return () => {
      if (notificationManagerRef.current) {
        console.log("Disconnecting notification stream");
        notificationManagerRef.current.disconnect();
        notificationManagerRef.current = null;
      }
    };
  }, [userId, isSignedIn]);

  // This component doesn't render anything
  return null;
}
