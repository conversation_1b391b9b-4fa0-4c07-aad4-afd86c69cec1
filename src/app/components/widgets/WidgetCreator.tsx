"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  X, 
  Loader2, 
  Eye, 
  Grid3X3, 
  Star, 
  MessageSquare, 
  CreditCard, 
  Shield, 
  ExternalLink 
} from "lucide-react";
import { iWidget, iBusiness, iProduct, WidgetType, CreateWidgetRequest } from "@/app/util/Interfaces";
import { DomainManagement } from "./DomainManagement";
import { toast } from "sonner";
import { getSecurityTheme, getSecurityInlineStyles, type SecurityLevel } from "@/app/util/widgetTheme";

interface WidgetCreatorProps {
  businessId: string;
  business?: iBusiness;
  onWidgetCreated: (widget: iWidget) => void;
  onClose: () => void;
}

const WIDGET_TYPES = [
  {
    type: 'REVIEW_CAROUSEL' as WidgetType,
    name: 'Review Carousel',
    description: 'Horizontal scrolling reviews with navigation',
    icon: <Eye className="w-5 h-5" />,
    recommended: true
  },
  {
    type: 'REVIEW_GRID' as WidgetType,
    name: 'Review Grid',
    description: 'Grid layout of multiple reviews',
    icon: <Grid3X3 className="w-5 h-5" />,
    recommended: false
  },
  {
    type: 'RATING_SUMMARY' as WidgetType,
    name: 'Rating Summary',
    description: 'Overall rating with review count',
    icon: <Star className="w-5 h-5" />,
    recommended: true
  },
  {
    type: 'MINI_REVIEW' as WidgetType,
    name: 'Mini Review',
    description: 'Compact single review display',
    icon: <MessageSquare className="w-5 h-5" />,
    recommended: false
  },
  {
    type: 'BUSINESS_CARD' as WidgetType,
    name: 'Business Card',
    description: 'Business info with rating',
    icon: <CreditCard className="w-5 h-5" />,
    recommended: true
  },
  {
    type: 'TRUST_BADGE' as WidgetType,
    name: 'Trust Badge',
    description: 'Simple trust indicator',
    icon: <Shield className="w-5 h-5" />,
    recommended: false
  },
  {
    type: 'REVIEW_POPUP' as WidgetType,
    name: 'Review Popup',
    description: 'Modal-style widget',
    icon: <ExternalLink className="w-5 h-5" />,
    recommended: false
  }
];

export function WidgetCreator({ businessId, business, onWidgetCreated, onClose }: WidgetCreatorProps) {
  
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<iProduct[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    type: '' as WidgetType,
    productId: 'all',
    securityLevel: 'SIMPLE' as 'SIMPLE' | 'SECURE',
    theme: 'light',
    primaryColor: '#3b82f6',
    borderRadius: '8px',
    maxReviews: 5,
    showLogo: true,
    showPoweredBy: true,
    showRating: true,
    showReviewText: true,
    showReviewDate: true,
    showReviewerName: true,
  });
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);

  // Fetch business products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch(`/api/get/products/all?businessId=${businessId}`, {
          credentials: "include",
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success && data.data) {
          setProducts(data.data);
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        // Don't show error toast for products as it's optional
        // Just log the error and continue with empty products array
      }
    };

    fetchProducts();
  }, [businessId]);

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast.error("Widget name is required");
      return;
    }

    if (!formData.type) {
      toast.error("Please select a widget type");
      return;
    }

    setLoading(true);
    try {
      const requestData: CreateWidgetRequest = {
        businessId,
        productId: formData.productId === 'all' ? undefined : formData.productId,
        name: formData.name,
        type: formData.type,
        securityLevel: formData.securityLevel,
        styling: {
          theme: formData.theme as 'light' | 'dark' | 'custom',
          primaryColor: formData.primaryColor,
          borderRadius: formData.borderRadius,
        },
        content: {
          maxReviews: formData.maxReviews,
          showLogo: formData.showLogo,
          showPoweredBy: formData.showPoweredBy,
          showRating: formData.showRating,
          showReviewText: formData.showReviewText,
          showReviewDate: formData.showReviewDate,
          showReviewerName: formData.showReviewerName,
        },
        allowedDomains: allowedDomains
      };

      const response = await fetch('/api/widgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        onWidgetCreated(data.data);
      } else {
        throw new Error(data.error || 'Failed to create widget');
      }
    } catch (error) {
      console.error("Error creating widget:", error);
      if (error instanceof SyntaxError) {
        toast.error("Server returned invalid response. Please try again.");
      } else if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to create widget");
      }
    } finally {
      setLoading(false);
    }
  };

  const selectedWidgetType = WIDGET_TYPES.find(w => w.type === formData.type);

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Create New Widget
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3].map((stepNum) => (
              <div key={stepNum} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNum 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {stepNum}
                </div>
                {stepNum < 3 && (
                  <div className={`w-12 h-1 mx-2 ${
                    step > stepNum ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* Step 1: Basic Info */}
          {step === 1 && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Widget Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Homepage Reviews, Sidebar Widget"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="product">Product (Optional)</Label>
                    <Select 
                      value={formData.productId} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, productId: value }))}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="All business reviews" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All business reviews</SelectItem>
                        {products.filter(product => product.id).map((product) => (
                          <SelectItem key={product.id} value={product.id!}>
                            {product.name || 'Unnamed Product'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      Leave empty to show all business reviews
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="securityLevel">Security Level</Label>
                    <div className="mt-2 space-y-3">
                      {(['SIMPLE', 'SECURE'] as SecurityLevel[]).map((level) => {
                        const theme = getSecurityTheme(level);
                        const styles = getSecurityInlineStyles(level, true);
                        const isSelected = formData.securityLevel === level;
                        
                        return (
                          <div 
                            key={level}
                            className={`p-4 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                              isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : 'hover:border-gray-300'
                            }`}
                            style={{
                              backgroundColor: styles.container.backgroundColor,
                              borderColor: isSelected ? theme.primary : styles.container.borderColor,
                              borderLeftColor: theme.primary,
                              borderLeftWidth: '4px'
                            }}
                            onClick={() => setFormData(prev => ({ ...prev, securityLevel: level }))}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-3">
                                <div 
                                  className="p-2 rounded-lg flex items-center justify-center"
                                  style={styles.icon}
                                >
                                  <span className="text-lg">{theme.icon}</span>
                                </div>
                                <div>
                                  <h4 className="font-medium" style={styles.text}>
                                    {theme.label}
                                  </h4>
                                  <p className="text-sm mt-1" style={styles.text}>
                                    {theme.description}
                                  </p>
                                  <div className="mt-2 space-y-1">
                                    {theme.features.map((feature, index) => (
                                      <div key={index} className="text-xs flex items-center space-x-1" style={styles.text}>
                                        <span>{feature}</span>
                                      </div>
                                    ))}
                                  </div>
                                  <div className="mt-2 text-xs italic" style={styles.text}>
                                    {theme.useCase}
                                  </div>
                                </div>
                              </div>
                              <div className={`w-4 h-4 rounded-full border-2 mt-1 ${
                                isSelected
                                  ? 'bg-blue-500 border-blue-500'
                                  : 'border-gray-300'
                              }`}>
                                {isSelected && (
                                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  onClick={() => setStep(2)}
                  disabled={!formData.name.trim()}
                >
                  Next: Choose Type
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Widget Type */}
          {step === 2 && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-4">Choose Widget Type</h3>
                
                <div className="grid gap-3">
                  {WIDGET_TYPES.map((widgetType) => (
                    <Card 
                      key={widgetType.type}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        formData.type === widgetType.type 
                          ? 'ring-2 ring-blue-500 bg-blue-50' 
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, type: widgetType.type }))}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-gray-100 rounded-lg">
                              {widgetType.icon}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{widgetType.name}</h4>
                                {widgetType.recommended && (
                                  <Badge variant="secondary" className="text-xs">
                                    Recommended
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {widgetType.description}
                              </p>
                            </div>
                          </div>
                          
                          <div className={`w-4 h-4 rounded-full border-2 ${
                            formData.type === widgetType.type
                              ? 'bg-blue-500 border-blue-500'
                              : 'border-gray-300'
                          }`}>
                            {formData.type === widgetType.type && (
                              <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button 
                  onClick={() => setStep(3)}
                  disabled={!formData.type}
                >
                  Next: Customize
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Customization */}
          {step === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Customize Widget</h3>
                
                <div className="space-y-6">
                  {/* Styling */}
                  <div>
                    <h4 className="font-medium mb-3">Styling</h4>
                    <div className="grid gap-4">
                      <div>
                        <Label htmlFor="theme">Theme</Label>
                        <Select 
                          value={formData.theme} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, theme: value }))}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">Light</SelectItem>
                            <SelectItem value="dark">Dark</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex items-center gap-2 mt-1">
                          <Input
                            type="color"
                            value={formData.primaryColor}
                            onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                            className="w-12 h-10 p-1 border rounded"
                          />
                          <Input
                            value={formData.primaryColor}
                            onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                            placeholder="#3b82f6"
                            className="flex-1"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="maxReviews">Max Reviews to Show</Label>
                        <Select 
                          value={formData.maxReviews.toString()} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, maxReviews: parseInt(value) }))}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {[1, 2, 3, 4, 5, 6, 8, 10].map(num => (
                              <SelectItem key={num} value={num.toString()}>
                                {num} review{num > 1 ? 's' : ''}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Content Options */}
                  <div>
                    <h4 className="font-medium mb-3">Content Options</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="showRating">Show Rating</Label>
                        <Switch
                          id="showRating"
                          checked={formData.showRating}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showRating: checked }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="showReviewText">Show Review Text</Label>
                        <Switch
                          id="showReviewText"
                          checked={formData.showReviewText}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showReviewText: checked }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="showReviewDate">Show Review Date</Label>
                        <Switch
                          id="showReviewDate"
                          checked={formData.showReviewDate}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showReviewDate: checked }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="showReviewerName">Show Reviewer Name</Label>
                        <Switch
                          id="showReviewerName"
                          checked={formData.showReviewerName}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showReviewerName: checked }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="showLogo">Show Business Logo</Label>
                        <Switch
                          id="showLogo"
                          checked={formData.showLogo}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showLogo: checked }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="showPoweredBy">Show "Powered by ReviewIt"</Label>
                        <Switch
                          id="showPoweredBy"
                          checked={formData.showPoweredBy}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showPoweredBy: checked }))}
                        />
                      </div>
                    </div>
   
                  </div>
               </div>
             </div>

             <div className="flex justify-between mt-6">
               <Button variant="outline" onClick={() => setStep(2)}>
                 Back
               </Button>
               <Button
                 onClick={handleSubmit}
                 disabled={loading}
               >
                 {loading ? (
                   <>
                     <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                     Creating...
                   </>
                 ) : (
                   'Create Widget'
                 )}
               </Button>
             </div>
           </div>
         )}
        </div>
      </DialogContent>
    </Dialog>
  );
}