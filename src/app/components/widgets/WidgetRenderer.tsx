'use client';

import { useEffect } from 'react';
import { Widget, Business, Product, Review, User } from '@prisma/client';
import { ReviewCarouselWidget } from './types/ReviewCarouselWidget';
import { ReviewGridWidget } from './types/ReviewGridWidget';
import { RatingSummaryWidget } from './types/RatingSummaryWidget';
import { MiniReviewWidget } from './types/MiniReviewWidget';
import { BusinessCardWidget } from './types/BusinessCardWidget';
import { TrustBadgeWidget } from './types/TrustBadgeWidget';
import { ReviewPopupWidget } from './types/ReviewPopupWidget';

interface WidgetRendererProps {
  widget: (Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: (Pick<Product, 'id' | 'name' | 'display_image' | 'rating' | 'address' | 'streetAddress' | 'city' | 'telephone' | 'website' | 'rating1Star' | 'rating2Stars' | 'rating3Stars' | 'rating4Stars' | 'rating5Stars'> & {
      _count: {
        reviews: number;
      };
      reviews: (Pick<Review, 'id' | 'rating' | 'body' | 'createdDate'> & {
        user: Pick<User, 'firstName' | 'lastName' | 'avatar' | 'userName'>;
      })[];
    }) | null;
    businessRating?: number;
    totalReviews?: number;
  }) | any; // Allow any widget-like object for secure widgets
  searchParams?: { [key: string]: string | string[] | undefined };
  isSecure?: boolean;
  domain?: string | null;
}

export function WidgetRenderer({ widget, searchParams = {}, isSecure = false, domain }: WidgetRendererProps) {
  
  // Track widget view on mount
  useEffect(() => {
    const trackView = async () => {
      try {
        await fetch(`/api/public/widgets/${widget.id}/track`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event: 'view',
            referrer: document.referrer,
            userAgent: navigator.userAgent
          })
        });
      } catch (error) {
        // Silently fail - don't break widget if tracking fails
        console.warn('Widget tracking failed:', error);
      }
    };

    trackView();
  }, [widget.id]);

  // Handle widget clicks with navigation
  const handleWidgetClick = async (elementType: string = 'widget') => {
    try {
      // Track the click
      await fetch(`/api/public/widgets/${widget.id}/track`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: 'click',
          elementType,
          referrer: document.referrer,
          userAgent: navigator.userAgent
        })
      });
    } catch (error) {
      // Silently fail
      console.warn('Widget click tracking failed:', error);
    }

    // Handle navigation based on element type
    const baseUrl = window.location.origin;
    let targetUrl = '';

    switch (elementType) {
      case 'trust-badge':
      case 'business-card':
      case 'rating-summary':
      case 'widget':
        // Navigate to product page to view all reviews
        if (widget.product?.id) {
          targetUrl = `${baseUrl}/product/${widget.product.id}`;
        }
        break;
      
      case 'write-review-button':
        // Navigate to write review page
        if (widget.product?.id) {
          targetUrl = `${baseUrl}/write-review?productId=${widget.product.id}`;
        }
        break;
      
      case 'review-item':
        // Navigate to specific review or product page
        if (widget.product?.id) {
          targetUrl = `${baseUrl}/product/${widget.product.id}`;
        }
        break;
      
      case 'powered-by':
        // Navigate to ReviewIt homepage
        targetUrl = 'https://reviewit.gy';
        break;
      
      default:
        // For carousel navigation, don't navigate
        if (elementType.includes('carousel-')) {
          return;
        }
        // Default to product page
        if (widget.product?.id) {
          targetUrl = `${baseUrl}/product/${widget.product.id}`;
        }
        break;
    }

    // Open in new tab/window to avoid breaking the parent site
    if (targetUrl) {
      window.open(targetUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const renderWidget = () => {
    const commonProps = {
      widget,
      onWidgetClick: handleWidgetClick
    };

    switch (widget.type) {
      case 'REVIEW_CAROUSEL':
        return <ReviewCarouselWidget {...commonProps} />;
      case 'REVIEW_GRID':
        return <ReviewGridWidget {...commonProps} />;
      case 'RATING_SUMMARY':
        return <RatingSummaryWidget {...commonProps} />;
      case 'MINI_REVIEW':
        return <MiniReviewWidget {...commonProps} />;
      case 'BUSINESS_CARD':
        return <BusinessCardWidget {...commonProps} />;
      case 'TRUST_BADGE':
        return <TrustBadgeWidget {...commonProps} />;
      case 'REVIEW_POPUP':
        return <ReviewPopupWidget {...commonProps} />;
      default:
        return (
          <div style={{ 
            padding: '16px', 
            textAlign: 'center', 
            color: '#666',
            fontFamily: 'Arial, sans-serif'
          }}>
            Widget type "{widget.type}" not supported
          </div>
        );
    }
  };

  return (
    <div 
      className="reviewit-widget" 
      data-widget-id={widget.id}
      style={{
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        fontSize: '14px',
        lineHeight: '1.5',
        color: '#333',
        backgroundColor: '#ffffff',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        borderRadius: '8px',
        border: '1px solid #e5e7eb'
      }}
    >
      {renderWidget()}
      
      {widget.showPoweredBy && (
        <div style={{ 
          fontSize: '10px', 
          color: '#999', 
          textAlign: 'center',
          marginTop: '8px',
          padding: '4px 0'
        }}>
          <a 
            href="https://reviewit.gy" 
            target="_blank" 
            rel="noopener noreferrer"
            style={{ 
              color: '#999', 
              textDecoration: 'none',
              fontSize: '10px'
            }}
            onClick={() => handleWidgetClick('powered-by')}
          >
            Powered by ReviewIt
          </a>
        </div>
      )}
    </div>
  );
}