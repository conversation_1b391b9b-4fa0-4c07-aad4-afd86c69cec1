"use client";

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  Globe, 
  Key, 
  Clock, 
  Activity, 
  AlertTriangle,
  CheckCircle,
  Copy,
  ExternalLink,
  Trash2,
  Plus,
  Loader2,
  Download,
  Eye,
  EyeOff,
  HelpCircle,
  X,
  XCircle
} from "lucide-react";
import { iWidget, WidgetSecuritySettings, iDomainVerification, iWidgetToken } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { getSecurityTheme, getSecurityInlineStyles, SecurityLevel } from "@/app/util/widgetTheme";

interface SecurityDashboardProps {
  widget: iWidget;
  onSecurityUpdate: (settings: Partial<WidgetSecuritySettings>) => void;
}

interface DomainStatus {
  domain: string;
  isVerified: boolean;
  verifiedAt?: Date | null;
  method: string;
  status: 'pending' | 'verified' | 'failed' | 'expired';
  verificationCode?: string;
  expiresAt?: Date;
}

export function SecurityDashboard({ widget, onSecurityUpdate }: SecurityDashboardProps) {
  const [loading, setLoading] = useState(false);
  const [securityConfig, setSecurityConfig] = useState<WidgetSecuritySettings>({
    securityLevel: widget.securityLevel,
    allowedDomains: widget.allowedDomains,
    tokenExpiry: widget.tokenExpiry,
    maxRequestsPerHour: widget.maxRequestsPerHour
  });
  
  const [domains, setDomains] = useState<DomainStatus[]>([]);
  const [tokens, setTokens] = useState<iWidgetToken[]>([]);
  const [newDomain, setNewDomain] = useState('');
  const [verificationMethod, setVerificationMethod] = useState<'HTML_FILE' | 'DNS_TXT' | 'META_TAG'>('HTML_FILE');
  const [showApiKey, setShowApiKey] = useState(false);
  const [showInstructionsModal, setShowInstructionsModal] = useState(false);
  const [currentVerificationCode, setCurrentVerificationCode] = useState('');
  const [selectedDomain, setSelectedDomain] = useState('');

  const loadSecurityData = useCallback(async () => {
    try {
      const response = await fetch(`/api/widgets/${widget.id}/security`);
      const data = await response.json();
      
      if (data.success) {
        setDomains(data.data.verifiedDomains || []);
        setTokens(data.data.activeTokens || []);
      }
    } catch (error) {
      console.error('Failed to load security data:', error);
    }
  }, [widget.id]);

  useEffect(() => {
    if (widget.securityLevel === 'SECURE') {
      loadSecurityData();
    }
  }, [widget.id, widget.securityLevel, loadSecurityData]);

  // Security level is now immutable - cannot be changed after widget creation

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast.error('Please enter a domain');
      return;
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
    if (!domainRegex.test(newDomain)) {
      toast.error('Invalid domain format');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/verify-domain`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          domain: newDomain.toLowerCase().trim(),
          method: verificationMethod 
        })
      });

      const data = await response.json();
      if (data.success) {
        setNewDomain('');
        setCurrentVerificationCode(data.verificationCode);
        setSelectedDomain(newDomain.toLowerCase().trim());
        setShowInstructionsModal(true);
        loadSecurityData();
        toast.success('Domain added successfully!');
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to add domain');
    } finally {
      setLoading(false);
    }
  };

  const verifyDomain = async (domain: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/verify/${domain}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ method: verificationMethod })
      });

      const data = await response.json();
      if (data.success) {
        loadSecurityData();
        toast.success('Domain verified successfully!');
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      toast.error(error.message || 'Domain verification failed');
    } finally {
      setLoading(false);
    }
  };

  const generateToken = async (domain: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/generate-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain })
      });

      const data = await response.json();
      if (data.success) {
        loadSecurityData();
        toast.success('Token generated successfully!');
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to generate token');
    } finally {
      setLoading(false);
    }
  };

  const revokeToken = async (domain: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/generate-token`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain })
      });

      const data = await response.json();
      if (data.success) {
        loadSecurityData();
        toast.success('Token revoked successfully');
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to revoke token');
    } finally {
      setLoading(false);
    }
  };

  const generateEmbedCode = async (domain: string) => {
    const tokenData = await generateToken(domain);
    if (tokenData) {
      const embedCode = tokenData.embedCode;
      navigator.clipboard.writeText(embedCode);
      toast.success('Embed code copied to clipboard!');
    }
  };

  // Security theme variables
  const securityLevel: SecurityLevel = securityConfig.securityLevel as SecurityLevel;
  const securityTheme = getSecurityTheme(securityLevel);
  const securityStyles = getSecurityInlineStyles(securityLevel);

  return (
    <div className="space-y-6">
      {/* Security Level Display (Read-only) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security Level
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 border rounded-lg bg-gray-50">
            <div className="flex items-start space-x-3">
              <div className="p-2 rounded-lg" style={{
                backgroundColor: getSecurityInlineStyles(securityConfig.securityLevel).icon.backgroundColor,
                color: getSecurityInlineStyles(securityConfig.securityLevel).icon.color
              }}>
                {securityConfig.securityLevel === 'SIMPLE' ? (
                  <Globe className="w-5 h-5" />
                ) : (
                  <Shield className="w-5 h-5" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="font-medium">
                  {securityConfig.securityLevel === 'SIMPLE' 
                    ? 'Simple Widget (Public)' 
                    : 'Secure Widget (Domain-Restricted)'
                  }
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  {securityConfig.securityLevel === 'SIMPLE'
                    ? 'Can be embedded anywhere. No domain restrictions.'
                    : 'Only works on verified domains. Requires domain verification.'
                  }
                </p>
                <div className="mt-2 text-xs px-2 py-1 rounded inline-block" style={{
                  backgroundColor: getSecurityInlineStyles(securityConfig.securityLevel).badge.backgroundColor,
                  color: getSecurityInlineStyles(securityConfig.securityLevel).badge.color
                }}>
                  {securityConfig.securityLevel === 'SIMPLE'
                    ? '⚠️ Can be copied and used on any website'
                    : '🔒 Domain verification and token authentication required'
                  }
                </div>
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            Security level cannot be changed after widget creation. Create a new widget to use a different security level.
          </p>
        </CardContent>
      </Card>

      {/* Secure Widget Configuration */}
      {securityConfig.securityLevel === 'SECURE' && (
        <>
          {/* API Key Display */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                API Key
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Input
                  type={showApiKey ? 'text' : 'password'}
                  value={widget.apiKey ? (showApiKey ? widget.apiKey : '••••••••••••••••') : 'Not generated'}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
                {widget.apiKey && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(widget.apiKey!);
                      toast.success('API key copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                This API key is used to sign tokens for your secure widgets. Keep it secure.
              </p>
            </CardContent>
          </Card>

          {/* Domain Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                Domain Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add New Domain */}
              <div className="space-y-3">
                <Label>Add New Domain</Label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="example.com"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    className="flex-1"
                  />
                  <Select value={verificationMethod} onValueChange={(value: any) => setVerificationMethod(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HTML_FILE">HTML File</SelectItem>
                      <SelectItem value="DNS_TXT">DNS TXT</SelectItem>
                      <SelectItem value="META_TAG">Meta Tag</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setShowInstructionsModal(true)}
                    title="View verification instructions"
                  >
                    <HelpCircle className="w-4 h-4" />
                  </Button>
                  <Button onClick={addDomain} disabled={loading}>
                    {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Plus className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Domain List */}
              <div className="space-y-3">
                <Label>Verified Domains</Label>
                {domains.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Globe className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No domains added yet</p>
                    <p className="text-sm">Add a domain above to get started</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {domains.map((domain) => (
                      <DomainCard
                        key={domain.domain}
                        domain={domain}
                        onVerify={() => verifyDomain(domain.domain)}
                        onGenerateToken={() => generateEmbedCode(domain.domain)}
                        onRevokeToken={() => revokeToken(domain.domain)}
                        loading={loading}
                      />
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Active Tokens */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                Active Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              {tokens.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Key className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No active tokens</p>
                  <p className="text-sm">Generate tokens for verified domains</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {tokens.map((token) => (
                    <TokenCard
                      key={token.id}
                      token={token}
                      onRevoke={() => revokeToken(token.domain)}
                      loading={loading}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
      
      {/* Verification Instructions Modal */}
      <Dialog open={showInstructionsModal} onOpenChange={setShowInstructionsModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Domain Verification Instructions
            </DialogTitle>
            <DialogDescription>
              Follow these steps to verify your domain ownership using the {verificationMethod.replace('_', ' ').toLowerCase()} method.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {verificationMethod === 'HTML_FILE' && (
              <div className="space-y-3">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">Step 1: Create Verification File</h4>
                  <p className="text-sm text-blue-800 mb-3">
                    Create a file named <code className="bg-blue-100 px-1 rounded">reviewit-verification.html</code> with the following content:
                  </p>
                  <div className="bg-white p-3 rounded border font-mono text-sm">
                    {currentVerificationCode || '[Verification code will appear here after adding domain]'}
                  </div>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">Step 2: Upload to Your Website</h4>
                  <p className="text-sm text-green-800">
                    Upload this file to the root directory of your website so it's accessible at:
                  </p>
                  <div className="bg-white p-3 rounded border font-mono text-sm mt-2">
                    https://yourdomain.com/reviewit-verification.html
                  </div>
                </div>
                
                <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <h4 className="font-medium text-purple-900 mb-2">Step 3: Verify</h4>
                  <p className="text-sm text-purple-800">
                    Click the "Verify" button next to your domain in the list above. We'll check if the file is accessible and contains the correct verification code.
                  </p>
                </div>
              </div>
            )}
            
            {verificationMethod === 'DNS_TXT' && (
              <div className="space-y-3">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">Step 1: Add DNS TXT Record</h4>
                  <p className="text-sm text-blue-800 mb-3">
                    Add the following TXT record to your domain's DNS settings:
                  </p>
                  <div className="space-y-2">
                    <div className="bg-white p-3 rounded border">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <strong>Name/Host:</strong><br/>
                          <code className="bg-gray-100 px-1 rounded">_reviewit-verification</code>
                        </div>
                        <div>
                          <strong>Type:</strong><br/>
                          <code className="bg-gray-100 px-1 rounded">TXT</code>
                        </div>
                        <div>
                          <strong>Value:</strong><br/>
                          <code className="bg-gray-100 px-1 rounded break-all">
                            {currentVerificationCode || '[Verification code will appear here after adding domain]'}
                          </code>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <h4 className="font-medium text-yellow-900 mb-2">Step 2: Wait for DNS Propagation</h4>
                  <p className="text-sm text-yellow-800">
                    DNS changes can take up to 24 hours to propagate, but usually take 5-15 minutes. You can check if the record is live using online DNS lookup tools.
                  </p>
                </div>
                
                <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <h4 className="font-medium text-purple-900 mb-2">Step 3: Verify</h4>
                  <p className="text-sm text-purple-800">
                    Once the DNS record is propagated, click the "Verify" button next to your domain. We'll query your DNS records to confirm the verification code.
                  </p>
                </div>
              </div>
            )}
            
            {verificationMethod === 'META_TAG' && (
              <div className="space-y-3">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">Step 1: Add Meta Tag</h4>
                  <p className="text-sm text-blue-800 mb-3">
                    Add the following meta tag to the <code className="bg-blue-100 px-1 rounded">&lt;head&gt;</code> section of your website's homepage:
                  </p>
                  <div className="bg-white p-3 rounded border font-mono text-sm break-all">
                    &lt;meta name="reviewit-verification" content="{currentVerificationCode || '[Verification code will appear here after adding domain]'}" /&gt;
                  </div>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">Step 2: Publish Changes</h4>
                  <p className="text-sm text-green-800">
                    Make sure to save and publish your changes so the meta tag is visible when we check your homepage.
                  </p>
                </div>
                
                <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <h4 className="font-medium text-purple-900 mb-2">Step 3: Verify</h4>
                  <p className="text-sm text-purple-800">
                    Click the "Verify" button next to your domain. We'll check your homepage's HTML source for the meta tag with the correct verification code.
                  </p>
                </div>
              </div>
            )}
            
            <div className="p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-medium text-gray-900 mb-2">💡 Tips</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Make sure your domain is accessible via HTTPS</li>
                <li>• Don't include "www" or "https://" when adding your domain</li>
                <li>• Verification usually takes a few seconds once properly set up</li>
                <li>• You can switch verification methods if one doesn't work for you</li>
              </ul>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button onClick={() => setShowInstructionsModal(false)}>
              Got it!
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Domain Card Component
function DomainCard({ 
  domain, 
  onVerify, 
  onGenerateToken, 
  onRevokeToken, 
  loading 
}: {
  domain: DomainStatus;
  onVerify: () => void;
  onGenerateToken: () => void;
  onRevokeToken: () => void;
  loading: boolean;
}) {
  const getStatusBadge = () => {
    switch (domain.status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800">Verified</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800">Expired</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  return (
    <div className="p-3 border rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="font-medium">{domain.domain}</span>
            {getStatusBadge()}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {domain.status === 'pending' && (
            <Button size="sm" variant="outline" onClick={onVerify} disabled={loading}>
              Verify
            </Button>
          )}
          {domain.status === 'verified' && (
            <>
              <Button size="sm" variant="outline" onClick={onGenerateToken} disabled={loading}>
                Get Embed Code
              </Button>
              <Button size="sm" variant="outline" onClick={onRevokeToken} disabled={loading}>
                <Trash2 className="w-3 h-3" />
              </Button>
            </>
          )}
        </div>
      </div>
      {domain.verifiedAt && (
        <p className="text-xs text-gray-500 mt-1">
          Verified on {new Date(domain.verifiedAt).toLocaleDateString()}
        </p>
      )}
    </div>
  );
}

// Token Card Component
function TokenCard({ 
  token, 
  onRevoke, 
  loading 
}: {
  token: iWidgetToken;
  onRevoke: () => void;
  loading: boolean;
}) {
  return (
    <div className="p-3 border rounded-lg">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <span className="font-medium">{token.domain}</span>
            <Badge className="bg-blue-100 text-blue-800">Active</Badge>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            <span>Expires: {new Date(token.expiresAt).toLocaleDateString()}</span>
            {token.lastUsed && (
              <span className="ml-3">Last used: {new Date(token.lastUsed).toLocaleDateString()}</span>
            )}
            <span className="ml-3">Requests: {token.requestCount}</span>
          </div>
        </div>
        <Button size="sm" variant="outline" onClick={onRevoke} disabled={loading}>
          <Trash2 className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );
}