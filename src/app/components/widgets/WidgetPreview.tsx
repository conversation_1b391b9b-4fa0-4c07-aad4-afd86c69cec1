"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Copy, ExternalLink, Smartphone, Monitor, Tablet, Shield, Globe } from "lucide-react";
import { iWidget } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { useState } from "react";
import { getSecurityTheme, getSecurityInlineStyles, SecurityLevel } from "@/app/util/widgetTheme";

interface WidgetPreviewProps {
  widget: iWidget;
  onBack: () => void;
}

export function WidgetPreview({ widget, onBack }: WidgetPreviewProps) {
  const [previewSize, setPreviewSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  
  // Security theme variables
  const securityLevel: SecurityLevel = widget.securityLevel || 'SIMPLE';
  const securityTheme = getSecurityTheme(securityLevel);
  const securityStyles = getSecurityInlineStyles(securityLevel);

  const getWidgetTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'REVIEW_CAROUSEL': 'Review Carousel',
      'REVIEW_GRID': 'Review Grid',
      'RATING_SUMMARY': 'Rating Summary',
      'MINI_REVIEW': 'Mini Review',
      'BUSINESS_CARD': 'Business Card',
      'TRUST_BADGE': 'Trust Badge',
      'REVIEW_POPUP': 'Review Popup'
    };
    return labels[type] || type;
  };

  const copyEmbedCode = async () => {
    let embedCode: string;
    
    if (securityLevel === 'SECURE') {
      // Secure widget embed code with token authentication
      embedCode = `<!-- ReviewIt Secure Widget -->
<div id="reviewit-secure-widget-${widget.id}" data-widget-id="${widget.id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/secure-embed.js';
    script.async = true;
    script.setAttribute('data-widget-id', '${widget.id}');
    script.setAttribute('data-domain-token', '[REPLACE_WITH_YOUR_DOMAIN_TOKEN]');
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Secure Widget -->`;
    } else {
      // Simple widget embed code
      embedCode = `<!-- ReviewIt Widget -->
<div id="reviewit-widget-${widget.id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/embed.js';
    script.async = true;
    script.onload = function() {
      new ReviewItWidget({
        widgetId: '${widget.id}',
        container: 'reviewit-widget-${widget.id}'
      });
    };
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Widget -->`;
    }

    try {
      await navigator.clipboard.writeText(embedCode);
      toast.success(`${securityLevel === 'SECURE' ? 'Secure' : 'Simple'} embed code copied to clipboard`);
    } catch (error) {
      toast.error("Failed to copy embed code");
    }
  };

  const copyIframeCode = async () => {
    const iframeCode = `<iframe 
  src="${window.location.origin}/widgets/iframe/${widget.id}"
  width="100%"
  height="400"
  frameborder="0"
  scrolling="no"
  title="ReviewIt Widget">
</iframe>`;

    try {
      await navigator.clipboard.writeText(iframeCode);
      toast.success("Iframe code copied to clipboard");
    } catch (error) {
      toast.error("Failed to copy iframe code");
    }
  };

  const getPreviewDimensions = () => {
    switch (previewSize) {
      case 'mobile':
        return { width: '375px', height: '600px' };
      case 'tablet':
        return { width: '768px', height: '500px' };
      case 'desktop':
        return { width: '100%', height: '400px' };
      default:
        return { width: '100%', height: '400px' };
    }
  };

  const dimensions = getPreviewDimensions();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={onBack} className="flex-shrink-0">
            <ArrowLeft className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Back to Widgets</span>
          </Button>
          
          <div className="min-w-0 flex-1">
            <h2 className="text-xl font-semibold truncate">{widget.name}</h2>
            <div className="flex flex-wrap items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                {getWidgetTypeLabel(widget.type)}
              </Badge>
              <Badge variant={widget.isActive ? "default" : "secondary"} className="text-xs">
                {widget.isActive ? "Active" : "Inactive"}
              </Badge>
              <Badge 
                variant={securityTheme.badgeVariant}
                style={securityStyles.badge}
                className="flex items-center gap-1 text-xs"
              >
                <span className="text-sm">
                  {securityTheme.icon}
                </span>
                {securityTheme.label}
              </Badge>
            </div>
          </div>
        </div>

        {/* Action Buttons - Mobile-friendly layout */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={copyEmbedCode} className="flex-1 sm:flex-none">
            <Copy className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Copy Embed Code</span>
            <span className="sm:hidden">Embed</span>
          </Button>
          
          <Button variant="outline" onClick={copyIframeCode} className="flex-1 sm:flex-none">
            <Copy className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Copy Iframe Code</span>
            <span className="sm:hidden">Iframe</span>
          </Button>
          
          <Button asChild className="flex-1 sm:flex-none">
            <a 
              href={`/widgets/iframe/${widget.id}`} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center justify-center"
            >
              <ExternalLink className="w-4 h-4 sm:mr-2" />
              <span className="hidden sm:inline">Open in New Tab</span>
              <span className="sm:hidden">Open</span>
            </a>
          </Button>
        </div>
      </div>

      {/* Preview Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Live Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Device Size Selector - Hidden on mobile */}
            <div className="hidden sm:flex flex-col sm:flex-row sm:items-center gap-2">
              <span className="text-sm font-medium">Preview Size:</span>
              <div className="flex items-center gap-1">
                <Button
                  variant={previewSize === 'mobile' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('mobile')}
                  className="flex-1 sm:flex-none"
                >
                  <Smartphone className="w-4 h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Mobile</span>
                </Button>
                <Button
                  variant={previewSize === 'tablet' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('tablet')}
                  className="flex-1 sm:flex-none hidden sm:inline-flex"
                >
                  <Tablet className="w-4 h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Tablet</span>
                </Button>
                <Button
                  variant={previewSize === 'desktop' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('desktop')}
                  className="flex-1 sm:flex-none hidden sm:inline-flex"
                >
                  <Monitor className="w-4 h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Desktop</span>
                </Button>
              </div>
            </div>

            {/* Preview Frame */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <div 
                className="mx-auto bg-white rounded border shadow-sm overflow-hidden"
                style={{ 
                  width: dimensions.width,
                  maxWidth: '100%',
                  height: dimensions.height 
                }}
              >
                <iframe
                  src={`${window.location.origin}/widgets/iframe/${widget.id}?preview=true`}
                  width="100%"
                  height="100%"
                  frameBorder="0"
                  scrolling="no"
                  title={`${widget.name} Preview`}
                  className="w-full h-full"
                  style={{ border: 'none', display: 'block' }}
                />
              </div>
            </div>

            {/* Widget Info */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">Type</span>
                <div className="font-medium mt-1">{getWidgetTypeLabel(widget.type)}</div>
              </div>
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">Theme</span>
                <div className="font-medium mt-1 capitalize">{widget.theme}</div>
              </div>
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">Max Reviews</span>
                <div className="font-medium mt-1">{widget.maxReviews}</div>
              </div>
              <div className="bg-muted/30 rounded-lg p-3">
                <span className="font-medium text-muted-foreground">Status</span>
                <div className="font-medium mt-1">
                  {widget.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Embed Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How to Embed</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {securityLevel === 'SECURE' ? (
              <>
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-2">
                    <Shield className="w-5 h-5 text-amber-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-amber-800 mb-1">Secure Widget Requirements</h4>
                      <p className="text-sm text-amber-700">
                        This secure widget requires domain verification and a valid domain token. 
                        Make sure your domain is registered and verified in the Security Dashboard before embedding.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Secure JavaScript Embed (Recommended)</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Secure embedding with domain token authentication
                  </p>
                  <div className="bg-muted rounded-lg p-4 border">
                    <pre className="text-xs sm:text-sm font-mono overflow-x-auto whitespace-pre-wrap break-all sm:break-normal">{`<!-- ReviewIt Secure Widget -->
<div id="reviewit-secure-widget-${widget.id}" data-widget-id="${widget.id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/secure-embed.js';
    script.async = true;
    script.setAttribute('data-widget-id', '${widget.id}');
    script.setAttribute('data-domain-token', '[REPLACE_WITH_YOUR_DOMAIN_TOKEN]');
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Secure Widget -->`}</pre>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    <strong>Important:</strong> Replace <code>[REPLACE_WITH_YOUR_DOMAIN_TOKEN]</code> with your actual domain token from the Security Dashboard.
                  </p>
                </div>
              </>
            ) : (
              <div>
                <h4 className="font-medium mb-2">JavaScript Embed (Recommended)</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  Dynamic loading with auto-resize and better performance
                </p>
                <div className="bg-muted rounded-lg p-4 border">
                  <pre className="text-xs sm:text-sm font-mono overflow-x-auto whitespace-pre-wrap break-all sm:break-normal">{`<!-- ReviewIt Widget -->
<div id="reviewit-widget-${widget.id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/embed.js';
    script.async = true;
    script.onload = function() {
      new ReviewItWidget({
        widgetId: '${widget.id}',
        container: 'reviewit-widget-${widget.id}'
      });
    };
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Widget -->`}</pre>
                </div>
              </div>
            )}

            <div>
              <h4 className="font-medium mb-2">Iframe Embed</h4>
              <p className="text-sm text-muted-foreground mb-3">
                {securityLevel === 'SECURE' 
                  ? 'Simple iframe for basic embedding (domain restrictions still apply)'
                  : 'Simple iframe for basic embedding'
                }
              </p>
              <div className="bg-muted rounded-lg p-4 border">
                <pre className="text-xs sm:text-sm font-mono overflow-x-auto whitespace-pre-wrap break-all sm:break-normal">{`<iframe 
  src="${window.location.origin}/widgets/iframe/${widget.id}"
  width="100%"
  height="400"
  frameborder="0"
  scrolling="no"
  title="ReviewIt Widget">
</iframe>`}</pre>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}