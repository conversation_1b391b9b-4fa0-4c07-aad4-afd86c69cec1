'use client';

import { Widget, Business, Product } from '@prisma/client';

interface RatingSummaryWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function RatingSummaryWidget({ widget, onWidgetClick }: RatingSummaryWidgetProps) {
  const rating = widget.product?.rating || widget.businessRating || 3;
  const reviewCount = widget.product?._count?.reviews || widget.totalReviews || 0;
  const businessName = widget.business.ownerName || 'Business';
  const productName = widget.product?.name;

  // Generate star display
  const renderStars = (rating: number, size: number = 16) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <span key={i} style={{ color: widget.primaryColor || '#fbbf24', fontSize: `${size}px` }}>
            ★
          </span>
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <span key={i} style={{ color: widget.primaryColor || '#fbbf24', fontSize: `${size}px` }}>
            ☆
          </span>
        );
      } else {
        stars.push(
          <span key={i} style={{ color: '#e5e7eb', fontSize: `${size}px` }}>
            ☆
          </span>
        );
      }
    }
    return stars;
  };

  const containerStyle: React.CSSProperties = {
    padding: '16px',
    borderRadius: widget.borderRadius || '8px',
    border: '1px solid #e5e7eb',
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    textAlign: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    maxWidth: '300px',
    margin: '0 auto'
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '8px',
    color: widget.theme === 'dark' ? '#ffffff' : '#1f2937'
  };

  const ratingStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: '700',
    color: widget.primaryColor || '#3b82f6',
    marginBottom: '4px'
  };

  const reviewCountStyle: React.CSSProperties = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
    marginTop: '4px'
  };

  return (
    <div 
      style={containerStyle}
      onClick={() => onWidgetClick('rating-summary')}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        e.currentTarget.style.transform = 'translateY(-1px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = 'none';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      {widget.showLogo && (
        <div style={{ marginBottom: '12px' }}>
          <div style={titleStyle}>
            {productName || businessName}
          </div>
        </div>
      )}

      {widget.showRating && (
        <>
          <div style={ratingStyle}>
            {rating.toFixed(1)}
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            {renderStars(rating, 20)}
          </div>
        </>
      )}

      <div style={reviewCountStyle}>
        {reviewCount === 0 ? (
          'No reviews yet'
        ) : reviewCount === 1 ? (
          '1 review'
        ) : (
          `${reviewCount.toLocaleString()} reviews`
        )}
      </div>

      {widget.business.isVerified && (
        <div style={{
          marginTop: '8px',
          fontSize: '10px',
          color: '#10b981',
          fontWeight: '500'
        }}>
          ✓ Verified Business
        </div>
      )}

      {/* Write a Review Button */}
      <button
        style={{
          marginTop: '12px',
          padding: '8px 16px',
          backgroundColor: widget.primaryColor || '#3b82f6',
          color: '#ffffff',
          border: 'none',
          borderRadius: '6px',
          fontSize: '12px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          width: '100%'
        }}
        onClick={(e) => {
          e.stopPropagation();
          onWidgetClick('write-review-button');
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.opacity = '0.9';
          e.currentTarget.style.transform = 'translateY(-1px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.opacity = '1';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        Write a Review
      </button>
    </div>
  );
}