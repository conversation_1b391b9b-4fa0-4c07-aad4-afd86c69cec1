'use client';

import { Widget, Business, Product } from '@prisma/client';
import { renderReviewBody } from '../../../util/htmlSanitizer';

interface ReviewGridWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function ReviewGridWidget({ widget, onWidgetClick }: ReviewGridWidgetProps) {
  const reviews = widget.product?.reviews || [];
  const displayReviews = reviews.slice(0, widget.maxReviews);

  // Generate star display
  const renderStars = (rating: number, size: number = 12) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    
    for (let i = 0; i < 5; i++) {
      stars.push(
        <span 
          key={i} 
          style={{ 
            color: i < fullStars ? (widget.primaryColor || '#fbbf24') : '#e5e7eb', 
            fontSize: `${size}px` 
          }}
        >
          ★
        </span>
      );
    }
    return stars;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const containerStyle: React.CSSProperties = {
    padding: '16px',
    borderRadius: widget.borderRadius || '8px',
    border: '1px solid #e5e7eb',
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    maxWidth: '500px',
    margin: '0 auto'
  };

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '16px',
    paddingBottom: '12px',
    borderBottom: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '4px',
    color: widget.theme === 'dark' ? '#ffffff' : '#1f2937'
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: displayReviews.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: '12px'
  };

  const reviewCardStyle: React.CSSProperties = {
    padding: '12px',
    borderRadius: '6px',
    backgroundColor: widget.theme === 'dark' ? '#374151' : '#f9fafb',
    border: `1px solid ${widget.theme === 'dark' ? '#4b5563' : '#e5e7eb'}`,
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  };

  if (displayReviews.length === 0) {
    return (
      <div style={containerStyle}>
        {widget.showLogo && (
          <div style={headerStyle}>
            <div style={titleStyle}>
              {widget.product?.name || widget.business.ownerName || 'Business'}
            </div>
          </div>
        )}
        
        <div style={{ textAlign: 'center', color: '#9ca3af', padding: '20px' }}>
          No reviews available yet
        </div>
      </div>
    );
  }

  return (
    <div 
      style={containerStyle}
      onClick={() => onWidgetClick('review-grid')}
    >
      {/* Header */}
      {widget.showLogo && (
        <div style={headerStyle}>
          <div style={titleStyle}>
            {widget.product?.name || widget.business.ownerName || 'Business'}
          </div>
          
          {widget.business.isVerified && (
            <div style={{
              fontSize: '11px',
              color: '#10b981',
              fontWeight: '500'
            }}>
              ✓ Verified Business
            </div>
          )}
        </div>
      )}

      {/* Reviews Grid */}
      <div style={gridStyle}>
        {displayReviews.map((review: any, index: number) => (
          <div 
            key={review.id}
            style={reviewCardStyle}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = widget.theme === 'dark' ? '#4b5563' : '#f3f4f6';
              e.currentTarget.style.transform = 'translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = widget.theme === 'dark' ? '#374151' : '#f9fafb';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            {/* Rating */}
            {widget.showRating && (
              <div style={{ marginBottom: '8px' }}>
                {renderStars(review.rating)}
              </div>
            )}

            {/* Review Text */}
            {widget.showReviewText && review.reviewText && (
              <div style={{
                fontSize: '12px',
                lineHeight: '1.4',
                marginBottom: '8px',
                color: widget.theme === 'dark' ? '#d1d5db' : '#4b5563'
              }}>
                <span>
                   "
                   <span 
                     dangerouslySetInnerHTML={renderReviewBody(
                       review.reviewText.length > 80 
                         ? review.reviewText.substring(0, 80) + '...'
                         : review.reviewText
                     ) || { __html: '' }}
                   />
                   "
                 </span>
              </div>
            )}

            {/* Reviewer Info */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              fontSize: '10px',
              color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              {widget.showReviewerName && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    backgroundColor: widget.primaryColor || '#3b82f6',
                    color: '#ffffff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '8px',
                    fontWeight: '600'
                  }}>
                    {review.user.avatar ? (
                      <img 
                        src={review.user.avatar} 
                        alt="Reviewer"
                        style={{
                          width: '100%',
                          height: '100%',
                          borderRadius: '50%',
                          objectFit: 'cover'
                        }}
                      />
                    ) : (
                      getInitials(review.user.firstName, review.user.lastName)
                    )}
                  </div>
                  <span>
                    {review.user.firstName} {review.user.lastName?.charAt(0)}.
                  </span>
                </div>
              )}
              
              {widget.showReviewDate && (
                <div>
                  {formatDate(review.createdDate)}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}