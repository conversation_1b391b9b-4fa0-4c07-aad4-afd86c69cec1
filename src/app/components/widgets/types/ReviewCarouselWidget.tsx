'use client';

import { useState, useEffect } from 'react';
import { Widget, Business, Product } from '@prisma/client';
import { renderReviewBody } from '../../../util/htmlSanitizer';

interface ReviewCarouselWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function ReviewCarouselWidget({ widget, onWidgetClick }: ReviewCarouselWidgetProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const reviews = widget.product?.reviews || [];
  const displayReviews = reviews.slice(0, widget.maxReviews);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || displayReviews.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayReviews.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, displayReviews.length]);

  // Generate star display
  const renderStars = (rating: number, size: number = 14) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    
    for (let i = 0; i < 5; i++) {
      stars.push(
        <span 
          key={i} 
          style={{ 
            color: i < fullStars ? (widget.primaryColor || '#fbbf24') : '#e5e7eb', 
            fontSize: `${size}px` 
          }}
        >
          ★
        </span>
      );
    }
    return stars;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    borderRadius: widget.borderRadius || '8px',
    border: '1px solid #e5e7eb',
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    maxWidth: '400px',
    margin: '0 auto',
    position: 'relative',
    overflow: 'hidden'
  };

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '16px',
    borderBottom: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
    paddingBottom: '12px'
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '8px',
    color: widget.theme === 'dark' ? '#ffffff' : '#1f2937'
  };

  const carouselContainerStyle: React.CSSProperties = {
    position: 'relative',
    minHeight: '120px'
  };

  const reviewStyle: React.CSSProperties = {
    opacity: 1,
    transform: 'translateX(0)',
    transition: 'all 0.3s ease-in-out'
  };

  const navigationStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '12px',
    marginTop: '16px'
  };

  const navButtonStyle: React.CSSProperties = {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    border: `1px solid ${widget.primaryColor || '#3b82f6'}`,
    backgroundColor: 'transparent',
    color: widget.primaryColor || '#3b82f6',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    transition: 'all 0.2s ease'
  };

  const dotsStyle: React.CSSProperties = {
    display: 'flex',
    gap: '6px'
  };

  const dotStyle = (isActive: boolean): React.CSSProperties => ({
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    backgroundColor: isActive ? (widget.primaryColor || '#3b82f6') : '#d1d5db',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  });

  if (displayReviews.length === 0) {
    return (
      <div style={containerStyle}>
        <div style={headerStyle}>
          {widget.showLogo && (
            <div style={titleStyle}>
              {widget.product?.name || widget.business.ownerName || 'Business'}
            </div>
          )}
        </div>
        
        <div style={{ textAlign: 'center', color: '#9ca3af', padding: '20px' }}>
          No reviews available yet
        </div>
      </div>
    );
  }

  const currentReview = displayReviews[currentIndex];

  return (
    <div 
      style={containerStyle}
      onClick={() => onWidgetClick('review-carousel')}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Header */}
      {widget.showLogo && (
        <div style={headerStyle}>
          <div style={titleStyle}>
            {widget.product?.name || widget.business.ownerName || 'Business'}
          </div>
          
          {widget.business.isVerified && (
            <div style={{
              fontSize: '11px',
              color: '#10b981',
              fontWeight: '500'
            }}>
              ✓ Verified Business
            </div>
          )}
        </div>
      )}

      {/* Carousel Content */}
      <div style={carouselContainerStyle}>
        <div 
          style={{
            ...reviewStyle,
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}
          onClick={(e) => {
            e.stopPropagation();
            onWidgetClick('review-item');
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = widget.theme === 'dark' ? '#374151' : '#f9fafb';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          {/* Rating */}
          {widget.showRating && (
            <div style={{ marginBottom: '12px' }}>
              {renderStars(currentReview.rating)}
            </div>
          )}

          {/* Review Text */}
          {widget.showReviewText && currentReview.reviewText && (
            <div style={{
              fontSize: '14px',
              lineHeight: '1.5',
              marginBottom: '12px',
              color: widget.theme === 'dark' ? '#d1d5db' : '#4b5563'
            }}>
              <span>
                "
                <span 
                  dangerouslySetInnerHTML={renderReviewBody(
                    currentReview.reviewText.length > 150 
                      ? currentReview.reviewText.substring(0, 150) + '...'
                      : currentReview.reviewText
                  ) || { __html: '' }}
                />
                "
              </span>
            </div>
          )}

          {/* Reviewer Info */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '12px',
            color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
          }}>
            {/* Avatar */}
            <div style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: widget.primaryColor || '#3b82f6',
              color: '#ffffff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              fontWeight: '600'
            }}>
              {currentReview.user.avatar ? (
                <img 
                  src={currentReview.user.avatar} 
                  alt="Reviewer"
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                getInitials(currentReview.user.firstName, currentReview.user.lastName)
              )}
            </div>

            <div>
              {widget.showReviewerName && (
                <div style={{ fontWeight: '500' }}>
                  {currentReview.user.firstName} {currentReview.user.lastName?.charAt(0)}.
                </div>
              )}
              
              {widget.showReviewDate && (
                <div>
                  {formatDate(currentReview.createdDate)}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      {displayReviews.length > 1 && (
        <div style={navigationStyle}>
          <button
            style={navButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              setCurrentIndex((prev) => prev === 0 ? displayReviews.length - 1 : prev - 1);
              onWidgetClick('carousel-prev');
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = widget.primaryColor || '#3b82f6';
              e.currentTarget.style.color = '#ffffff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = widget.primaryColor || '#3b82f6';
            }}
          >
            ‹
          </button>

          <div style={dotsStyle}>
            {displayReviews.map((_: any, index: number) => (
              <div
                key={index}
                style={dotStyle(index === currentIndex)}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentIndex(index);
                  onWidgetClick('carousel-dot');
                }}
              />
            ))}
          </div>

          <button
            style={navButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              setCurrentIndex((prev) => (prev + 1) % displayReviews.length);
              onWidgetClick('carousel-next');
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = widget.primaryColor || '#3b82f6';
              e.currentTarget.style.color = '#ffffff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = widget.primaryColor || '#3b82f6';
            }}
          >
            ›
          </button>
        </div>
      )}
    </div>
  );
}