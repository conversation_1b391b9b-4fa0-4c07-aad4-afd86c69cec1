'use client';

import { Widget, Business, Product } from '@prisma/client';

interface BusinessCardWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function BusinessCardWidget({ widget, onWidgetClick }: BusinessCardWidgetProps) {
  const rating = widget.product?.rating || widget.businessRating || 3;
  const reviewCount = widget.product?._count?.reviews || widget.totalReviews || 0;
  const businessName = widget.business.ownerName || 'Business';
  const productName = widget.product?.name;
  const address = widget.product?.streetAddress || widget.product?.address;
  const city = widget.product?.city;
  const telephone = widget.product?.telephone;
  const website = widget.product?.website?.[0];
  const displayImage = widget.product?.display_image;

  // Generate star display
  const renderStars = (rating: number, size: number = 14) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <span key={i} style={{ color: widget.primaryColor || '#fbbf24', fontSize: `${size}px` }}>
            ★
          </span>
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <span key={i} style={{ color: widget.primaryColor || '#fbbf24', fontSize: `${size}px` }}>
            ☆
          </span>
        );
      } else {
        stars.push(
          <span key={i} style={{ color: '#e5e7eb', fontSize: `${size}px` }}>
            ☆
          </span>
        );
      }
    }
    return stars;
  };

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    borderRadius: widget.borderRadius || '8px',
    border: '1px solid #e5e7eb',
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    maxWidth: '350px',
    margin: '0 auto',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '16px',
    gap: '12px'
  };

  const imageStyle: React.CSSProperties = {
    width: '50px',
    height: '50px',
    borderRadius: '8px',
    objectFit: 'cover',
    backgroundColor: '#f3f4f6'
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: '600',
    marginBottom: '4px',
    color: widget.theme === 'dark' ? '#ffffff' : '#1f2937'
  };

  const subtitleStyle: React.CSSProperties = {
    fontSize: '14px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
    marginBottom: '8px'
  };

  const ratingContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '16px'
  };

  const ratingTextStyle: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '600',
    color: widget.primaryColor || '#3b82f6'
  };

  const reviewCountStyle: React.CSSProperties = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
  };

  const contactInfoStyle: React.CSSProperties = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
    lineHeight: '1.4'
  };

  const linkStyle: React.CSSProperties = {
    color: widget.primaryColor || '#3b82f6',
    textDecoration: 'none'
  };

  return (
    <div 
      style={containerStyle}
      onClick={() => onWidgetClick('business-card')}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      {/* Header with logo and name */}
      <div style={headerStyle}>
        {widget.showLogo && displayImage && (
          <img 
            src={displayImage} 
            alt={productName || businessName}
            style={imageStyle}
          />
        )}
        
        <div style={{ flex: 1 }}>
          <div style={titleStyle}>
            {productName || businessName}
          </div>
          
          {widget.business.isVerified && (
            <div style={{
              fontSize: '11px',
              color: '#10b981',
              fontWeight: '500'
            }}>
              ✓ Verified Business
            </div>
          )}
        </div>
      </div>

      {/* Rating */}
      {widget.showRating && (
        <div style={ratingContainerStyle}>
          <div style={ratingTextStyle}>
            {rating.toFixed(1)}
          </div>
          <div>
            {renderStars(rating)}
          </div>
          <div style={reviewCountStyle}>
            ({reviewCount.toLocaleString()})
          </div>
        </div>
      )}

      {/* Contact Information */}
      <div style={contactInfoStyle}>
        {address && (
          <div style={{ marginBottom: '4px' }}>
            📍 {address}{city && `, ${city}`}
          </div>
        )}
        
        {telephone && (
          <div style={{ marginBottom: '4px' }}>
            📞 <a href={`tel:${telephone}`} style={linkStyle}>{telephone}</a>
          </div>
        )}
        
        {website && (
          <div style={{ marginBottom: '4px' }}>
            🌐 <a 
              href={website.startsWith('http') ? website : `https://${website}`} 
              target="_blank" 
              rel="noopener noreferrer"
              style={linkStyle}
              onClick={(e) => {
                e.stopPropagation();
                onWidgetClick('website-link');
              }}
            >
              Visit Website
            </a>
          </div>
        )}
      </div>

      {/* Call to Action Buttons */}
      <div style={{
        marginTop: '16px',
        display: 'flex',
        gap: '8px'
      }}>
        <button
          style={{
            flex: 1,
            padding: '8px 12px',
            backgroundColor: widget.primaryColor || '#3b82f6',
            color: '#ffffff',
            border: 'none',
            borderRadius: '6px',
            fontSize: '12px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'opacity 0.2s ease'
          }}
          onClick={(e) => {
            e.stopPropagation();
            onWidgetClick('business-card');
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.opacity = '0.9';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.opacity = '1';
          }}
        >
          View Reviews
        </button>
        
        <button
          style={{
            flex: 1,
            padding: '8px 12px',
            backgroundColor: 'transparent',
            color: widget.primaryColor || '#3b82f6',
            border: `1px solid ${widget.primaryColor || '#3b82f6'}`,
            borderRadius: '6px',
            fontSize: '12px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onClick={(e) => {
            e.stopPropagation();
            onWidgetClick('write-review-button');
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = widget.primaryColor || '#3b82f6';
            e.currentTarget.style.color = '#ffffff';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = widget.primaryColor || '#3b82f6';
          }}
        >
          Write Review
        </button>
      </div>
    </div>
  );
}