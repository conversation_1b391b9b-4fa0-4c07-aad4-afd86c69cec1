"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Globe, 
  Plus, 
  Trash2, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { iWidget } from "@/app/util/Interfaces";

interface DomainManagementProps {
  widget: iWidget;
  onDomainsUpdated: (domains: string[]) => void;
}

export function DomainManagement({ widget, onDomainsUpdated }: DomainManagementProps) {
  const [domains, setDomains] = useState<string[]>(widget.allowedDomains || []);
  const [newDomain, setNewDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Only show domain management for SECURE widgets
  if (widget.securityLevel !== 'SECURE') {
    return null;
  }

  const validateDomain = (domain: string): boolean => {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain.toLowerCase());
  };

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast.error("Please enter a domain");
      return;
    }

    const cleanDomain = newDomain.toLowerCase().trim();
    
    if (!validateDomain(cleanDomain)) {
      toast.error("Please enter a valid domain (e.g., example.com)");
      return;
    }

    if (domains.includes(cleanDomain)) {
      toast.error("Domain already exists");
      return;
    }

    if (domains.length >= 50) {
      toast.error("Maximum 50 domains allowed per widget");
      return;
    }

    setValidating(true);
    try {
      const updatedDomains = [...domains, cleanDomain];
      await updateDomains(updatedDomains);
      setNewDomain("");
      toast.success("Domain added successfully");
    } catch (error) {
      toast.error("Failed to add domain");
    } finally {
      setValidating(false);
    }
  };

  const removeDomain = async (domainToRemove: string) => {
    const confirmMessage = `Are you sure you want to remove ${domainToRemove} from the allowed domains? This domain will no longer be able to embed this widget.`;
    if (!window.confirm(confirmMessage)) return;
    
    try {
      const updatedDomains = domains.filter(d => d !== domainToRemove);
      await updateDomains(updatedDomains);
      toast.success("Domain removed successfully");
    } catch (error) {
      toast.error("Failed to remove domain");
    }
  };

  const updateDomains = async (newDomains: string[]) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/domains`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ domains: newDomains }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update domains: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setDomains(newDomains);
        onDomainsUpdated(newDomains);
      } else {
        throw new Error(data.error || 'Failed to update domains');
      }
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1 sm:gap-2">
          <Globe className="w-4 h-4" />
          <span className="hidden sm:inline">Domain Settings</span>
          <span className="sm:hidden">Domains</span>
          {domains.length > 0 && (
            <Badge variant="secondary" className="ml-1">
              {domains.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Domain Management - {widget.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Info Section */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Globe className="w-4 h-4 text-blue-600" />
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Domain Allowlist</h4>
                  <p className="text-sm text-muted-foreground">
                    Control which websites can embed this widget. Only domains in this list will be able to display your widget.
                    {domains.length === 0 && " Currently, any domain can embed this widget."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Add Domain Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Add New Domain</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="domain">Domain</Label>
                  <Input
                    id="domain"
                    placeholder="example.com"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addDomain()}
                    disabled={validating || loading}
                  />
                </div>
                <div className="flex items-end">
                  <Button 
                    onClick={addDomain} 
                    disabled={validating || loading || !newDomain.trim()}
                    className="gap-2"
                  >
                    {validating ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Plus className="w-4 h-4" />
                    )}
                    Add
                  </Button>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Enter domain without protocol (e.g., "example.com" not "https://example.com")
              </p>
            </CardContent>
          </Card>

          {/* Current Domains */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Allowed Domains
                <Badge variant="outline">
                  {domains.length}/50
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {domains.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h4 className="font-medium mb-2">No Domain Restrictions</h4>
                  <p className="text-sm text-muted-foreground">
                    This widget can be embedded on any website. Add domains to restrict embedding.
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {domains.map((domain) => (
                    <div key={domain} className="flex items-center justify-between p-3 border rounded-lg gap-2">
                      <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="font-mono text-sm truncate">{domain}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDomain(domain)}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <h4 className="font-medium text-yellow-800">Security Notice</h4>
                  <p className="text-sm text-yellow-700">
                    Domain restrictions help prevent unauthorized use of your widgets. 
                    Only add domains you trust and control.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

      </DialogContent>
    </Dialog>
  );
}