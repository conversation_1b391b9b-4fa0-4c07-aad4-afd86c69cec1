"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Eye, 
  MousePointer, 
  BarChart3, 
  <PERSON><PERSON>dingUp, 
  ExternalLink,
  Calendar,
  Loader2
} from "lucide-react";
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, BarChart, Bar } from "recharts";
import { iWidget, WidgetAnalyticsResponse } from "@/app/util/Interfaces";
import { toast } from "sonner";

interface WidgetAnalyticsProps {
  widget: iWidget;
  onBack: () => void;
}

export function WidgetAnalytics({ widget, onBack }: WidgetAnalyticsProps) {
  const [analytics, setAnalytics] = useState<WidgetAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30'); // Default to 30 days
  const [chartType, setChartType] = useState<'line' | 'bar'>('line'); // Chart type toggle

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/widgets/${widget.id}/analytics?period=${period}`, {
          credentials: "include",
        });
        const data = await response.json();
        
        if (data.success) {
          setAnalytics(data);
        } else {
          toast.error(data.error || "Failed to load analytics");
        }
      } catch (error) {
        console.error("Error fetching analytics:", error);
        toast.error("Failed to load analytics");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [widget.id, period]);

  const getWidgetTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'REVIEW_CAROUSEL': 'Review Carousel',
      'REVIEW_GRID': 'Review Grid',
      'RATING_SUMMARY': 'Rating Summary',
      'MINI_REVIEW': 'Mini Review',
      'BUSINESS_CARD': 'Business Card',
      'TRUST_BADGE': 'Trust Badge',
      'REVIEW_POPUP': 'Review Popup'
    };
    return labels[type] || type;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Widgets
          </Button>
          <div>
            <h2 className="text-xl font-semibold">{widget.name} Analytics</h2>
          </div>
        </div>
        
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin" />
          <span className="ml-2">Loading analytics...</span>
        </div>
      </div>
    );
  }

  const metrics = analytics?.data?.metrics;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Widgets
          </Button>
          
          <div>
            <h2 className="text-xl font-semibold">{widget.name} Analytics</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline">
                {getWidgetTypeLabel(widget.type)}
              </Badge>
              <Badge variant={widget.isActive ? "default" : "secondary"}>
                {widget.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="px-3 py-2 border rounded-md bg-background"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.totalViews?.toLocaleString() || widget.viewCount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Last {period} days
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.totalClicks?.toLocaleString() || widget.clickCount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Last {period} days
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.clickThroughRate?.toFixed(2) || 
                (widget.viewCount > 0 
                  ? ((widget.clickCount / widget.viewCount) * 100).toFixed(2)
                  : '0.00')}%
            </div>
            <p className="text-xs text-muted-foreground">
              Click-through rate
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Used</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {widget.lastUsed 
                ? new Date(widget.lastUsed).toLocaleDateString()
                : 'Never'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Most recent activity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Referrers */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Top Referrers</CardTitle>
        </CardHeader>
        <CardContent>
          {metrics?.topReferrers && metrics.topReferrers.length > 0 ? (
            <div className="space-y-3">
              {metrics.topReferrers.map((referrer, index) => (
                <div key={referrer.domain} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{referrer.domain}</div>
                      <div className="text-sm text-muted-foreground">
                        {referrer.views.toLocaleString()} views
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{referrer.percentage}%</div>
                    <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${referrer.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <ExternalLink className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No referrer data available yet</p>
              <p className="text-sm">Data will appear once your widget starts receiving traffic</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Daily Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Daily Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          {metrics?.dailyStats && metrics.dailyStats.length > 0 ? (
            <div className="space-y-4">
              {/* Chart Type Toggle */}
              <div className="flex items-center gap-2">
                <Button
                  variant={chartType === 'line' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartType('line')}
                >
                  Line Chart
                </Button>
                <Button
                  variant={chartType === 'bar' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartType('bar')}
                >
                  Bar Chart
                </Button>
              </div>

              {/* Chart */}
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  {chartType === 'line' ? (
                    <LineChart data={metrics.dailyStats}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        className="text-xs"
                      />
                      <YAxis className="text-xs" />
                      <Tooltip 
                        labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', { 
                          weekday: 'long', 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                        formatter={(value: number, name: string) => [
                          value.toLocaleString(),
                          name === 'views' ? 'Views' : 'Clicks'
                        ]}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="views" 
                        stroke="#3b82f6" 
                        strokeWidth={2}
                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="clicks" 
                        stroke="#10b981" 
                        strokeWidth={2}
                        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
                      />
                    </LineChart>
                  ) : (
                    <BarChart data={metrics.dailyStats}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        className="text-xs"
                      />
                      <YAxis className="text-xs" />
                      <Tooltip 
                        labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', { 
                          weekday: 'long', 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                        formatter={(value: number, name: string) => [
                          value.toLocaleString(),
                          name === 'views' ? 'Views' : 'Clicks'
                        ]}
                      />
                      <Bar dataKey="views" fill="#3b82f6" name="views" />
                      <Bar dataKey="clicks" fill="#10b981" name="clicks" />
                    </BarChart>
                  )}
                </ResponsiveContainer>
              </div>

              {/* Legend */}
              <div className="flex items-center justify-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Views</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Clicks</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No Data Available</h3>
              <p>Daily performance data will appear once your widget starts receiving traffic</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Widget Details */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Widget Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Created:</span>
              <div className="text-muted-foreground">
                {new Date(widget.createdAt).toLocaleDateString()}
              </div>
            </div>
            <div>
              <span className="font-medium">Type:</span>
              <div className="text-muted-foreground">{getWidgetTypeLabel(widget.type)}</div>
            </div>
            <div>
              <span className="font-medium">Theme:</span>
              <div className="text-muted-foreground capitalize">{widget.theme}</div>
            </div>
            <div>
              <span className="font-medium">Max Reviews:</span>
              <div className="text-muted-foreground">{widget.maxReviews}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}