"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  MousePointer, 
  Settings, 
  BarChart3, 
  Copy, 
  Trash2, 
  MoreHorizontal,
  ExternalLink,
  Loader2,
  Shield,
  Globe
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { iWidget } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { SecurityDashboard } from "./SecurityDashboard";
import { getSecurityTheme, getSecurityInlineStyles, type SecurityLevel } from "@/app/util/widgetTheme";

interface WidgetListProps {
  widgets: iWidget[];
  loading: boolean;
  onWidgetUpdated: (widget: iWidget) => void;
  onWidgetDeleted: (widgetId: string) => void;
  onPreview: (widget: iWidget) => void;
  onAnalytics: (widget: iWidget) => void;
}

export function WidgetList({ 
  widgets, 
  loading, 
  onWidgetUpdated, 
  onWidgetDeleted, 
  onPreview, 
  onAnalytics 
}: WidgetListProps) {
  const [togglingId, setTogglingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [securityDialogWidget, setSecurityDialogWidget] = useState<iWidget | null>(null);

  const getWidgetTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'REVIEW_CAROUSEL': 'Review Carousel',
      'REVIEW_GRID': 'Review Grid',
      'RATING_SUMMARY': 'Rating Summary',
      'MINI_REVIEW': 'Mini Review',
      'BUSINESS_CARD': 'Business Card',
      'TRUST_BADGE': 'Trust Badge',
      'REVIEW_POPUP': 'Review Popup'
    };
    return labels[type] || type;
  };

  const getWidgetTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'REVIEW_CAROUSEL': 'bg-blue-100 text-blue-800',
      'REVIEW_GRID': 'bg-green-100 text-green-800',
      'RATING_SUMMARY': 'bg-purple-100 text-purple-800',
      'MINI_REVIEW': 'bg-orange-100 text-orange-800',
      'BUSINESS_CARD': 'bg-pink-100 text-pink-800',
      'TRUST_BADGE': 'bg-yellow-100 text-yellow-800',
      'REVIEW_POPUP': 'bg-indigo-100 text-indigo-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const copyEmbedCode = async (widget: iWidget) => {
    const securityLevel: SecurityLevel = widget.securityLevel || 'SIMPLE';
    
    let embedCode: string;
    
    if (securityLevel === 'SECURE') {
      // Secure widget embed code with token authentication
      embedCode = `<!-- ReviewIt Secure Widget -->
<div data-reviewit-secure-widget="${widget.id}" data-token="YOUR_DOMAIN_TOKEN"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/secure-embed.js';
    script.async = true;
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Secure Widget -->`;
    } else {
      // Simple widget embed code
      embedCode = `<!-- ReviewIt Widget -->
<div id="reviewit-widget-${widget.id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widgets/embed.js';
    script.async = true;
    script.onload = function() {
      new ReviewItWidget({
        widgetId: '${widget.id}',
        container: 'reviewit-widget-${widget.id}'
      });
    };
    document.head.appendChild(script);
  })();
</script>
<!-- End ReviewIt Widget -->`;
    }

    try {
      await navigator.clipboard.writeText(embedCode);
      const securityTheme = getSecurityTheme(securityLevel);
      toast.success(`${securityTheme.label} embed code copied to clipboard`);
    } catch (error) {
      toast.error("Failed to copy embed code");
    }
  };

  const toggleWidgetStatus = async (widget: iWidget) => {
    setTogglingId(widget.id);
    try {
      const response = await fetch(`/api/widgets/${widget.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          isActive: !widget.isActive
        }),
      });

      const data = await response.json();
      if (data.success) {
        onWidgetUpdated(data.data);
        toast.success(`Widget ${widget.isActive ? 'deactivated' : 'activated'} successfully`);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      toast.error("Failed to update widget status");
    } finally {
      setTogglingId(null);
    }
  };

  const deleteWidget = async (widget: iWidget) => {
    if (!confirm(`Are you sure you want to delete "${widget.name}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingId(widget.id);
    try {
      const response = await fetch(`/api/widgets/${widget.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const data = await response.json();
      if (data.success) {
        onWidgetDeleted(widget.id);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      toast.error("Failed to delete widget");
    } finally {
      setDeletingId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading widgets...</span>
      </div>
    );
  }

  if (widgets.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
              <Eye className="w-6 h-6 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold">No widgets yet</h3>
            <p className="text-muted-foreground max-w-sm">
              Create your first widget to start embedding reviews on your website
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Separate widgets by status
  const activeWidgets = widgets.filter(widget => widget.isActive);
  const inactiveWidgets = widgets.filter(widget => !widget.isActive);

  const renderWidgetCard = (widget: iWidget) => {
    const securityLevel: SecurityLevel = widget.securityLevel || 'SIMPLE';
    const securityTheme = getSecurityTheme(securityLevel);
    const securityStyles = getSecurityInlineStyles(securityLevel, widget.isActive);
    
    return (
      <Card 
        key={widget.id} 
        className={`group hover:shadow-lg transition-all duration-200 ${
          widget.isActive 
            ? 'hover:shadow-md' 
            : 'opacity-75'
        }`}
        style={securityStyles.container}
      >
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1 flex-1 min-w-0">
            <div className="flex items-center gap-2 min-w-0">
              <div className="bg-muted rounded-full p-2 flex-shrink-0">
                <span className="text-sm">{securityTheme.icon}</span>
              </div>
              <CardTitle className="text-lg font-semibold truncate">{widget.name}</CardTitle>
            </div>
            
            {widget.product && (
              <p className="text-sm text-muted-foreground truncate">
                {widget.product.name}
              </p>
            )}
            
            <div className="flex flex-wrap items-center gap-2 mt-2">
              <Badge 
                variant={securityTheme.badgeVariant}
                className="text-xs flex-shrink-0"
                style={securityStyles.badge}
              >
                {securityTheme.label}
              </Badge>
              <Badge 
                variant={widget.isActive ? "default" : "secondary"}
                className="text-xs flex-shrink-0"
              >
                {widget.isActive ? "Active" : "Inactive"}
              </Badge>
              <Badge 
                variant="outline" 
                className={`text-xs flex-shrink-0 ${getWidgetTypeColor(widget.type)}`}
              >
                {getWidgetTypeLabel(widget.type)}
              </Badge>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex-shrink-0">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onPreview(widget)}>
                <ExternalLink className="w-4 h-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => copyEmbedCode(widget)}>
                <Copy className="w-4 h-4 mr-2" />
                Copy Embed Code
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAnalytics(widget)}>
                <BarChart3 className="w-4 h-4 mr-2" />
                Analytics
              </DropdownMenuItem>
              {widget.securityLevel === 'SECURE' && (
                <DropdownMenuItem onClick={() => setSecurityDialogWidget(widget)}>
                  <Shield className="w-4 h-4 mr-2" />
                  Security Settings
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => toggleWidgetStatus(widget)}
                disabled={togglingId === widget.id}
              >
                {togglingId === widget.id ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Settings className="w-4 h-4 mr-2" />
                )}
                {widget.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => deleteWidget(widget)}
                disabled={deletingId === widget.id}
                className="text-red-600"
              >
                {deletingId === widget.id ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Stats Grid - Better mobile layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm mb-4">
            <div className="flex items-center gap-2">
              <div className="bg-muted rounded-full p-1.5">
                <Eye className="w-4 h-4 text-muted-foreground" />
              </div>
              <div>
                <div className="font-medium">{widget.viewCount.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Views</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="bg-muted rounded-full p-1.5">
                <MousePointer className="w-4 h-4 text-muted-foreground" />
              </div>
              <div>
                <div className="font-medium">{widget.clickCount.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Clicks</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="bg-muted rounded-full p-1.5">
                <BarChart3 className="w-4 h-4 text-muted-foreground" />
              </div>
              <div>
                <div className="font-medium">
                  {widget.viewCount > 0 
                    ? Math.round((widget.clickCount / widget.viewCount) * 100 * 100) / 100
                    : 0}%
                </div>
                <div className="text-xs text-muted-foreground">CTR</div>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              {widget.lastUsed 
                ? `Last used ${new Date(widget.lastUsed).toLocaleDateString()}`
                : 'Never used'
              }
            </div>
          </div>

          {/* Action Buttons - Mobile-friendly layout */}
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Primary actions row */}
            <div className="flex flex-wrap gap-2 flex-1">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onPreview(widget)}
                className="flex-1 sm:flex-none"
              >
                <ExternalLink className="w-4 h-4 sm:mr-1" />
                <span className="hidden sm:inline">Preview</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => copyEmbedCode(widget)}
                className="flex-1 sm:flex-none"
              >
                <Copy className="w-4 h-4 sm:mr-1" />
                <span className="hidden sm:inline">Copy</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onAnalytics(widget)}
                className="flex-1 sm:flex-none"
              >
                <BarChart3 className="w-4 h-4 sm:mr-1" />
                <span className="hidden sm:inline">Analytics</span>
              </Button>
              
              {widget.securityLevel === 'SECURE' && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSecurityDialogWidget(widget)}
                  className="flex-1 sm:flex-none gap-1 sm:gap-2"
                >
                  <Shield className="w-4 h-4" />
                  <span className="hidden sm:inline">Security</span>
                </Button>
              )}
            </div>


          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-8">
      {/* Active Widgets Section */}
      {activeWidgets.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="w-2 h-8 bg-green-500 rounded-full"></div>
            <h3 className="text-xl font-bold text-gray-900">Active Widgets</h3>
            <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
              {activeWidgets.length} active
            </Badge>
          </div>
          <div className="grid gap-4 sm:gap-6 grid-cols-1">
            {activeWidgets.map(renderWidgetCard)}
          </div>
        </div>
      )}

      {/* Inactive Widgets Section */}
      {inactiveWidgets.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="w-2 h-8 bg-gray-400 rounded-full"></div>
            <h3 className="text-xl font-bold text-gray-600">Inactive Widgets</h3>
            <Badge variant="secondary" className="bg-gray-100 text-gray-600 border-gray-200">
              {inactiveWidgets.length} inactive
            </Badge>
          </div>
          <div className="grid gap-4 sm:gap-6 grid-cols-1">
             {inactiveWidgets.map(renderWidgetCard)}
           </div>
         </div>
       )}

       {/* Empty State */}
       {activeWidgets.length === 0 && inactiveWidgets.length === 0 && (
         <Card className="border-dashed border-2 border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50">
           <CardContent className="flex flex-col items-center justify-center py-16">
             <div className="text-center">
               <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                 <BarChart3 className="w-10 h-10 text-blue-600" />
               </div>
               <h3 className="text-2xl font-bold text-gray-900 mb-3">No widgets yet</h3>
               <p className="text-gray-600 mb-8 max-w-md leading-relaxed">
                 Create your first widget to start embedding reviews on your website and showcase your customer feedback to boost conversions
               </p>
               <div className="flex flex-col sm:flex-row gap-3 justify-center">
                 <Button className="gap-2 bg-blue-600 hover:bg-blue-700">
                   <BarChart3 className="w-4 h-4" />
                   Create Your First Widget
                 </Button>
                 <Button variant="outline" className="gap-2">
                   <Eye className="w-4 h-4" />
                   View Examples
                 </Button>
               </div>
             </div>
           </CardContent>
         </Card>
       )}

       {/* Security Dashboard Dialog */}
        <Dialog open={!!securityDialogWidget} onOpenChange={(open) => !open && setSecurityDialogWidget(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Security Settings - {securityDialogWidget?.name}
              </DialogTitle>
            </DialogHeader>
            {securityDialogWidget && (
              <SecurityDashboard
                widget={securityDialogWidget}
                onSecurityUpdate={(settings) => {
                  const updatedWidget = { ...securityDialogWidget, ...settings };
                  onWidgetUpdated(updatedWidget);
                  // Don't close dialog automatically to allow multiple updates
                }}
              />
            )}
          </DialogContent>
        </Dialog>
     </div>
  );
}