"use client";

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Code, 
  Copy, 
  Download, 
  ExternalLink, 
  Shield, 
  Globe,
  AlertTriangle,
  CheckCircle,
  Eye,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react";
import { iWidget, iDomainVerification } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { getSecurityTheme, getSecurityInlineStyles, SecurityLevel } from "@/app/util/widgetTheme";

interface EmbedCodeGeneratorProps {
  widget: iWidget;
}

export function EmbedCodeGenerator({ widget }: EmbedCodeGeneratorProps) {
  const [selectedDomain, setSelectedDomain] = useState('');
  const [embedCode, setEmbedCode] = useState('');
  const [verifiedDomains, setVerifiedDomains] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewSize, setPreviewSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  
  // Security theme variables
  const securityLevel: SecurityLevel = widget.securityLevel || 'SIMPLE';
  const securityTheme = getSecurityTheme(securityLevel);
  const securityStyles = getSecurityInlineStyles(securityLevel);

  const loadVerifiedDomains = useCallback(async () => {
    try {
      const response = await fetch(`/api/widgets/${widget.id}/security`);
      const data = await response.json();
      
      if (data.success) {
        const verified = data.data.verifiedDomains
          ?.filter((d: any) => d.isVerified)
          ?.map((d: any) => d.domain) || [];
        setVerifiedDomains(verified);
      }
    } catch (error) {
      console.error('Failed to load verified domains:', error);
    }
  }, [widget.id]);

  const generateSimpleEmbedCode = useCallback(() => {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://reviewit.gy';
    const code = `<iframe 
  src="${baseUrl}/widgets/iframe/${widget.id}" 
  width="100%" 
  height="400" 
  frameborder="0"
  title="ReviewIt Widget"
  style="border: none; border-radius: 8px;">
</iframe>`;
    setEmbedCode(code);
  }, [widget.id]);

  useEffect(() => {
    if (widget.securityLevel === 'SIMPLE') {
      generateSimpleEmbedCode();
    } else {
      loadVerifiedDomains();
    }
  }, [widget, generateSimpleEmbedCode, loadVerifiedDomains]);

  const generateSecureEmbedCode = async () => {
    if (!selectedDomain) {
      toast.error('Please select a verified domain');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/widgets/${widget.id}/generate-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: selectedDomain })
      });

      const data = await response.json();
      if (data.success) {
        setEmbedCode(data.data.embedCode);
        toast.success('Secure embed code generated!');
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to generate embed code');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(embedCode);
    toast.success('Embed code copied to clipboard!');
  };

  const downloadAsFile = () => {
    const blob = new Blob([embedCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reviewit-widget-${widget.id}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Embed code downloaded!');
  };

  const getPreviewDimensions = () => {
    switch (previewSize) {
      case 'mobile':
        return { width: '375px', height: '400px' };
      case 'tablet':
        return { width: '768px', height: '400px' };
      case 'desktop':
        return { width: '100%', height: '400px' };
      default:
        return { width: '100%', height: '400px' };
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="w-5 h-5" />
            Embed Code Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Security Level Indicator */}
          <div 
            className="flex items-center justify-between p-3 rounded-lg"
            style={{
              backgroundColor: securityStyles.container.backgroundColor,
              borderLeft: `4px solid ${securityTheme.primary}`,
              border: `1px solid ${securityTheme.border}`
            }}
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg" style={{ color: securityTheme.primary }}>
                {securityTheme.icon}
              </span>
              <span className="text-sm font-medium" style={{ color: securityTheme.text }}>
                {securityTheme.label}
              </span>
              <Badge 
                variant={securityTheme.badgeVariant}
                style={securityStyles.badge}
              >
                {securityLevel === 'SIMPLE' ? 'Public' : 'Secure'}
              </Badge>
            </div>
          </div>

          {/* Domain Selection for Secure Widgets */}
          {widget.securityLevel === 'SECURE' && (
            <div className="space-y-3">
              <Label>Select Verified Domain</Label>
              <div className="flex space-x-2">
                <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Choose a verified domain..." />
                  </SelectTrigger>
                  <SelectContent>
                    {verifiedDomains.length === 0 ? (
                      <SelectItem value="" disabled>
                        No verified domains available
                      </SelectItem>
                    ) : (
                      verifiedDomains.map(domain => (
                        <SelectItem key={domain} value={domain}>
                          {domain}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <Button 
                  onClick={generateSecureEmbedCode} 
                  disabled={!selectedDomain || loading}
                >
                  Generate Code
                </Button>
              </div>
              
              {verifiedDomains.length === 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-yellow-800">No verified domains</p>
                      <p className="text-yellow-700">
                        You need to verify at least one domain before generating secure embed codes.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Security Warning for Simple Widgets */}
          {widget.securityLevel === 'SIMPLE' && (
            <div 
              className="p-3 rounded-lg"
              style={{
                backgroundColor: securityStyles.container.backgroundColor,
                border: `1px solid ${securityTheme.border}`
              }}
            >
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 mt-0.5" style={{ color: securityTheme.primary }} />
                <div className="text-sm">
                  <p className="font-medium" style={{ color: securityTheme.text }}>Security Notice</p>
                  <p style={{ color: securityTheme.text, opacity: 0.8 }}>
                    This embed code can be copied and used on any website. 
                    Consider upgrading to a Secure Widget for better control.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Embed Code Display */}
          {embedCode && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Embed Code</Label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={copyToClipboard}>
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadAsFile}>
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
              
              <Textarea
                value={embedCode}
                readOnly
                rows={6}
                className="font-mono text-xs"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview */}
      {embedCode && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Preview
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  variant={previewSize === 'mobile' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('mobile')}
                >
                  <Smartphone className="w-4 h-4" />
                </Button>
                <Button
                  variant={previewSize === 'tablet' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('tablet')}
                >
                  <Tablet className="w-4 h-4" />
                </Button>
                <Button
                  variant={previewSize === 'desktop' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewSize('desktop')}
                >
                  <Monitor className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div 
                className="mx-auto bg-white rounded-lg shadow-sm overflow-hidden"
                style={getPreviewDimensions()}
              >
                <div 
                  dangerouslySetInnerHTML={{ __html: embedCode }}
                  className="w-full h-full"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              Preview showing {previewSize} view
            </p>
          </CardContent>
        </Card>
      )}

      {/* Implementation Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="w-5 h-5" />
            Implementation Guide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="html" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="html">HTML</TabsTrigger>
              <TabsTrigger value="wordpress">WordPress</TabsTrigger>
              <TabsTrigger value="react">React</TabsTrigger>
            </TabsList>
            
            <TabsContent value="html" className="space-y-3">
              <div className="text-sm space-y-2">
                <p className="font-medium">HTML Implementation:</p>
                <ol className="list-decimal list-inside space-y-1 text-gray-600">
                  <li>Copy the embed code above</li>
                  <li>Paste it into your HTML where you want the widget to appear</li>
                  <li>The widget will automatically load when the page loads</li>
                </ol>
              </div>
            </TabsContent>
            
            <TabsContent value="wordpress" className="space-y-3">
              <div className="text-sm space-y-2">
                <p className="font-medium">WordPress Implementation:</p>
                <ol className="list-decimal list-inside space-y-1 text-gray-600">
                  <li>Go to your WordPress admin dashboard</li>
                  <li>Edit the page/post where you want to add the widget</li>
                  <li>Add a "Custom HTML" block</li>
                  <li>Paste the embed code into the HTML block</li>
                  <li>Save and publish your page</li>
                </ol>
              </div>
            </TabsContent>
            
            <TabsContent value="react" className="space-y-3">
              <div className="text-sm space-y-2">
                <p className="font-medium">React Implementation:</p>
                <div className="bg-gray-100 p-3 rounded font-mono text-xs">
                  {widget.securityLevel === 'SIMPLE' ? (
                    `// Simple Widget
<iframe 
  src="https://reviewit.gy/widgets/iframe/${widget.id}"
  width="100%"
  height="400"
  frameBorder="0"
  title="ReviewIt Widget"
  style={{border: 'none', borderRadius: '8px'}}
/>`
                  ) : (
                    `// Secure Widget
useEffect(() => {
  const script = document.createElement('script');
  script.src = 'https://reviewit.gy/widgets/secure-embed.js';
  document.body.appendChild(script);
  
  return () => {
    document.body.removeChild(script);
  };
}, []);

// In your JSX:
<div 
  data-reviewit-secure-widget="${widget.id}" 
  data-token="[YOUR_DOMAIN_TOKEN]"
/>`
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Security Features */}
      {widget.securityLevel === 'SECURE' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Security Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Domain Verification</p>
                  <p className="text-sm text-gray-600">
                    Widget only works on domains you've verified and approved
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Token Authentication</p>
                  <p className="text-sm text-gray-600">
                    Each domain gets a unique, time-limited authentication token
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Rate Limiting</p>
                  <p className="text-sm text-gray-600">
                    Automatic protection against abuse with configurable limits
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Real-time Validation</p>
                  <p className="text-sm text-gray-600">
                    Every request is validated against your security settings
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}