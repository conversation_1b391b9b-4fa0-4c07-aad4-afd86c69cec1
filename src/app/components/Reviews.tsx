"use client";
import { iProduct, iReview } from "../util/Interfaces";
import { useQuery } from "@tanstack/react-query";
import { getReviews } from "../util/serverFunctions";
import LoadingSpinner from "./LoadingSpinner";
import ProductCard from "./ProductCard";
import "dayjs/locale/en"; // Import the English locale
import ReviewCard from "./ReviewCard";
import Link from "next/link";
import WriteAReview from "./WriteAReview";
import RatingDistribution from "@/components/product/RatingDistribution";
import { CompanyActivityCard } from "@/components/company-activity-card";
import { useAuth } from "@clerk/nextjs";
import Breadcrumb from "./Breadcrumb";
import FAQSection from "./FAQSection";
import Schema from "./Schema";
import ProductShowcase from "./ProductShowcase";
import { Eye } from "lucide-react";
import { useState, useEffect } from "react";
import { fetchProductAndReviews } from "@/app/actions/reviewsPageActions";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { generateShareMetadata } from "../lib/shareUtils";

const Reviews = ({ productId }: { productId: string }) => {
  const [product, setProduct] = useState<iProduct | null>(null);
  const [reviews, setReviews] = useState<iReview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userId } = useAuth();
  console.log("this is the product id in the component", productId)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Validate productId
        if (!productId || productId.trim() === '') {
          setError('No product ID provided. Please check the URL.');
          setIsLoading(false);
          return;
        }

        console.log("Fetching product and reviews for productId:", productId);
        
        // Use the server action to fetch both product and reviews
        const data = await fetchProductAndReviews(productId);
        
        console.log("Server action response:", data);

        if (!data.success) {
          console.error("Server action failed:", data.error);
          throw new Error(data.error || 'Failed to fetch data');
        }

        if (!data.data?.product || !data.data?.reviews) {
          console.error("Missing data - product:", !!data.data?.product, "reviews:", !!data.data?.reviews);
          throw new Error('Product or reviews data is missing');
        }

        console.log("Setting product:", data.data.product);
        console.log("Setting reviews:", data.data.reviews.length, "reviews");
        
        setProduct(data.data.product);
        setReviews(data.data.reviews);

        setIsLoading(false);
      } catch (error) {
        console.error("Error in fetchData:", error);
        setError("Failed to load reviews");
        setIsLoading(false);
      }
    };

    fetchData();
  }, [productId]);

  const productCardOptions = {
    showLatestReview: false,
    size: "rating-sm",
    showWriteReview: true,
    showClaimThisProduct: true,
  };

  if (isLoading) return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
        <LoadingSpinner />
      </div>
    </div>
  );
  if (error) return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 text-center max-w-md">
        <div className="text-red-500 text-2xl font-bold mb-3">Oops!</div>
        <p className="text-gray-600 text-lg">{error}</p>
      </div>
    </div>
  );
  if (!product) return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 text-center max-w-md">
        <div className="text-gray-500 text-2xl font-bold mb-3">Product Not Found</div>
        <p className="text-gray-600 text-lg">The product you're looking for doesn't exist.</p>
      </div>
    </div>
  );

  // Generate breadcrumb items
  const breadcrumbItems = [
    { label: "Home", href: "/" },
    { label: "Products", href: "/products" },
    { label: product.name, href: `/reviews?id=${productId}` },
  ];

  // Generate share metadata for the product
  const shareMetadata = product
    ? generateShareMetadata({
      title: `${product.name} | ${reviews.length} Reviews`,
      description: product.description,
      url: typeof window !== "undefined" ? window.location.href : "",
      imageUrl: product.display_image,
      rating: product.rating,
      reviewCount: reviews.length,
    })
    : null;

  // No additional functions needed

  if (reviews.length === 0) {
    return (
      <div className="flex flex-col w-full min-h-screen">
        {/* Full-width ProductCard at the very top */}
        <div className="w-full bg-white/80 backdrop-blur-sm shadow-xl border-b border-white/20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
            <Breadcrumb items={breadcrumbItems} />
            <div className="flex justify-center items-center w-full mt-6">
              <div className="w-full [&>div>div]:max-w-none [&>div>div]:w-full">
                <ProductCard
                  options={productCardOptions}
                  reviews={reviews}
                  product={product}
                  currentUserId={userId ? userId : null}
                  useProductLink={true}
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* Content section underneath */}
        <div className="flex flex-col w-full bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
          <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 py-12 space-y-12">
            <ProductShowcase product={product} />
            
            <div className="w-full text-center space-y-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 max-w-2xl mx-auto">
                <h3 className="text-3xl font-bold text-gray-900 mb-3">No Reviews Yet</h3>
                <p className="text-gray-600 text-lg max-w-md mx-auto mb-6">
                  Be the first to share your experience with this product and help others make informed decisions.
                </p>
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                  <Link
                    href={`/cr?id=${productId}&rating=3`}
                    className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-myTheme-primary to-myTheme-accent hover:from-myTheme-primary/90 hover:to-myTheme-accent/90 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300"
                  >
                    Write the First Review
                  </Link>
                  <Link
                    href={`/product/${productId}`}
                    className="inline-flex items-center px-8 py-4 text-lg font-semibold text-gray-700 bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-gray-300 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300"
                  >
                    <Eye className="mr-2 h-5 w-5" />
                    View Product Details
                  </Link>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-shadow duration-300">
                <CompanyActivityCard product={product} reviews={reviews} />
              </div>
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-shadow duration-300">
                <RatingDistribution reviews={reviews} />
              </div>
            </div>
            
            <FAQSection
              product={product}
              category={product.tags[0]}
              productName={product.name}
            />
          </div>
        </div>
        
        <Schema
          productName={product.name}
          description={product.description}
          rating={0}
          reviewCount={0}
          reviews={[]}
          image={product.display_image}
          category={product.tags[0]}
          sku={product.id}
          brand={product.business?.ownerName || product.name}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Full-width ProductCard at the very top */}
      <div className="w-full bg-white/80 backdrop-blur-sm shadow-xl border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
          <Breadcrumb items={breadcrumbItems} />
          <div className="flex justify-center items-center w-full mt-6">
            <div className="w-full [&>div>div]:max-w-none [&>div>div]:w-full">
              <ProductCard
                options={productCardOptions}
                reviews={reviews}
                product={product}
                currentUserId={userId ? userId : null}
                useProductLink={true}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Content section underneath */}
      <div className="flex flex-col w-full bg-gradient-to-br from-myTheme-lightbg via-white to-blue-50">
        <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 py-12 space-y-12">
          <ProductShowcase product={product} />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
              <CompanyActivityCard product={product} reviews={reviews} />
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
              <RatingDistribution reviews={reviews} />
            </div>
          </div>
          
          <div className="flex flex-col w-full justify-center items-center gap-8 py-10 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gray-900 mb-3">Share Your Experience</h3>
              <p className="text-gray-600 text-lg">Help others make informed decisions</p>
            </div>
            <div className="flex flex-col sm:flex-row w-full justify-center items-center gap-4">
              <WriteAReview hideRecentReviews={true} />
            </div>
            {shareMetadata && (
              <div className="flex justify-center">
                <ShareButtonWrapper
                  metadata={shareMetadata}
                  className="w-full sm:w-auto"
                />
              </div>
            )}
          </div>
          
          <div className="space-y-10">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-gray-900 mb-3">Customer Reviews</h2>
              <p className="text-gray-600 text-lg">{reviews.length} {reviews.length === 1 ? 'review' : 'reviews'} for {product.name}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {reviews.map((review: iReview) => (
                <div key={review.id} className="transform hover:scale-105 transition-transform duration-300">
                  <ReviewCard review={review} />
                </div>
              ))}
            </div>
          </div>
          
          <FAQSection
            product={product}
            category={product.tags[0]}
            productName={product.name}
          />
        </div>
      </div>
      
      <Schema
        productName={product.name}
        description={product.description}
        rating={calculateAverageRating(reviews)}
        reviewCount={reviews.length}
        reviews={reviews.map((review) => ({
          author: review.createdBy || "Anonymous",
          reviewRating: review.rating,
          reviewBody: review.body,
          datePublished:
            review.createdDate?.toString() || new Date().toString(),
        }))}
        image={product.display_image}
        category={product.tags[0]}
        sku={product.id}
        brand={product.business?.ownerName || product.name}
      />
    </div>
  );
};

// Helper function to calculate average rating
const calculateAverageRating = (reviews: iReview[]): number => {
  if (reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return sum / reviews.length;
};

export default Reviews;
