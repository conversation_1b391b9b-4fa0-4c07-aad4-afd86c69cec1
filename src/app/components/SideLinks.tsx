import Link from "next/link";
import Version from "./Version";
import { sideLinks } from "@/app/util/links";
import { IconContext } from "react-icons";
import { FiHome, FiPackage, FiGrid, FiPlusSquare, FiUser, FiHelpCircle, FiAlertTriangle, FiChevronDown, FiFileText, FiBarChart } from "react-icons/fi";
import { IoPricetagOutline } from "react-icons/io5";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "@/app/hooks/useUser";
import { iUser } from "@/app/util/Interfaces";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Skeleton } from "./Skeleton";

interface SideLinksProps {
  onSideLinkClick: () => void;
}

const iconMap: { [key: string]: React.ElementType } = {
  home: FiHome,
  product: FiPackage,
  category: FiGrid,
  add: FiPlusSquare,
  user: FiUser,
  price: IoPricetagOutline,
  bug: FiAlertTriangle,
};

const SideLinks: React.FC<SideLinksProps> = ({ onSideLinkClick }) => {
  const pathname = usePathname();
  const [isBusinessesOpen, setIsBusinessesOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const { user, isLoading: userIsLoading, isLoggedIn, isNotLoggedIn } = useUser();

  useEffect(() => {
    const checkAdminPermission = async () => {
      try {
        const response = await fetch('/api/admin/check-permission');
        const data = await response.json();
        setIsAdmin(data.isAdmin);
      } catch (error) {
        console.error('Error checking admin permission:', error);
        setIsAdmin(false);
      }
    };

    checkAdminPermission();
  }, []);

  return (
    <IconContext.Provider value={{ className: "w-5 h-5" }}>
      <div className="flex flex-col flex-1 justify-start items-start">
        <nav className="w-full space-y-1">
          {sideLinks.map((link, index: number) => {
            // Skip admin link if user is not admin
            if (link.adminOnly && !isAdmin) return null;
            
            // Skip "My Profile" link if user is not logged in
            if (link.name === "My Profile" && isNotLoggedIn) return null;

            const Icon = iconMap[link.icon] || FiHelpCircle;
            const isActive = pathname === link.link;

            if (link.name === 'My Businesses') {
              // Show skeleton only while actually loading user data
              if (userIsLoading) {
                return (
                  <div key={index} className="w-full px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-blue-400 to-blue-600 rounded animate-pulse"></div>
                      <span className="animate-pulse text-blue-600 font-medium">Checking Biz...</span>
                    </div>
                  </div>
                );
              }

              // Hide completely if user is not logged in
              if (isNotLoggedIn) {
                return null;
              }

              return (
                <Collapsible
                  key={index}
                  className="w-full"
                  open={isBusinessesOpen}
                  onOpenChange={setIsBusinessesOpen}>
                  <CollapsibleTrigger
                    className={`
                      flex items-center px-4 py-3 text-sm font-medium rounded-xl w-full transition-all duration-200 group
                      ${isActive
                        ? 'bg-gradient-to-r from-blue-50 to-orange-50 text-myTheme-accent font-semibold shadow-sm border border-blue-100'
                        : 'text-gray-700 hover:text-myTheme-accent hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/30 border border-transparent hover:border-gray-200'}
                    `}
                  >
                    <div className={`p-2 rounded-lg mr-3 transition-all duration-200 ${isActive ? 'bg-gradient-to-br from-blue-100 to-orange-100' : 'bg-gray-100 group-hover:bg-orange-100'}`}>
                      <Icon className={`transition-colors duration-200 ${isActive ? 'text-myTheme-accent' : 'text-gray-600 group-hover:text-myTheme-accent'}`} />
                    </div>
                    <span className="flex-1 text-left">{link.name}</span>
                    <FiChevronDown className="ml-auto w-4 h-4 text-gray-400 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                    {isActive && (
                      <span className="ml-2 w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-orange-500 animate-pulse"></span>
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="ml-4 mt-2 space-y-1">
                    <Link
                      href={link.link}
                      className="flex items-center px-4 py-2.5 text-sm text-gray-600 hover:text-myTheme-accent rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/20 w-full transition-all duration-200 group"
                      onClick={onSideLinkClick}
                    >
                      <div className="p-1.5 rounded-lg mr-3 bg-gray-100 group-hover:bg-orange-100 transition-all duration-200">
                        <IoPricetagOutline className="w-4 h-4 text-gray-500 group-hover:text-myTheme-accent transition-colors duration-200" />
                      </div>
                      My Businesses
                    </Link>
                    {user?.businesses && user.businesses.length > 0 && (
                      <Link
                        href="/owner-admin"
                        className="flex items-center px-4 py-2.5 text-sm text-gray-600 hover:text-myTheme-accent rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/20 w-full transition-all duration-200 group"
                        onClick={onSideLinkClick}
                      >
                        <div className="p-1.5 rounded-lg mr-3 bg-gray-100 group-hover:bg-orange-100 transition-all duration-200">
                          <FiBarChart className="w-4 h-4 text-gray-500 group-hover:text-myTheme-accent transition-colors duration-200" />
                        </div>
                        Advanced Dashboard
                      </Link>
                    )}
                    <Link
                      href="/claim-product"
                      className="flex items-center px-4 py-2.5 text-sm text-gray-600 hover:text-myTheme-accent rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/20 w-full transition-all duration-200 group"
                      onClick={onSideLinkClick}
                    >
                      <div className="p-1.5 rounded-lg mr-3 bg-gray-100 group-hover:bg-orange-100 transition-all duration-200">
                        <FiPackage className="w-4 h-4 text-gray-500 group-hover:text-myTheme-accent transition-colors duration-200" />
                      </div>
                      Claim Product
                    </Link>
                    <Link
                      href="/claims"
                      className="flex items-center px-4 py-2.5 text-sm text-gray-600 hover:text-myTheme-accent rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/20 w-full transition-all duration-200 group"
                      onClick={onSideLinkClick}
                    >
                      <div className="p-1.5 rounded-lg mr-3 bg-gray-100 group-hover:bg-orange-100 transition-all duration-200">
                        <FiFileText className="w-4 h-4 text-gray-500 group-hover:text-myTheme-accent transition-colors duration-200" />
                      </div>
                      My Claims
                    </Link>
                    <Link
                      href="/submit"
                      className="flex items-center px-4 py-2.5 text-sm text-gray-600 hover:text-myTheme-accent rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/20 w-full transition-all duration-200 group"
                      onClick={onSideLinkClick}
                    >
                      <div className="p-1.5 rounded-lg mr-3 bg-gray-100 group-hover:bg-orange-100 transition-all duration-200">
                        <FiPlusSquare className="w-4 h-4 text-gray-500 group-hover:text-myTheme-accent transition-colors duration-200" />
                      </div>
                      Add Product/Business
                    </Link>
                  </CollapsibleContent>
                </Collapsible>
              );
            }

            return (
              <Link
                key={index}
                href={link.link}
                onClick={onSideLinkClick}
                className={`
                  flex items-center px-4 py-3 text-sm font-medium rounded-xl w-full transition-all duration-200 group
                  ${isActive
                    ? 'bg-gradient-to-r from-blue-50 to-orange-50 text-myTheme-accent font-semibold shadow-sm border border-blue-100'
                    : 'text-gray-700 hover:text-myTheme-accent hover:bg-gradient-to-r hover:from-gray-50 hover:to-orange-50/30 border border-transparent hover:border-gray-200'}
                `}
              >
                <div className={`p-2 rounded-lg mr-3 transition-all duration-200 ${isActive ? 'bg-gradient-to-br from-blue-100 to-orange-100' : 'bg-gray-100 group-hover:bg-orange-100'}`}>
                  <Icon className={`transition-colors duration-200 ${isActive ? 'text-myTheme-accent' : 'text-gray-600 group-hover:text-myTheme-accent'}`} />
                </div>
                <span className="flex-1 text-left">{link.name}</span>
                {isActive && (
                  <span className="ml-2 w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-orange-500 animate-pulse"></span>
                )}
              </Link>
            );
          })}
        </nav>
        <div className="flex h-full flex-1 justify-center items-end w-full mt-8">
          <Version />
        </div>
      </div>
    </IconContext.Provider>
  );
};

export default SideLinks;
