"use client";
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Share2 } from "lucide-react";
import { socialPlatforms, getSocialShareUrl } from '../lib/socialSharing';

interface ShareButtonProps {
    title: string;
    text: string;
    url: string;
    imageUrl?: string;
    className?: string;
}

const ShareButton: React.FC<ShareButtonProps> = ({
    title,
    text,
    url,
    imageUrl,
    className = '',
}) => {
    const [isOpen, setIsOpen] = useState(false);

    const handleShare = async (platform: string) => {
        if (platform === 'copy') {
            try {
                await navigator.clipboard.writeText(url);
                // You might want to show a toast notification here
            } catch (err) {
                console.error('Failed to copy:', err);
            }
        } else {
            const shareUrl = getSocialShareUrl({
                platform: platform as any,
                data: { title, text, url, imageUrl },
            });
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
        setIsOpen(false);
    };

    return (
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    className={`flex items-center gap-2 ${className}`}
                >
                    <Share2 size={16} />
                    Share
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                {socialPlatforms.map((platform) => (
                    <DropdownMenuItem
                        key={platform.id}
                        onClick={() => handleShare(platform.id)}
                        className="cursor-pointer"
                    >
                        {platform.name}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default ShareButton; 