"use client";

import React, { useEffect, useState } from "react";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";

interface HeroStats {
  reviews: number;
  businesses: number;
  categories: number;
  activeUsers: number;
}

const ReviewsExploreHero: React.FC = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState<HeroStats>({
    reviews: 0,
    businesses: 0,
    categories: 0,
    activeUsers: 0,
  });

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      // Calculate total reviews
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      // Get unique categories from tags
      const uniqueCategories = new Set<string>();
      allProducts.forEach((product) => {
        product.tags?.forEach((tag) => uniqueCategories.add(tag));
      });

      // Calculate unique reviewers from all reviews
      const uniqueReviewers = new Set<string>();
      allProducts.forEach((product) => {
        product.reviews?.forEach((review) => {
          if (review.userId) {
            uniqueReviewers.add(review.userId);
          }
        });
      });

      setStats({
        reviews: totalReviews,
        businesses: allProducts.length,
        categories: uniqueCategories.size,
        activeUsers: uniqueReviewers.size,
      });
    } else {
      // Reset stats if no products
      setStats({
        reviews: 0,
        businesses: 0,
        categories: 0,
        activeUsers: 0,
      });
    }
  }, [allProducts]);

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return `${Math.floor(num / 1000)}K+`;
    }
    return `${num}+`;
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-myTheme-primary/90 to-myTheme-primary py-16">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.5),transparent)] pointer-events-none" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Explore Reviews from Guyana
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
            Discover authentic reviews and experiences from real Guyanese
            customers. Use our advanced search and filtering tools to find
            exactly what you&apos;re looking for.
          </p>

          {/* Real Data Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mt-12">
            {!allProducts || allProducts.length === 0 ? (
              // Loading skeleton with better animation
              <>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="text-center transform transition-all duration-300 hover:scale-105">
                  <div className="text-3xl font-bold text-white mb-1 drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">
                    {formatNumber(stats.reviews)}
                  </div>
                  <div className="text-white/80 text-sm">Total Reviews</div>
                </div>
                <div className="text-center transform transition-all duration-300 hover:scale-105">
                  <div className="text-3xl font-bold text-white mb-1 drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">
                    {formatNumber(stats.businesses)}
                  </div>
                  <div className="text-white/80 text-sm">Businesses</div>
                </div>
                <div className="text-center transform transition-all duration-300 hover:scale-105">
                  <div className="text-3xl font-bold text-white mb-1 drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">
                    {formatNumber(stats.categories)}
                  </div>
                  <div className="text-white/80 text-sm">Categories</div>
                </div>
                <div className="text-center transform transition-all duration-300 hover:scale-105">
                  <div className="text-3xl font-bold text-white mb-1 drop-shadow-[0_1px_1px_rgba(0,0,0,1)]">
                    {formatNumber(stats.activeUsers)}
                  </div>
                  <div className="text-white/80 text-sm">Active Reviewers</div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ReviewsExploreHero;
