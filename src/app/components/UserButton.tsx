import React, { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useClerk, useUser } from "@clerk/nextjs";
import { LogOut, SquareUserRound, Store, User, Settings, ChevronRight } from "lucide-react";
import { avatarTriggerAtom } from "@/app/store/store";
import { useAtom } from "jotai";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { getUser } from "../util/serverFunctions";
import { iUser } from "../util/Interfaces";
import Link from "next/link";
import { cn } from "@/lib/utils";

const UserButtonComponent: React.FC = () => {
  const router = useRouter();
  const { isLoaded, isSignedIn, user } = useUser();
  const { signOut } = useClerk();
  const [userInDb, setUserFromDb] = React.useState<iUser>();
  const [avatarFromAtom] = useAtom(avatarTriggerAtom);
  const [isHovered, setIsHovered] = React.useState(false);

  useEffect(() => {
    const getUserInDB = async () => {
      if (user) {
        const res = await getUser();
        setUserFromDb(res.data);
      }
    };
    getUserInDB();
  }, [user]);

  if (!isLoaded) {
    return (
      <div className="w-8 h-8 rounded-full bg-gray-200 animate-pulse"></div>
    );
  }

  if (!isSignedIn) {
    return (
      <a
        href="https://accounts.reviewit.gy/sign-in"
        className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-myTheme-accent hover:text-white hover:bg-myTheme-accent bg-transparent transition-all duration-200 rounded-md shadow-sm hover:shadow-md"
      >
        Sign In
      </a>
    );
  }

  const handleLogoutClick = async () => {
    await signOut();
    router.push("/");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div
          className={cn(
            "relative w-9 h-9 overflow-hidden rounded-full cursor-pointer transition-all duration-200",
            isHovered ? "ring-2 ring-myTheme-accent ring-offset-2" : ""
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Image
            src={
              avatarFromAtom ||
              userInDb?.avatar ||
              user?.imageUrl ||
              "/logo.png"
            }
            alt="avatar"
            layout="fill"
            objectFit="cover"
            className="rounded-full transition-transform duration-300 hover:scale-110"
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-64 p-0 rounded-lg shadow-lg border border-gray-100"
        align="end"
        forceMount
        sideOffset={8}
      >
        <div className="p-3 bg-gradient-to-r from-gray-50 to-white border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10 overflow-hidden rounded-full">
              <Image
                src={
                  avatarFromAtom ||
                  userInDb?.avatar ||
                  user?.imageUrl ||
                  "/logo.png"
                }
                alt="avatar"
                layout="fill"
                objectFit="cover"
                className="rounded-full"
              />
            </div>
            <div className="flex flex-col">
              <p className="text-sm font-medium leading-none">{user?.fullName}</p>
              <p className="text-xs leading-none text-muted-foreground mt-1 truncate max-w-[180px]">
                {user?.primaryEmailAddress?.emailAddress}
              </p>
            </div>
          </div>
        </div>

        <div className="py-1">
          <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer hover:bg-gray-50 focus:bg-gray-50">
            <Link href="/userprofile" className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <SquareUserRound className="mr-3 h-4 w-4 text-gray-500" />
                <span>My Profile</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
          </DropdownMenuItem>

          {userInDb?.businesses && userInDb.businesses.length > 0 && (
            <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer hover:bg-gray-50 focus:bg-gray-50">
              <Link href="/mybusinesses" className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  <Store className="mr-3 h-4 w-4 text-gray-500" />
                  <span>My Businesses</span>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </Link>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem asChild className="px-3 py-2 cursor-pointer hover:bg-gray-50 focus:bg-gray-50">
            <Link href="/settings" className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <Settings className="mr-3 h-4 w-4 text-gray-500" />
                <span>Settings</span>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Link>
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator className="my-1" />

        <DropdownMenuItem
          onClick={handleLogoutClick}
          className="px-3 py-2 cursor-pointer hover:bg-red-50 focus:bg-red-50 text-red-600 hover:text-red-700"
        >
          <LogOut className="mr-3 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserButtonComponent;
