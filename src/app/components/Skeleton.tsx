import { cn } from "@/lib/utils";

interface SkeletonProps {
    className?: string;
    children?: React.ReactNode;
}

export function Skeleton({ className, ...props }: SkeletonProps) {
    return (
        <div
            className={cn("animate-pulse rounded-md bg-gray-200", className)}
            {...props}
        />
    );
}

// Pre-built skeleton components for common use cases
export function NavSkeleton() {
    return (
        <div className="inline-flex items-center px-2 md:px-3 lg:px-4 py-2 text-sm font-medium rounded-md">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="ml-1 w-4 h-4" />
        </div>
    );
}

export function UserAvatarSkeleton() {
    return (
        <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-4 w-16" />
        </div>
    );
}

export function ButtonSkeleton({ width = "w-20" }: { width?: string }) {
    return <Skeleton className={`h-9 ${width} rounded-md`} />;
}

export default Skeleton; 