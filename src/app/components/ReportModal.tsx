'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface ReportModalProps {
    isOpen: boolean;
    onClose: () => void;
    reviewId: string;
    onSubmit: (data: { reason: string; additionalNotes?: string }) => Promise<void>;
    isLoading?: boolean;
}

const REPORT_REASONS = [
    'Inappropriate content',
    'Spam or misleading',
    'Offensive language',
    'False information',
    'Other',
];

export default function ReportModal({ isOpen, onClose, reviewId, onSubmit, isLoading }: ReportModalProps) {
    const [reason, setReason] = useState('');
    const [additionalNotes, setAdditionalNotes] = useState('');
    const [error, setError] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        if (!reason) {
            setError('Please select a reason for reporting');
            return;
        }

        try {
            await onSubmit({ reason, additionalNotes });
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to submit report');
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Report Review</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="reason">Reason for reporting</Label>
                        <select
                            id="reason"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            className="w-full p-2 border rounded-md"
                            disabled={isLoading}
                        >
                            <option value="">Select a reason</option>
                            {REPORT_REASONS.map((r) => (
                                <option key={r} value={r}>
                                    {r}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="notes">Additional notes (optional)</Label>
                        <Textarea
                            id="notes"
                            value={additionalNotes}
                            onChange={(e) => setAdditionalNotes(e.target.value)}
                            placeholder="Please provide any additional details..."
                            disabled={isLoading}
                        />
                    </div>

                    {error && (
                        <div className="text-red-500 text-sm">{error}</div>
                    )}

                    <div className="flex justify-end space-x-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={isLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isLoading}
                        >
                            {isLoading ? 'Submitting...' : 'Submit Report'}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
} 