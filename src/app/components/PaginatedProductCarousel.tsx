"use client";

import React, { useState, useMemo } from 'react';
import { iProduct } from '@/app/util/Interfaces';
import MiniProductCard from './MiniProductCard'; // Changed import
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginatedProductCarouselProps {
  allProducts: iProduct[];
  // currentUserId might not be needed if MiniProductCard doesn't use it for claim/owner features
  // currentUserId: string | null;
  miniCardOptions: { // Renamed and simplified options
    size: string; // For rating stars
    showWriteReview: boolean;
  };
  itemsPerPage?: number;
  title?: string;
}

const PaginatedProductCarousel: React.FC<PaginatedProductCarouselProps> = ({
  allProducts,
  // currentUserId,
  miniCardOptions,
  itemsPerPage = 3, // Default to 3 items per page for a carousel view
  title = "Products You Might Be Interested In",
}) => {
  const [currentPage, setCurrentPage] = useState(0);

  const sortedProducts = useMemo(() => {
    return [...allProducts].sort((a, b) => {
      const reviewsA = a._count?.reviews ?? a.reviews?.length ?? 0;
      const reviewsB = b._count?.reviews ?? b.reviews?.length ?? 0;
      return reviewsB - reviewsA; // Sort by most reviews descending
    });
  }, [allProducts]);

  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = sortedProducts.slice(startIndex, endIndex);

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1));
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 0));
  };

  if (!allProducts || allProducts.length === 0) {
    return null; // Or some placeholder if no products
  }

  return (
    <div className="py-8">
      {title && <h2 className="text-2xl font-semibold mb-6 text-center sm:text-left">{title}</h2>}
      <div className="relative">
        <div className="flex overflow-x-auto space-x-4 pb-4 scrollbar-hide">
          {currentProducts.map(product => (
            <div key={product.id} className="w-80 md:w-96 flex-shrink-0 h-full"> {/* Added h-full */}
              <MiniProductCard // Changed component
                product={product}
                options={miniCardOptions}
                // currentUserId={currentUserId} // Pass if MiniProductCard needs it
              />
            </div>
          ))}
          {currentProducts.length === 0 && <p>No products match the criteria.</p>}
        </div>

        {totalPages > 1 && (
          <div className="flex justify-center items-center mt-6 space-x-2">
            <Button
              onClick={handlePrevPage}
              disabled={currentPage === 0}
              variant="outline"
              size="icon"
              aria-label="Previous products"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {currentPage + 1} of {totalPages}
            </span>
            <Button
              onClick={handleNextPage}
              disabled={currentPage === totalPages - 1 || totalPages === 0}
              variant="outline"
              size="icon"
              aria-label="Next products"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaginatedProductCarousel;