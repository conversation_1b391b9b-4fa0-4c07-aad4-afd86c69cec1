import ShareButton from './ShareButton';
import { ShareMetadata, getShareImageUrl } from '../lib/shareUtils';

interface ShareButtonWrapperProps {
    metadata: ShareMetadata;
    className?: string;
}

export function ShareButtonWrapper({ metadata, className }: ShareButtonWrapperProps) {
    return (
        <ShareButton
            title={String(metadata.title ?? '')}
            text={metadata.description ?? ''}
            url={metadata.openGraph?.url?.toString() ?? ''}
            imageUrl={getShareImageUrl(metadata)}
            className={className}
        />
    );
} 