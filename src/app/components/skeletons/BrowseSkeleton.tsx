"use client";

import { Skeleton } from "@/components/ui/skeleton";

const BrowseSkeleton = () => {
  const skeletonItems = Array(8).fill(null);

  return (
    <div className="flex flex-col w-full min-h-screen p-4 space-y-6">
      {/* Search and filters section */}
      <div className="flex flex-col space-y-4 w-full max-w-7xl mx-auto">
        <div className="flex justify-between items-center w-full">
          <Skeleton className="h-8 w-48" /> {/* Title */}
          <Skeleton className="h-10 w-32" /> {/* Action button */}
        </div>
        <Skeleton className="h-12 w-full rounded-lg" /> {/* Search bar */}
        <div className="flex gap-4 flex-wrap">
          {Array(4)
            .fill(null)
            .map((_, index) => (
              <Skeleton
                key={index}
                className="h-10 w-24 rounded-md"
              /> /* Filter chips */
            ))}
        </div>
      </div>

      {/* Browse grid */}
      <div className="w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
        {skeletonItems.map((_, index) => (
          <div key={index} className="flex flex-col space-y-3">
            <Skeleton className="w-full aspect-square rounded-lg" /> {/* Image */}
            <Skeleton className="h-5 w-3/4" /> {/* Title */}
            <Skeleton className="h-4 w-1/2" /> {/* Subtitle */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BrowseSkeleton;
