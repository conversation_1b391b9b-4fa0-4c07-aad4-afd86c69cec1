"use client";
import { Skeleton } from "@/components/ui/skeleton";
import ReviewBoxSkeleton from "./ReviewBoxSkeleton";
import LoadingSpinner from "../LoadingSpinner";

const TopReviewsSkeleton = () => {
  // Create an array of 6 items to match the default visible reviews count
  const skeletonItems = Array(6).fill(null);

  return (
    <div className="flex flex-col w-full h-full justify-center items-center space-y-6 relative z-0">
      <div className="w-full flex flex-col items-center space-y-4">
        <Skeleton className="h-8 w-48" /> {/* Title skeleton */}
        <div className="flex gap-4 flex-wrap justify-center">
          <Skeleton className="h-10 w-28 rounded-md" />{" "}
          {/* Filter button skeleton */}
          <Skeleton className="h-10 w-36 rounded-md" />{" "}
          {/* Filter button skeleton */}
          <Skeleton className="h-10 w-32 rounded-md" />{" "}
          {/* Filter button skeleton */}
        </div>
      </div>

      {/* Loading indicator overlay */}
      <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
        <div className="bg-white/80 rounded-xl p-6 shadow-md flex flex-col items-center">
          <LoadingSpinner size="md" />
          <p className="mt-2 text-gray-600 font-medium">Loading reviews...</p>
        </div>
      </div>

      <div className="w-full grid mx-auto items-start justify-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
        {skeletonItems.map((_, index) => (
          <ReviewBoxSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

export default TopReviewsSkeleton;
