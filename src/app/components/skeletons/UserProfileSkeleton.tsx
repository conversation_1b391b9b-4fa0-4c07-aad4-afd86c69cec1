"use client";

import { Skeleton } from "@/components/ui/skeleton";

const UserProfileSkeleton = () => {
  // Generate random gradient similar to the actual page
  const gradientColors = [
    'from-rose-50 via-white to-pink-50',
    'from-blue-50 via-white to-indigo-50', 
    'from-green-50 via-white to-emerald-50',
    'from-purple-50 via-white to-violet-50',
    'from-orange-50 via-white to-amber-50',
    'from-cyan-50 via-white to-teal-50',
    'from-indigo-50 via-white to-cyan-50'
  ];
  
  const animatedGradients = [
    ['from-rose-400/20 to-pink-600/20', 'from-pink-400/20 to-rose-600/20', 'from-rose-400/10 to-pink-600/10'],
    ['from-blue-400/20 to-purple-600/20', 'from-cyan-400/20 to-blue-600/20', 'from-purple-400/10 to-pink-600/10'],
    ['from-green-400/20 to-emerald-600/20', 'from-emerald-400/20 to-teal-600/20', 'from-green-400/10 to-emerald-600/10']
  ];
  
  const selectedGradient = gradientColors[1]; // Use blue gradient for consistency
  const selectedAnimated = animatedGradients[1];

  return (
    <div className={`min-h-screen bg-gradient-to-br ${selectedGradient} relative`}>
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[0]} rounded-full blur-3xl animate-pulse`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[1]} rounded-full blur-3xl animate-pulse delay-1000`}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br ${selectedAnimated[2]} rounded-full blur-3xl animate-pulse delay-500`}></div>
      </div>

      {/* Hero Section Skeleton */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-cyan-600/5"></div>
        <div className="relative pt-20 pb-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Welcome Message Skeleton */}
            <div className="text-center mb-8">
              <Skeleton className="h-12 sm:h-16 md:h-20 w-80 mx-auto mb-4 bg-gradient-to-r from-blue-200 to-purple-200" />
              <Skeleton className="h-6 w-96 mx-auto bg-gray-200" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="relative px-4 sm:px-6 lg:px-8 pb-12">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            
            {/* Profile Header Section */}
            <div className="relative">
              {/* Cover Photo Skeleton */}
              <div className="h-48 sm:h-64 bg-gradient-to-r from-blue-400 to-purple-500 animate-pulse relative">
                <div className="absolute inset-0 bg-black/10"></div>
              </div>
              
              {/* Profile Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
                  {/* Avatar */}
                  <div className="relative">
                    <Skeleton className="w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-white shadow-lg" />
                    <Skeleton className="absolute bottom-2 right-2 w-8 h-8 rounded-full bg-white/80" />
                  </div>
                  
                  {/* User Info */}
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-8 w-48 bg-white/80" />
                    <Skeleton className="h-5 w-32 bg-white/60" />
                    <Skeleton className="h-4 w-64 bg-white/60" />
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Skeleton className="h-10 w-24 rounded-lg bg-white/80" />
                    <Skeleton className="h-10 w-10 rounded-lg bg-white/80" />
                  </div>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="p-6 border-b border-gray-100">
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                {["Reviews", "Comments", "Likes", "Businesses"].map((stat, index) => (
                  <div key={index} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 text-center">
                    <Skeleton className="h-8 w-16 mx-auto mb-2" />
                    <Skeleton className="h-4 w-20 mx-auto" />
                  </div>
                ))}
              </div>
            </div>

            {/* Tabs Section */}
            <div className="p-6">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200 mb-6">
                <div className="flex space-x-8">
                  {["Reviews", "Comments", "Liked", "Businesses", "Settings"].map((tab, index) => (
                    <Skeleton key={index} className="h-10 w-20 rounded-t-lg" />
                  ))}
                </div>
              </div>

              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <Skeleton className="h-10 flex-1 rounded-lg" />
                <Skeleton className="h-10 w-32 rounded-lg" />
                <Skeleton className="h-10 w-32 rounded-lg" />
              </div>

              {/* Content Grid */}
              <div className="space-y-6">
                {/* Reviews/Content Items */}
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <div key={item} className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                    {/* Review Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="w-10 h-10 rounded-full" />
                        <div className="space-y-1">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                      </div>
                      <Skeleton className="h-8 w-20 rounded-full" />
                    </div>
                    
                    {/* Review Content */}
                    <div className="space-y-3">
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                    
                    {/* Review Images */}
                    <div className="flex gap-2 mt-4">
                      <Skeleton className="w-20 h-20 rounded-lg" />
                      <Skeleton className="w-20 h-20 rounded-lg" />
                      <Skeleton className="w-20 h-20 rounded-lg" />
                    </div>
                    
                    {/* Review Actions */}
                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-4">
                        <Skeleton className="h-8 w-16 rounded-full" />
                        <Skeleton className="h-8 w-16 rounded-full" />
                        <Skeleton className="h-8 w-16 rounded-full" />
                      </div>
                      <Skeleton className="h-8 w-20 rounded-lg" />
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <Skeleton className="h-10 w-10 rounded-lg" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileSkeleton;