"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

const ReviewBoxSkeleton = () => {
  // Animation variants for the card
  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="bg-white/80 border border-gray-100 rounded-xl p-4 shadow-sm w-full relative flex flex-col h-[300px] overflow-hidden backdrop-blur-sm"
    >
      {/* Enhanced shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-100/70 to-transparent bg-[length:200%_100%] animate-shimmer" />

      {/* Header Section Skeleton */}
      <div className="flex items-start space-x-2 mb-1.5 flex-shrink-0">
        <div className="flex-shrink-0">
          <Skeleton className="w-7 h-7 rounded-full" />
        </div>

        <div className="flex-grow min-w-0">
          <div className="flex flex-wrap items-center gap-1 mb-0.5">
            <Skeleton className="h-3 w-20" />
            <div className="text-gray-400 text-xs">•</div>
            <Skeleton className="h-2.5 w-14" />
          </div>

          <div className="flex items-center gap-1">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-6" />
          </div>
        </div>
      </div>

      {/* Product Link & Tags Skeleton */}
      <div className="mb-1">
        <div className="inline-flex items-center gap-1">
          <Skeleton className="w-3 h-3 rounded-md" />
          <Skeleton className="h-3 w-24" />
        </div>
        <div className="flex flex-wrap items-center gap-1 mt-0.5">
          <Skeleton className="h-4 w-12 rounded-full" />
          <Skeleton className="h-4 w-12 rounded-full" />
        </div>
      </div>

      {/* Review Content Skeleton */}
      <div className="flex-grow overflow-hidden min-h-0 h-[130px]">
        <Skeleton className="h-4 w-3/4 mb-1" />
        <Skeleton className="h-2.5 w-full mb-1" />
        <Skeleton className="h-2.5 w-full mb-1" />
        <Skeleton className="h-2.5 w-2/3 mb-1" />

        <div className="flex gap-1 mt-2">
          <Skeleton className="h-10 w-10 rounded-md" />
          <Skeleton className="h-10 w-10 rounded-md" />
          <Skeleton className="h-10 w-10 rounded-md" />
        </div>

        <div className="mt-auto pt-1 border-t border-gray-100">
          <Skeleton className="h-6 w-32 rounded-full" />
        </div>
      </div>

      {/* Action Buttons Skeleton */}
      <div className="flex items-center justify-between gap-2 pt-1 border-t border-gray-100 mt-auto flex-shrink-0">
        <div className="flex items-center gap-2">
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-12" />
        </div>
      </div>
    </motion.div>
  );
};

export default ReviewBoxSkeleton;
