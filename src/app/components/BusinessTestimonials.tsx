"use client";

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { getProducts } from "@/app/util/serverFunctions";
import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";

interface Testimonial {
  name: string;
  business: string;
  content: string;
  rating: number;
  image?: string;
}

const BusinessTestimonials: React.FC = () => {
  const [allProducts, setAllProducts] = useAtom(allProductsStore);

  // Background fetch with atom as initial data
  const { data, isLoading } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - consider data fresh
    initialData:
      allProducts?.length > 0
        ? { success: true, data: allProducts }
        : undefined,
  }) as any;

  // Update the atom when fresh data arrives
  React.useEffect(() => {
    if (data && data.success && data.data && data.data !== allProducts) {
      setAllProducts(data.data);
    }
  }, [data, allProducts, setAllProducts]);

  // Default testimonials to show if no real data is available
  const defaultTestimonials: Testimonial[] = [
    {
      name: "Sarah Chen",
      business: "Chen's Restaurant",
      content:
        "ReviewIt helped us understand our customers better and improve our service quality. The insights we gained led to a 30% increase in repeat customers.",
      rating: 5,
    },
    {
      name: "Michael Singh",
      business: "Singh Electronics",
      content:
        "The analytics dashboard gives us insights we never had before. We can now respond to customer feedback quickly and effectively. Highly recommended!",
      rating: 5,
    },
    {
      name: "Jennifer Williams",
      business: "Bella Beauty Salon",
      content:
        "Our customer engagement improved significantly since joining ReviewIt. We've seen a 40% increase in new clients through positive reviews.",
      rating: 5,
    },
  ];

  // Generate testimonials from real data if available
  const generateTestimonialsFromData = () => {
    if (!allProducts || allProducts.length === 0) {
      return defaultTestimonials;
    }

    // Find businesses with owners and good ratings
    const businessesWithOwners = allProducts
      .filter(product => product.hasOwner && product.rating >= 4.0)
      .slice(0, 3);

    if (businessesWithOwners.length < 3) {
      return defaultTestimonials;
    }

    // Create testimonials from real business data
    return businessesWithOwners.map(business => {
      // Generate a realistic testimonial based on business data
      const testimonialContent = [
        `ReviewIt has transformed how we connect with our customers. The platform's analytics have been invaluable for our growth.`,
        `Since joining ReviewIt, we've seen a significant increase in customer engagement and satisfaction. The review management tools are excellent.`,
        `The ability to respond to customer reviews directly has improved our relationship with our clients. ReviewIt's platform is intuitive and powerful.`
      ][Math.floor(Math.random() * 3)];

      return {
        name: business.ownerId ? `${business.name} Owner` : "Business Owner",
        business: business.name,
        content: testimonialContent,
        rating: Math.round(business.rating),
        image: business.display_image
      };
    });
  };

  const testimonials = generateTestimonialsFromData();

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Business Owners Say
          </h2>
          <p className="text-xl text-gray-600">
            Real feedback from real businesses across Guyana
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {isLoading ? (
            // Loading skeletons
            Array(3).fill(0).map((_, index) => (
              <Card key={index} className="bg-white border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex text-yellow-400 mb-4 animate-pulse">
                    <div className="bg-yellow-100 h-4 w-24 rounded"></div>
                  </div>
                  <div className="h-24 bg-gray-100 rounded mb-4 animate-pulse"></div>
                  <div>
                    <div className="h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-100 rounded w-24 animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex text-yellow-400 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4 italic">
                    &ldquo;{testimonial.content}&rdquo;
                  </blockquote>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.business}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default BusinessTestimonials;
