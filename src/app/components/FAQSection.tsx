import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { iProduct } from "@/app/util/Interfaces"
import { generateProductFAQs } from "@/app/util/faqGenerator"
import { useProductAnalytics } from "@/app/hooks/useProductAnalytics"

interface FAQ {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  faqs?: FAQ[];
  category?: string;
  productName: string;
  product?: iProduct;
}

export default function FAQSection({ faqs = [], category, productName, product }: FAQSectionProps) {
  // Initialize analytics if product is available
  const { logManualEvent } = useProductAnalytics(product?.id || '', {
    trackClicks: true,
  });

  const defaultFAQs: FAQ[] = [
    {
      question: "How do I write a review?",
      answer: "Click the 'Write Review' button on any product page. You can rate the product/service, write your experience, and add photos or videos to your review."
    },
    {
      question: "Are the reviews verified?",
      answer: "All reviews are from registered users. Reviews with verified purchase badges indicate the reviewer has proven their interaction with the business."
    },
    {
      question: "Can business owners respond to reviews?",
      answer: "Yes, verified business owners can respond to reviews through their business dashboard after claiming their listing."
    }
  ];

  // Generate product-specific FAQs if product data is available
  const productSpecificFAQs = product ? generateProductFAQs(product) : [];

  // Combine all FAQs, prioritizing product-specific ones
  const allFAQs = [...productSpecificFAQs, ...faqs, ...defaultFAQs];

  // Schema markup for FAQs
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": allFAQs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  // Track FAQ interactions
  const handleFAQInteraction = (question: string) => {
    if (product?.id) {
      logManualEvent('faq_interaction', { question });
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto py-8">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />

      <h2 className="text-2xl font-bold mb-6">
        Frequently Asked Questions about {productName}
        {category && ` in ${category}`}
      </h2>

      <Accordion type="single" collapsible className="w-full">
        {allFAQs.map((faq, index) => (
          <AccordionItem
            key={index}
            value={`item-${index}`}
            onClick={() => handleFAQInteraction(faq.question)}
          >
            <AccordionTrigger className="text-left">
              {faq.question}
            </AccordionTrigger>
            <AccordionContent>
              <p className="text-gray-600">{faq.answer}</p>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
} 