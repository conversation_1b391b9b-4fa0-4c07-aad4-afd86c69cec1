"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useAuth } from "@clerk/nextjs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, CheckCircle, XCircle, Clock, Search, Filter, SortAsc, SortDesc, RefreshCw } from "lucide-react";
import { iProductClaim } from "@/app/util/Interfaces";
import Link from "next/link";
import { toast } from "sonner";

// Debounce utility function
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return debouncedValue;
}

// Error types for better error handling
type ErrorType =
    | "NETWORK_ERROR"
    | "AUTHENTICATION_ERROR"
    | "SERVER_ERROR"
    | "NOT_FOUND"
    | "UNKNOWN";

interface ErrorDetails {
    type: ErrorType;
    message: string;
    retryable: boolean;
}

export default function ClaimsHistoryComponent() {
    const { userId } = useAuth();
    const [claims, setClaims] = useState<iProductClaim[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<ErrorDetails | null>(null);
    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms debounce
    const [statusFilter, setStatusFilter] = useState<string>("all");
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loadingMore, setLoadingMore] = useState(false);
    const [retryCount, setRetryCount] = useState(0);
    const MAX_RETRIES = 3;

    // Enhanced error handling function
    const handleError = (err: any): ErrorDetails => {
        console.error("Error fetching claims:", err);

        if (!navigator.onLine) {
            return {
                type: "NETWORK_ERROR",
                message: "You're offline. Please check your internet connection.",
                retryable: true
            };
        }

        if (err instanceof Error) {
            if (err.message.includes("401") || err.message.includes("Unauthorized")) {
                return {
                    type: "AUTHENTICATION_ERROR",
                    message: "Your session has expired. Please sign in again.",
                    retryable: false
                };
            }

            if (err.message.includes("404")) {
                return {
                    type: "NOT_FOUND",
                    message: "No claims found matching your criteria.",
                    retryable: false
                };
            }

            if (err.message.includes("500")) {
                return {
                    type: "SERVER_ERROR",
                    message: "Server error. Please try again later.",
                    retryable: true
                };
            }
        }

        return {
            type: "UNKNOWN",
            message: "An unexpected error occurred. Please try again.",
            retryable: true
        };
    };

    // Fetch claims with enhanced error handling and retry logic
    const fetchClaims = useCallback(async (loadMore = false) => {
        if (!userId) return;

        try {
            if (loadMore) {
                setLoadingMore(true);
            } else {
                setLoading(true);
            }

            const params = new URLSearchParams({
                page: loadMore ? page.toString() : "1",
                limit: "10",
                ...(statusFilter !== "all" && { status: statusFilter }),
                sortOrder,
                ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
            });

            const response = await fetch(`/api/user/claims?${params}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            // Ensure data.claims is an array, even if empty
            const claimsData = Array.isArray(data.claims) ? data.claims : [];
            setClaims(prev => loadMore ? [...prev, ...claimsData] : claimsData);
            setHasMore(data.hasMore || false);
            setError(null);
            setRetryCount(0); // Reset retry count on success
        } catch (err) {
            const errorDetails = handleError(err);
            setError(errorDetails);

            if (errorDetails.retryable && retryCount < MAX_RETRIES) {
                setRetryCount(prev => prev + 1);
                toast.error(`${errorDetails.message} Retrying... (${retryCount + 1}/${MAX_RETRIES})`);

                // Exponential backoff for retries
                const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
                setTimeout(() => {
                    fetchClaims(loadMore);
                }, backoffTime);
            } else {
                toast.error(errorDetails.message);
            }
        } finally {
            setLoading(false);
            setLoadingMore(false);
        }
    }, [userId, statusFilter, sortOrder, page, debouncedSearchTerm, retryCount]);

    // Effect to fetch claims when filters change
    useEffect(() => {
        fetchClaims();
    }, [fetchClaims]);

    const loadMore = () => {
        setPage(prev => prev + 1);
        fetchClaims(true);
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "PENDING":
                return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200"><Clock className="w-3 h-3 mr-1" /> Pending</Badge>;
            case "APPROVED":
                return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200"><CheckCircle className="w-3 h-3 mr-1" /> Approved</Badge>;
            case "REJECTED":
                return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200"><XCircle className="w-3 h-3 mr-1" /> Rejected</Badge>;
            default:
                return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200"><AlertCircle className="w-3 h-3 mr-1" /> Unknown</Badge>;
        }
    };

    // Client-side filtering is no longer needed since we're using server-side search
    const filteredClaims = claims;

    if (!userId) {
        return (
            <div className="p-6 text-center">
                <p className="text-gray-600">Please sign in to view your claims.</p>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="p-6 space-y-4">
                {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2">
                            <Skeleton className="h-4 w-[250px]" />
                            <Skeleton className="h-4 w-[200px]" />
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6 text-center">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                    <p className="text-red-700 font-medium">{error.message}</p>
                    {error.retryable && (
                        <Button
                            onClick={() => {
                                setRetryCount(0);
                                fetchClaims();
                            }}
                            className="mt-4"
                            variant="outline"
                        >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Try Again
                        </Button>
                    )}
                </div>
                {error.type === "AUTHENTICATION_ERROR" && (
                    <Link href="https://accounts.reviewit.gy/sign-in">
                        <Button variant="default">Sign In</Button>
                    </Link>
                )}
            </div>
        );
    }

    return (
        <div className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search claims..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                            aria-label="Search claims"
                        />
                    </div>
                </div>
                <div className="flex gap-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                            <Filter className="h-4 w-4 mr-2" />
                            <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="APPROVED">Approved</SelectItem>
                            <SelectItem value="REJECTED">Rejected</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button
                        variant="outline"
                        onClick={() => setSortOrder(prev => prev === "asc" ? "desc" : "asc")}
                        aria-label={`Sort ${sortOrder === "asc" ? "newest first" : "oldest first"}`}
                    >
                        {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                    </Button>
                </div>
            </div>

            {filteredClaims.length === 0 ? (
                <div className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No claims found</h3>
                    <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
                    <Link href="/claim-product">
                        <Button>Claim a Product</Button>
                    </Link>
                </div>
            ) : (
                <div className="space-y-4">
                    {filteredClaims.map((claim) => (
                        <Card key={claim.id} className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-2">
                                <div className="flex justify-between items-start">
                                    <div>
                                        <CardTitle className="text-lg">
                                            <Link href={`/product/${claim.productId}`} className="hover:text-myTheme-primary">
                                                {claim.product?.name || "Unknown Product"}
                                            </Link>
                                        </CardTitle>
                                        <p className="text-sm text-gray-500">
                                            Claimed on {new Date(claim.createdAt).toLocaleDateString()}
                                        </p>
                                    </div>
                                    {getStatusBadge(claim.status)}
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {claim.contactInfo && (
                                        <div className="text-sm space-y-1">
                                            <p className="font-medium">Contact Information:</p>
                                            {(() => {
                                                try {
                                                    const contactData = JSON.parse(claim.contactInfo);
                                                    return (
                                                        <div className="pl-3 space-y-1 text-gray-600">
                                                            {contactData.name && <p>Name: {contactData.name}</p>}
                                                            {contactData.email && <p>Email: {contactData.email}</p>}
                                                            {contactData.phone && <p>Phone: {contactData.phone}</p>}
                                                        </div>
                                                    );
                                                } catch (e) {
                                                    // Fallback to showing raw data if parsing fails
                                                    return <p className="pl-3 text-gray-600">{claim.contactInfo}</p>;
                                                }
                                            })()}
                                        </div>
                                    )}
                                    {claim.additionalInfo && (
                                        <p className="text-sm">
                                            <span className="font-medium">Additional Info:</span> {claim.additionalInfo}
                                        </p>
                                    )}
                                    {claim.images && claim.images.length > 0 && (
                                        <div className="flex gap-2 mt-2">
                                            {claim.images.map((image, index) => (
                                                <img
                                                    key={index}
                                                    src={image}
                                                    alt={`Claim evidence ${index + 1}`}
                                                    className="h-20 w-20 object-cover rounded-md"
                                                />
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}

                    {hasMore && (
                        <div className="text-center mt-6">
                            <Button
                                onClick={loadMore}
                                disabled={loadingMore}
                                variant="outline"
                            >
                                {loadingMore ? "Loading..." : "Load More"}
                            </Button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
} 