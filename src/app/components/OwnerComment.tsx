"use client";
import React, { useState } from "react";
import { iComment, iUser, iReview } from "../util/Interfaces";
import dayjs from "dayjs";
import Link from "next/link";
import {
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MessageSquareIcon,
  CheckCircleIcon,
  ReplyIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import OptionsMenu from "./CommentOptionsMenu";
import { useAuth } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { toast } from "sonner";
import { Tooltip } from "@mantine/core";

interface OwnerCommentProps {
  comment: iComment;
  onDelete: (commentId: string) => Promise<void>;
  depth: number;
  clerkUserId: string;
  currentUser: iUser;
  productName: string;
  review: iReview | undefined;
}

const DEPTH_COLORS = [
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
  "border-blue-100",
  "border-blue-50",
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
];

const OwnerComment: React.FC<OwnerCommentProps> = ({
  comment: initialComment,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser,
  productName,
  review,
}) => {
  const { userId } = useAuth();
  const [comment, setComment] = useState(initialComment);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyBody, setReplyBody] = useState("");

  // More robust ownership check - try multiple ID fields
  const isCommentOwner = Boolean(userId && (
    userId === comment.user?.clerkUserId || 
    userId === comment.user?.id || 
    userId === comment.userId ||
    clerkUserId === comment.user?.clerkUserId ||
    clerkUserId === comment.user?.id ||
    clerkUserId === comment.userId
  ));
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];

  const handleReply = async () => {
    // Owner comments should use the proper permission system
    // This is handled by the parent Comment component
    setIsReplying(false);
    setReplyBody("");
  };

  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (comment.id && isCommentOwner) {
      // Removed onEdit call
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
    }
  };

  const handleDelete = async () => {
    if (comment.id && isCommentOwner) {
      await onDelete(comment.id);
      const updatedComment = { ...comment, isDeleted: true };
      setComment(updatedComment);
    }
  };

  return (
    <div
      id={comment.id || `comment-${Date.now()}`}
      className="w-full mt-3"
    >
      <div className="flex">
        <div className="flex-1">
          <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center text-sm mb-3">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Avatar className="w-8 h-8">
                    <AvatarImage
                      src={comment.user?.avatar || "/default-avatar.png"}
                      alt={`${comment.user?.firstName} ${comment.user?.lastName}`}
                    />
                    <AvatarFallback className="bg-blue-500 text-white text-xs">
                      {comment.user?.firstName?.charAt(0)}
                      {comment.user?.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-blue-500 rounded-full border border-white flex items-center justify-center">
                    <CheckCircleIcon className="w-2 h-2 text-white" />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Link
                    href={profileUrl(comment.user!)}
                    className="font-medium text-gray-900 text-sm hover:text-blue-600 transition-colors"
                  >
                    @{comment.user?.userName}
                  </Link>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Owner
                  </span>
                </div>
              </div>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-500 text-sm">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>
              {isCommentOwner && (
                <div className="ml-auto">
                  <OptionsMenu
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setIsEditing={setIsEditing}
                  />
                </div>
              )}
            </div>
            <div className="text-sm leading-relaxed break-words">
              {isEditing ? (
                <Textarea
                  value={editedBody}
                  onChange={(e) => setEditedBody(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] bg-white text-gray-900 placeholder-gray-500 text-sm"
                  placeholder="Share your thoughts as a verified business owner..."
                />
              ) : comment.isDeleted ? (
                <span className="italic text-gray-400 text-sm">
                  {comment.body}
                </span>
              ) : showFullComment || comment.body.length <= 200 ? (
                <div className="whitespace-pre-line text-gray-700 leading-relaxed text-sm">{comment.body}</div>
              ) : (
                <>
                  <div className="whitespace-pre-line text-gray-700 leading-relaxed text-sm">
                    {comment.body.slice(0, 200)}...
                  </div>
                  <button
                    onClick={() => setShowFullComment(true)}
                    className="text-blue-600 hover:text-blue-700 text-xs font-medium mt-2 underline flex items-center"
                  >
                    <span>Read more</span>
                    <ChevronDownIcon className="w-3 h-3 ml-1" />
                  </button>
                </>
              )}
            </div>
            {isEditing && (
              <div className="flex mt-4 space-x-2">
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  <SaveIcon className="w-4 h-4 mr-1" />
                  Save
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  size="sm"
                  variant="outline"
                  className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Cancel
                </Button>
              </div>
            )}
            {/* Reply functionality is now handled by the parent Comment component with proper permissions */}
            {isReplying && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <Textarea
                  value={replyBody}
                  onChange={(e) => setReplyBody(e.target.value)}
                  placeholder="Write a thoughtful reply to this verified business owner..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] bg-white text-gray-900 placeholder-gray-500 text-sm"
                />
                <div className="flex justify-end mt-3 space-x-2">
                  <Button
                    onClick={handleReply}
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!replyBody.trim()}
                  >
                    <ReplyIcon className="w-4 h-4 mr-1" />
                    Post Reply
                  </Button>
                  <Button
                    onClick={() => {
                      setIsReplying(false);
                      setReplyBody("");
                    }}
                    size="sm"
                    variant="outline"
                    className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OwnerComment;
