"use client";

import React, { useEffect, useState } from "react";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { Building2, ArrowRight, Zap, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface BusinessStats {
  activeBusinesses: number;
  totalReviews: number;
  satisfactionRate: number;
  responseRate: number;
}

const BusinessPageHero: React.FC = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState<BusinessStats>({
    activeBusinesses: 0,
    totalReviews: 0,
    satisfactionRate: 0,
    responseRate: 0,
  });

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      // Calculate total reviews
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      // Calculate businesses with owners (claimed businesses)
      const claimedBusinesses = allProducts.filter(
        (product) => product.hasOwner
      ).length;

      // Calculate average rating as a proxy for satisfaction rate
      const totalRatings = allProducts.reduce((acc, product) => {
        return acc + (product.rating || 0);
      }, 0);
      const avgRating = totalRatings / allProducts.length;
      const satisfactionRate = Math.round((avgRating / 5) * 100);

      // For response rate, we'll use a calculated value based on businesses with owners
      const responseRate = Math.round((claimedBusinesses / allProducts.length) * 100);

      setStats({
        activeBusinesses: claimedBusinesses || Math.round(allProducts.length * 0.6), // Fallback if no owner data
        totalReviews,
        satisfactionRate: satisfactionRate || 95, // Fallback if no rating data
        responseRate: responseRate || 85, // Fallback if no owner data
      });
    } else {
      // Reset stats if no products
      setStats({
        activeBusinesses: 500, // Default values for initial render
        totalReviews: 10000,
        satisfactionRate: 95,
        responseRate: 85,
      });
    }
  }, [allProducts]);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-myTheme-primary/90 to-myTheme-primary">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,rgba(255,255,255,0.5),transparent)] pointer-events-none" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Building2 className="w-12 h-12 text-white/90" />
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Grow Your Business
            </h1>
          </div>

          <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed">
            Take control of your online presence with ReviewIt. Get detailed
            insights, respond to reviews, and build trust with authentic
            customer feedback.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/claim-product">
              <Button
                size="lg"
                className="bg-white text-myTheme-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <Zap className="w-5 h-5 mr-2" />
                Get Started Now
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>

            <Link href="/mybusinesses">
              <Button
                variant="outline"
                size="lg"
                className="border-white border-2 text-white hover:bg-white/20 font-semibold px-8 py-4 text-lg rounded-full shadow-md"
              >
                <Users className="w-5 h-5 mr-2" />
                View My Businesses
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {!allProducts || allProducts.length === 0 ? (
              // Loading skeleton with animation
              <>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1 animate-pulse">
                    <div className="bg-white/20 rounded w-16 h-8 mx-auto"></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    <div className="bg-white/20 rounded w-20 h-4 mx-auto animate-pulse"></div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{stats.activeBusinesses}+</div>
                  <div className="text-white/80 text-sm">Active Businesses</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{stats.totalReviews.toLocaleString()}+</div>
                  <div className="text-white/80 text-sm">Customer Reviews</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{stats.satisfactionRate}%</div>
                  <div className="text-white/80 text-sm">Satisfaction Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{stats.responseRate}%</div>
                  <div className="text-white/80 text-sm">Response Rate</div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BusinessPageHero;
