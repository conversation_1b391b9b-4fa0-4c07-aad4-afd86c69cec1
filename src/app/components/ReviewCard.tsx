import React, { useEffect, useState } from "react";
import Image from "next/legacy/image";
import Link from "next/link";
import { iReview, iVoteCount, iUser, iReviewUser } from "../util/Interfaces";
import type { User } from "@clerk/nextjs/server";
import dayjs from "dayjs";
import DOMPurify from "dompurify";
import { useAtom } from "jotai";
import { currentReviewAtom } from "../store/store";
import { updateHelpfulVote, removeHelpfulVote, sendLikeNotification } from "../util/serverFunctions";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { useUser as useClerkUser } from "@clerk/nextjs";
import { useUser } from "@/app/hooks/useUser";
import { MdOutlineThumbUp } from "react-icons/md";
import { toast } from "sonner";
import ImageGallery from "./ImageGallery";
import VideoEmbed from "./VideoEmbed";
import ReviewStats from "./ReviewStats";
import { ReadOnlyRating } from "./RatingSystem";
import { MessageSquare } from "lucide-react";
import { generateShareMetadata } from "../lib/shareUtils";
import { ShareButtonWrapper } from "./ShareButtonWrapper";
import { stripSpecificHtmlTags } from "../util/helpers";
import ReportButton from "./ReportButton";
import AdminApprovedBadge from "./AdminApprovedBadge";
import ReviewOptionsMenu from "./ReviewOptionsMenu";

interface ReviewCardProps {
  review: iReview;
  showFullContent?: boolean;
  onDelete?: (reviewId: string) => Promise<void>;
}

interface ThumbsUpSectionProps {
  review: iReview;
  hasUserLiked: boolean;
  hideButton: boolean;
  auth: ReturnType<typeof useAuth>;
  handleHelpfulClick: () => void;
  voteCount: iVoteCount | null;
  isLoading?: boolean;
  isAuthor: boolean;
}

const ThumbsUpSection = ({
  hasUserLiked,
  auth,
  handleHelpfulClick,
  voteCount,
  isLoading,
  isAuthor,
}: ThumbsUpSectionProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle null voteCount case
  const voteCountValue =
    Math.max(0, voteCount?.helpfulVotes ?? 0) +
    (hasUserLiked && (voteCount?.helpfulVotes ?? 0) === 0 ? 1 : 0);

  if (!mounted) {
    return (
      <span className="flex items-center gap-1.5 text-myTheme-primary px-2 py-1 bg-myTheme-primary/10 rounded-full text-xs md:text-sm">
        <MdOutlineThumbUp className="text-sm md:text-base" />
        {voteCountValue}
      </span>
    );
  }

  if (isAuthor) {
    return (
      <span
        className="flex items-center gap-1.5 text-gray-400 px-2 py-1 bg-gray-100 rounded-full text-xs md:text-sm cursor-not-allowed"
        title="You cannot vote on your own review."
      >
        <MdOutlineThumbUp className="text-sm md:text-base" />
        {voteCountValue}
      </span>
    );
  }

  if (!auth.isSignedIn) {
    return (
      <span className="flex items-center gap-1.5 text-myTheme-primary px-2 py-1 bg-myTheme-primary/10 rounded-full text-xs md:text-sm">
        <MdOutlineThumbUp className="text-sm md:text-base" />
        {voteCountValue}
      </span>
    );
  }

  return (
    <button
      onClick={handleHelpfulClick}
      disabled={isLoading || isAuthor}
      className={`flex items-center gap-1.5 px-2 py-1 rounded-full text-xs md:text-sm transition-colors ${
        isAuthor
          ? "text-gray-400 bg-gray-100 cursor-not-allowed"
          : hasUserLiked
            ? "text-green-600 bg-green-100 hover:bg-green-200"
            : "text-gray-600 bg-gray-100 hover:bg-gray-200"
      } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
      aria-label={
        isAuthor
          ? "You cannot vote on your own review"
          : hasUserLiked
            ? "Remove helpful vote"
            : "Mark as helpful"
      }
      aria-disabled={isLoading || isAuthor}
    >
      <MdOutlineThumbUp
        className={`text-sm md:text-base ${isLoading ? "animate-pulse" : ""} ${
          hasUserLiked && !isAuthor ? "text-inherit" : "text-inherit"
        }`}
      />
      {voteCountValue}
    </button>
  );
};

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  showFullContent = false,
  onDelete,
}) => {
  const {
    user: reviewUser,
    createdDate,
    title,
    body,
    rating,
    comments,
    voteCount,
    images,
  } = review;
  const auth = useAuth();

  // Debug: log helpfulVotes coming from API
  useEffect(() => {
    console.log('ReviewCard voteCount', {
      reviewId: review.id,
      helpfulVotes: voteCount?.helpfulVotes,
      voteCount,
    });
  }, [review.id, voteCount]);
  const [reviewAtom, setReview] = useAtom(currentReviewAtom);
  const formattedBody = body.replace(/<p><\/p>/g, "<br>");
  const queryClient = useQueryClient();
  const [hideButton, setHideButton] = React.useState(false);
  const { isSignedIn: authSignedIn } = useAuth();
  const { user, isAllowed, securityReason } = useUser();
  const { user: clerkUser } = useClerkUser();
  const userInDbId = clerkUser?.publicMetadata?.id as string | undefined;
  const isAuthor = !!(review.userId === userInDbId && authSignedIn && isAllowed);

  // Check if current user is the review owner (more robust check)
  const isReviewOwner = Boolean(auth.userId && (
    auth.userId === review.userId ||
    userInDbId === review.user?.id ||
    userInDbId === review.userId
  ));

  // Handle review deletion
  const handleDeleteReview = async () => {
    if (!review.id || !isReviewOwner) return;
    
    try {
      const response = await fetch('/api/delete/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: review.id }),
      });

      if (response.ok) {
        toast.success('Review deleted successfully');
        if (onDelete) {
          await onDelete(review.id);
        }
        // Invalidate queries to refresh the UI
        queryClient.invalidateQueries({ queryKey: ['reviews'] });
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to delete review');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };


  // Create a local state for optimistic updates
  const [localVoteCount, setLocalVoteCount] = useState<number>(
    voteCount?.helpfulVotes || 0
  );
  const [localHasUserLiked, setLocalHasUserLiked] = useState<boolean>(
    false
  );

  const likedData = review.likedBy || reviewAtom?.likedBy || [];
  const hasUserLiked = likedData.some((user) => user.id === userInDbId);

  // Update local state with the actual value after checking conditions
  useEffect(() => {
    setLocalHasUserLiked(hasUserLiked);
  }, [hasUserLiked]);

  // Update local state when props change, but only if we're not in the middle of a mutation
  useEffect(() => {
    setLocalVoteCount(voteCount?.helpfulVotes ?? 0);
    setLocalHasUserLiked(hasUserLiked);
  }, [voteCount?.helpfulVotes, hasUserLiked]);

  const mutation = useMutation<void, Error, void, { previousVoteCount: number; previousHasUserLiked: boolean; previousReview: iReview | null }>({
    mutationKey: ['helpfulVote', review.id],
    mutationFn: async () => {
      if (!review.id || !userInDbId || !clerkUser) {
        throw new Error("Missing required data for vote operation");
      }
      if (!authSignedIn) {
        throw new Error("User must be signed in to vote");
      }

      try {
        // Use hasUserLiked from props, not the local state which has already been updated optimistically
        if (hasUserLiked) {
          await removeHelpfulVote({ reviewId: review.id, userInDbId });
        } else {
          await updateHelpfulVote({ reviewId: review.id, userInDbId });
          
          // Notification is now handled directly in the helpful vote API endpoint
        }
      } catch (error) {
        console.error("Vote operation failed:", error);
        throw error;
      }
    },
    onSuccess: (_data, _vars, context) => {
      // Decide message based on the user state BEFORE the mutation (captured in context)
      toast.success(
        context?.previousHasUserLiked
          ? "Vote removed successfully!"
          : "Like saved successfully!",
      );
      setHideButton(!hasUserLiked);

      // Ensure fresh data – invalidate caches so they refetch with correct counts
      queryClient.invalidateQueries({ queryKey: ["review", review.id] });
      if (review.productId) {
        queryClient.invalidateQueries({ queryKey: ["product", review.productId] });
      }
    },
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [review.id] });

      // Snapshot the previous value
      const previousReview = queryClient.getQueryData<iReview>([review.id]);

      // Create an optimistic review update
      const optimisticReview: iReview = { ...review };
      if (!optimisticReview.voteCount) {
        optimisticReview.voteCount = {
          id: voteCount?.id || '',
          reviewId: review.id || '',
          helpfulVotes: 0,
          unhelpfulVotes: voteCount?.unhelpfulVotes || 0,
          review: review,
        };
      }

      // Update vote count and liked status
      if (localHasUserLiked) {
        optimisticReview.voteCount.helpfulVotes = (optimisticReview.voteCount.helpfulVotes || 0) - 1;
        optimisticReview.likedBy = (optimisticReview.likedBy || []).filter(
          (user: iUser) => user.id !== userInDbId
        );
      } else {
        optimisticReview.voteCount.helpfulVotes = (optimisticReview.voteCount.helpfulVotes || 0) + 1;
        // Add user info for the like
        if (clerkUser && userInDbId) {
          const userDisplayName = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim();
          const likedByUser: iUser = {
            id: userInDbId,
            userName: userDisplayName || 'Anonymous User',
            firstName: clerkUser?.firstName || '',
            lastName: clerkUser?.lastName || '',
            avatar: clerkUser?.imageUrl || '',
            createdDate: new Date(),
            email: clerkUser?.emailAddresses?.[0]?.emailAddress || '',
            clerkUserId: clerkUser?.id || '',
            isDeleted: false,
            role: 'USER' as const
          };
          optimisticReview.likedBy = [
            ...(optimisticReview.likedBy || []),
            likedByUser
          ];
        }
      }

      // Update both the atom and the cache
      setReview(optimisticReview);
      queryClient.setQueryData([review.id], optimisticReview);

      // Update local state optimistically
      setLocalVoteCount(optimisticReview.voteCount.helpfulVotes);
      setLocalHasUserLiked(!localHasUserLiked);

      return {
        previousReview: previousReview || null,
        previousVoteCount: localVoteCount,
        previousHasUserLiked: localHasUserLiked,
      };
    },
    onError: (err: unknown, _: unknown, context: { previousVoteCount: number; previousHasUserLiked: boolean; previousReview: iReview | null } | undefined) => {
      if (context) {
        // Revert on error
        setLocalVoteCount(context.previousVoteCount);
        setLocalHasUserLiked(context.previousHasUserLiked);
        if (context.previousReview) {
          setReview(context.previousReview);
          queryClient.setQueryData([review.id], context.previousReview);
        }
      }
      // Show error message
      const errorMessage = err instanceof Error ? err.message : "An unexpected error occurred";
      toast.error(
        localHasUserLiked
          ? `Failed to remove vote: ${errorMessage}`
          : `Vote failed: ${errorMessage}`
      );
    },
  });

  const handleHelpfulClick = () => {
    if (!authSignedIn || isAuthor || mutation.isPending) return;
    mutation.mutate(undefined);
  };

  // Allow non-authenticated users to view reviews (removed early return)

  // Generate metadata for sharing
  const metadata = generateShareMetadata({
    title: `${title} - Review of ${review.product?.name} | Rating: ${rating}/5`,
    description: `${stripSpecificHtmlTags(body).substring(0, 100)}... | Review by @${review.user?.userName}`,
    imageUrl: review.product?.display_image,
    url: `/fr?id=${review.id}&productid=${review.productId}`,
    rating: rating,
    reviewCount: 1,
  });

  return (
    <div className="bg-white/60 backdrop-blur-md rounded-2xl border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300 group flex flex-col overflow-hidden">
      <div className="flex flex-col p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
          <Link
            href={profileUrl(review.user!)}
            className="flex items-center w-full sm:w-auto group/avatar"
          >
            <div className="relative">
              <Image
                src={reviewUser?.avatar || "/logo.png"}
                alt={reviewUser?.id ?? "avatar"}
                width={48}
                height={48}
                className="rounded-full object-cover w-12 h-12 ring-2 ring-white/50 shadow-lg transition-transform duration-300 group-hover/avatar:scale-105"
              />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-myTheme-primary/20 to-myTheme-secondary/20 opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-300" />
            </div>
            <div className="ml-3">
              <div className="flex items-center gap-2 mb-1">
                <p className="font-semibold text-gray-900 group-hover:text-myTheme-primary transition-colors text-sm md:text-base">
                  @{reviewUser?.userName}
                </p>
                <AdminApprovedBadge review={review} size="sm" />
              </div>
              <div className="flex items-center gap-2">
                <ReadOnlyRating name={review.id!} rating={rating} size="sm" />
                <span className="text-xs md:text-sm text-gray-600 font-medium">
                  {dayjs(createdDate?.toString()).format("MMM D, YYYY")}
                </span>
              </div>
            </div>
          </Link>
          <div className="mt-2 sm:mt-0 flex flex-col items-end gap-2">
            <ReviewStats
              review={review}
              setReview={() => {
                setReview(review);
              }}
            />
            <div className="flex items-center gap-2">
              <ReportButton
                reviewId={review.id!}
                authorId={review.userId}
                className="mt-1"
                onReport={(reportId) => {
                  toast.success("Review reported successfully");
                }}
              />
              {!review.isDeleted && isReviewOwner && authSignedIn && isAllowed && (
                <ReviewOptionsMenu
                  onDelete={handleDeleteReview}
                />
              )}
            </div>
          </div>
        </div>

        <Link
          href={`/fr/?id=${review.id}&productid=${review.productId}`}
          onClick={() => setReview(review)}
          className="block mb-4 group/title"
        >
          <h2 className="text-lg md:text-xl font-bold text-gray-900 group-hover/title:text-myTheme-primary transition-colors leading-tight">
            {title}
          </h2>
        </Link>
        
        <div className="flex-1">
          <div
            className={`text-xs md:text-sm text-gray-700 leading-relaxed mb-4 ${showFullContent ? "" : "line-clamp-4"}`}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(
                showFullContent
                  ? formattedBody
                  : formattedBody.substring(0, 200) +
                      (formattedBody.length > 200 ? "..." : ""),
              ),
            }}
          />
          <div className="mb-3">
            {images && images.length > 0 && <ImageGallery images={images} />}
          </div>
          <div className="mb-3">
            {review.videos && review.videos[0] && (
              <VideoEmbed url={review.videos[0]} />
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between pt-4 border-t border-white/20 mt-auto">
          <div className="flex items-center space-x-3">
            <ThumbsUpSection
              review={review}
              hasUserLiked={localHasUserLiked}
              hideButton={hideButton}
              auth={auth}
              handleHelpfulClick={handleHelpfulClick}
              voteCount={{
                helpfulVotes: localVoteCount,
                id: voteCount?.id || '',
                reviewId: review.id || '',
                unhelpfulVotes: voteCount?.unhelpfulVotes || 0,
                review: review
              }}
              isLoading={mutation.isPending}
              isAuthor={isAuthor}
            />
          </div>
          <div className="flex items-center gap-3">
            <Link
              href={`/fr?id=${review.id}&productid=${review.productId}`}
              onClick={() => setReview(review)}
              className="group/comment"
            >
              <span className="text-xs md:text-sm font-medium text-myTheme-primary hover:text-myTheme-primary/80 transition-colors flex items-center gap-1.5 px-2 md:px-3 py-1 md:py-1.5 rounded-full bg-white/50 hover:bg-white/70">
                <MessageSquare size={14} className="md:w-4 md:h-4 transition-transform group-hover/comment:scale-110" />
                {comments && comments.length > 0 ? `${comments.length}` : "Add"}
              </span>
            </Link>
            <ShareButtonWrapper metadata={metadata} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewCard;
