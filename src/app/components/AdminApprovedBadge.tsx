import React from "react";
import { Shield, CheckCircle } from "lucide-react";
import { iReview } from "../util/Interfaces";

interface AdminApprovedBadgeProps {
  review: iReview;
  className?: string;
  size?: "sm" | "md" | "lg";
}

const AdminApprovedBadge: React.FC<AdminApprovedBadgeProps> = ({
  review,
  className = "",
  size = "sm",
}) => {
  // Only show if review is verified by admin
  if (!review.isVerified || !review.verifiedBy) {
    return null;
  }

  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-3 py-1.5",
    lg: "text-base px-4 py-2",
  };

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16,
  };

  return (
    <div
      className={`inline-flex items-center gap-1.5 bg-green-100 text-green-800 rounded-full font-medium border border-green-200 ${sizeClasses[size]} ${className}`}
    >
      <Shield size={iconSizes[size]} className="text-green-600" />
      <span>Admin Verified</span>
      <CheckCircle size={iconSizes[size]} className="text-green-600" />
    </div>
  );
};

export default AdminApprovedBadge;
