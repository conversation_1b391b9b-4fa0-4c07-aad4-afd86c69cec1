import React, { useMemo, useCallback, useState } from "react";
import { iComment, iUser, iProduct, iReview } from "../util/Interfaces";
import Comment from "./Comment";
import { Button } from "@/components/ui/button";
import { Search, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type CommentSortType = 'best' | 'newest' | 'oldest';

interface CommentListProps {
  comments: iComment[];
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  clerkUserId: string;
  currentUser: iUser;
  product?: iProduct;
  review?: iReview; // Add review data to determine review author
  sortBy?: CommentSortType;
  onSortChange?: (sortBy: CommentSortType) => void;
  isUserAuthenticated?: boolean;
  /**
   * When provided, the list will initially collapse to only show the thread that
   * contains this comment. Useful for deep-linking from notifications.
   */
  focusCommentId?: string;
}

const CommentList: React.FC<CommentListProps> = ({
  comments,
  onReply,
  onEdit,
  onDelete,
  clerkUserId,
  currentUser,
  product,
  review,
  sortBy = 'best',
  focusCommentId,
  onSortChange,
  isUserAuthenticated = false,
}) => {
  // Removed debug console.log statements

  type SortOption = "best" | "new" | "old";
  const [localSortBy, setLocalSortBy] = useState<SortOption>("best");
  const [showAll, setShowAll] = useState<boolean>(() => (focusCommentId ? false : true));
  const [searchQuery, setSearchQuery] = useState("");

  // Use prop sortBy if provided, otherwise use local state
  const currentSortBy: SortOption = sortBy ? (sortBy === 'newest' ? 'new' : sortBy === 'oldest' ? 'old' : 'best') : localSortBy;

  // Handle sort change
  const handleSortChange = (newSort: SortOption) => {
    if (onSortChange) {
      // Convert to CommentSortType and call parent handler
      const convertedSort: CommentSortType = newSort === 'new' ? 'newest' : newSort === 'old' ? 'oldest' : 'best';
      onSortChange(convertedSort);
    } else {
      // Use local state
      setLocalSortBy(newSort);
    }
  };

  // Memoize the organizeComments function
  const organizeComments = useCallback((comments: iComment[]): iComment[] => {
    const commentMap = new Map<string, iComment>();
    const rootComments: iComment[] = [];

    comments.forEach((comment) => {
      comment.replies = [];
      commentMap.set(comment.id!, comment);
    });

    comments.forEach((comment) => {
      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.replies!.push(comment);
        }
      } else {
        rootComments.push(comment);
      }
    });

    return rootComments;
  }, []);

  // Sort function based on selected sort option
  const sortComments = useCallback(
    (comments: iComment[]): iComment[] => {
      const sortedComments = [...comments];

      // Helper function to sort a single level of comments
      const sortCommentLevel = (a: iComment, b: iComment) => {
        switch (currentSortBy) {
          case "new":
            return (
              new Date(b.createdDate).getTime() -
              new Date(a.createdDate).getTime()
            );
          case "old":
            return (
              new Date(a.createdDate).getTime() -
              new Date(b.createdDate).getTime()
            );
          case "best":
          default:
            // Calculate scores
            const scoreA = (a.upvotes || 0) - (a.downvotes || 0);
            const scoreB = (b.upvotes || 0) - (b.downvotes || 0);

            // If scores are equal, sort by recency
            if (scoreA === scoreB) {
              return (
                new Date(b.createdDate).getTime() -
                new Date(a.createdDate).getTime()
              );
            }

            // Otherwise sort by score
            return scoreB - scoreA;
        }
      };

      // Sort root comments
      sortedComments.sort(sortCommentLevel);

      // Sort replies recursively
      const sortReplies = (comment: iComment) => {
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.sort(sortCommentLevel);
          comment.replies.forEach(sortReplies);
        }
      };

      // Sort replies for each root comment
      sortedComments.forEach(sortReplies);

      return sortedComments;
    },
    [currentSortBy]
  );

  // Filter comments based on search query
  const filterComments = useCallback(
    (comments: iComment[]): iComment[] => {
      if (!searchQuery.trim()) return comments;

      return comments.filter(
        (comment) =>
          comment.body.toLowerCase().includes(searchQuery.toLowerCase()) ||
          comment.user?.userName
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    },
    [searchQuery]
  );

  // Compute processed comments first
  const processedComments = useMemo(() => {
    const organized = organizeComments(comments);
    const sorted = sortComments(organized);
    return filterComments(sorted);
  }, [comments, organizeComments, sortComments, filterComments]);

  // Derive visible comments depending on focus
  const includesComment = useCallback((comment: iComment, id: string): boolean => {
    if (comment.id === id) return true;
    return comment.replies ? comment.replies.some((c) => includesComment(c, id)) : false;
  }, []);

  const visibleComments = useMemo(() => {
    if (showAll || !focusCommentId) return processedComments;
    return processedComments.filter((root) => includesComment(root, focusCommentId));
  }, [processedComments, showAll, focusCommentId, includesComment]);

  // Memoize the renderComment function
  const renderComment = useCallback(
    (comment: iComment, depth: number = 0) => (
      <Comment
        clerkUserId={clerkUserId}
        key={comment.id || "no key"}
        comment={comment}
        onReply={(parentId, body) => onReply(parentId, body)}
        onEdit={onEdit}
        onDelete={onDelete}
        depth={depth}
        currentUser={currentUser}
        product={product}
        review={review}
        isUserAuthenticated={isUserAuthenticated}
      >
        {comment.replies &&
          comment.replies.map((reply) => renderComment(reply, depth + 1))}
      </Comment>
    ),
    [clerkUserId, onReply, onEdit, onDelete, currentUser, product, review, isUserAuthenticated]
  );

  return (
    <div className="flex flex-col w-full bg-gray-50 max-w-full overflow-hidden rounded-md">
      {/* Comment header with sort, search, and focus toggle */}
      <div className="flex items-center justify-between border-b border-gray-200 p-3 bg-white">
        <div className="flex items-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center text-sm font-medium text-gray-700 gap-1 h-8"
              >
                Sort by: {currentSortBy.charAt(0).toUpperCase() + currentSortBy.slice(1)}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => handleSortChange("best")}>
                🏆 Best (Most upvoted)
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange("new")}>
                🕒 Newest first
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange("old")}>
                📅 Oldest first
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="relative flex items-center">
          <Search className="h-4 w-4 absolute left-2 text-gray-400" />
          <input
            type="text"
            placeholder="Search comments"
            className="pl-8 pr-3 py-1 text-sm border border-gray-200 rounded-full w-full max-w-[180px] md:max-w-[240px] focus:outline-none focus:ring-1 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      {focusCommentId && (
          <Button
            variant="outline"
            size="sm"
            className="ml-4"
            onClick={() => setShowAll((prev) => !prev)}
          >
            {showAll ? "Focus on comment" : "Show all comments"}
          </Button>
        )}
      </div>

      {/* Comments list */}
      <div className="p-3 space-y-3">
        {visibleComments.length > 0 ? (
          visibleComments.map((comment) => renderComment(comment))
        ) : (
          <div className="text-center py-6 text-gray-500">
            {searchQuery ? "No comments match your search" : "No comments yet"}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(CommentList);
