"use client";
import React, { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // Assuming you have these components

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}

const InstallPWA: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);
  const [installSuccess, setInstallSuccess] = useState(false);

  useEffect(() => {
    const checkStandaloneMode = () => {
      const isStandalone =
        window.matchMedia("(display-mode: standalone)").matches ||
        (window.navigator as any).standalone ||
        document.referrer.includes("android-app://");

      setIsStandalone(isStandalone);
    };

    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    // Check if beforeinstallprompt is supported
    setIsInstallable("beforeinstallprompt" in window);

    window.addEventListener(
      "beforeinstallprompt",
      handleBeforeInstallPrompt as EventListener,
    );

    // Check initial state
    checkStandaloneMode();

    // Set up a listener for changes in display mode
    const mediaQuery = window.matchMedia("(display-mode: standalone)");
    mediaQuery.addListener(checkStandaloneMode);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt as EventListener,
      );
      mediaQuery.removeListener(checkStandaloneMode);
    };
  }, []);

  const handleInstallClick = useCallback(async () => {
    if (!deferredPrompt) {
      setInstallError("Installation prompt not available");
      return;
    }

    setIsInstalling(true);
    setInstallError(null);

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      if (outcome === "accepted") {
        setInstallSuccess(true);
      } else {
      }
    } catch (error) {
      setInstallError("Failed to install the app. Please try again later.");
    } finally {
      setIsInstalling(false);
      setDeferredPrompt(null);
      setIsInstallable(false);
    }
  }, [deferredPrompt]);

  // If the app is already installed (isStandalone) or not installable (!isInstallable && !deferredPrompt), 
  // don't render anything
  if (isStandalone || (!isInstallable && !deferredPrompt)) {
    return null;
  }

  return (
    <section className="flex justify-center w-full py-12 md:py-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl shadow-2xl my-8">
      <div className="container grid items-center justify-center gap-6 px-4 text-center md:px-6">
        <div className="space-y-3">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">
            🚀 Elevate Your Experience!
          </h2>
          <p className="mx-auto max-w-[700px] text-indigo-100 text-base md:text-lg">
            Install our app for lightning-fast access, offline capabilities, and a seamless experience on your device.
          </p>
        </div>
        {isInstallable && deferredPrompt && (
          <Button
            size="lg"
            className="bg-white text-indigo-600 hover:bg-gray-100 disabled:opacity-70 transition-transform duration-200 ease-in-out hover:scale-105 shadow-lg font-semibold py-3 px-8 rounded-lg text-lg mx-auto flex items-center gap-2"
            onClick={handleInstallClick}
            disabled={isInstalling}
            aria-label="Install Progressive Web App"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7 10 12 15 17 10" />
              <line x1="12" x2="12" y1="15" y2="3" />
            </svg>
            {isInstalling ? "Installing..." : "Install App"}
          </Button>
        )}
        {installError && (
          <Alert variant="destructive">
            <AlertTitle>Installation Error</AlertTitle>
            <AlertDescription>{installError}</AlertDescription>
          </Alert>
        )}
        {installSuccess && (
          <Alert>
            <AlertTitle>Installation Successful</AlertTitle>
            <AlertDescription>
              The app has been successfully installed. You can now access it
              from your home screen.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </section>
  );
};

export default InstallPWA;
