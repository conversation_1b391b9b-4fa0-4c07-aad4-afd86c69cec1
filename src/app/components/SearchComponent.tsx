"use client";
import React, { useState, useEffect, ChangeEvent } from "react";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { iProduct } from "../util/Interfaces";
import SearchResults from "./SearchResult";

interface SearchBoxProps {
  onSearch?: (query: string) => void;
}

const SearchBox: React.FC<SearchBoxProps> = ({ onSearch }) => {
  const [searchResults, setSearchResults] = useState<iProduct[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [tags, setTags] = useState<iProduct[]>([]);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);
  const [allProducts] = useAtom(allProductsStore);
  const [isSearching, setIsSearching] = useState(false);

  // Function to search using cached API
  const searchWithAPI = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setTags([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch('/api/productsearch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json();

      if (data.success && data.data) {
        // Filter results by name match
        const nameMatches = data.data.filter((product: iProduct) =>
          product.name.toLowerCase().includes(query.toLowerCase())
        );

        // Filter results by tag match
        const tagMatches = data.data.filter((product: iProduct) =>
          product.tags?.some((tag: string) =>
            tag.toLowerCase().includes(query.toLowerCase())
          )
        );

        setSearchResults(nameMatches);
        setTags(tagMatches);
      } else {
        // Fallback to client-side search if API fails
        fallbackToClientSearch(query);
      }
    } catch (error) {
      console.error('Search API error, falling back to client-side:', error);
      // Fallback to client-side search
      fallbackToClientSearch(query);
    } finally {
      setIsSearching(false);
    }
  };

  // Fallback client-side search (original implementation)
  const fallbackToClientSearch = (query: string) => {
    try {
      const productsArray = Array.isArray(allProducts) ? allProducts : [];
      setSearchResults(filteredProducts(productsArray, query));
      setTags(filteredProductsByTags(productsArray, query));
    } catch (error) {
      console.error("Error processing search results:", error);
      setSearchResults([]);
      setTags([]);
    }
  };

  const filteredProducts = (products: iProduct[], searchTerm: string) => {
    if (!Array.isArray(products)) {
      console.error("Expected products to be an array but got:", typeof products);
      return [];
    }

    const filteredProducts = products.filter((product) => {
      try {
        if (!product || !product.name) return false;
        return product.name.toLowerCase().includes(searchTerm.toLowerCase());
      } catch (error) {
        console.error("Error filtering product by name:", error);
        return false;
      }
    });

    filteredProducts.sort((a, b) => {
      try {
        const aIndex = a.name.toLowerCase().indexOf(searchTerm.toLowerCase());
        const bIndex = b.name.toLowerCase().indexOf(searchTerm.toLowerCase());
        return aIndex - bIndex;
      } catch (error) {
        console.error("Error sorting products:", error);
        return 0;
      }
    });

    return filteredProducts;
  };

  const filteredProductsByTags = (products: iProduct[], searchTerm: string) => {
    if (!Array.isArray(products)) {
      console.error("Expected products to be an array but got:", typeof products);
      return [];
    }

    const lowercaseSearchTerm = searchTerm.toLowerCase();

    const filteredProducts = products.filter((product) => {
      try {
        if (!product || !product.tags || !Array.isArray(product.tags)) return false;
        return product.tags.some((tag) =>
          tag && tag.toLowerCase().includes(lowercaseSearchTerm)
        );
      } catch (error) {
        console.error("Error filtering product by tags:", error);
        return false;
      }
    });

    filteredProducts.sort((a, b) => {
      try {
        const aTagIndex = a.tags.findIndex((tag) =>
          tag && tag.toLowerCase().includes(lowercaseSearchTerm)
        );
        const bTagIndex = b.tags.findIndex((tag) =>
          tag && tag.toLowerCase().includes(lowercaseSearchTerm)
        );
        return aTagIndex - bTagIndex;
      } catch (error) {
        console.error("Error sorting products by tags:", error);
        return 0;
      }
    });

    return filteredProducts;
  };

  useEffect(() => {
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    const timeout = setTimeout(() => {
      if (searchTerm.trim()) {
        // Use cached API search for non-empty queries
        searchWithAPI(searchTerm);
      } else {
        // Clear results for empty search
        setSearchResults([]);
        setTags([]);
      }
    }, 300); // Slightly longer debounce for API calls

    setTypingTimeout(timeout);

    return () => {
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    if (onSearch) {
      onSearch(newValue);
    }
  };

  return (
    <div className="flex flex-col px-4 pt-6 md:pt-8 h-full w-full md:w-5/6 items-center bg-transparent relative z-[100]">
      <div className="w-full space-y-4">
        <div className="search-section w-full">
          <input
            type="search"
            className="block w-full py-4 pl-4 pr-4 text-sm text-black border border-gray-400 rounded-lg bg-gray-50 focus:ring-myTheme-neutral focus:border-myTheme-light shadow-sm"
            placeholder="Company | Service | Product..."
            value={searchTerm}
            onChange={handleInputChange}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-myTheme-neutral"></div>
            </div>
          )}
        </div>
        {searchTerm && (
          <div className="results-section fixed left-1/2 -translate-x-1/2 z-[999] w-full max-w-[95vw] md:max-w-[800px] bg-white shadow-lg rounded-lg mt-1">
            <SearchResults results={searchResults} tags={tags} />
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchBox;
