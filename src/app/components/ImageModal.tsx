import { useEffect } from 'react';
import Image from 'next/image';

interface ImageModalProps {
    src: string;
    alt: string;
    onClose: () => void;
}

const ImageModal = ({ src, alt, onClose }: ImageModalProps) => {
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
        document.addEventListener('keydown', handleEscape);

        return () => {
            document.body.style.overflow = 'unset';
            document.removeEventListener('keydown', handleEscape);
        };
    }, [onClose]);

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 p-4 sm:p-6"
            onClick={onClose}
        >
            <div className="relative w-full h-full max-w-[90vh] max-h-[90vh] flex items-center justify-center">
                <button
                    onClick={onClose}
                    className="absolute -top-12 right-0 text-white hover:text-gray-300 text-4xl z-10 p-2"
                    aria-label="Close modal"
                >
                    ×
                </button>
                <div className="w-full h-full aspect-square flex items-center justify-center">
                    <div className="w-full h-full flex items-center justify-center">
                        <img
                            src={src}
                            alt={alt}
                            className="w-full h-full object-contain"
                            sizes="90vh"
                            onClick={(e) => e.stopPropagation()}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ImageModal; 