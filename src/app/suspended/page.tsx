'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@clerk/nextjs';
import { useEffect, useState, Suspense } from 'react';
import ErrorBoundary from '../components/ErrorBoundary';

function SuspendedContent() {
  const { signOut } = useAuth();
  const searchParams = useSearchParams();
  const suspendedUntilParam = searchParams.get('until');
  const suspendedReasonParam = searchParams.get('reason');
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (!suspendedUntilParam) {
      setTimeRemaining('Suspension duration not specified');
      return;
    }
    
    try {
      const suspendedUntil = new Date(decodeURIComponent(suspendedUntilParam));
      
      // Check if the date is valid
      if (isNaN(suspendedUntil.getTime())) {
        throw new Error('Invalid date format');
      }
      
      // Calculate and format time remaining
      const calculateTimeRemaining = () => {
        try {
          const now = new Date();
          if (now >= suspendedUntil) {
            setTimeRemaining('Your suspension period has ended. Please try refreshing the page or signing in again.');
            return;
          }
          
          const diffMs = suspendedUntil.getTime() - now.getTime();
          const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const diffHrs = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          
          const parts = [];
          if (diffDays > 0) parts.push(`${diffDays} day${diffDays !== 1 ? 's' : ''}`);
          if (diffHrs > 0) parts.push(`${diffHrs} hour${diffHrs !== 1 ? 's' : ''}`);
          if (diffMins > 0) parts.push(`${diffMins} minute${diffMins !== 1 ? 's' : ''}`);
          
          setTimeRemaining(parts.join(', '));
        } catch (err) {
          console.error('Error calculating time remaining:', err);
          setError('Unable to calculate remaining suspension time');
        }
      };
      
      calculateTimeRemaining();
      const interval = setInterval(calculateTimeRemaining, 60000); // Update every minute
      
      return () => clearInterval(interval);
    } catch (err) {
      console.error('Error processing suspension date:', err);
      setError('Invalid suspension information provided');
    }
  }, [suspendedUntilParam]);
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-yellow-100 mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Account Suspended</h1>
          <p className="text-gray-600 mb-6">
            Your account has been temporarily suspended from accessing ReviewIt.
          </p>
          
          {error && (
            <div className="mb-6 p-4 bg-red-50 rounded-md">
              <p className="text-red-600">{error}</p>
            </div>
          )}
          
          {!error && (
            <div className="mb-6 p-4 bg-gray-50 rounded-md">
              <h2 className="text-lg font-medium text-gray-800 mb-2">Suspension Period</h2>
              <p className="text-gray-600">
                {timeRemaining ? (
                  <>
                    Your account will be reinstated in:
                    <span className="block text-lg font-medium text-yellow-600 mt-2">
                      {timeRemaining}
                    </span>
                  </>
                ) : (
                  'Calculating remaining time...'
                )}
              </p>
            </div>
          )}
          
          {suspendedReasonParam && (
            <div className="mb-6 p-4 bg-blue-50 rounded-md">
              <h2 className="text-lg font-medium text-gray-800 mb-2">Reason</h2>
              <p className="text-gray-600">{decodeURIComponent(suspendedReasonParam)}</p>
            </div>
          )}
          
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Need Help?</h2>
            <p className="text-gray-600">
              If you believe this suspension is in error or have questions about your account status, please contact our support team.
            </p>
          </div>
          
          <div className="flex flex-col space-y-3">
            <a 
              href="mailto:<EMAIL>" 
              className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Contact Support
            </a>
            <button 
              onClick={() => signOut()}
              className="inline-flex items-center justify-center px-5 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

export default function SuspendedPage() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingFallback />}>
        <SuspendedContent />
      </Suspense>
    </ErrorBoundary>
  );
}
