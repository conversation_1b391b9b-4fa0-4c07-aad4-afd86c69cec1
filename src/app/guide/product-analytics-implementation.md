# Product Analytics Implementation Guide

## Overview
This guide outlines the implementation of a comprehensive product analytics system for Review It. The system will track user interactions with products and provide valuable insights to business owners.

## Tasks

### 1. Interface Definitions [✓]
- [✓] Create `iProductViewEvent` interface for tracking individual view events
- [✓] Create `iProductAnalytics` interface for aggregated analytics
- [✓] Create `iUserProductInteraction` interface for tracking user-specific interactions
- [✓] Add analytics-related fields to existing `iProduct` interface

### 2. Cookie-Based Tracking System [✓]
- [✓] Implement sophisticated view counting with throttling
- [✓] Track unique visitors vs returning visitors
- [✓] Store user interaction history
- [✓] Implement session tracking

### 3. Analytics Data Collection [ ]
- [ ] Track product view duration
- [ ] Track user navigation path
- [ ] Track interaction events (clicks, scrolls)
- [ ] Track time of day/week patterns
- [ ] Implement review interaction tracking

### 4. Database Schema Updates [✓]
- [✓] Add analytics tables to Prisma schema
- [✓] Create relations between models
- [✓] Add indexes for efficient querying
- [✓] Create migrations for new tables

### 5. API Endpoints [✓]
- [✓] Create endpoint for logging view events
- [✓] Create endpoint for retrieving analytics
- [✓] Implement analytics aggregation endpoints
- [✓] Add business owner-specific analytics endpoints

### 6. Business Owner Dashboard [ ]
- [ ] Design analytics dashboard layout
- [ ] Implement data visualization components
- [ ] Add filtering and date range selection
- [ ] Create export functionality

### 7. Privacy and Security [ ]
- [ ] Implement data anonymization
- [ ] Add consent management
- [ ] Ensure GDPR compliance
- [ ] Add data retention policies

## Implementation Progress

### ✓ Step 1: Interface Definitions (Completed)
We've added the following interfaces to `src/app/util/Interfaces.tsx`:
- `iProductViewEvent`: Tracks individual product view events with detailed metadata
- `iProductAnalytics`: Stores aggregated analytics data for products
- `iUserProductInteraction`: Tracks user-specific interactions with products
- Updated `iProduct` interface with analytics-related fields

### ✓ Step 2: Database Schema Updates (Completed)
Added new models to `prisma/schema.prisma`:

1. **ProductViewEvent**
   - Tracks individual view events
   - Stores metadata like duration, source, device type
   - Links to both Product and User (optional)
   - Includes session tracking

2. **ProductAnalytics**
   - Stores aggregated analytics per product
   - Tracks total views, unique visitors
   - Stores time-based statistics (peak hours, weekday patterns)
   - One-to-one relationship with Product

3. **UserProductInteraction**
   - Tracks user-specific interaction history
   - Stores view count, review status, and engagement metrics
   - Unique constraint on user-product pairs

4. **Updated Existing Models**
   - Added relations to Product and User models
   - Added appropriate indexes for performance

### ✓ Step 3: Database Migration (Completed)
Successfully generated and applied the migration `20250416000138_add_product_analytics` which:
- Created new tables for analytics tracking
- Added necessary relations and indexes
- Updated existing tables with new relations
- Generated updated Prisma Client

### ✓ Step 4: Cookie-Based Tracking (Completed)
Created `src/app/util/analyticsUtils.ts` with the following features:
1. Session Management
   - Persistent session tracking with 30-day cookies
   - Secure cookie settings (httpOnly, secure in production)
   - Session ID generation using UUID

2. View Tracking
   - Sophisticated view counting with throttling (every 3 visits)
   - First-visit detection
   - Anonymous and authenticated user tracking
   - Device and source tracking

3. Analytics Recording
   - Event logging for all views
   - Aggregated analytics updates
   - User interaction tracking
   - Time-based statistics (peak hours, weekday patterns)

### ✓ Step 5: API Endpoints (Completed)
Created two main API endpoints for analytics:

1. **Log View Event** (`/api/analytics/log-view`)
   - Records product views with metadata
   - Handles both anonymous and authenticated users
   - Includes source and device tracking
   - Implements view throttling logic

2. **Get Analytics** (`/api/analytics/get-stats`)
   - Retrieves comprehensive analytics data
   - Includes access control for business owners
   - Supports date range filtering
   - Provides aggregated metrics:
     - Engagement rates
     - Review rates
     - View trends
     - Time-based analytics

Features:
- Secure authentication using Clerk
- Business owner access control
- Error handling and logging
- Data aggregation and formatting
- Date range filtering

### Next Step: Business Owner Dashboard
We should now create the dashboard components for business owners to view analytics:
1. Analytics overview component
2. Detailed metrics views
3. Charts and visualizations
4. Date range selection
5. Export functionality

Would you like to proceed with implementing the business owner dashboard? 