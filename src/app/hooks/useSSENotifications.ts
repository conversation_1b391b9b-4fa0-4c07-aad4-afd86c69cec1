/**
 * React Hook for SSE Notifications
 * Provides real-time notifications with fallback to polling
 */

import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { useAuth } from '@clerk/nextjs';
import { SSENotificationService, SSEMessage, NotificationPayload } from '../util/sseNotificationService';
import { getNotifications } from '../util/serverFunctions';
import { ownerNotificationsAtom, userNotificationsAtom, likeNotificationsAtom } from '../store/store';
import { iProductOwnerNotification, iUserNotification, FetchedNotificationsData, LikeNotification } from '../util/Interfaces';
import { markNotificationAsRead as apiMarkAsRead } from '../util/NotificationFunctions';
import debounce from 'lodash/debounce';

// Extended notification payload with additional fields we need
interface ExtendedNotificationPayload extends NotificationPayload {
  from_name?: string;
  from_id?: string;
  notification_type?: string;
  product_id?: string;
  review_id?: string;
  comment_id?: string;
}

// --- Type Guards and Interfaces ---

const isOwnerNotification = (notification: any): notification is iProductOwnerNotification => {
  return 'product_id' in notification && 'review_id' in notification;
};

export interface UseSSENotificationsReturn {
  notifications: FetchedNotificationsData;
  unreadCount: number;
  unreadCounts: { user: number; owner: number; like: number };
  isSSEConnected: boolean;
  isLoading: boolean;
  error: Error | null;
  reconnect: () => void;
  disconnect: () => void;
  isUsingPolling: boolean;
  markAsRead: (notificationId: string, type: 'user' | 'owner' | 'like') => Promise<void>;
  refetch: () => void;
}

export interface UseSSENotificationsConfig {
  enabled?: boolean;
  fallbackToPolling?: boolean;
  pollingInterval?: number;
  debug?: boolean;
}

// --- The Hook ---

// Default export for backward compatibility
export default useSSENotifications;

export function useSSENotifications(
  userId?: string,
  config: UseSSENotificationsConfig = {}
): UseSSENotificationsReturn {
  const {
    enabled = true,
    fallbackToPolling = true,
    pollingInterval = 15000, // Slower polling when SSE is primary
    debug = false,
  } = config;

  const auth = useAuth();
  const effectiveUserId = userId || auth.userId;

  // --- State Management ---

  const [userNotifications, setUserNotifications] = useAtom(userNotificationsAtom);
  const [ownerNotifications, setOwnerNotifications] = useAtom(ownerNotificationsAtom);
  const [likeNotifications, setLikeNotifications] = useAtom(likeNotificationsAtom);

  const [isSSEConnected, setIsSSEConnected] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [sseError, setSSEError] = useState<Error | null>(null);
  const [isUsingPolling, setIsUsingPolling] = useState(false);

  const sseServiceRef = useRef<SSENotificationService | null>(null);
  const existingNotificationsRef = useRef<{ user: iUserNotification[]; owner: iProductOwnerNotification[]; like: LikeNotification[] }>({ user: [], owner: [], like: [] });
  const batchUpdateRef = useRef<NodeJS.Timeout | null>(null);

  const sseSupported = typeof window !== 'undefined' && typeof window.EventSource !== 'undefined';

  // --- Local Storage Caching ---
  const CACHE_KEY = `notifications_${effectiveUserId}`;
  
  // Save notifications to cache
  const saveToCache = useCallback((data: FetchedNotificationsData) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data));
    }
  }, [CACHE_KEY]);
  
  // Load notifications from cache
  const loadFromCache = useCallback((): FetchedNotificationsData | null => {
    if (typeof window !== 'undefined') {
      const cached = localStorage.getItem(CACHE_KEY);
      return cached ? JSON.parse(cached) : null;
    }
    return null;
  }, [CACHE_KEY]);
  
  // Initialize state from cache
  useEffect(() => {
    if (isInitialLoad) {
      const cached = loadFromCache();
      if (cached) {
        setUserNotifications(sortNotifications(cached.userNotifications || []));
        setOwnerNotifications(sortNotifications(cached.ownerNotifications || []));
        setLikeNotifications(sortNotifications(cached.likeNotifications || []) as LikeNotification[]);
      }
      setIsInitialLoad(false);
    }
  }, [isInitialLoad, loadFromCache, setUserNotifications, setOwnerNotifications, setLikeNotifications]);
  
  // Save to cache when notifications change
  useEffect(() => {
    if (!isInitialLoad) {
      saveToCache({
        userNotifications: userNotifications || [],
        ownerNotifications: ownerNotifications || [],
        likeNotifications: likeNotifications || [],
      });
    }
  }, [userNotifications, ownerNotifications, likeNotifications, isInitialLoad, saveToCache]);

  // --- Data Transformation and Sorting ---

  const convertToNotification = (payload: NotificationPayload | ExtendedNotificationPayload): iUserNotification | iProductOwnerNotification | LikeNotification => {
    const extendedPayload = payload as ExtendedNotificationPayload;
    const base = {
      id: payload.id,
      read: payload.read || false,
      created_at: new Date(payload.created_at),
      from_name: extendedPayload.from_name || '',
      from_id: extendedPayload.from_id || '',
      notification_type: extendedPayload.notification_type || '',
    };
    
    if (isOwnerNotification(extendedPayload)) {
      return {
        ...base,
        product_id: extendedPayload.product_id || '',
        review_id: extendedPayload.review_id || '',
      } as iProductOwnerNotification;
    }
    
    // Extract vote type from notification type
    let action_type: 'like' | 'dislike' | 'helpful' | undefined;
    let target_type: 'comment' | 'review' | undefined;
    let content = payload.message;

    if (extendedPayload.notification_type) {
      if (extendedPayload.notification_type.includes('reply')) {
        // Reply notifications should NOT have action_type or target_type to prevent grouping
        content = `replied to your ${extendedPayload.comment_id ? 'comment' : 'review'}`;
      } else if (extendedPayload.notification_type.includes('like')) {
        action_type = 'like';
        target_type = extendedPayload.comment_id ? 'comment' : 'review';
        content = `liked your ${extendedPayload.comment_id ? 'comment' : 'review'}`;
      } else if (extendedPayload.notification_type.includes('dislike')) {
        action_type = 'dislike';
        target_type = extendedPayload.comment_id ? 'comment' : 'review';
        content = `disliked your ${extendedPayload.comment_id ? 'comment' : 'review'}`;
      } else if (extendedPayload.notification_type.includes('helpful')) {
        action_type = 'helpful';
        target_type = 'review'; // Helpful votes are only for reviews
        content = 'found your review helpful';
      }
    }
    
    return {
      ...base,
      content,
      review_id: extendedPayload.review_id || '',
      comment_id: extendedPayload.comment_id || '',
      action_type,
      target_type,
    } as iUserNotification;
  };

  const sortNotifications = <T extends { read: boolean; created_at?: Date | string }>(notifications: T[]): T[] => {
    if (!notifications) return [];
    return [...notifications].sort((a, b) => {
      if (a.read !== b.read) return a.read ? 1 : -1;
      return new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime();
    });
  };
  
  // Debounced state update for batching
  const updateNotifications = useCallback(() => {
    if (existingNotificationsRef.current.user.length > 0) {
        setUserNotifications(prev => sortNotifications([...(prev || []), ...existingNotificationsRef.current.user].filter((n, i, self) => i === self.findIndex(t => t.id === n.id))));
        existingNotificationsRef.current.user = [];
    }
    if (existingNotificationsRef.current.owner.length > 0) {
        setOwnerNotifications(prev => sortNotifications([...(prev || []), ...existingNotificationsRef.current.owner].filter((n, i, self) => i === self.findIndex(t => t.id === n.id))));
        existingNotificationsRef.current.owner = [];
    }
    if (existingNotificationsRef.current.like.length > 0) {
        setLikeNotifications(prev => sortNotifications([...(prev || []), ...existingNotificationsRef.current.like].filter((n, i, self) => i === self.findIndex(t => t.id === n.id))) as LikeNotification[]);
        existingNotificationsRef.current.like = [];
    }
  }, [setUserNotifications, setOwnerNotifications, setLikeNotifications]);

  const debouncedSetNotifications = useMemo(
    () => debounce(updateNotifications, 300),
    [updateNotifications]
  );

  // --- Navigation Event Listener ---
  useEffect(() => {
    const handleNavigation = () => {
      if (window.location.pathname === '/notifications') {
        // Force re-initialization when navigating to notifications
        setIsInitialLoad(true);
      }
    };

    window.addEventListener('popstate', handleNavigation);
    return () => window.removeEventListener('popstate', handleNavigation);
  }, []);

  // --- SSE Connection Logic ---

  const initializeSSE = useCallback(() => {
    if (!effectiveUserId || !enabled || !sseSupported) {
      if (fallbackToPolling) setIsUsingPolling(true);
      return;
    }

    sseServiceRef.current?.disconnect();

    const service = new SSENotificationService({
      userId: effectiveUserId,
      debug,
      onConnectionOpen: () => {
        setIsSSEConnected(true);
        setSSEError(null);
        setIsUsingPolling(false);
      },
      onConnectionError: (error) => {
        setIsSSEConnected(false);
        setSSEError(new Error('SSE connection failed.'));
        if (fallbackToPolling) setIsUsingPolling(true);
      },
      onMaxReconnectAttemptsReached: () => {
        setIsSSEConnected(false);
        setSSEError(new Error('SSE max retries reached.'));
        if (fallbackToPolling) setIsUsingPolling(true);
      },
      onMessage: (message: SSEMessage) => {
        const { event, type, notification } = message as any;
        if (!notification) return;
        
        // Handle special message formats (heartbeat, connected)
        if ('time' in notification && 'message' in notification) {
          // This is a system message like heartbeat, not a real notification
          return;
        }
        
        // Special handling for like notifications to preserve all fields
        let converted: iUserNotification | iProductOwnerNotification | LikeNotification;
        if (type === 'like') {
          converted = notification as LikeNotification;
        } else {
          converted = convertToNotification(notification as NotificationPayload);
        }
        
        if (event === 'notification_read') {
          // Handle notification_read events - backend sends notification_id field
          const notificationId = notification.notification_id || notification.id;
          
          if (type === 'user') {
            setUserNotifications(prev => 
              prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
            );
          } else if (type === 'owner') {
            setOwnerNotifications(prev => 
              prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
            );
          } else if (type === 'like') {
            setLikeNotifications(prev => 
              prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
            );
          }
        } else if (event === 'new_notification') {
          if (type === 'user') {
            const userNotif = converted as iUserNotification;
            
            // Group similar vote notifications
            setUserNotifications(prev => {
              const notifications = prev || [];
              
              // Check for duplicate notification by ID first
              const isDuplicate = notifications.some(n => n.id === userNotif.id);
              if (isDuplicate) {
                return notifications; // Don't add duplicate
              }
              
              // Only group vote notifications
              if (userNotif.action_type && userNotif.target_type) {
                // Find existing notification for the same content and action
                const existingIndex = notifications.findIndex(n => 
                  n.action_type === userNotif.action_type &&
                  n.target_type === userNotif.target_type &&
                  ((n.comment_id && n.comment_id === userNotif.comment_id) ||
                   (n.review_id && n.review_id === userNotif.review_id))
                );
                
                if (existingIndex !== -1) {
                  // Update existing notification
                  const existing = notifications[existingIndex];
                  const updatedNotif = {
                    ...existing,
                    additional_users: [
                      ...(existing.additional_users || []),
                      { name: userNotif.from_name, id: userNotif.from_id }
                    ],
                    total_count: (existing.total_count || 1) + 1,
                    created_at: new Date() // Update timestamp
                  };
                  
                  // Create new array with updated notification
                  const newNotifications = [...notifications];
                  newNotifications[existingIndex] = updatedNotif;
                  return sortNotifications(newNotifications);
                }
              }
              
              return sortNotifications([userNotif, ...notifications]);
            });
          } else if (type === 'owner') {
            const ownerNotif = converted as iProductOwnerNotification;
            setOwnerNotifications(prev => {
              const notifications = prev || [];
              
              // Check for duplicate notification by ID first
              const isDuplicate = notifications.some(n => n.id === ownerNotif.id);
              if (isDuplicate) {
                return notifications; // Don't add duplicate
              }
              
              return sortNotifications([ownerNotif, ...notifications]);
            });
          } else if (type === 'like') {
            const likeNotif = converted as LikeNotification;
            setLikeNotifications(prev => {
              const notifications = prev || [];
              if (notifications.some(n => n.id === likeNotif.id)) return notifications;
              return sortNotifications([likeNotif, ...notifications]);
            });
          }
        } else if (event === 'existing_notification') {
          const convertedNotif = converted as iUserNotification | iProductOwnerNotification | LikeNotification;
          
          if (type === 'user') {
            const userNotif = convertedNotif as iUserNotification;
            // Check if this notification is already in the batch or current state
            const isInBatch = existingNotificationsRef.current.user.some(n => n.id === userNotif.id);
            if (!isInBatch) {
              existingNotificationsRef.current.user.push(userNotif);
            } else {
              // Removed debug console.log statements
            }
          } else if (type === 'owner') {
            const ownerNotif = convertedNotif as iProductOwnerNotification;
            // Check if this notification is already in the batch or current state
            const isInBatch = existingNotificationsRef.current.owner.some(n => n.id === ownerNotif.id);
            if (!isInBatch) {
              existingNotificationsRef.current.owner.push(ownerNotif);
            } else {
              // Removed debug console.log statements
            }
          } else if (type === 'like') {
            const likeNotif = convertedNotif as LikeNotification;
            // Check if this notification is already in the batch or current state
            const isInBatch = existingNotificationsRef.current.like.some(n => n.id === likeNotif.id);
            if (!isInBatch) {
              existingNotificationsRef.current.like.push(likeNotif);
            } else {
              // Removed debug console.log statements
            }
          }
          debouncedSetNotifications();
        }
      },
    });

    sseServiceRef.current = service;
    service.connect();

  }, [effectiveUserId, enabled, sseSupported, fallbackToPolling, debug, setUserNotifications, setOwnerNotifications, setLikeNotifications, debouncedSetNotifications]);

  // --- Polling Fallback Logic ---

  const queryClient = useQueryClient();
  const { isLoading: isPollingLoading, error: pollingError, refetch: refetchNotifications } = useQuery<FetchedNotificationsData, Error>({
    queryKey: ['notifications-polling', effectiveUserId],
    queryFn: async () => {
      if (!effectiveUserId) throw new Error('User ID is required for polling.');
      const res = await getNotifications(effectiveUserId);
      if (!res.success || !res.data) throw new Error(res.error || 'Failed to fetch notifications.');
      setUserNotifications(sortNotifications(res.data.userNotifications || []));
      setOwnerNotifications(sortNotifications(res.data.ownerNotifications || []));
      setLikeNotifications(sortNotifications(res.data.likeNotifications || []) as LikeNotification[]);
      return res.data;
    },
    enabled: isUsingPolling && !!effectiveUserId,
    refetchInterval: pollingInterval,
    refetchOnWindowFocus: true,
  });

  // --- Effects ---

  useEffect(() => {
    // Initialize on mount
    initializeSSE();

    // Reconnect when tab becomes visible again
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        initializeSSE();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Capture the current ref values for cleanup
    const currentBatchTimeout = batchUpdateRef.current;
    const currentSSEService = sseServiceRef.current;

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      currentSSEService?.disconnect();
      // Clear any pending batch update timeout
      if (currentBatchTimeout) {
        clearTimeout(currentBatchTimeout);
      }
    };
  }, [initializeSSE]);

  useEffect(() => {
    if (userNotifications !== undefined && ownerNotifications !== undefined && likeNotifications !== undefined && isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [userNotifications, ownerNotifications, likeNotifications, isInitialLoad]);

  // --- User Actions ---

  const markAsRead = useCallback(async (notificationId: string, type: 'user' | 'owner' | 'like') => {
    if (!effectiveUserId) return;
    
    const isUser = type === 'user';
    const isLike = type === 'like';
    const notifications = isLike ? likeNotifications : (isUser ? userNotifications : ownerNotifications);
    const setNotifications = isLike ? setLikeNotifications : (isUser ? setUserNotifications : setOwnerNotifications);
    
    const originalState = [...(notifications || [])];
    const optimisticState = originalState.map(n => n.id === notificationId ? { ...n, read: true } : n);
    
    setNotifications(optimisticState as any);

    try {
      const result = await apiMarkAsRead(notificationId, effectiveUserId, type);
      if (!result.success) throw new Error('Server failed to mark as read.');
    } catch (error) {
      setNotifications(originalState as any); // Rollback on failure
      console.error('Failed to mark notification as read:', error);
    }
  }, [effectiveUserId, userNotifications, ownerNotifications, likeNotifications, setUserNotifications, setOwnerNotifications, setLikeNotifications]);

  const reconnect = useCallback(() => {
    initializeSSE();
  }, [initializeSSE]);

  const disconnect = useCallback(() => {
    sseServiceRef.current?.disconnect();
    setIsSSEConnected(false);
  }, []);

  // --- Memoized Return Values ---

  const unreadCounts = useMemo(() => ({
    user: userNotifications?.filter(n => !n.read).length || 0,
    owner: ownerNotifications?.filter(n => !n.read).length || 0,
    like: likeNotifications?.filter(n => !n.read).length || 0,
  }), [userNotifications, ownerNotifications, likeNotifications]);

  return {
    notifications: {
      userNotifications: userNotifications || [],
      ownerNotifications: ownerNotifications || [],
      likeNotifications: likeNotifications || [],
    },
    unreadCount: unreadCounts.user + unreadCounts.owner + unreadCounts.like,
    unreadCounts,
    isSSEConnected,
    isLoading: isInitialLoad || (isUsingPolling && isPollingLoading),
    error: sseError || pollingError || null,
    reconnect,
    disconnect,
    isUsingPolling,
    markAsRead,
    refetch: refetchNotifications,
  };
}