import { useState } from 'react';
import { toast } from 'sonner';

interface ShareOptions {
    title: string;
    text: string;
    url: string;
    imageUrl?: string;
}

export const useShare = () => {
    const [isSharing, setIsSharing] = useState(false);

    const share = async ({ title, text, url, imageUrl }: ShareOptions) => {
        setIsSharing(true);

        try {
            if (navigator.share) {
                const shareData: any = {
                    title,
                    text,
                    url,
                };

                // Only add files if we have an image URL
                if (imageUrl) {
                    try {
                        const response = await fetch(imageUrl);
                        const blob = await response.blob();
                        shareData.files = [
                            new File([blob], 'image.jpg', { type: 'image/jpeg' })
                        ];
                    } catch (imageError) {
                        console.warn('Failed to fetch image for share:', imageError);
                    }
                }

                await navigator.share(shareData);
            } else {
                // Fallback to clipboard copy
                await navigator.clipboard.writeText(url);
                toast.success("Link copied to clipboard!");
            }
        } catch (error) {
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    // User cancelled the share
                    return;
                }
                console.error('Share failed:', error);
                // Fallback to clipboard copy on error
                await navigator.clipboard.writeText(url);
                toast.success("Link copied to clipboard!");
            }
        } finally {
            setIsSharing(false);
        }
    };

    return { share, isSharing };
}; 