'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';

interface TrackingOptions {
    trackDuration?: boolean;
    trackScrollDepth?: boolean;
    trackClicks?: boolean;
}

interface ManualEventData {
    eventType: string;
    metadata?: Record<string, any>;
}

/**
 * Hook for tracking product analytics on the client side
 * 
 * @param productId - The ID of the product to track
 * @param options - Configuration options for tracking
 * @returns Object with tracking state and manual event logging function
 */
export function useProductAnalytics(productId: string, options: TrackingOptions = {}) {
    const { userId } = useAuth();
    const [isTracking, setIsTracking] = useState(false);
    const startTime = useRef(Date.now());
    const maxScrollDepth = useRef(0);
    const interactions = useRef(0);

    // Helper function to determine device type
    const getDeviceType = () => {
        const ua = navigator.userAgent;
        if (/mobile|android|iphone/i.test(ua)) return 'mobile';
        if (/tablet|ipad/i.test(ua)) return 'tablet';
        return 'desktop';
    };

    // Helper function to log duration
    const logDuration = useCallback(async () => {
        if (!productId) return;

        const duration = Math.floor((Date.now() - startTime.current) / 1000);

        if (duration < 1) return; // Don't log very short visits

        try {
            await fetch('/api/analytics/update-view', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    productId,
                    duration,
                    scrollDepth: maxScrollDepth.current,
                    interactions: interactions.current
                }),
            });
        } catch (error) {
            console.error('[Analytics] Error updating view duration:', error);
        }
    }, [productId]);

    // Log initial view
    useEffect(() => {
        if (!productId) return;

        const logView = async () => {
            try {
                // Get referrer and device info
                const referrer = document.referrer;
                const deviceType = getDeviceType();

                // Log the view
                const response = await fetch('/api/analytics/log-view', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        productId,
                        source: referrer,
                        deviceType
                    }),
                });

                if (response.ok) {
                    setIsTracking(true);
                } else {
                    console.error(`[Analytics] Failed to log view for product ID: ${productId}`);
                }
            } catch (error) {
                console.error('[Analytics] Error logging product view:', error);
            }
        };

        logView();

        // Cleanup function to log duration when component unmounts
        return () => {
            if (options.trackDuration) {
                logDuration();
            }
        };
    }, [productId, options.trackDuration, logDuration]);

    // Track scroll depth
    useEffect(() => {
        if (!options.trackScrollDepth || !isTracking || !productId) return;

        const handleScroll = () => {
            const scrollTop = window.scrollY;
            const docHeight = document.documentElement.scrollHeight;
            const winHeight = window.innerHeight;
            const scrollPercent = scrollTop / (docHeight - winHeight) * 100;

            if (scrollPercent > maxScrollDepth.current) {
                maxScrollDepth.current = scrollPercent;
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [options.trackScrollDepth, isTracking, productId]);

    // Track interaction events
    useEffect(() => {
        if (!options.trackClicks || !isTracking || !productId) return;

        const handleInteraction = () => {
            interactions.current += 1;
        };

        document.addEventListener('click', handleInteraction);
        return () => document.removeEventListener('click', handleInteraction);
    }, [options.trackClicks, isTracking, productId]);

    // Function to log manual events
    const logManualEvent = async (eventType: string, metadata?: Record<string, any>) => {
        if (!productId || !isTracking) return;

        try {
            await fetch('/api/analytics/log-event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    productId,
                    eventType,
                    metadata,
                    timestamp: new Date().toISOString()
                }),
            });
        } catch (error) {
            console.error(`[Analytics] Error logging manual event ${eventType}:`, error);
        }
    };

    return {
        isTracking,
        logManualEvent
    };
}