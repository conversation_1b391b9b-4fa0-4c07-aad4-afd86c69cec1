/**
 * Hook to sync products data between server cache and client state
 * Provides manual refresh capability for immediate updates
 */
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { getProducts } from "@/app/util/serverFunctions";
import { iProduct } from "@/app/util/Interfaces";

export function useProductsSync() {
  const queryClient = useQueryClient();
  const [allProducts, setAllProducts] = useAtom(allProductsStore);

  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    refetchOnWindowFocus: false,
    staleTime: 1 * 60 * 1000, // 1 minute
    initialData: allProducts?.length > 0 ? { success: true, data: allProducts } : undefined,
  });

  // Force refresh function that bypasses cache
  const forceRefresh = async () => {
    // Invalidate React Query cache
    await queryClient.invalidateQueries({ queryKey: ["products"] });
    
    // Trigger server cache invalidation
    try {
      await fetch('/api/admin/cache/invalidate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'invalidate-all-products' })
      });
    } catch (err) {
      console.warn('Failed to invalidate server cache:', err);
    }
    
    // Force refetch
    return refetch();
  };

  // Update atom when fresh data arrives
  if (data && data.success && data.data && data.data !== allProducts) {
    setAllProducts(data.data);
  }

  return {
    products: (data?.success ? data.data : allProducts) as iProduct[] || [],
    isLoading,
    isError,
    error,
    refetch,
    forceRefresh
  };
}