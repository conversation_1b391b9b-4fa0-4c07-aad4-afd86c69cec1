import { MetadataRoute } from 'next'
import { prisma } from '@/app/util/prismaClient'

// Constants for sitemap configuration
const ITEMS_PER_SITEMAP = 1000; // Google's limit is 50,000, but we'll use a smaller number for better performance
const MAX_SITEMAPS = 50; // Google's limit is 50,000 sitemaps

interface SitemapEntry {
  url: string;
  lastModified: Date;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
}

// Helper function to validate product data
function isValidProduct(product: any): boolean {
  return (
    product &&
    product.id &&
    !product.isDeleted &&
    (product.updatedAt || product.createdDate)
  );
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // Get total count of active products
    const totalProducts = await prisma.product.count({
      where: {
        isDeleted: false,
        // Additional safety checks
        id: { not: '' },
        name: { not: '' }
      }
    });

    // Calculate number of sitemaps needed
    const totalSitemaps = Math.min(
      Math.ceil(totalProducts / ITEMS_PER_SITEMAP),
      MAX_SITEMAPS
    );

    // Static routes with enhanced metadata
    const staticRoutes: SitemapEntry[] = [
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/browse`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/submit`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/reviews`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/install`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.8,
      },
    ];

    // If we have a small number of products, return everything in one sitemap
    if (totalProducts <= ITEMS_PER_SITEMAP) {
      const products = await prisma.product.findMany({
        where: {
          isDeleted: false,
          // Additional safety checks
          id: { not: '' },
          name: { not: '' }
        },
        select: {
          id: true,
          updatedAt: true,
          createdDate: true,
          rating: true,
          name: true,
          _count: {
            select: { reviews: true }
          }
        },
        orderBy: { updatedAt: 'desc' }
      });

      // Filter out any invalid products and map to sitemap entries
      const productRoutes: SitemapEntry[] = products
        .filter(isValidProduct)
        .map((product) => ({
          url: `${process.env.NEXT_PUBLIC_APP_URL}/reviews?id=${product.id}`,
          lastModified: product.updatedAt || product.createdDate || new Date(),
          changeFrequency: 'weekly',
          priority: calculatePriority(product.rating, product._count?.reviews || 0),
        }));

      // Fetch active users to include their profiles in the sitemap
      const users = await prisma.user.findMany({
        where: {
          status: 'ACTIVE',
          isDeleted: false,
          userName: { not: '' },
        },
        select: {
          userName: true,
          createdDate: true,
        },
      });

      const userRoutes: SitemapEntry[] = users
        .filter(isValidUser)
        .map((user) => ({
          url: `${process.env.NEXT_PUBLIC_APP_URL}/userprofile/@${user.userName}`,
          lastModified: user.createdDate || new Date(),
          changeFrequency: 'weekly',
          priority: 0.6,
        }));

      return [...staticRoutes, ...productRoutes, ...userRoutes];
    }

    // For larger sites, implement pagination
    const currentPage = 1; // This would be dynamic in a multi-sitemap setup
    const skip = (currentPage - 1) * ITEMS_PER_SITEMAP;

    const products = await prisma.product.findMany({
      where: {
        isDeleted: false,
        // Additional safety checks
        id: { not: '' },
        name: { not: '' }
      },
      select: {
        id: true,
        updatedAt: true,
        createdDate: true,
        rating: true,
        name: true,
        _count: {
          select: { reviews: true }
        }
      },
      orderBy: { updatedAt: 'desc' },
      skip,
      take: ITEMS_PER_SITEMAP,
    });

    // Filter out any invalid products and map to sitemap entries
    const productRoutes: SitemapEntry[] = products
      .filter(isValidProduct)
      .map((product) => ({
        url: `${process.env.NEXT_PUBLIC_APP_URL}/reviews?id=${product.id}`,
        lastModified: product.updatedAt || product.createdDate || new Date(),
        changeFrequency: 'weekly',
        priority: calculatePriority(product.rating, product._count?.reviews || 0),
      }));

    // Fetch paginated active users for this sitemap batch
    const users = await prisma.user.findMany({
      where: {
        status: 'ACTIVE',
        isDeleted: false,
        userName: { not: '' },
      },
      select: {
        userName: true,
        createdDate: true,
      },
      skip,
      take: ITEMS_PER_SITEMAP,
    });

    const userRoutes: SitemapEntry[] = users
      .filter(isValidUser)
      .map((user) => ({
        url: `${process.env.NEXT_PUBLIC_APP_URL}/userprofile/@${user.userName}`,
        lastModified: user.createdDate || new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      }));

    return [...staticRoutes, ...productRoutes, ...userRoutes];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return static routes only if there's an error
    return [
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/browse`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/submit`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/reviews`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL}/install`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.8,
      },
    ];
  }
}

// Helper function to calculate priority based on product rating and review count
// Helper function to validate user data
function isValidUser(user: any): boolean {
  return user && user.userName && user.userName.trim() !== '';
}

// Helper function to calculate priority based on product rating and review count
function calculatePriority(rating: number, reviewCount: number): number {
  // Base priority
  let priority = 0.6;

  // Increase priority based on rating
  if (rating >= 4) priority += 0.1;
  if (rating >= 4.5) priority += 0.1;

  // Increase priority based on review count
  if (reviewCount >= 10) priority += 0.1;
  if (reviewCount >= 50) priority += 0.1;

  // Ensure priority doesn't exceed 1.0
  return Math.min(priority, 1.0);
}
