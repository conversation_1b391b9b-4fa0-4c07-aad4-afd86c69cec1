import { ReactNode } from 'react';
import Script from 'next/script';

export default function WidgetIframeLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          html, body {
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            height: 100vh !important;
            width: 100vw !important;
          }
          
          /* Hide any potential global elements */
          nav, header, footer, .navbar, .header, .footer {
            display: none !important;
          }
        `
      }} />
      
      <div style={{
        width: '100vw',
        height: '100vh',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px',
        background: 'transparent',
        margin: 0
      }}>
        {children}
      </div>
      
      <Script id="widget-tracking" strategy="afterInteractive">
        {`
          function trackEvent(event, data = {}) {
            const widgetId = window.location.pathname.split('/').pop();
            if (!window.location.search.includes('preview=true')) {
              fetch('/api/public/widgets/' + widgetId + '/track', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  event: event,
                  referrer: document.referrer,
                  userAgent: navigator.userAgent,
                  ...data
                })
              }).catch(() => {});
            }
          }
          
          trackEvent('view');
          
          document.addEventListener('click', function(e) {
            trackEvent('click', {
              elementType: e.target.tagName.toLowerCase(),
              elementClass: e.target.className
            });
          });
        `}
      </Script>
    </>
  );
}