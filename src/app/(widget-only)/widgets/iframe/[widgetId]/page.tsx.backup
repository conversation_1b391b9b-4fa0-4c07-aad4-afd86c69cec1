"use client";

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';

interface WidgetIframePageProps {
  params: {
    widgetId: string;
  };
}

export default function WidgetIframePage({ params }: WidgetIframePageProps) {
  const [widget, setWidget] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWidget = async () => {
      try {
        // Check if this is a preview request
        const urlParams = new URLSearchParams(window.location.search);
        const isPreview = urlParams.get('preview') === 'true';
        const apiUrl = `/api/public/widgets/${params.widgetId}${isPreview ? '?preview=true' : ''}`;
        
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error('Widget not found');
        }
        const data = await response.json();
        setWidget(data.data);
      } catch (err) {
        console.error('Error fetching widget:', err);
        setError('Failed to load widget');
      } finally {
        setLoading(false);
      }
    };

    fetchWidget();
  }, [params.widgetId]);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100px',
        padding: '20px',
        background: '#f9fafb',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        color: '#6b7280',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
      }}>
        <div style={{
          width: '20px',
          height: '20px',
          border: '2px solid #e5e7eb',
          borderTop: '2px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          marginRight: '8px'
        }}></div>
        Loading widget...
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error || !widget) {
    return (
      <div style={{
        padding: '20px',
        background: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        color: '#dc2626',
        textAlign: 'center',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
      }}>
        <strong>Widget Error:</strong> {error || 'Widget not found'}
      </div>
    );
  }

  // Limit reviews based on widget settings
  const reviews = widget.product?.reviews?.slice(0, widget.maxReviews) || [];
  
  // Calculate average rating
  const totalReviews = (widget.product?.rating1Star || 0) + (widget.product?.rating2Stars || 0) +
                      (widget.product?.rating3Stars || 0) + (widget.product?.rating4Stars || 0) +
                      (widget.product?.rating5Stars || 0);
  
  const averageRating = totalReviews > 0 ? (widget.product?.rating || 0) : 0;

  // Dynamic styles based on widget configuration
  const widgetContainerStyle = {
    fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    background: widget.theme === 'dark' ? '#1a1a1a' : '#ffffff',
    borderRadius: widget.borderRadius,
    padding: '16px',
    maxWidth: '100%',
    boxSizing: 'border-box' as 'border-box'
  };

  const businessNameStyle = {
    fontWeight: 600,
    fontSize: '16px',
    color: widget.primaryColor || '#3b82f6'
  };

  const ratingTextStyle = {
    fontSize: '14px',
    color: widget.theme === 'dark' ? '#d1d5db' : '#6b7280'
  };

  const reviewItemStyle = {
    padding: '12px',
    border: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
    borderRadius: '8px',
    background: widget.theme === 'dark' ? '#111827' : '#f9fafb'
  };

  const reviewerNameStyle = {
    fontWeight: 500,
    fontSize: '14px'
  };

  const reviewDateStyle = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
  };

  const poweredByStyle = {
    textAlign: 'center' as 'center',
    marginTop: '16px',
    fontSize: '11px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
  };

  const poweredByLinkStyle = {
    color: widget.primaryColor || '#3b82f6',
    textDecoration: 'none'
  };

  return (
    <div className={`widget-${widget.type.toLowerCase().replace('_', '-')} theme-${widget.theme}`}>
      <div style={widgetContainerStyle}>
        {widget.showLogo && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px', gap: '8px' }}>
            <div style={businessNameStyle}>
              {widget.business?.ownerName || 'Business'}
            </div>
            {widget.business?.isVerified && (
              <div style={{
                background: '#10b981',
                color: 'white',
                fontSize: '10px',
                padding: '2px 6px',
                borderRadius: '4px',
                textTransform: 'uppercase',
                fontWeight: 500
              }}>Verified</div>
            )}
          </div>
        )}

        {widget.showRating && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <div style={{ display: 'flex', gap: '2px' }}>
              {[1, 2, 3, 4, 5].map((star) => (
                <svg
                  key={star}
                  width="16px"
                  height="16px"
                  fill={star <= averageRating ? 'currentColor' : 'none'}
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                  />
                </svg>
              ))}
            </div>
            <div style={ratingTextStyle}>
              {averageRating.toFixed(1)} ({totalReviews} reviews)
            </div>
          </div>
        )}

        {reviews.length > 0 ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {reviews.map((review: any) => (
              <div key={review.id} style={reviewItemStyle}>
                {widget.showRating && (
                  <div style={{ display: 'flex', gap: '1px', marginBottom: '4px' }}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        width="16px"
                        height="16px"
                        fill={star <= review.rating ? 'currentColor' : 'none'}
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                        />
                      </svg>
                    ))}
                  </div>
                )}
                
                {widget.showReviewText && (
                  <>
                    <div style={{ fontSize: '14px', lineHeight: 1.4, marginBottom: '4px' }}>
                      <strong>{review.title}</strong>
                    </div>
                    <div style={{ fontSize: '14px', lineHeight: 1.4, marginBottom: '4px' }}>
                      {review.body.length > 150
                        ? `${review.body.substring(0, 150)}...`
                        : review.body
                      }
                    </div>
                  </>
                )}
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  {widget.showReviewerName && (
                    <div style={reviewerNameStyle}>
                      {review.user.firstName} {review.user.lastName?.charAt(0)}.
                    </div>
                  )}
                  {widget.showReviewDate && (
                    <div style={reviewDateStyle}>
                      {new Date(review.createdDate).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px', color: '#ef4444' }}>
            No reviews available
          </div>
        )}

        {widget.showPoweredBy && (
          <div style={poweredByStyle}>
            Powered by <a href="https://reviewit.gy" target="_blank" rel="noopener" style={poweredByLinkStyle}>ReviewIt</a>
          </div>
        )}
      </div>
    </div>
  );
}