"use client";

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';
import { WidgetRenderer } from '@/app/components/widgets/WidgetRenderer';

// Disable Next.js layout for this page
export const dynamic = 'force-dynamic';

interface WidgetIframePageProps {
  params: {
    widgetId: string;
  };
}

export default function WidgetIframePage({ params }: WidgetIframePageProps) {
  const [widget, setWidget] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWidget = async () => {
      try {
        // Check if this is a preview request
        const urlParams = new URLSearchParams(window.location.search);
        const isPreview = urlParams.get('preview') === 'true';
        
        // Get the embedding domain from multiple sources
        let embedDomain = urlParams.get('embedDomain'); // Primary source: URL parameter
        
        // Fallback to localStorage if URL param is missing
        if (!embedDomain) {
          try {
            const storedDomain = localStorage.getItem('reviewit_embed_domain');
            if (storedDomain) {
              embedDomain = storedDomain;
              // Removed debug console.log statement
            }
          } catch (e) {
            console.warn('Unable to access localStorage for embed domain');
          }
        }
        const apiUrl = `/api/public/widgets/${params.widgetId}${isPreview ? '?preview=true' : ''}`;
        
        // Try to get the parent window's referrer to identify the actual embedding site
        let parentReferrer = '';
        let embedContext = 'iframe';
        
        try {
          // Check if we're in an iframe
          if (window.parent && window.parent !== window) {
            embedContext = 'embedded-iframe';
            // Try to get parent referrer (will fail for cross-origin)
            try {
              parentReferrer = window.parent.document.referrer || '';
            } catch (e) {
              // Cross-origin iframe - this is expected for external embeddings
              parentReferrer = 'cross-origin-iframe';
            }
          }
        } catch (e) {
          // Fallback
          embedContext = 'unknown';
        }

        // Prepare headers with embedding context information
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        
        // Add embedding context information
        if (parentReferrer && parentReferrer !== 'cross-origin-iframe') {
          headers['X-Parent-Referrer'] = parentReferrer;
        }
        if (embedDomain) {
          headers['X-Embed-Domain'] = embedDomain; // Pass the actual embedding domain
        }
        headers['X-Embed-Context'] = embedContext;
        headers['X-Frame-Depth'] = window.parent === window ? '0' : '1';
        
        const response = await fetch(apiUrl, { headers });
        if (!response.ok) {
          throw new Error('Widget not found');
        }
        const data = await response.json();
        setWidget(data.data);
      } catch (err) {
        console.error('Error fetching widget:', err);
        setError('Failed to load widget');
      } finally {
        setLoading(false);
      }
    };

    fetchWidget();
  }, [params.widgetId]);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100px',
        padding: '20px',
        background: '#f9fafb',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        color: '#6b7280',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
      }}>
        <div style={{
          width: '20px',
          height: '20px',
          border: '2px solid #e5e7eb',
          borderTop: '2px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          marginRight: '8px'
        }}></div>
        Loading widget...
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error || !widget) {
    return (
      <div style={{
        padding: '20px',
        background: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        color: '#dc2626',
        textAlign: 'center',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
      }}>
        <strong>Widget Error:</strong> {error || 'Widget not found'}
      </div>
    );
  }

  // Use the existing layout system with WidgetRenderer
  return (
    <WidgetRenderer 
      widget={widget} 
      searchParams={{}} 
    />
  );
}