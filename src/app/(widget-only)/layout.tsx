import "@/app/globals.css";
import { Poppins } from "next/font/google";
import type { Metadata } from "next";
import Script from "next/script";

const poppins = Poppins({
  weight: ["400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  style: "normal",
});

// Minimal metadata for widget-only pages
export const metadata: Metadata = {
  title: "ReviewIt Widget",
  robots: {
    index: false,
    follow: false,
  },
};

export default function WidgetOnlyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="noindex, nofollow" />
        {/* Disable Clerk client-side scripts for widgets */}
        <Script id="disable-clerk" strategy="beforeInteractive">
          {`
            // Override Clerk environment variables to prevent client-side initialization
            window.__clerk_publishable_key = undefined;
            window.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = undefined;
            
            // Prevent any automatic authentication calls
            if (typeof window !== 'undefined') {
              const originalFetch = window.fetch;
              window.fetch = function(...args) {
                const url = args[0];
                // Block authentication-related API calls in widget context
                if (typeof url === 'string' && (
                  url.includes('/api/get/user') || 
                  url.includes('/api/auth') ||
                  url.includes('clerk.') ||
                  url.includes('/v1/client')
                )) {
                  console.warn('Blocked authentication call in widget context:', url);
                  return Promise.reject(new Error('Authentication disabled in widget context'));
                }
                return originalFetch.apply(this, args);
              };
            }
          `}
        </Script>
      </head>
      <body className={`${poppins.className} bg-white`}>
        {children}
      </body>
    </html>
  );
}