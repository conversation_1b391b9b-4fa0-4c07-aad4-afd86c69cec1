import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Test Metadata Page',
    description: 'This is a test page to verify metadata rendering',
    openGraph: {
        title: 'Test Metadata Page',
        description: 'This is a test page to verify metadata rendering',
        url: 'https://reviewit.gy/test-metadata',
        images: [
            {
                url: 'https://reviewit.gy/logo.png',
                width: 1200,
                height: 630,
                alt: 'Test Image',
            }
        ],
        type: 'website',
        siteName: 'Review It',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Test Metadata Page',
        description: 'This is a test page to verify metadata rendering',
        images: ['https://reviewit.gy/logo.png'],
    },
};

export default function TestMetadataPage() {
    return (
        <div className="min-h-screen bg-white p-8">
            <h1 className="text-2xl font-bold mb-4">Test Metadata Page</h1>
            <p>This page has static metadata to test if Next.js is properly rendering metadata.</p>
            <div className="mt-4">
                <h2 className="text-xl font-semibold mb-2">Expected Metadata:</h2>
                <pre className="bg-gray-100 p-4 rounded">
                    {JSON.stringify(metadata, null, 2)}
                </pre>
            </div>
        </div>
    );
} 