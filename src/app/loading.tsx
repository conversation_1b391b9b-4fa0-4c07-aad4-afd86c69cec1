"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="flex h-full w-full flex-col justify-start bg-gradient-to-b from-gray-50 via-white to-gray-50">
      {/* Hero Section Skeleton */}
      <section className="relative h-[550px] sm:h-[500px] md:h-[600px] lg:h-[500px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-400 animate-pulse"></div>
        <div className="relative flex justify-center items-center h-full w-full text-center">
          <div className="flex flex-1 justify-center flex-col relative max-w-6xl mx-auto px-4">
            <div className="flex flex-col h-full w-full py-4 sm:py-8">
              {/* Title Skeletons */}
              <div className="flex-1 md:mt-4 space-y-4">
                <Skeleton className="h-12 sm:h-16 md:h-12 lg:h-20 w-3/4 mx-auto bg-white/20" />
                <Skeleton className="h-8 sm:h-10 md:h-8 lg:h-12 w-2/3 mx-auto bg-white/20" />
                <Skeleton className="h-6 sm:h-8 md:h-6 lg:h-10 w-1/2 mx-auto bg-white/20" />
                <Skeleton className="h-5 sm:h-6 md:h-5 lg:h-8 w-2/5 mx-auto bg-white/20" />
              </div>
              
              {/* Search and Stats Skeleton */}
              <div className="flex flex-col justify-end space-y-6 md:space-y-8">
                <Skeleton className="h-12 w-full max-w-2xl mx-auto rounded-full bg-white/20" />
                <div className="flex flex-wrap justify-center gap-4 sm:gap-6">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-10 w-24 rounded-full bg-white/20" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Write Review CTA Skeleton */}
      <div className="flex justify-center py-8 md:py-12 px-4 bg-white">
        <Skeleton className="h-14 w-48 rounded-full" />
      </div>

      {/* Top Reviews Section Skeleton */}
      <section className="flex w-full flex-col justify-center relative z-[4] mb-12 md:mb-16">
        <div className="flex w-full h-full flex-row flex-wrap sm:px-4 px-4 md:px-2 pb-4">
          <div className="flex flex-col w-full h-full justify-center items-center space-y-8">
            {/* Header */}
            <div className="w-full flex flex-col items-center space-y-6">
              <div className="text-center space-y-3">
                <Skeleton className="h-10 w-80 mx-auto" />
                <Skeleton className="h-6 w-96 mx-auto" />
              </div>
              <div className="flex gap-3 flex-wrap justify-center">
                {[1, 2, 3].map((i) => (
                  <Skeleton key={i} className="h-10 w-28 rounded-lg" />
                ))}
              </div>
            </div>
            
            {/* Reviews Grid */}
            <div className="w-full grid mx-auto items-start justify-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Skeleton key={i} className="h-[300px] w-full rounded-xl" />
              ))}
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mt-8">
              <Skeleton className="h-12 w-40 rounded-lg" />
              <Skeleton className="h-12 w-48 rounded-lg" />
            </div>
          </div>
        </div>
      </section>

      {/* Quick Categories Skeleton */}
      <section className="relative z-[1]">
        <div className="flex flex-col justify-center items-center w-full bg-gradient-to-b from-gray-50 to-white py-12 sm:py-16">
          <div className="flex flex-col items-center gap-2 mb-10 px-4">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-6 w-48" />
          </div>
          
          <div className="max-w-6xl mx-auto w-full px-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Additional Sections Skeleton */}
      <section className="flex w-full flex-col justify-center relative z-[4] py-12 md:py-16">
        <div className="max-w-7xl mx-auto w-full px-4 space-y-16 md:space-y-20">
          {/* Top Reviewers Skeleton */}
          <div className="mb-8">
            <div className="w-full px-4 py-8">
              <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-lg p-6">
                <div className="text-center mb-6">
                  <Skeleton className="h-10 w-64 mx-auto mb-4" />
                  <Skeleton className="h-6 w-96 mx-auto" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <Skeleton key={i} className="h-24 w-full rounded-lg" />
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Company Categories Skeleton */}
          <div className="mb-8">
            <div className="w-full bg-gray-50 py-16 rounded-lg">
              <div className="max-w-6xl mx-auto px-4">
                <div className="text-center mb-12">
                  <Skeleton className="h-8 w-64 mx-auto mb-4" />
                  <Skeleton className="h-4 w-96 mx-auto" />
                </div>
                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <Skeleton key={i} className="h-32 w-full rounded-xl" />
                  ))}
                </div>
                <div className="mt-12 text-center">
                  <Skeleton className="h-12 w-48 mx-auto rounded-lg" />
                </div>
              </div>
            </div>
          </div>
          
          {/* Value Proposition Skeleton */}
          <div className="mb-8">
            <div className="w-full py-12 bg-gradient-to-br from-white via-blue-50/30 to-white rounded-lg">
              <div className="max-w-4xl mx-auto px-4 text-center">
                <div className="mb-8">
                  <Skeleton className="h-8 w-64 mx-auto mb-3" />
                  <Skeleton className="h-6 w-96 mx-auto" />
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex flex-col items-center">
                      <Skeleton className="w-12 h-12 rounded-full mb-2" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  ))}
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Skeleton className="h-12 w-48 rounded-full" />
                  <Skeleton className="h-12 w-44 rounded-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer CTA Skeleton */}
      <section className="bg-gradient-to-r from-gray-400 to-gray-500 py-16 animate-pulse">
        <div className="max-w-4xl mx-auto text-center px-4">
          <Skeleton className="h-10 w-80 mx-auto mb-4 bg-white/20" />
          <Skeleton className="h-6 w-96 mx-auto mb-8 bg-white/20" />
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Skeleton className="h-12 w-40 rounded-lg bg-white/20" />
            <Skeleton className="h-12 w-40 rounded-lg bg-white/20" />
          </div>
        </div>
      </section>
    </div>
  );
}
