'use client';

import Link from 'next/link';
import { useAuth } from '@clerk/nextjs';

export default function BannedPage() {
  const { signOut } = useAuth();
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-5">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Account Banned</h1>
          <p className="text-gray-600 mb-6">
            Your account has been permanently banned from accessing ReviewIt due to violations of our terms of service.
          </p>
          
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <h2 className="text-lg font-medium text-gray-800 mb-2">What This Means</h2>
            <p className="text-gray-600">
              A permanent ban means you can no longer access your account or use ReviewIt services. This decision was made after careful review of your account activity.
            </p>
          </div>
          
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Appeal Process</h2>
            <p className="text-gray-600">
              If you believe this ban was applied in error, you may appeal this decision by contacting our administration team. Please include your account details and any relevant information that may help us review your case.
            </p>
          </div>
          
          <div className="flex flex-col space-y-3">
            <a 
              href="mailto:<EMAIL>" 
              className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Contact Administration
            </a>
            <button 
              onClick={() => signOut()}
              className="inline-flex items-center justify-center px-5 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
