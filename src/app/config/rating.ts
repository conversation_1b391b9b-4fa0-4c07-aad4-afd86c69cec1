const DEFAULT_MINIMUM_REVIEWS = 5;

// Parse PUBLIC_MINIMUM_REVIEWS environment variable, falling back to default if:
// - The variable is not set
// - The variable is an empty string or only whitespace
// - The variable cannot be parsed as a positive integer
export const MINIMUM_REVIEWS = (() => {
  const envValue = process.env.PUBLIC_MINIMUM_REVIEWS;
  if (!envValue || envValue.trim() === '') return DEFAULT_MINIMUM_REVIEWS;
  
  const parsedValue = parseInt(envValue.trim(), 10);
  return (!isNaN(parsedValue) && parsedValue > 0) ? parsedValue : DEFAULT_MINIMUM_REVIEWS;
})();