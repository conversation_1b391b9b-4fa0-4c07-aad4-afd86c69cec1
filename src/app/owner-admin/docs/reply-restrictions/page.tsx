import React from 'react';

const ReplyRestrictionsDoc = () => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Reply Restrictions Documentation</h1>
      
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Feature Overview</h2>
        <p className="mb-4">
          The reply restrictions feature ensures that only the original reviewer can reply to a business owner's comment on their review. This prevents other users from engaging in conversations between the reviewer and the business.
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Implementation Details</h2>
        
        <h3 className="text-xl font-medium mb-2">Server-Side Enforcement</h3>
        <p className="mb-4">
          The backend API enforces reply restrictions by:
        </p>
        <ul className="list-disc pl-6 mb-4">
          <li>Checking if the parent comment is by a business owner</li>
          <li>Verifying the current user is the original reviewer</li>
          <li>Returning 403 Forbidden for unauthorized reply attempts</li>
        </ul>

        <h3 className="text-xl font-medium mb-2">Client-Side UI</h3>
        <p className="mb-4">
          The frontend provides visual feedback to users:
        </p>
        <ul className="list-disc pl-6 mb-4">
          <li>Reply buttons are hidden for restricted comments</li>
          <li>Restriction messages explain why replies are disabled</li>
          <li>Toast notifications show errors during reply attempts</li>
        </ul>

        <h3 className="text-xl font-medium mb-2">API Endpoints</h3>
        <ul className="list-disc pl-6 mb-4">
          <li><code>POST /api/comments/reply-permission</code> - Checks reply permissions</li>
          <li><code>POST /api/create/comment</code> - Enforces restrictions during reply creation</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">User Experience</h2>
        <p className="mb-4">
          Business owners can reply to any review. Only the original reviewer can reply to the owner's comments. Other users see a message explaining the restriction.
        </p>
      </section>
    </div>
  );
};

export default ReplyRestrictionsDoc;
