"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { getTrafficTrends } from "@/app/util/analyticsService";
import { iTrafficTrend, iAnalyticsPeriod } from "@/app/util/Interfaces";
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";
import { AlertTriangle } from "lucide-react";

interface TrafficTrendsChartProps {
  businessId: string;
  startDate: Date;
  endDate: Date;
  formatNumber: (num: number) => string;
}

export function TrafficTrendsChart({ 
  businessId, 
  startDate, 
  endDate, 
  formatNumber 
}: TrafficTrendsChartProps) {
  // Create period object for API call
  const period: iAnalyticsPeriod = {
    startDate,
    endDate
  };

  // Fetch traffic trends data
  const { 
    data: trafficTrends, 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ["trafficTrends", businessId, startDate.toISOString(), endDate.toISOString()],
    queryFn: () => getTrafficTrends(businessId, period),
    enabled: !!businessId,
    retry: 1, // Only retry once to avoid showing mock data
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Show error state
  if (isError || !trafficTrends) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center">
        <AlertTriangle className="h-12 w-12 text-amber-500 mb-2" />
        <h3 className="text-lg font-semibold">Unable to load traffic trends</h3>
        <p className="text-muted-foreground text-sm max-w-md">
          We couldn't retrieve traffic source trends data. This could be due to insufficient data in the selected time period.
        </p>
      </div>
    );
  }

  // Show empty state if no data
  if (trafficTrends.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center">
        <h3 className="text-lg font-semibold">No Traffic Data Available</h3>
        <p className="text-muted-foreground text-sm max-w-md">
          There is no traffic source data available for the selected time period. Try selecting a different date range.
        </p>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={trafficTrends}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 20,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="date"
          tickFormatter={(date) => format(new Date(date), "MMM d")}
        />
        <YAxis tickFormatter={(value) => formatNumber(value)} />
        <Tooltip
          formatter={(value, name) => [
            formatNumber(value as number),
            name,
          ]}
          labelFormatter={(label) =>
            format(new Date(label as string), "MMMM d, yyyy")
          }
        />
        <Legend
          wrapperStyle={{ paddingTop: '20px' }}
          iconType="line"
        />
        <Line
          type="monotone"
          dataKey="Google"
          stroke="#4285F4"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="Direct"
          stroke="#10b981"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="Social"
          stroke="#f97316"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="Other"
          stroke="#9333ea"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
