"use client";

import { LucideIcon } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatCardProps {
    title: string;
    value: string | number;
    icon: LucideIcon;
    description?: string;
    change?: number | null;
    loading?: boolean;
    className?: string;
}

export function StatCard({
    title,
    value,
    icon: Icon,
    description,
    change = null,
    loading = false,
    className
}: StatCardProps) {
    const getChangeColor = (changeValue: number) => {
        if (changeValue > 0) return "text-green-600";
        if (changeValue < 0) return "text-red-600";
        return "text-gray-500";
    };

    const getChangeIcon = (changeValue: number) => {
        if (changeValue > 0) return "↑";
        if (changeValue < 0) return "↓";
        return "→";
    };

    return (
        <Card className={cn("p-3 sm:p-6", className)}>
            <CardHeader className="flex flex-row items-center justify-between pb-2 p-0">
                <CardTitle className="text-xs sm:text-sm font-medium truncate pr-2">{title}</CardTitle>
                <div className="bg-muted rounded-full p-1.5 sm:p-2 flex-shrink-0">
                    <Icon className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                </div>
            </CardHeader>
            <CardContent className="p-0 pt-2">
                {loading ? (
                    <div className="h-6 sm:h-9 w-1/2 rounded-md bg-muted animate-pulse" />
                ) : (
                    <div className="text-lg sm:text-2xl font-bold">
                        {typeof value === 'number' && !isNaN(value) ?
                            new Intl.NumberFormat('en-US', {
                                notation: value > 9999 ? "compact" : "standard",
                                maximumFractionDigits: 1
                            }).format(value)
                            : value
                        }
                    </div>
                )}
                {loading ? (
                    <div className="h-4 sm:h-5 w-3/4 rounded-md bg-muted animate-pulse mt-1" />
                ) : (
                    <>
                        {change !== null && (
                            <div className="flex items-center mt-1">
                                <div className={`flex items-center text-xs ${getChangeColor(change)} truncate`}>
                                    <span className="mr-1">{getChangeIcon(change)}</span>
                                    <span className="hidden sm:inline">{Math.abs(change).toFixed(1)}% from previous period</span>
                                    <span className="sm:hidden">{Math.abs(change).toFixed(1)}%</span>
                                </div>
                            </div>
                        )}
                        {description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{description}</p>
                        )}
                    </>
                )}
            </CardContent>
        </Card>
    );
}