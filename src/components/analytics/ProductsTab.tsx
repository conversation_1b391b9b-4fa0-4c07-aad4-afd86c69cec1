"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip } from "recharts";
import { useQuery } from "@tanstack/react-query";
import { TopAttentionProducts } from "@/components/admin/TopAttentionProducts";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { iBusinessAnalyticsEnhanced } from "@/app/util/Interfaces";

import { getProductConversions } from "@/app/util/analyticsService";
import { AlertTriangle } from "lucide-react";

interface ProductsTabProps {
  analyticsData: iBusinessAnalyticsEnhanced;
}

export function ProductsTab({ analyticsData }: ProductsTabProps) {
  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };
  
  // Fetch real product conversion data
  const businessId = analyticsData.businessId;

  // Top attention products for this business
  const { data: attentionData } = useQuery({
    queryKey: ['businessTopAttention', businessId],
    queryFn: () =>
      fetch(`/api/owner-admin/analytics/top-attention?businessId=${businessId}&limit=5`).then(r => r.json()),
    enabled: !!businessId,
  });

  const { data: productConversions, isLoading, isError } = useQuery({
    queryKey: ["productConversions", analyticsData.businessId],
    queryFn: () => getProductConversions(analyticsData.businessId),
    enabled: !!analyticsData.businessId,
    retry: 1, // Only retry once to avoid showing mock data
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Product Performance</CardTitle>
          <CardDescription>
            Compare views and conversions across your products. Blue bars show total page views while green bars represent actual conversions (reviews, inquiries, or purchases). Use this to identify your top-performing products and those needing attention.
          </CardDescription>
        </CardHeader>
        <CardContent className="min-h-[300px] sm:min-h-[400px] p-2 sm:p-6">
          {isLoading && (
            <div className="flex items-center justify-center h-[300px] sm:h-[400px]">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          )}
          
          {isError && (
            <div className="flex flex-col items-center justify-center h-[300px] sm:h-[400px] text-center">
              <AlertTriangle className="h-12 w-12 text-amber-500 mb-2" />
              <h3 className="text-lg font-semibold">Unable to load product data</h3>
              <p className="text-muted-foreground text-sm max-w-md">
                We couldn't retrieve product conversion data. Using estimated values instead.
              </p>
            </div>
          )}
          
          {!isLoading && !isError && productConversions && (
            <ResponsiveContainer width="100%" height={window.innerWidth < 640 ? 300 : 400}>
              <BarChart
                data={productConversions.map((product) => ({
                  name: product.name,
                  Views: product.views,
                  Conversion: product.conversions,
                }))}
                margin={{
                  top: 20,
                  right: 10,
                  left: -15,
                  bottom: window.innerWidth < 640 ? 90 : 70,
                }}
              >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={window.innerWidth < 640 ? 90 : 70}
                tick={{ fontSize: window.innerWidth < 640 ? 8 : 10 }}
              />
              <YAxis tickFormatter={(value) => formatNumber(value)} />
              <Tooltip
                formatter={(value) => [formatNumber(value as number), ""]}
              />
              <Bar dataKey="Views" fill="#3b82f6" name="Views" />
              <Bar dataKey="Conversion" fill="#10b981" name="Conversions" />
            </BarChart>
          </ResponsiveContainer>
          )}
          <div className="flex justify-center mt-4 sm:mt-8">
            <Button asChild>
              <Link href="/product">
                View Public Product Pages
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {attentionData?.data && attentionData.data.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Most Attention (Depth × Time)</CardTitle>
          </CardHeader>
          <CardContent>
            <TopAttentionProducts products={attentionData.data} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}