"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DateRangePicker } from "@/components/analytics/DateRangePicker";
import { ChevronLeft, RefreshCw } from "lucide-react";
import { iBusiness, iAnalyticsPeriod } from "@/app/util/Interfaces";

interface AnalyticsHeaderProps {
  business: iBusiness;
  businesses: iBusiness[];
  selectedBusiness: string | null;
  setSelectedBusiness: (businessId: string | null) => void;
  onPeriodChange: (period: iAnalyticsPeriod) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
}

export function AnalyticsHeader({
  business,
  businesses,
  selectedBusiness,
  setSelectedBusiness,
  onPeriodChange,
  onRefresh,
  isRefreshing,
}: AnalyticsHeaderProps) {
  const router = useRouter();

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/owner-admin")}
            className="w-full sm:w-auto"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Back to dashboard</span>
            <span className="sm:hidden">Back</span>
          </Button>

          {businesses.length > 1 && (
            <Select
              value={selectedBusiness || business.id}
              onValueChange={setSelectedBusiness}
            >
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Select business" />
              </SelectTrigger>
              <SelectContent>
                {businesses.map((b) => (
                  <SelectItem key={b.id} value={b.id}>
                    {b.ownerName || `Business ${b.id.substring(0, 8)}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="flex-1 sm:flex-none">
            <DateRangePicker onRangeChange={onPeriodChange} />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={onRefresh}
            disabled={isRefreshing}
            className="flex-shrink-0"
          >
            <RefreshCw
              className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
            />
          </Button>
        </div>
      </div>

      <div className="mt-4 sm:mt-6">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight">
          {business.ownerName || "Business"} Analytics
        </h2>
        <p className="text-sm sm:text-base text-muted-foreground mt-1">
          Analyze your business performance and customer engagement
        </p>
      </div>
    </>
  );
}