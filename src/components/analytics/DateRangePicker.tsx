"use client";

import { useState, useEffect, useMemo } from "react";
import { addDays, format, subDays, subMonths, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { iAnalyticsPeriod } from "@/app/util/Interfaces";

// Define the allowed period options, including 'mtd'
type PeriodOption = "custom" | "7d" | "30d" | "90d" | "mtd" | "ytd";

interface DateRangePickerProps {
    onRangeChange: (range: iAnalyticsPeriod) => void;
    defaultPeriod?: PeriodOption; // Use the updated type here too
}

export function DateRangePicker({ onRangeChange, defaultPeriod = "30d" }: DateRangePickerProps) {
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    // Use the updated PeriodOption type for the state
    const [selectedPeriod, setSelectedPeriod] = useState<PeriodOption>(defaultPeriod);
    const [comparison, setComparison] = useState<"previous_period" | "previous_year" | "none">("previous_period");
    const [dateRange, setDateRange] = useState<{
        startDate: Date;
        endDate: Date;
        label: string;
    }>({
        startDate: subDays(new Date(), 30),
        endDate: new Date(),
        label: "Last 30 days",
    });

    // Define preset date ranges
    const presets = useMemo(() => [
        {
            id: "7d",
            label: "Last 7 days",
            startDate: subDays(new Date(), 7),
            endDate: new Date(),
        },
        {
            id: "30d",
            label: "Last 30 days",
            startDate: subDays(new Date(), 30),
            endDate: new Date(),
        },
        {
            id: "90d",
            label: "Last 90 days",
            startDate: subDays(new Date(), 90),
            endDate: new Date(),
        },
        {
            id: "mtd",
            label: "Month to date",
            startDate: startOfMonth(new Date()),
            endDate: new Date(),
        },
        {
            id: "ytd",
            label: "Year to date",
            startDate: new Date(new Date().getFullYear(), 0, 1),
            endDate: new Date(),
        },
        {
            id: "custom",
            label: "Custom range",
            startDate: subDays(new Date(), 30),
            endDate: new Date(),
        },
    ], []);

    // Update date range when preset changes
    useEffect(() => {
        const preset = presets.find((p) => p.id === selectedPeriod);
        if (preset && selectedPeriod !== "custom") {
            setDateRange({
                startDate: preset.startDate,
                endDate: preset.endDate,
                label: preset.label,
            });
        }
    }, [selectedPeriod, presets]);

    // Notify parent component when date range changes
    useEffect(() => {
        onRangeChange({
            startDate: dateRange.startDate,
            endDate: dateRange.endDate,
            comparison: comparison,
        });
    }, [dateRange, comparison, onRangeChange]);

    // Format the date range for display
    const formatDateRange = () => {
        if (selectedPeriod !== "custom") {
            return dateRange.label;
        }
        return `${format(dateRange.startDate, "LLL dd, yyyy")} - ${format(dateRange.endDate, "LLL dd, yyyy")}`;
    };

    // Handle calendar selection for custom range
    const handleCalendarSelect = (date: Date | undefined) => {
        if (!date) return;

        const newDateRange = {
            ...dateRange,
            label: "Custom range",
        };

        if (!dateRange.startDate || (dateRange.startDate && dateRange.endDate)) {
            // Start a new selection
            newDateRange.startDate = date;
            newDateRange.endDate = date;
        } else {
            // Complete the selection
            if (date < dateRange.startDate) {
                newDateRange.startDate = date;
            } else {
                newDateRange.endDate = date;
                setIsCalendarOpen(false);
            }
        }

        setDateRange(newDateRange);
        setSelectedPeriod("custom");
    };

    return (
        <div className="flex flex-col sm:flex-row gap-2">
            <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        className="justify-start text-left font-normal w-[260px]"
                    >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formatDateRange()}
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                    <div className="border-b px-3 py-2">
                        <div className="flex items-center justify-between">
                            <h3 className="text-sm font-medium">Date range</h3>
                            <div className="flex gap-1">
                                {presets.slice(0, 5).map((preset) => (
                                    <Button
                                        key={preset.id}
                                        variant={selectedPeriod === preset.id ? "default" : "outline"}
                                        size="sm"
                                        className="text-xs"
                                        onClick={() => {
                                            // Now preset.id (including 'mtd') is assignable to PeriodOption
                                            setSelectedPeriod(preset.id as PeriodOption);
                                            setIsCalendarOpen(false);
                                        }}
                                    >
                                        {preset.id.toUpperCase()}
                                    </Button>
                                ))}
                            </div>
                        </div>
                    </div>
                    <Calendar
                        mode="range"
                        selected={{
                            from: dateRange.startDate,
                            to: dateRange.endDate,
                        }}
                        onSelect={(range) => {
                            if (range?.from) {
                                handleCalendarSelect(range.from);
                            }
                            if (range?.to) {
                                handleCalendarSelect(range.to);
                            }
                        }}
                        numberOfMonths={2}
                        disabled={{ after: new Date() }}
                    />
                </PopoverContent>
            </Popover>

            <Select
                value={comparison}
                onValueChange={(value) => setComparison(value as any)}
            >
                <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Comparison" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="previous_period">Previous period</SelectItem>
                    <SelectItem value="previous_year">Previous year</SelectItem>
                    <SelectItem value="none">No comparison</SelectItem>
                </SelectContent>
            </Select>
        </div>
    );
}