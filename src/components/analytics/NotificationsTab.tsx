"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatCard } from "@/components/analytics/StatCard";
import { Bell, CheckCircle, AlertCircle, TrendingUp, Clock } from "lucide-react";
import { iBusinessAnalyticsEnhanced } from "@/app/util/Interfaces";
import { useQuery } from "@tanstack/react-query";
import { getNotificationAnalytics } from "@/app/util/analyticsService";

interface NotificationsTabProps {
  analyticsData: iBusinessAnalyticsEnhanced;
}

export function NotificationsTab({ analyticsData }: NotificationsTabProps) {
  const [timeframe, setTimeframe] = useState<"7" | "30" | "90">("30");
  
  // Fetch notification analytics data
  const { data: notificationData, isLoading } = useQuery({
    queryKey: ["notificationAnalytics", analyticsData.businessId, timeframe],
    queryFn: () => getNotificationAnalytics(analyticsData.businessId, parseInt(timeframe)),
    enabled: !!analyticsData.businessId,
    retry: 1,
  });

  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Get notification type label
  const getNotificationTypeLabel = (type: string): string => {
    const typeMap: Record<string, string> = {
      "review": "New Review",
      "comment": "New Comment",
      "reply": "Reply",
      "like": "Like",
      "claim": "Product Claim",
      "system": "System",
    };
    return typeMap[type] || type;
  };

  // Get notification type color
  const getNotificationTypeColor = (type: string): string => {
    const colorMap: Record<string, string> = {
      "review": "#3b82f6", // blue
      "comment": "#10b981", // green
      "reply": "#8b5cf6", // purple
      "like": "#ef4444", // red
      "claim": "#f59e0b", // amber
      "system": "#6b7280", // gray
    };
    return colorMap[type] || "#6b7280";
  };

  return (
    <div className="space-y-6">
      {/* Timeframe selector */}
      <Tabs 
        defaultValue="30" 
        value={timeframe}
        onValueChange={(value) => setTimeframe(value as "7" | "30" | "90")}
        className="w-full"
      >
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="7">Last 7 Days</TabsTrigger>
          <TabsTrigger value="30">Last 30 Days</TabsTrigger>
          <TabsTrigger value="90">Last 90 Days</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Overview metrics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Notifications"
          value={isLoading ? 0 : notificationData?.totalNotifications || 0}
          icon={Bell}
          description="Total notifications sent"
          loading={isLoading}
        />
        <StatCard
          title="Read Rate"
          value={isLoading ? "0%" : formatPercentage(
            notificationData?.readVsUnread.read 
              ? (notificationData.readVsUnread.read / (notificationData.readVsUnread.read + notificationData.readVsUnread.unread)) * 100 
              : 0
          )}
          icon={CheckCircle}
          description="Notifications that were read"
          loading={isLoading}
        />
        <StatCard
          title="Click-Through Rate"
          value={isLoading ? "0%" : formatPercentage(notificationData?.clickThroughRate || 0)}
          icon={TrendingUp}
          description="Notifications that were clicked"
          loading={isLoading}
        />
        <StatCard
          title="Unread Notifications"
          value={isLoading ? 0 : notificationData?.readVsUnread.unread || 0}
          icon={AlertCircle}
          description="Notifications still unread"
          loading={isLoading}
        />
      </div>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
          <CardDescription>
            Distribution of notifications by type
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-[300px] flex items-center justify-center">
              {isLoading ? (
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={(notificationData?.byType || []).map(item => ({
                        name: getNotificationTypeLabel(item.type),
                        value: item.count
                      }))}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {(notificationData?.byType || []).map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={getNotificationTypeColor(entry.type)} 
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [
                        formatNumber(value as number),
                        "Notifications",
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
            <div className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                      <div className="h-2 bg-gray-200 rounded w-full"></div>
                    </div>
                  ))}
                </div>
              ) : (
                (notificationData?.byType || []).map((item, index) => {
                  const color = getNotificationTypeColor(item.type);
                  const total = notificationData?.totalNotifications || 1;
                  const percentage = (item.count / total) * 100;
                  
                  return (
                    <div key={item.type} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">
                          {getNotificationTypeLabel(item.type)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {formatNumber(item.count)} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="h-2 rounded-full bg-muted overflow-hidden">
                        <div
                          className="h-full"
                          style={{
                            width: `${percentage}%`,
                            backgroundColor: color,
                          }}
                        ></div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notifications Over Time */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications Over Time</CardTitle>
          <CardDescription>
            Daily notification volume trends
          </CardDescription>
        </CardHeader>
        <CardContent className="aspect-[2/1] min-h-[350px] w-full p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={(notificationData?.notificationsPerDay || []).map(
                  ({ date, count }) => ({
                    date,
                    count: Number(count),
                  })
                )}
                margin={{
                  top: 10,
                  right: 10,
                  left: 0,
                  bottom: 10,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => {
                    const dateObj = new Date(date);
                    return format(dateObj, "MMM d");
                  }}
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis
                  width={40}
                  tickFormatter={(value) => formatNumber(value)}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip
                  formatter={(value) => [
                    formatNumber(value as number),
                    "Notifications",
                  ]}
                  labelFormatter={(label) =>
                    format(new Date(label as string), "MMMM d, yyyy")
                  }
                />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
