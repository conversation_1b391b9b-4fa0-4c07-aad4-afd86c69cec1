"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, <PERSON>Chart, Line } from "recharts";
import { format } from "date-fns";
import Link from "next/link";
import { Card, CardContent, CardDescription, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { iBusinessAnalyticsEnhanced } from "@/app/util/Interfaces";
import { useState, useEffect } from "react";

interface ReviewsTabProps {
  analyticsData: iBusinessAnalyticsEnhanced;
}

interface ReviewAnalyticsData {
  reviewVolume: Array<{
    createdDate: string;
    _count: { id: number };
    avgRating?: number; // Added for daily average ratings
  }>;
  ratingDistribution: Array<{
    rating: number;
    _count: { rating: number };
  }>;
  qualityMetrics: {
    _avg: { rating: number };
    _count: { id: number };
  };
}

export function ReviewsTab({ analyticsData }: ReviewsTabProps) {
  const [reviewData, setReviewData] = useState<ReviewAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchReviewAnalytics = async () => {
      try {
        const response = await fetch('/api/admin/reviews/analytics?days=14&includeDaily=true');
        const result = await response.json();
        if (result.success) {
          setReviewData(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch review analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReviewAnalytics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading review analytics...</div>
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Review Analytics</CardTitle>
          <CardDescription>Comprehensive analysis of customer feedback patterns. The rating distribution shows how customers rate your products, while sentiment trends reveal changes in customer satisfaction over time.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 p-2 sm:p-6">
          <div className="h-[250px] sm:h-[350px]">
            <h3 className="text-sm font-medium mb-4">
              Rating Distribution
            </h3>
            <p className="text-xs text-muted-foreground mb-3">
              Shows how many reviews you&apos;ve received for each star rating. Higher concentrations in 4-5 stars indicate strong customer satisfaction.
            </p>
            <ResponsiveContainer width="100%" height="90%">
              <BarChart
                data={reviewData?.ratingDistribution.map(item => ({
                  rating: `${item.rating} Star${item.rating !== 1 ? 's' : ''}`,
                  count: item._count.rating
                })).reverse() || []}
                margin={{
                  top: 20,
                  right: 10,
                  left: -15,
                  bottom: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="rating" 
                  tick={{ fontSize: window.innerWidth < 640 ? 8 : 10 }}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#f59e0b" name="Reviews" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="h-[250px] sm:h-[350px]">
            <h3 className="text-sm font-medium mb-4">
              Review Sentiment Over Time
            </h3>
            <p className="text-xs text-muted-foreground mb-3">
              Tracks average rating trends over the past 14 days. Rising trends indicate improving customer satisfaction, while declining trends may signal issues to address.
            </p>
            <ResponsiveContainer width="100%" height="90%">
              <LineChart
                data={reviewData?.reviewVolume.slice(-14).map(item => ({
                  date: item.createdDate,
                  rating: item.avgRating || reviewData.qualityMetrics._avg.rating || 0
                })) || []}
                margin={{
                  top: 20,
                  right: 10,
                  left: -15,
                  bottom: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => format(new Date(date), window.innerWidth < 640 ? "M/d" : "MMM d")}
                  tick={{ fontSize: window.innerWidth < 640 ? 8 : 10 }}
                  angle={window.innerWidth < 640 ? -45 : 0}
                  textAnchor={window.innerWidth < 640 ? "end" : "middle"}
                  height={window.innerWidth < 640 ? 60 : 30}
                />
                <YAxis domain={[0, 5]} ticks={[0, 1, 2, 3, 4, 5]} />
                <Tooltip
                  labelFormatter={(label) =>
                    format(new Date(label as string), "MMMM d, yyyy")
                  }
                />
                <Line
                  type="monotone"
                  dataKey="rating"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  dot={true}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="lg:col-span-2">
            <h3 className="text-sm font-medium mb-4">Review Volume</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Shows the daily count of reviews submitted over the past 14 days. This helps identify review submission patterns, peak activity days, and overall engagement trends.
            </p>
            <ResponsiveContainer width="100%" height={window.innerWidth < 640 ? 180 : 200}>
              <BarChart
                data={reviewData?.reviewVolume.slice(-14).map(item => ({
                  date: item.createdDate,
                  reviews: item._count.id
                })) || []}
                margin={{
                  top: 20,
                  right: 10,
                  left: -15,
                  bottom: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date) => format(new Date(date), window.innerWidth < 640 ? "M/d" : "MMM d")}
                  tick={{ fontSize: window.innerWidth < 640 ? 8 : 10 }}
                  angle={window.innerWidth < 640 ? -45 : 0}
                  textAnchor={window.innerWidth < 640 ? "end" : "middle"}
                  height={window.innerWidth < 640 ? 60 : 30}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(label) =>
                    format(new Date(label as string), "MMMM d, yyyy")
                  }
                />
                <Bar dataKey="reviews" fill="#8884d8" name="Reviews" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="ml-auto" asChild>
            <Link href="/owner-admin/reviews">
              View Detailed Review Analytics
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}