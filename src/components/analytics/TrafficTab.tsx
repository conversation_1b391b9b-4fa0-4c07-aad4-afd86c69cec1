"use client";

import { <PERSON>sponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Download } from "lucide-react";
import { iBusinessAnalyticsEnhanced, iTrafficSourceEnhanced } from "@/app/util/Interfaces";
import { TrafficTrendsChart } from "./TrafficTrendsChart";

interface TrafficTabProps {
  analyticsData: iBusinessAnalyticsEnhanced;
  trafficSourcesData: iTrafficSourceEnhanced[] | null | undefined;
  isLoadingTraffic: boolean;
  isErrorTraffic: boolean;
}

// Empty fallback for when no traffic data is available
const emptyTrafficSources: iTrafficSourceEnhanced[] = [];

export function TrafficTab({
  analyticsData,
  trafficSourcesData,
  isLoadingTraffic,
  isErrorTraffic
}: TrafficTabProps) {
  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Traffic Sources Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Sources</CardTitle>
          <CardDescription>
            Visitor counts by referral source. Compare the effectiveness of different channels like search engines, social media, direct visits, and referral sites to understand which marketing efforts drive the most traffic.
          </CardDescription>
        </CardHeader>
        <CardContent className="aspect-[4/3] sm:aspect-[3/1] min-h-[250px] sm:min-h-[350px] w-full p-2 sm:p-6">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart
              data={analyticsData.trafficSources.map(
                ({ source, count }) => ({
                  name: source,
                  value: count,
                })
              )}
              margin={{
                top: 20,
                right: 10,
                left: -15,
                bottom: 20,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: window.innerWidth < 640 ? 8 : 10 }}
                angle={window.innerWidth < 640 ? -45 : 0}
                textAnchor={window.innerWidth < 640 ? "end" : "middle"}
                height={window.innerWidth < 640 ? 60 : 30}
              />
              <YAxis tickFormatter={(value) => formatNumber(value)} />
              <Tooltip
                formatter={(value) => [
                  formatNumber(value as number),
                  "Visitors",
                ]}
              />
              <Bar dataKey="value" fill="#3b82f6" name="Visitors" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Traffic Sources Table */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Sources Detail</CardTitle>
          <CardDescription>Detailed performance metrics for each traffic source including visitor counts, session data, bounce rates, and conversion rates. Use this data to optimize your marketing strategy and focus on high-performing channels.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingTraffic ? (
            <div className="flex items-center justify-center py-16">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : isErrorTraffic ? (
            <div className="text-center py-8 text-red-500">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Error loading traffic sources data</p>
            </div>
          ) : (
            <div className="overflow-x-auto -mx-3 sm:mx-0">
              <div className="min-w-[600px] sm:w-full">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 font-medium">Source</th>
                    <th className="text-left py-3 font-medium">Medium</th>
                    <th className="text-right py-3 font-medium">
                      Visitors
                    </th>
                    <th className="text-right py-3 font-medium">
                      Sessions
                    </th>
                    <th className="text-right py-3 font-medium">
                      Bounce Rate
                    </th>
                    <th className="text-right py-3 font-medium">
                      Conv. Rate
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {(trafficSourcesData || emptyTrafficSources).map(
                    (source) => (
                      <tr key={source.id} className="border-b">
                        <td className="py-3">{source.source}</td>
                        <td className="py-3">{source.medium}</td>
                        <td className="py-3 text-right">
                          {formatNumber(source.visitors)}
                        </td>
                        <td className="py-3 text-right">
                          {formatNumber(source.sessions)}
                        </td>
                        <td className="py-3 text-right">
                          {formatPercentage(source.bounceRate)}
                        </td>
                        <td className="py-3 text-right">
                          {formatPercentage(source.conversionRate)}
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="ml-auto">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </CardFooter>
      </Card>

      {/* Traffic Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Trends</CardTitle>
          <CardDescription>
            Daily traffic breakdown by source over the last 14 days. Track which channels drive the most visitors and identify growth patterns across Google search, direct visits, social media, and other referral sources.
          </CardDescription>
        </CardHeader>
        <CardContent className="aspect-[4/3] sm:aspect-video min-h-[250px] sm:min-h-[350px] w-full p-2 sm:p-6">
          <TrafficTrendsChart 
            businessId={analyticsData.businessId} 
            startDate={new Date(analyticsData.viewsPerDay[0]?.date || Date.now() - 14 * 24 * 60 * 60 * 1000)} 
            endDate={new Date(analyticsData.viewsPerDay[analyticsData.viewsPerDay.length - 1]?.date || Date.now())} 
            formatNumber={formatNumber} 
          />
        </CardContent>
      </Card>
    </div>
  );
}