"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { format } from "date-fns";
import Link from "next/link";
import { Card, CardContent, CardDescription, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { StatCard } from "@/components/analytics/StatCard";
import { Eye, Users, Star, TrendingUp, ArrowRight, Smartphone, Monitor, Tablet, Info } from "lucide-react";
import { iBusinessAnalyticsEnhanced } from "@/app/util/Interfaces";
import { useQuery } from "@tanstack/react-query";
import { getMetricsChanges } from "@/app/util/analyticsService";

interface OverviewTabProps {
  analyticsData: iBusinessAnalyticsEnhanced;
}

export function OverviewTab({ analyticsData }: OverviewTabProps) {
  // Fetch real percentage changes for metrics
  const { data: metricsChanges, isLoading: isLoadingChanges } = useQuery({
    queryKey: ["metricsChanges", analyticsData.businessId],
    queryFn: () => getMetricsChanges(analyticsData.businessId),
    enabled: !!analyticsData.businessId,
    retry: 1, // Only retry once to avoid showing stale data
  });
  
  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US", {
      notation: num > 9999 ? "compact" : "standard",
      maximumFractionDigits: 1,
    }).format(num);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Get device icon
  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case "mobile":
        return <Smartphone className="h-4 w-4" />;
      case "desktop":
        return <Monitor className="h-4 w-4" />;
      case "tablet":
        return <Tablet className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Overview metrics */}
      <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Views"
          value={analyticsData.totalViews}
          icon={Eye}
          description="Page views across all products"
          change={isLoadingChanges ? 0 : (metricsChanges?.totalViews || 0)}
          loading={isLoadingChanges}
        />
        <StatCard
          title="Unique Visitors"
          value={analyticsData.uniqueVisitors}
          icon={Users}
          description="Individual visitors"
          change={isLoadingChanges ? 0 : (metricsChanges?.uniqueVisitors || 0)}
          loading={isLoadingChanges}
        />
        <StatCard
          title="Total Reviews"
          value={analyticsData.totalReviews}
          icon={Star}
          description="Customer reviews received"
          change={isLoadingChanges ? 0 : (metricsChanges?.totalReviews || 0)}
          loading={isLoadingChanges}
        />
        <StatCard
          title="Conversion Rate"
          value={formatPercentage(analyticsData.conversionRate)}
          icon={TrendingUp}
          description="Unique visitors resulting in reviews"
          change={isLoadingChanges ? 0 : (metricsChanges?.conversionRate || 0)}
          loading={isLoadingChanges}
        />
      </div>

      {/* Views Chart */}
      <Card>
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-base sm:text-lg">Views Over Time</CardTitle>
          <CardDescription className="text-sm">Daily page view trends across all your products. Monitor traffic patterns, identify peak days, and track overall growth.</CardDescription>
        </CardHeader>
        <CardContent className="aspect-[4/3] sm:aspect-[3/1] min-h-[250px] sm:min-h-[350px] w-full p-2 sm:p-6">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={analyticsData.viewsPerDay.map(
                ({ date, views }) => ({
                  date: date,
                  views: views,
                })
              )}
              margin={{
                top: 10,
                right: 5,
                left: -15,
                bottom: 10,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickFormatter={(date) => {
                  const dateObj = new Date(date);
                  return format(dateObj, window.innerWidth < 640 ? "M/d" : "MMM d");
                }}
                tick={{ fontSize: 10 }}
                interval={window.innerWidth < 640 ? "preserveEnd" : "preserveStartEnd"}
                minTickGap={15}
              />
              <YAxis
                width={40}
                tickFormatter={(value) => formatNumber(value)}
                tick={{ fontSize: 10 }}
              />
              <Tooltip
                formatter={(value) => [
                  formatNumber(value as number),
                  "Views",
                ]}
                labelFormatter={(label) =>
                  format(new Date(label as string), "MMMM d, yyyy")
                }
              />
              <Line
                type="monotone"
                dataKey="views"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
        <CardFooter className="flex-col sm:flex-row justify-between gap-3 sm:gap-0 pt-3 sm:pt-6">
          <div className="text-sm text-muted-foreground text-center sm:text-left">
            {formatNumber(
              analyticsData.viewsPerDay.reduce(
                (sum, { views }) => sum + views,
                0
              )
            )}{" "}
            total views in this period
          </div>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            View Details
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </CardFooter>
      </Card>

      {/* Device Breakdown */}
      <Card>
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-base sm:text-lg">Device Breakdown</CardTitle>
          <CardDescription className="text-sm">Visitor distribution across desktop, mobile, and tablet devices.</CardDescription>
        </CardHeader>
        <CardContent className="p-3 sm:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
            <div className="h-[200px] sm:h-[300px] flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analyticsData.deviceTypes.map(
                      ({ type, count }) => ({
                        name: type,
                        value: count,
                      })
                    )}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {analyticsData.deviceTypes.map(
                      ({ type }, index) => {
                        const COLORS = ["#3b82f6", "#f97316", "#10b981"];
                        return (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        );
                      }
                    )}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [
                      formatNumber(value as number),
                      "Views",
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="space-y-3 sm:space-y-4">
              {analyticsData.deviceTypes.map(
                ({ type, count }, index) => {
                  const COLORS = ["#3b82f6", "#f97316", "#10b981"];
                  return (
                    <div key={type} className="flex items-center">
                      <div className="w-12 sm:w-16 flex items-center">
                        <div
                          className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center"
                          style={{
                            backgroundColor:
                              COLORS[index % COLORS.length] + "20",
                          }}
                        >
                          {getDeviceIcon(type)}
                        </div>
                      </div>
                      <div className="flex-1 ml-2 sm:ml-4">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs sm:text-sm font-medium">
                            {type}
                          </span>
                          <span className="text-xs sm:text-sm text-muted-foreground">
                            {formatNumber(count)} (
                            {(
                              (count / analyticsData.totalViews) *
                              100
                            ).toFixed(1)}
                            %)
                          </span>
                        </div>
                        <div className="h-1.5 sm:h-2 rounded-full bg-muted overflow-hidden">
                          <div
                            className="h-full"
                            style={{
                              width: `${(count / analyticsData.totalViews) * 100}%`,
                              backgroundColor:
                                COLORS[index % COLORS.length],
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  );
                }
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Products */}
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0 pb-3 sm:pb-6">
          <div>
            <CardTitle className="text-base sm:text-lg">Top Performing Products</CardTitle>
            <CardDescription className="text-sm">
              Products with the most views and engagement
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            See All Products
          </Button>
        </CardHeader>
        <CardContent className="p-3 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            {analyticsData.topProducts.map((product, index) => (
              <div key={product.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1 min-w-0">
                    <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-muted flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                      <span className="text-xs font-medium">
                        {index + 1}
                      </span>
                    </div>
                    <span className="font-medium text-sm sm:text-base truncate">{product.name}</span>
                  </div>
                  <Button variant="ghost" size="sm" asChild className="ml-2 flex-shrink-0">
                    <Link href={`/product/${product.id}`}>
                      View
                    </Link>
                  </Button>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between text-xs sm:text-sm gap-2 sm:gap-0">
                  <div className="flex space-x-3 sm:space-x-4">
                    <div className="flex items-center space-x-1 text-muted-foreground">
                      <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span>{formatNumber(product.views)}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-muted-foreground">
                      <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span>{formatPercentage(product.conversion)}</span>
                    </div>
                  </div>
                  <div className="text-muted-foreground">
                    {formatNumber(
                      product.views * (product.conversion / 100)
                    )}{" "}
                    conversions
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}