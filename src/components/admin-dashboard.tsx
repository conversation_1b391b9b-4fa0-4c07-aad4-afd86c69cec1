"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getPendingReviews,
  getApprovedReviews,
  getRejectedReviews
} from "@/app/util/adminFunctions";

import { StatsOverview } from "@/components/admin/stats-overview";
import { QuickActions } from "@/components/admin/quick-actions";
import { RecentActivity } from "@/components/admin/recent-activity";
import LoadingSpinner from "@/app/components/LoadingSpinner";

export default function AdminDashboard() {
  const {
    data: pendingReviewsData,
    isLoading: isPendingLoading,
    isError: isPendingError
  } = useQuery({
    queryKey: ["pendingReviews"],
    queryFn: getPendingReviews,
  });

  // Fetch approved reviews count only
  const {
    data: approvedReviewsData,
    isLoading: isApprovedLoading,
    isError: isApprovedError
  } = useQuery({
    queryKey: ["approvedReviews"],
    queryFn: getApprovedReviews,
  });

  // Fetch rejected reviews count only
  const {
    data: rejectedReviewsData,
    isLoading: isRejectedLoading,
    isError: isRejectedError
  } = useQuery({
    queryKey: ["rejectedReviews"],
    queryFn: getRejectedReviews,
  });

  const pendingCount = pendingReviewsData?.count || 0;
  const approvedCount = approvedReviewsData?.count || 0;
  const rejectedCount = rejectedReviewsData?.count || 0;

  const isLoading = isPendingLoading || isApprovedLoading || isRejectedLoading;
  const isError = isPendingError || isApprovedError || isRejectedError;

  if (isError) {
    return (
      <div className="p-4 text-red-500">
        <h2 className="text-xl font-bold">Error Loading Dashboard</h2>
        <p>There was an error loading the admin dashboard. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="px-4 text-3xl font-bold">Admin Dashboard</h1>

      <StatsOverview
        pendingCount={pendingCount}
        approvedCount={approvedCount}
        rejectedCount={rejectedCount}
        isLoading={isLoading}
      />

      <QuickActions />

      <RecentActivity />
    </div>
  );
} 
