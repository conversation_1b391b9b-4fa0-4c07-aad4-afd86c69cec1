"use client";

import React, { useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { Edit, Settings, Crown, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { iProduct } from "@/app/util/Interfaces";
import { isProductOwner } from "@/app/util/clientFunctions";
import { toast } from "sonner";

/**
 * ProductEditButton Component
 *
 * Renders an edit button for product owners to modify their product listings.
 * Only displays for authenticated users who own the product (either as creator or business owner).
 *
 * Features:
 * - Automatic ownership detection using user ID comparison
 * - Loading state with spinner during navigation
 * - Error handling with toast notifications
 * - Customizable styling and variants
 * - Optional owner badge for business owners
 * - Responsive design support
 */

interface ProductEditButtonProps {
  /** The product object containing ownership and ID information */
  product: iProduct;
  /** Button style variant (default: "outline") */
  variant?: "default" | "outline" | "ghost";
  /** Button size (default: "default") */
  size?: "default" | "sm" | "lg";
  /** Additional CSS classes to apply */
  className?: string;
  /** Whether to show a crown badge for business owners */
  showOwnerBadge?: boolean;
}

const ProductEditButton: React.FC<ProductEditButtonProps> = ({
  product,
  variant = "outline",
  size = "default",
  className = "",
  showOwnerBadge = false,
}) => {
  const { userId, isLoaded } = useAuth();
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  // Safety check: ensure product exists and has required data
  if (!product || !product.id) {
    return null;
  }

  // Don't render if user is not authenticated or auth is not loaded
  if (!isLoaded || !userId) {
    return null;
  }

  // Check if user owns the product using the utility function
  // This checks both direct ownership (createdById) and business ownership
  const isOwner = isProductOwner(userId, product);

  // Don't render if user doesn't own the product
  if (!isOwner) {
    return null;
  }

  /**
   * Handles the edit button click event
   * Navigates to the edit product page with the product ID as a query parameter
   */
  const handleEditClick = async () => {
    // Double-check product ID exists (redundant safety check)
    if (!product?.id) {
      console.error("ProductEditButton: Product ID is missing");
      toast.error("Unable to edit product - invalid product data");
      return;
    }

    try {
      setIsNavigating(true);
      // Navigate to the existing edit product page
      router.push(`/editproduct?pid=${product.id}`);
    } catch (error) {
      console.error("ProductEditButton navigation error:", error);
      toast.error("Failed to navigate to edit page");
      setIsNavigating(false);
    }
  };

  // Determine the type of ownership for better UX
  const isBusinessOwner = product.business?.ownerId === userId;
  const isProductCreator = product.createdById === userId;

  // Final safety check before render
  if (!product.id) {
    console.warn(
      "ProductEditButton: Attempting to render without valid product ID",
    );
    return null;
  }

  return (
    <Button
      onClick={handleEditClick}
      variant={variant}
      size={size}
      disabled={isNavigating || !product.id}
      className={`flex items-center gap-2 transition-all duration-200 hover:shadow-md ${className}`}
      title={
        isBusinessOwner ? "Edit your business listing" : "Edit product details"
      }
    >
      {isNavigating ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Edit className="h-4 w-4" />
      )}
      <span>{isNavigating ? "Opening..." : "Edit Product"}</span>
      {showOwnerBadge && isBusinessOwner && !isNavigating && (
        <Crown className="h-3 w-3 text-yellow-600" />
      )}
    </Button>
  );
};

export default ProductEditButton;
