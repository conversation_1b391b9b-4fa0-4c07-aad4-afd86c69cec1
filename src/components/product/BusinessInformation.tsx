import React from 'react';
import { BadgeCheck } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { iProduct } from '@/app/util/Interfaces';

interface BusinessInformationProps {
    product: iProduct;
}

const BusinessInformation: React.FC<BusinessInformationProps> = ({ product }) => {
    if (!product.business) return null;

    return (
        <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">About the Business</h2>
            <div className="flex items-start gap-4">
                <Avatar className="h-12 w-12">
                    <AvatarImage
                        src={product.business.owner?.avatar || ""}
                        alt={product.business.ownerName || `${product.business.owner?.firstName} ${product.business.owner?.lastName}`}
                    />
                    <AvatarFallback>
                        {product.business.ownerName?.charAt(0) ||
                            product.business.owner?.firstName?.charAt(0)}
                    </AvatarFallback>
                </Avatar>
                <div>
                    <h3 className="font-medium">
                        {product.business.ownerName ||
                            `${product.business.owner?.firstName} ${product.business.owner?.lastName}`}
                    </h3>
                    {product.business.businessDescription && (
                        <p className="text-gray-700 mt-2">
                            {product.business.businessDescription}
                        </p>
                    )}
                    {product.business.isVerified === true && (
                        <div className="mt-2 flex items-center gap-1 text-blue-600">
                            <BadgeCheck className="h-4 w-4" aria-hidden="true" />
                            <span className="text-sm">Verified Business</span>
                        </div>
                    )}
                    {product.business.isVerified === false && (
                        <div className="mt-2 flex items-center gap-1 text-gray-500">
                            <span className="text-sm">Unverified Business</span>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};

export default BusinessInformation; 