import React, { useState } from 'react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Star, SlidersHorizontal, X } from 'lucide-react';
import {
    Sheet,
    She<PERSON><PERSON>ontent,
    She<PERSON><PERSON>eader,
    She<PERSON><PERSON><PERSON><PERSON>,
    She<PERSON><PERSON>rigger,
    SheetFooter,
    SheetClose
} from '@/components/ui/sheet';

export type SortOption = 'newest' | 'oldest' | 'highest' | 'lowest' | 'most-helpful';
export type RatingFilter = 0 | 1 | 2 | 3 | 4 | 5;

interface ReviewFilterProps {
    onSortChange: (sort: SortOption) => void;
    onRatingFilterChange: (rating: RatingFilter) => void;
    onHasImagesChange: (hasImages: boolean) => void;
    onHasVideosChange: (hasVideos: boolean) => void;
    selectedSort?: SortOption;
    selectedRating?: RatingFilter;
    hasImages?: boolean;
    hasVideos?: boolean;
    totalReviews: number;
}

const ReviewFilter: React.FC<ReviewFilterProps> = ({
    onSortChange,
    onRatingFilterChange,
    onHasImagesChange,
    onHasVideosChange,
    selectedSort = 'newest',
    selectedRating = 0,
    hasImages = false,
    hasVideos = false,
    totalReviews
}) => {
    // Local state for mobile filter sheet
    const [localSort, setLocalSort] = useState<SortOption>(selectedSort);
    const [localRating, setLocalRating] = useState<RatingFilter>(selectedRating);
    const [localHasImages, setLocalHasImages] = useState<boolean>(hasImages);
    const [localHasVideos, setLocalHasVideos] = useState<boolean>(hasVideos);

    // Handle apply filters in mobile view
    const handleApplyFilters = () => {
        onSortChange(localSort);
        onRatingFilterChange(localRating);
        onHasImagesChange(localHasImages);
        onHasVideosChange(localHasVideos);
    };

    // Handle reset filters
    const handleResetFilters = () => {
        setLocalSort('newest');
        setLocalRating(0);
        setLocalHasImages(false);
        setLocalHasVideos(false);

        // Apply immediately
        onSortChange('newest');
        onRatingFilterChange(0);
        onHasImagesChange(false);
        onHasVideosChange(false);
    };

    return (
        <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
            <div className="flex items-center gap-2">
                <h3 className="text-base font-medium">
                    {totalReviews} {totalReviews === 1 ? 'Review' : 'Reviews'}
                </h3>

                {/* Desktop: Active filter tags */}
                <div className="hidden md:flex flex-wrap gap-2">
                    {selectedRating > 0 && (
                        <div className="inline-flex items-center gap-1.5 bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            {selectedRating} <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" /> & Up
                            <button
                                onClick={() => onRatingFilterChange(0)}
                                className="ml-1 text-gray-500 hover:text-gray-700"
                            >
                                <X className="h-3 w-3" />
                            </button>
                        </div>
                    )}

                    {hasImages && (
                        <div className="inline-flex items-center gap-1.5 bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            With Images
                            <button
                                onClick={() => onHasImagesChange(false)}
                                className="ml-1 text-gray-500 hover:text-gray-700"
                            >
                                <X className="h-3 w-3" />
                            </button>
                        </div>
                    )}

                    {hasVideos && (
                        <div className="inline-flex items-center gap-1.5 bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            With Videos
                            <button
                                onClick={() => onHasVideosChange(false)}
                                className="ml-1 text-gray-500 hover:text-gray-700"
                            >
                                <X className="h-3 w-3" />
                            </button>
                        </div>
                    )}
                </div>
            </div>

            <div className="flex items-center gap-2">
                {/* Desktop: Sort dropdown */}
                <div className="hidden md:block w-[180px]">
                    <Select value={selectedSort} onValueChange={(value) => onSortChange(value as SortOption)}>
                        <SelectTrigger>
                            <SelectValue placeholder="Sort by" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="newest">Newest first</SelectItem>
                            <SelectItem value="oldest">Oldest first</SelectItem>
                            <SelectItem value="highest">Highest rated</SelectItem>
                            <SelectItem value="lowest">Lowest rated</SelectItem>
                            <SelectItem value="most-helpful">Most helpful</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* Desktop: Star rating filter */}
                <div className="hidden md:flex items-center gap-4 ml-2">
                    <div className="flex items-center space-x-1">
                        {[5, 4, 3, 2, 1].map((rating) => (
                            <button
                                key={rating}
                                onClick={() => onRatingFilterChange(rating as RatingFilter)}
                                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm ${selectedRating === rating
                                    ? 'bg-yellow-400 text-white'
                                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                                    }`}
                            >
                                {rating}
                            </button>
                        ))}
                    </div>

                    {(selectedRating > 0 || hasImages || hasVideos) && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleResetFilters}
                            className="text-xs h-8"
                        >
                            Clear
                        </Button>
                    )}
                </div>

                {/* Mobile: Filter button with sheet */}
                <div className="md:hidden flex items-center justify-between w-full">
                    <Select value={selectedSort} onValueChange={(value) => onSortChange(value as SortOption)}>
                        <SelectTrigger className="w-[140px]">
                            <SelectValue placeholder="Sort by" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="newest">Newest first</SelectItem>
                            <SelectItem value="oldest">Oldest first</SelectItem>
                            <SelectItem value="highest">Highest rated</SelectItem>
                            <SelectItem value="lowest">Lowest rated</SelectItem>
                            <SelectItem value="most-helpful">Most helpful</SelectItem>
                        </SelectContent>
                    </Select>

                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="outline" size="sm" className="gap-2">
                                <SlidersHorizontal className="h-4 w-4" />
                                Filter
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="bottom" className="h-[80vh]">
                            <SheetHeader>
                                <SheetTitle>Filter Reviews</SheetTitle>
                            </SheetHeader>

                            <div className="py-6 space-y-6">
                                {/* Rating filter */}
                                <div className="space-y-3">
                                    <h3 className="text-sm font-medium">Minimum Rating</h3>
                                    <div className="flex justify-between items-center">
                                        {[0, 1, 2, 3, 4, 5].map((rating) => (
                                            <button
                                                key={rating}
                                                onClick={() => setLocalRating(rating as RatingFilter)}
                                                className={`flex items-center justify-center w-10 h-10 rounded-full text-sm ${localRating === rating
                                                    ? 'bg-yellow-400 text-white'
                                                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                                                    }`}
                                            >
                                                {rating === 0 ? 'All' : rating}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* Media filters */}
                                <div className="space-y-3">
                                    <h3 className="text-sm font-medium">Media Filters</h3>
                                    <div className="flex flex-col gap-2">
                                        <button
                                            onClick={() => setLocalHasImages(!localHasImages)}
                                            className={`flex items-center justify-center px-4 py-2 rounded-md text-sm ${localHasImages
                                                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                                                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-transparent'
                                                }`}
                                        >
                                            Show only reviews with images
                                        </button>

                                        <button
                                            onClick={() => setLocalHasVideos(!localHasVideos)}
                                            className={`flex items-center justify-center px-4 py-2 rounded-md text-sm ${localHasVideos
                                                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                                                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-transparent'
                                                }`}
                                        >
                                            Show only reviews with videos
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <SheetFooter className="flex-row space-x-2 sm:space-x-0">
                                <SheetClose asChild>
                                    <Button variant="outline" className="w-full">Cancel</Button>
                                </SheetClose>

                                <Button
                                    variant="default"
                                    className="w-full"
                                    onClick={handleApplyFilters}
                                >
                                    <SheetClose>Apply Filters</SheetClose>
                                </Button>

                                <Button
                                    variant="ghost"
                                    className="absolute left-4 bottom-4"
                                    onClick={handleResetFilters}
                                >
                                    Reset Filters
                                </Button>
                            </SheetFooter>
                        </SheetContent>
                    </Sheet>
                </div>
            </div>
        </div>
    );
};

export default ReviewFilter; 