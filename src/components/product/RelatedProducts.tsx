import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { iProduct } from '@/app/util/Interfaces';
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';

interface RelatedProductsProps {
    product: iProduct;
}

const RelatedProducts: React.FC<RelatedProductsProps> = ({ product }) => {
    if (!product.relatedProducts || product.relatedProducts.length === 0) {
        return null;
    }

    return (
        <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Related Products</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {product.relatedProducts.map((relatedProduct) => (
                    <Link
                        key={relatedProduct.id}
                        href={`/product/${relatedProduct.id}`}
                        className="block group"
                    >
                        <div className="flex items-start gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-gray-50 border border-transparent hover:border-gray-200">
                            <div className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-md overflow-hidden flex-shrink-0">
                                <Image
                                    src={relatedProduct.display_image}
                                    alt={relatedProduct.name}
                                    fill
                                    className="object-cover"
                                    loading="lazy"
                                    sizes="(max-width: 768px) 64px, 80px"
                                />
                            </div>
                            <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-sm sm:text-base line-clamp-1 group-hover:text-blue-600 transition-colors">
                                    {relatedProduct.name}
                                </h3>
                                <p className="text-gray-500 text-xs sm:text-sm line-clamp-2 mt-1">
                                    {relatedProduct.description}
                                </p>
                                <div className="mt-2">
                                    <RatingDisplayWithThreshold
                                        ratingData={calculateWeightedRating(relatedProduct.reviews || [], {
                                            globalAverageRating: relatedProduct.rating
                                        })}
                                        size="xs"
                                        showReviewCount={true}
                                        showConfidence={false}
                                        minimumReviewsMessage="No ratings yet"
                                        className="text-xs"
                                    />
                                </div>
                            </div>
                        </div>
                    </Link>
                ))}
            </div>
        </section>
    );
};

export default RelatedProducts;