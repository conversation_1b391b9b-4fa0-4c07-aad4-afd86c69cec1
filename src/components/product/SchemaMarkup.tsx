import React from 'react';
import { iProduct } from '@/app/util/Interfaces';

interface SchemaMarkupProps {
    product: iProduct;
}

const SchemaMarkup: React.FC<SchemaMarkupProps> = ({ product }) => {
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "Product",
        name: product.name,
        description: product.description,
        image: product.display_image,
        aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: Math.max(1, Math.min(5, product.rating || 0)),
            reviewCount: Math.max(1, product._count?.reviews || 1),
            bestRating: "5",
            worstRating: "1"
        },
        offers: {
            "@type": "Offer",
            availability: "https://schema.org/InStock",
            price: "0",
            priceCurrency: "GYD",
            hasMerchantReturnPolicy: {
                "@type": "MerchantReturnPolicy",
                applicableCountry: "GY",
                returnPolicyCategory: "https://schema.org/MerchantReturnFiniteReturnWindow",
                merchantReturnDays: 30,
                returnMethod: "https://schema.org/ReturnByMail",
                returnFees: "https://schema.org/FreeReturn"
            },
            shippingDetails: {
                "@type": "OfferShippingDetails",
                shippingRate: {
                    "@type": "MonetaryAmount",
                    value: "0",
                    currency: "GYD"
                },
                shippingDestination: {
                    "@type": "DefinedRegion",
                    addressCountry: "GY"
                },
                deliveryTime: {
                    "@type": "ShippingDeliveryTime",
                    handlingTime: {
                        "@type": "QuantitativeValue",
                        minValue: 0,
                        maxValue: 1,
                        unitCode: "DAY"
                    },
                    transitTime: {
                        "@type": "QuantitativeValue",
                        minValue: 1,
                        maxValue: 3,
                        unitCode: "DAY"
                    }
                }
            }
        },
        brand: {
            "@type": "Brand",
            name: product.business?.ownerName || "Review It"
        },
        review: product.reviews?.slice(0, 3).map(review => ({
            "@type": "Review",
            reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
                worstRating: "1"
            },
            author: {
                "@type": "Person",
                name: `${review.user?.firstName} ${review.user?.lastName}`
            },
            datePublished: review.createdDate,
            reviewBody: review.body
        }))
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify(schemaData)
            }}
        />
    );
};

export default SchemaMarkup; 