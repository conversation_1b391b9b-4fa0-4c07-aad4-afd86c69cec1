import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, CheckCircle, Building2, Users, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface TrustBadgeProps {
    product: any;
}

const TrustBadge: React.FC<TrustBadgeProps> = ({ product }) => {
    // Check if the product is verified
    const isVerified = product.hasOwner && product.business?.isVerified === true;
    const isClaimedAndUnverified = product.hasOwner && product.business?.isVerified === false;

    return (
        <Card className="w-full border-2 border-blue-100 shadow-sm">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-semibold text-blue-800 flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-blue-600" />
                        Trusted by ReviewIt
                    </CardTitle>
                    {isVerified && (
                        <Badge className="bg-blue-500 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified
                        </Badge>
                    )}
                    {isClaimedAndUnverified && (
                        <span className="text-xs text-gray-400 font-normal px-2 py-1 rounded-full bg-gray-100">
                            Unverified
                        </span>
                    )}
                </div>
            </CardHeader>
            <CardContent className="p-4 space-y-4">
                <div className="space-y-3">
                    <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                            <CheckCircle className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                            <h3 className="font-medium text-blue-800">Verified Products</h3>
                            <p className="text-sm text-gray-600">
                                When you see the verified badge, it means we have directly contacted and verified this business.
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                            <Building2 className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                            <h3 className="font-medium text-blue-800">Claim Your Business</h3>
                            <p className="text-sm text-gray-600">
                                Business owners can claim their listings to manage reviews, respond to customers, and showcase their products.
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                            <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                            <h3 className="font-medium text-blue-800">Trusted Reviews</h3>
                            <p className="text-sm text-gray-600">
                                Our verification process ensures that reviews come from real customers who have experienced the product.
                            </p>
                        </div>
                    </div>
                </div>

                <div className="pt-3 border-t border-gray-100">
                    {isVerified ? (
                        <div className="text-center">
                            <p className="text-sm text-gray-600 mb-3">
                                This business has been verified by our team. You can trust the reviews and information provided.
                            </p>
                            <Link href="/about/verification">
                                <Button variant="outline" className="w-full text-blue-600 border-blue-200 hover:bg-blue-50">
                                    Learn about our verification process
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    ) : isClaimedAndUnverified ? (
                        <div className="text-center">
                            <p className="text-sm text-gray-600 mb-3">
                                This business is claimed but has not completed our verification process.
                            </p>
                            <Link href="/about/verification">
                                <Button variant="outline" className="w-full text-blue-600 border-blue-200 hover:bg-blue-50">
                                    Learn about verification
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    ) : (
                        <div className="text-center">
                            <p className="text-sm text-gray-600 mb-3">
                                Is this your business? Claim it to manage reviews and showcase your products.
                            </p>
                            <Link href="/claim-business">
                                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                                    Claim this business
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default TrustBadge; 