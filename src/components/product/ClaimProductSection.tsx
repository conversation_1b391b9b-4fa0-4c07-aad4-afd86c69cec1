'use client'

import React from 'react';
import { Button } from '@/components/ui/button';

interface ClaimProductSectionProps {
    productId: string;
}

const ClaimProductSection: React.FC<ClaimProductSectionProps> = ({ productId }) => {
    const handleClaimClick = () => {
        window.open(`/claim-product/${productId}`, '_blank');
    };

    return (
        <div className="mt-8 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold mb-4">Own this business?</h3>
            <Button
                variant="default"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                onClick={handleClaimClick}
            >
                Claim This Product
            </Button>
            <p className="text-sm text-gray-500 mt-2">
                Verify ownership to manage this listing and respond to reviews
            </p>
        </div>
    );
};

export default ClaimProductSection;