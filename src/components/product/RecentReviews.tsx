import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { iProduct } from "@/app/util/Interfaces";
import ReviewCard from "@/app/components/ReviewCard";

interface RecentReviewsProps {
  product: iProduct;
}

const RecentReviews: React.FC<RecentReviewsProps> = ({ product }) => {
  return (
    <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Recent Reviews</h2>
        <Link
          href={`/reviews?id=${product.id}`}
          className="text-blue-600 hover:underline"
        >
          See all reviews
        </Link>
      </div>

      {product.reviews && product.reviews.length > 0 ? (
        <div className="space-y-4">
          {product.reviews.slice(0, 3).map((review) => (
            <ReviewCard
              key={review.id}
              review={review}
              showFullContent={false}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No reviews yet. Be the first to review this product!</p>
          <Link href={`/cr/?id=${product.id}&rating=3`}>
            <Button className="mt-4">Write a Review</Button>
          </Link>
        </div>
      )}
    </section>
  );
};

export default RecentReviews;
