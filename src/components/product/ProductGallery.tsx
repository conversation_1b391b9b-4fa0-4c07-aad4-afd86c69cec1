import React, { useState } from 'react';
import { iProduct } from '@/app/util/Interfaces';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface ProductGalleryProps {
    product: iProduct;
}

const ProductGallery: React.FC<ProductGalleryProps> = ({ product }) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);

    const openLightbox = (image: string) => {
        setSelectedImage(image);
    };

    const closeLightbox = () => {
        setSelectedImage(null);
    };

    return (
        <section className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Gallery</h2>

            {/* Images */}
            {product.images && product.images.length > 0 && (
                <div className="mb-6">
                    <h3 className="text-lg font-medium mb-3">Images</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {product.images.map((image, i) => (
                            <div
                                key={i}
                                className="aspect-square rounded-lg overflow-hidden bg-gray-100 cursor-pointer"
                                onClick={() => openLightbox(image)}
                            >
                                {/* Replace Next.js Image with regular img tag */}
                                <img
                                    src={image}
                                    alt={`${product.name} - image ${i + 1}`}
                                    className="object-cover w-full h-full"
                                    loading="lazy"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Videos */}
            {product.videos && product.videos.length > 0 && (
                <div>
                    <h3 className="text-lg font-medium mb-3">Videos</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {product.videos.map((video, i) => (
                            <div
                                key={i}
                                className="aspect-video rounded-lg overflow-hidden bg-gray-100"
                            >
                                <video
                                    src={video}
                                    controls
                                    className="w-full h-full"
                                    poster={product.display_image}
                                    aria-label={`${product.name} - video ${i + 1}`}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Lightbox */}
            <Dialog open={!!selectedImage} onOpenChange={(open) => !open && closeLightbox()}>
                <DialogContent className="max-w-4xl p-0">
                    {selectedImage && (
                        <div className="relative aspect-square w-full">
                            {/* Replace Next.js Image with regular img tag in lightbox too */}
                            <img
                                src={selectedImage}
                                alt={`${product.name} - full size`}
                                className="object-contain w-full h-full"
                            />
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </section>
    );
};

export default ProductGallery;