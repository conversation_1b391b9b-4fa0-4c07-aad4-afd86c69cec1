import React from 'react';
import Image from 'next/image';
import { iProduct, iPromotion } from '@/app/util/Interfaces';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Tag, Copy, CheckCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

interface ProductPromotionsProps {
    product: iProduct;
}

// Function to fetch active promotions for a product
const fetchActivePromotions = async (productId: string): Promise<iPromotion[]> => {
    const response = await fetch(`/api/promotions?productId=${productId}&activeOnly=true`);
    if (!response.ok) {
        throw new Error('Failed to fetch promotions');
    }
    const data = await response.json();
    return data.success ? data.data : [];
};

// Function to track promotion view
const trackPromotionView = async (promotionId: string) => {
    try {
        await fetch(`/api/promotions/${promotionId}/track`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'view' }),
        });
    } catch (error) {
        console.warn('Failed to track promotion view:', error);
    }
};

// Function to track promotion click
const trackPromotionClick = async (promotionId: string) => {
    try {
        await fetch(`/api/promotions/${promotionId}/track`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'click' }),
        });
    } catch (error) {
        console.warn('Failed to track promotion click:', error);
    }
};

const ProductPromotions: React.FC<ProductPromotionsProps> = ({ product }) => {
    // State for tracking copied codes (must be at the top)
    const [copiedCodes, setCopiedCodes] = React.useState<Set<string>>(new Set());

    // Fetch active promotions for this product
    const { data: promotions = [], isLoading, error } = useQuery({
        queryKey: ['promotions', product.id],
        queryFn: () => fetchActivePromotions(product.id!),
        staleTime: 5 * 60 * 1000, // 5 minutes
        enabled: !!product.id,
    });

    // Track promotion views when component mounts and promotions are loaded
    React.useEffect(() => {
        if (promotions.length > 0) {
            promotions.forEach(promotion => {
                if (promotion.id) {
                    trackPromotionView(promotion.id);
                }
            });
        }
    }, [promotions]);

    // Show loading state
    if (isLoading) {
        return (
            <Card className="w-full overflow-hidden border-2 border-amber-400 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-amber-400 to-yellow-500 p-4">
                    <CardTitle className="text-xl font-bold text-white flex items-center">
                        <Tag className="h-5 w-5 mr-2" />
                        Current Promotions
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                    <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    // Don't render if there are no promotions or there's an error
    if (error || !promotions || promotions.length === 0) {
        return null;
    }

    // Helper function to get discount text
    const getDiscountText = (promotion: iPromotion) => {
        if (promotion.discountPercentage) {
            return `${promotion.discountPercentage}% OFF`;
        }
        if (promotion.discountAmount) {
            return `$${promotion.discountAmount} OFF`;
        }
        if (promotion.promotionCode) {
            return promotion.promotionCode;
        }
        return 'SPECIAL OFFER';
    };



    // Handle promotion click
    const handlePromotionClick = (promotion: iPromotion) => {
        if (promotion.id) {
            trackPromotionClick(promotion.id);
        }
        
        // If there's a promotion code, copy it to clipboard
        if (promotion.promotionCode) {
            copyPromotionCode(promotion.promotionCode);
        } else {
            // For promotions without codes, show a helpful message
            alert(`${promotion.title}: ${promotion.description}\n\nThis promotion is automatically applied - no code needed!`);
        }
    };

    // Copy promotion code to clipboard
    const copyPromotionCode = async (code: string) => {
        try {
            await navigator.clipboard.writeText(code);
            setCopiedCodes(prev => new Set(prev).add(code));
            
            // Reset the copied state after 3 seconds
            setTimeout(() => {
                setCopiedCodes(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(code);
                    return newSet;
                });
            }, 3000);
        } catch (error) {
            console.warn('Failed to copy promotion code:', error);
            // Fallback: show alert with the code
            alert(`Promotion Code: ${code}`);
        }
    };

    return (
        <Card className="w-full overflow-hidden border-2 border-amber-400 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-amber-400 to-yellow-500 p-4">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold text-white flex items-center">
                        <Tag className="h-5 w-5 mr-2" />
                        Current Promotions
                    </CardTitle>
                    <Badge variant="outline" className="bg-white text-amber-600 border-amber-600">
                        {promotions.length} Active
                    </Badge>
                </div>
            </CardHeader>
            <CardContent className="p-0">
                <div className="divide-y divide-gray-200">
                    {promotions.map((promotion) => (
                        <div 
                            key={promotion.id} 
                            className="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                            onClick={() => handlePromotionClick(promotion)}
                        >
                            <div className="flex gap-4">
                                <div className="relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                                    {promotion.image ? (
                                        <Image
                                            src={promotion.image}
                                            alt={promotion.title}
                                            fill
                                            className="object-cover"
                                            sizes="96px"
                                        />
                                    ) : (
                                        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-amber-100 to-yellow-200">
                                            <span className="text-amber-800 font-bold text-xs text-center px-2">
                                                {getDiscountText(promotion)}
                                            </span>
                                        </div>
                                    )}
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-start justify-between">
                                        <h3 className="font-semibold text-lg">{promotion.title}</h3>
                                        <Badge className="bg-amber-500 text-white">
                                            {getDiscountText(promotion)}
                                        </Badge>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">{promotion.description}</p>
                                    <div className="flex items-center text-xs text-gray-500 mt-2">
                                        <Calendar className="h-3 w-3 mr-1" />
                                        <span>Valid until {new Date(promotion.endDate).toLocaleDateString()}</span>
                                    </div>
                                    {promotion.promotionCode && (
                                        <div className="mt-2 flex items-center gap-2">
                                            <span className="text-xs font-medium text-amber-700 bg-amber-100 px-2 py-1 rounded">
                                                Code: {promotion.promotionCode}
                                            </span>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                className="h-6 px-2 text-xs"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    copyPromotionCode(promotion.promotionCode!);
                                                }}
                                            >
                                                {copiedCodes.has(promotion.promotionCode) ? (
                                                    <>
                                                        <CheckCircle className="h-3 w-3 mr-1" />
                                                        Copied!
                                                    </>
                                                ) : (
                                                    <>
                                                        <Copy className="h-3 w-3 mr-1" />
                                                        Copy
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};

export default ProductPromotions;