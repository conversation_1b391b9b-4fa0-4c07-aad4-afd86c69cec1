import React from 'react';
import { Star, StarHalf } from 'lucide-react';

interface StarRatingProps {
    rating: number;
    size?: 'sm' | 'md' | 'lg';
    showRating?: boolean;
    className?: string;
}

const StarRating: React.FC<StarRatingProps> = ({
    rating,
    size = 'md',
    showRating = false,
    className = '',
}) => {
    // Ensure rating is between 0 and 5
    const normalizedRating = Math.min(Math.max(rating, 0), 5);

    // Calculate full stars, half stars, and empty stars
    const fullStars = Math.floor(normalizedRating);
    const hasHalfStar = normalizedRating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    // Size classes
    const sizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-4 h-4',
        lg: 'w-5 h-5',
    };

    return (
        <div className={`flex items-center ${className}`}>
            <div className="flex">
                {[...Array(fullStars)].map((_, i) => (
                    <Star
                        key={`full-${i}`}
                        className={`text-yellow-400 fill-yellow-400 ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                ))}

                {hasHalfStar && (
                    <StarHalf
                        className={`text-yellow-400 fill-yellow-400 ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                )}

                {[...Array(emptyStars)].map((_, i) => (
                    <Star
                        key={`empty-${i}`}
                        className={`text-gray-300 ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                ))}
            </div>

            {showRating && (
                <span className="ml-1 text-sm font-medium">
                    {normalizedRating.toFixed(1)}
                </span>
            )}
        </div>
    );
};

export default StarRating; 