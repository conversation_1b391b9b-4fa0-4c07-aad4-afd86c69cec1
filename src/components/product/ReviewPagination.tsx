import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ReviewPaginationProps {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}

const ReviewPagination: React.FC<ReviewPaginationProps> = ({
    currentPage,
    totalPages,
    onPageChange
}) => {
    if (totalPages <= 1) {
        return null;
    }

    const goToPage = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            onPageChange(page);
        }
    };

    const renderPageButtons = () => {
        let pages = [];
        const maxVisiblePages = 5;

        if (totalPages <= maxVisiblePages) {
            // Show all pages if there are few
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Always show first and last page, and pages around current
            let startPage = Math.max(1, currentPage - 1);
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 3);

            if (endPage - startPage < maxVisiblePages - 3) {
                startPage = Math.max(1, endPage - (maxVisiblePages - 3));
            }

            // Add first page
            pages.push(1);

            // Add ellipsis after first page if needed
            if (startPage > 2) {
                pages.push(-1); // Represent ellipsis
            } else if (startPage === 2) {
                pages.push(2);
            }

            // Add pages around current
            for (let i = Math.max(3, startPage); i <= Math.min(totalPages - 2, endPage); i++) {
                pages.push(i);
            }

            // Add ellipsis before last page if needed
            if (endPage < totalPages - 1) {
                pages.push(-2); // Represent ellipsis
            } else if (endPage === totalPages - 1) {
                pages.push(totalPages - 1);
            }

            // Add last page
            if (totalPages > 1) {
                pages.push(totalPages);
            }
        }

        return pages.map((page, index) => {
            if (page < 0) {
                // This is an ellipsis
                return (
                    <span key={`ellipsis-${index}`} className="px-2 text-gray-400">...</span>
                );
            }

            return (
                <Button
                    key={page}
                    variant={currentPage === page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => goToPage(page)}
                    aria-current={currentPage === page ? 'page' : undefined}
                    className={`w-9 h-9 p-0 ${currentPage === page ? 'bg-blue-600 text-white' : ''}`}
                >
                    {page}
                </Button>
            );
        });
    };

    return (
        <div className="flex items-center justify-center space-x-2 mt-6">
            <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage === 1}
                aria-label="Previous page"
                className="w-9 h-9 p-0"
            >
                <ChevronLeft className="h-4 w-4" />
            </Button>

            {renderPageButtons()}

            <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                aria-label="Next page"
                className="w-9 h-9 p-0"
            >
                <ChevronRight className="h-4 w-4" />
            </Button>
        </div>
    );
};

export default ReviewPagination; 