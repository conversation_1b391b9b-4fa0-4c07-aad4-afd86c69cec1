interface ResponseTimeProps {
  averageHours: number;
  locale: string;
}

export const ResponseTimeStat = ({ averageHours, locale }: ResponseTimeProps) => {
  if (isNaN(averageHours)) return null;
  
  return (
    <div className="metric-card bg-card p-4 rounded-lg shadow-sm">
      <span className="text-sm font-medium text-muted-foreground">
        {locale === 'fr' ? 'Temps moyen de réponse' : 'Average Response Time'}
      </span>
      <div className="text-2xl font-bold mt-1">
        {Math.round(averageHours)}h
      </div>
    </div>
  );
};