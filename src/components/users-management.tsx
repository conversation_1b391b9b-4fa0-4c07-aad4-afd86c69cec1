"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Search,
  UserCog,
  Mail,
  Calendar,
  FileText,
  MessageSquare,
  Package,
  AlertCircle,
  Shield,
  Ban,
  Clock
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";

import { getAllUsers } from "@/app/util/adminFunctions";
import { iUser } from "@/app/util/Interfaces";
import { UserDetails } from "./admin/UserDetails";

export default function UsersManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<iUser | null>(null);
  const queryClient = useQueryClient();

  // Fetch all users
  const {
    data: usersData,
    isLoading,
    isError
  } = useQuery({
    queryKey: ["allUsers"],
    queryFn: getAllUsers,
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async ({ userId, updates }: { userId: string, updates: Partial<iUser> }) => {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to update user" }));
        throw new Error(errorData.error || 'Failed to update user');
      }
      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["allUsers"] });
      const status = variables.updates.status;
      if (status === "SUSPENDED") {
        let message = "User suspended";
        if (variables.updates.suspendedUntil) {
          try {
            const date = new Date(variables.updates.suspendedUntil as string | Date);
            // Check if date is valid before formatting
            if (!isNaN(date.getTime())) {
              message += ` until ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
            }
          } catch (e) {
            // if date parsing fails, just use the generic message
            console.error("Failed to parse suspendedUntil date for toast:", e);
          }
        }
        toast.success(message + ".");
      } else if (status === "BANNED") {
        toast.success("User banned successfully.");
      } else if (status === "ACTIVE") {
        toast.success("User status set to active.");
      } else {
        toast.success("User updated successfully.");
      }
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update user status.");
    },
  });

  const users = usersData?.users || [];
  const userCount = usersData?.count || 0;

  // Filter users based on search term
  const filteredUsers = users.filter((user: iUser) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.userName?.toLowerCase().includes(searchLower) ||
      user.firstName?.toLowerCase().includes(searchLower) ||
      user.lastName?.toLowerCase().includes(searchLower) ||
      user.email?.toLowerCase().includes(searchLower)
    );
  });

  // Format date
  const formatDate = (dateString?: Date) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get initials for avatar fallback
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase();
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status?: string) => {
    switch (status) {
      case "ACTIVE":
        return "secondary";
      case "SUSPENDED":
        return "outline";
      case "BANNED":
        return "destructive";
      default:
        return "default";
    }
  };

  // Get status badge icon
  const getStatusBadgeIcon = (status?: string) => {
    switch (status) {
      case "ACTIVE":
        return <Shield className="w-3 h-3" />;
      case "SUSPENDED":
        return <Clock className="w-3 h-3" />;
      case "BANNED":
        return <Ban className="w-3 h-3" />;
      default:
        return null;
    }
  };

  return (
    <>
      {/* Search bar */}
      <div className="flex justify-between items-center mb-6">
        {/* Mobile search button */}
        <div className="md:hidden">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <Search className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="top">
              <SheetHeader>
                <SheetTitle>Search Users</SheetTitle>
                <SheetDescription>
                  Search by username, name, or email
                </SheetDescription>
              </SheetHeader>
              <div className="py-4">
                <Input
                  type="search"
                  placeholder="Search users..."
                  className="w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Desktop search */}
        <div className="hidden md:block md:flex-1 md:max-w-sm">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Stats card */}
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">
            Total Users
          </CardTitle>
          <UserCog className="w-4 h-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{userCount}</div>
          <p className="text-xs text-muted-foreground">
            Registered users in the system
          </p>
        </CardContent>
      </Card>

      {/* Users list */}
      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : isError ? (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-500">
              <AlertCircle className="w-5 h-5" />
              <p>Error loading users</p>
            </div>
          </CardContent>
        </Card>
      ) : filteredUsers.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              {searchTerm ? "No users match your search" : "No users found"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <ScrollArea className="h-[calc(100vh-350px)] md:h-[600px]">
          <div className="space-y-4">
            {filteredUsers.map((user: iUser) => (
              <Card key={user.id} className="overflow-hidden">
                <CardHeader className="flex flex-row items-center gap-4 space-y-0">
                  <Avatar className="w-12 h-12">
                    <AvatarImage
                      src={user.avatar || ""}
                      alt={user.userName || "User"}
                    />
                    <AvatarFallback>
                      {getInitials(user.firstName, user.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg font-medium">
                      {user.firstName} {user.lastName}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-1">
                      <span>@{user.userName}</span>
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm break-all">
                        <Mail className="w-4 h-4 flex-shrink-0 text-muted-foreground" />
                        <span>{user.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 flex-shrink-0 text-muted-foreground" />
                        <span className="text-sm">Joined: {formatDate(user.createdDate)}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 flex-shrink-0 text-muted-foreground" />
                        <span className="text-sm">Reviews: {user._count?.reviews || 0}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-4 h-4 flex-shrink-0 text-muted-foreground" />
                        <span className="text-sm">Comments: {user._count?.comments || 0}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 flex-shrink-0 text-muted-foreground" />
                        <span className="text-sm">Products: {user._count?.product || 0}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-wrap justify-between gap-2 border-t bg-muted/20 px-6 py-3">
                  <div className="flex gap-2">
                    {(user.businesses?.length ?? 0) > 0 && (
                      <Badge variant="outline">Business Owner</Badge>
                    )}
                    <Badge variant={getStatusBadgeVariant(user.status)} className="flex items-center gap-1">
                      {getStatusBadgeIcon(user.status)}
                      {user.status || "UNKNOWN"}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedUser(user)}
                        >
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl">
                        {selectedUser && (
                          <UserDetails
                            user={selectedUser}
                            onClose={() => setSelectedUser(null)}
                            onUpdateUser={async (userId, updates) => {
                              await updateUserMutation.mutateAsync({ userId, updates });
                            }}
                          />
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </ScrollArea>
      )}
    </>
  );
} 