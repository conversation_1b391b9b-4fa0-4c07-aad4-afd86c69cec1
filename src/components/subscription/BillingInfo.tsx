"use client";

import { Calendar, CreditCard } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { iSubscriptionBilling, iSubscriptionTier } from "@/app/util/Interfaces";

interface BillingInfoProps {
    billing: iSubscriptionBilling;
    tier: iSubscriptionTier;
    onAddPaymentMethod: () => void;
}

export function BillingInfo({ billing, tier, onAddPaymentMethod }: BillingInfoProps) {
    // Format date
    const formatDate = (date: Date) => {
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Billing Information</CardTitle>
                <CardDescription>Manage your payment methods and billing details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                        <p className="text-sm font-medium">Current Plan</p>
                        <div className="flex items-center">
                            <Badge className="mr-2">{tier.name}</Badge>
                            <span className="text-sm">${tier.price}/{tier.billingCycle}</span>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <p className="text-sm font-medium">Next Billing Date</p>
                        <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{formatDate(billing.nextBillingDate)}</span>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <p className="text-sm font-medium">Payment Method</p>
                        <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>
                                {billing.paymentMethod ? (
                                    <>
                                        {billing.paymentMethod} {billing.lastFour && `(**** **** **** ${billing.lastFour})`}
                                    </>
                                ) : tier.price === 0
                                    ? "No payment method required (Free Plan)"
                                    : "No payment method added"
                                }
                            </span>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <p className="text-sm font-medium">Status</p>
                        <div className="flex items-center">
                            <Badge
                                variant="outline"
                                className={
                                    billing.status === "active"
                                        ? "bg-green-100 text-green-800"
                                        : billing.status === "past_due"
                                            ? "bg-red-100 text-red-800"
                                            : "bg-gray-100 text-gray-800"
                                }
                            >
                                {billing.status.charAt(0).toUpperCase() + billing.status.slice(1).replace('_', ' ')}
                            </Badge>
                        </div>
                    </div>
                </div>

                {/* Add payment method button (disabled for free plan) */}
                <div className="pt-4">
                    <Button
                        disabled={tier.price === 0}
                        onClick={onAddPaymentMethod}
                    >
                        <CreditCard className="h-4 w-4 mr-2" />
                        {tier.price === 0
                            ? "No Payment Method Required"
                            : billing.paymentMethod
                                ? "Update Payment Method"
                                : "Add Payment Method"
                        }
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
} 