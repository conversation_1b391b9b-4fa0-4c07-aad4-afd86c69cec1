"use client";

import { LineChart } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { iSubscriptionUsage, iSubscriptionTier } from "@/app/util/Interfaces";

interface UsageStatsProps {
    usage: iSubscriptionUsage;
    tier: iSubscriptionTier;
}

export function UsageStats({ usage, tier }: UsageStatsProps) {
    // Get usage percentage for a metric
    const getUsagePercentage = (current: number, max: number) => {
        if (max === -1) return 0; // Unlimited
        return Math.min(100, (current / max) * 100);
    };

    // Get color based on usage percentage
    const getProgressColor = (percentage: number) => {
        if (percentage >= 90) return "text-red-500";
        if (percentage >= 75) return "text-amber-500";
        return "text-green-500";
    };

    // Calculate percentages
    const productsPercentage = getUsagePercentage(usage.productCount, tier.maxProducts);
    const promotionsPercentage = getUsagePercentage(usage.promotionCount, tier.maxPromotions);
    const reviewsPercentage = getUsagePercentage(usage.reviewCount, tier.maxReviews);

    return (
        <Card>
            <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
                <CardDescription>Your current usage compared to plan limits</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Products Usage */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Products</span>
                        <span className={`text-sm ${getProgressColor(productsPercentage)}`}>
                            {usage.productCount} / {tier.maxProducts === -1 ? "Unlimited" : tier.maxProducts}
                        </span>
                    </div>
                    <Progress
                        value={productsPercentage}
                        className={productsPercentage >= 90 ? "text-red-500" : ""}
                    />
                    {productsPercentage >= 90 && tier.maxProducts !== -1 && (
                        <p className="text-xs text-red-500">
                            You&apos;re approaching your product limit. Consider upgrading your plan.
                        </p>
                    )}
                </div>

                {/* Promotions Usage */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Promotions</span>
                        <span className={`text-sm ${getProgressColor(promotionsPercentage)}`}>
                            {usage.promotionCount} / {tier.maxPromotions === -1 ? "Unlimited" : tier.maxPromotions}
                        </span>
                    </div>
                    <Progress
                        value={promotionsPercentage}
                        className={promotionsPercentage >= 90 ? "text-red-500" : ""}
                    />
                    {promotionsPercentage >= 90 && tier.maxPromotions !== -1 && (
                        <p className="text-xs text-red-500">
                            You&apos;re approaching your promotion limit. Consider upgrading your plan.
                        </p>
                    )}
                </div>

                {/* Reviews Usage */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Reviews</span>
                        <span className={`text-sm ${getProgressColor(reviewsPercentage)}`}>
                            {usage.reviewCount} / {tier.maxReviews === -1 ? "Unlimited" : tier.maxReviews}
                        </span>
                    </div>
                    <Progress
                        value={reviewsPercentage}
                        className={reviewsPercentage >= 90 ? "text-red-500" : ""}
                    />
                    {reviewsPercentage >= 90 && tier.maxReviews !== -1 && (
                        <p className="text-xs text-red-500">
                            You&apos;re approaching your review limit. Consider upgrading your plan.
                        </p>
                    )}
                </div>

                {/* Monthly Views */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Monthly Views</span>
                        <span className="text-sm text-muted-foreground">
                            {usage.monthlyViews.toLocaleString()}
                        </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <LineChart className="h-4 w-4" />
                        <span>Usage measured on a rolling 30-day basis</span>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
} 