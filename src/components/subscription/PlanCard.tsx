"use client";

import { CheckCircle2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { iSubscriptionTier } from "@/app/util/Interfaces";

interface PlanCardProps {
    tier: iSubscriptionTier;
    isCurrentPlan?: boolean;
    onUpgrade: (tierId: string) => void;
}

export function PlanCard({ tier, isCurrentPlan = false, onUpgrade }: PlanCardProps) {
    return (
        <Card className={isCurrentPlan ? "border-2 border-blue-500" : ""}>
            <CardHeader>
                <CardTitle className="flex items-center">
                    {tier.name}
                    {isCurrentPlan && <Badge className="ml-2">Current</Badge>}
                </CardTitle>
                <CardDescription>{tier.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <p className="text-2xl font-bold">
                    ${tier.price}
                    <span className="text-sm font-normal text-muted-foreground">/{tier.billingCycle}</span>
                </p>
                <ul className="space-y-2">
                    {tier.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                            <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                            <span>{feature}</span>
                        </li>
                    ))}
                </ul>
            </CardContent>
            <CardFooter>
                <Button
                    className="w-full"
                    variant={isCurrentPlan ? "outline" : "default"}
                    disabled={isCurrentPlan}
                    onClick={() => onUpgrade(tier.id)}
                >
                    {isCurrentPlan ? "Current Plan" : "Upgrade"}
                </Button>
            </CardFooter>
        </Card>
    );
} 