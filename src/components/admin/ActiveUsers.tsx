'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare, Star } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import React from 'react';

interface ActiveUser {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
    _count: {
        reviews: number;
        comments: number;
    };
}

interface ActiveUsersProps {
    users: ActiveUser[];
}

export function ActiveUsers({ users }: ActiveUsersProps) {
    return (
        <div className="space-y-6">
            {users.map((user, index) => (
                <React.Fragment key={user.id}>
                    <div className="flex items-center space-x-4">
                        <Avatar>
                            <AvatarImage src={user.avatar || undefined} />
                            <AvatarFallback>
                                {user.firstName[0]}
                                {user.lastName[0]}
                            </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                            <p className="text-sm font-medium leading-none">
                                {user.firstName} {user.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                                @{user.userName}
                            </p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 text-yellow-500" />
                                <span className="text-sm">{user._count.reviews}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                                <MessageSquare className="h-4 w-4 text-blue-500" />
                                <span className="text-sm">{user._count.comments}</span>
                            </div>
                        </div>
                    </div>
                    {index < users.length - 1 && <Separator />}
                </React.Fragment>
            ))}
        </div>
    );
}