'use client';

import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ReviewDistributionProps {
    data: Array<{
        rating: number;
        count: number;
        percentage: number;
    }>;
}

export function ReviewDistribution({ data }: ReviewDistributionProps) {
    const chartData = data.map(item => ({
        name: `${item.rating} Stars`,
        count: item.count,
        percentage: item.percentage
    }));

    return (
        <div className="w-full h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                        formatter={(value: number, name: string) => {
                            if (name === 'count') {
                                const item = data.find(d => d.rating.toString() === value.toString() || `${d.rating} Stars` === name);
                                return [`${value} reviews (${item?.percentage.toFixed(1)}%)`, 'Reviews'];
                            }
                            return [value, name];
                        }}
                    />
                    <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
            </ResponsiveContainer>
        </div>
    );
} 