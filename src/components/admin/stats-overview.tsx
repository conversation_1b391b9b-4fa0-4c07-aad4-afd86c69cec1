import { <PERSON>, MessageSquare, <PERSON><PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getReviewStats } from "@/app/util/adminFunctions";

interface StatsOverviewProps {
  pendingCount: number;
  approvedCount: number;
  rejectedCount: number;
  isLoading: boolean;
}

export function StatsOverview({
  pendingCount,
  approvedCount,
  rejectedCount,
  isLoading,
}: StatsOverviewProps) {
  const router = useRouter();
  const totalReviews = approvedCount + pendingCount + rejectedCount;

  // Fetch review statistics
  const { data: reviewStats, isLoading: isStatsLoading } = useQuery({
    queryKey: ["reviewStats"],
    queryFn: getReviewStats,
  });

  const isPageLoading = isLoading || isStatsLoading;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
          <Clock className="w-4 h-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isPageLoading ? "..." : pendingCount}
          </div>
          <Button
            variant="link"
            className="p-0 h-auto text-xs text-muted-foreground"
            onClick={() => router.push("/admin/reviews")}
          >
            View pending reviews →
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
          <MessageSquare className="w-4 h-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isPageLoading ? "..." : totalReviews}
          </div>
          <p className="text-xs text-muted-foreground">Across all statuses</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">System Status</CardTitle>
          <ShieldAlert className="w-4 h-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Healthy</div>
          <p className="text-xs text-muted-foreground">
            All systems operational
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">Review Rate</CardTitle>
          <TrendingUp className="w-4 h-4 text-purple-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isPageLoading
              ? "..."
              : reviewStats?.success && reviewStats?.data
                ? reviewStats.data.averagePerHour
                : 0}
            /hr
          </div>
          <p className="text-xs text-muted-foreground">
            {reviewStats?.success && reviewStats?.data
              ? reviewStats.data.last24Hours
              : 0}{" "}
            reviews in 24h
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
