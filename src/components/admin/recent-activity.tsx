import { useQuery } from "@tanstack/react-query";
import { MessageSquare, MessageCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getRecentNotifications } from "@/app/util/adminFunctions";

interface Activity {
  type: string;
  message: string;
  created_at: Date;
  notification_type: string;
}

export function RecentActivity() {
  // Fetch recent notifications (which includes both reviews and comments)
  const {
    data: activities,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["recentActivities"],
    queryFn: getRecentNotifications,
  });

  if (isLoading) {
    return (
      <div className="px-4">
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !activities?.success || !activities?.notifications) {
    return (
      <div>
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            Error loading activities
          </CardContent>
        </Card>
      </div>
    );
  }

  if (activities.notifications.length === 0) {
    return (
      <div>
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            No recent activity
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
      <Card>
        <CardContent className="pt-6">
          <ScrollArea className="h-[300px]">
            <div className="space-y-4">
              {activities.notifications.map(
                (
                  activity: {
                    notification_type: string;
                    message: string;
                    created_at: string | Date;
                  },
                  i: number
                ) => (
                  <div
                    key={i}
                    className="flex items-start gap-4 pb-4 last:pb-0 border-b last:border-0"
                  >
                    {activity.notification_type === "review" && (
                      <MessageSquare className="w-4 h-4 text-blue-500 mt-1" />
                    )}
                    {activity.notification_type === "comment" && (
                      <MessageCircle className="w-4 h-4 text-green-500 mt-1" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm">{activity.message}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(activity.created_at), {
                          addSuffix: true,
                        })}
                      </p>
                    </div>
                  </div>
                )
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
