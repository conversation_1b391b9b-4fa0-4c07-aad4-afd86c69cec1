"use client";

import Image from "next/image";
import { Activity } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import React from "react";

interface AttentionProduct {
  productId: string;
  avgScrollDepth: number;
  avgDuration: number;
  attentionScore: number;
  views: number;
  product: {
    id: string;
    name: string;
    display_image: string | null;
  } | null;
}

interface TopAttentionProductsProps {
  products: AttentionProduct[];
}

export function TopAttentionProducts({ products }: TopAttentionProductsProps) {
  if (!products || products.length === 0) {
    return <p className="text-sm text-muted-foreground">No data available</p>;
  }

  return (
    <div className="space-y-6">
      {products.map((item, index) => (
        <React.Fragment key={item.productId}>
          <div className="flex items-center space-x-4">
            {item.product?.display_image && (
              <div className="relative h-16 w-16 flex-shrink-0">
                <Image
                  src={item.product.display_image}
                  alt={item.product.name}
                  fill
                  className="rounded-md object-cover"
                />
              </div>
            )}
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium leading-none line-clamp-1">
                {item.product?.name || "Unknown"}
              </p>
              <p className="text-xs text-muted-foreground">
                Avg depth: {Math.round(item.avgScrollDepth)}px • Avg time: {Math.round(item.avgDuration)}s
              </p>
            </div>
            <div className="flex items-center space-x-1">
              <Activity className="h-4 w-4 text-green-500" />
              <span className="text-sm">{Math.round(item.attentionScore)}</span>
            </div>
          </div>
          {index < products.length - 1 && <Separator />}
        </React.Fragment>
      ))}
    </div>
  );
}
