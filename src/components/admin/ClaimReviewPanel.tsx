import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

interface Claim {
    id: string;
    status: 'PENDING' | 'APPROVED' | 'REJECTED';
    product: {
        id: string;
        name: string;
        description: string;
    };
    user: {
        id: string;
        name: string;
        email: string;
    };
    contactInfo: string;
    additionalInfo: string;
    images: string[];
    createdAt: string;
}

interface ClaimReviewPanelProps {
    claims: Claim[];
    onClaimUpdated: () => void;
}

export function ClaimReviewPanel({ claims, onClaimUpdated }: ClaimReviewPanelProps) {
    const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
    const [feedback, setFeedback] = useState('');
    const [loading, setLoading] = useState(false);

    const handleReview = async (status: 'APPROVED' | 'REJECTED') => {
        if (!selectedClaim) return;

        try {
            setLoading(true);
            const response = await fetch('/api/admin/review-claim', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    claimId: selectedClaim.id,
                    status,
                    feedback,
                }),
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message);
            }

            toast.success(`Claim ${status.toLowerCase()} successfully`);

            setSelectedClaim(null);
            setFeedback('');
            onClaimUpdated();
        } catch (error) {
            toast.error(error instanceof Error ? error.message : 'Failed to review claim');
        } finally {
            setLoading(false);
        }
    };

    const renderClaimCard = (claim: Claim) => (
        <Card key={claim.id} className="mb-4">
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle>{claim.product.name}</CardTitle>
                    <Badge variant={claim.status === 'PENDING' ? 'default' : claim.status === 'APPROVED' ? 'secondary' : 'destructive'}>
                        {claim.status}
                    </Badge>
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div>
                        <h4 className="font-medium">Product Description</h4>
                        <p className="text-sm text-gray-500">{claim.product.description}</p>
                    </div>
                    <div>
                        <h4 className="font-medium">Claimant</h4>
                        <p className="text-sm text-gray-500">{claim.user.name} ({claim.user.email})</p>
                    </div>
                    <div>
                        <h4 className="font-medium">Contact Information</h4>
                        <p className="text-sm text-gray-500">{claim.contactInfo}</p>
                    </div>
                    <div>
                        <h4 className="font-medium">Additional Information</h4>
                        <p className="text-sm text-gray-500">{claim.additionalInfo}</p>
                    </div>
                    {claim.images.length > 0 && (
                        <div>
                            <h4 className="font-medium">Supporting Images</h4>
                            <div className="grid grid-cols-2 gap-2 mt-2">
                                {claim.images.map((image, index) => (
                                    <img
                                        key={index}
                                        src={image}
                                        alt={`Supporting image ${index + 1}`}
                                        className="w-full h-32 object-cover rounded"
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                    {claim.status === 'PENDING' && (
                        <div className="space-y-4">
                            <Textarea
                                placeholder="Enter feedback (required for rejection)"
                                value={feedback}
                                onChange={(e) => setFeedback(e.target.value)}
                            />
                            <div className="flex gap-2">
                                <Button
                                    variant="default"
                                    onClick={() => handleReview('APPROVED')}
                                    disabled={loading}
                                >
                                    Approve
                                </Button>
                                <Button
                                    variant="destructive"
                                    onClick={() => handleReview('REJECTED')}
                                    disabled={loading || !feedback}
                                >
                                    Reject
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );

    return (
        <Tabs defaultValue="pending" className="w-full">
            <TabsList>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
            </TabsList>
            <TabsContent value="pending">
                {claims
                    .filter((claim) => claim.status === 'PENDING')
                    .map(renderClaimCard)}
            </TabsContent>
            <TabsContent value="approved">
                {claims
                    .filter((claim) => claim.status === 'APPROVED')
                    .map(renderClaimCard)}
            </TabsContent>
            <TabsContent value="rejected">
                {claims
                    .filter((claim) => claim.status === 'REJECTED')
                    .map(renderClaimCard)}
            </TabsContent>
        </Tabs>
    );
} 