'use client';

import Image from 'next/image';
import { Star, MessageSquare } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import React from 'react';

interface TopProduct {
    id: string;
    name: string;
    description: string;
    rating: number;
    display_image: string;
    _count: {
        reviews: number;
    };
}

interface TopProductsProps {
    products: TopProduct[];
}

export function TopProducts({ products }: TopProductsProps) {
    return (
        <div className="space-y-6">
            {products.map((product, index) => (
                <React.Fragment key={product.id}>
                    <div className="flex items-center space-x-4">
                        <div className="relative h-16 w-16 flex-shrink-0">
                            <Image
                                src={product.display_image}
                                alt={product.name}
                                fill
                                className="rounded-md object-cover"
                            />
                        </div>
                        <div className="flex-1 space-y-1">
                            <p className="text-sm font-medium leading-none line-clamp-1">
                                {product.name}
                            </p>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                                {product.description}
                            </p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 text-yellow-500" />
                                <span className="text-sm">{product.rating.toFixed(1)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                                <MessageSquare className="h-4 w-4 text-blue-500" />
                                <span className="text-sm">{product._count.reviews}</span>
                            </div>
                        </div>
                    </div>
                    {index < products.length - 1 && <Separator />}
                </React.Fragment>
            ))}
        </div>
    );
}