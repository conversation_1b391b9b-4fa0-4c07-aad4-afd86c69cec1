import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
    Mail,
    Calendar,
    FileText,
    MessageSquare,
    Package,
    AlertCircle,
    Shield,
    Ban,
    Clock,
    UserCog
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { iUser } from "@/app/util/Interfaces";

interface UserDetailsProps {
    user: iUser;
    onClose: () => void;
    onUpdateUser: (userId: string, updates: Partial<iUser>) => Promise<void>;
}

export function UserDetails({ user, onClose, onUpdateUser }: UserDetailsProps) {
    const [status, setStatus] = useState<"ACTIVE" | "SUSPENDED" | "BANNED">(
        (user.status || "ACTIVE") as "ACTIVE" | "SUSPENDED" | "BANNED"
    );
    const [suspendedUntil, setSuspendedUntil] = useState<string>(() => {
        if (user.suspendedUntil) {
            try {
                const d = new Date(user.suspendedUntil);
                const year = d.getFullYear();
                const month = (d.getMonth() + 1).toString().padStart(2, '0');
                const day = d.getDate().toString().padStart(2, '0');
                const hours = d.getHours().toString().padStart(2, '0');
                const minutes = d.getMinutes().toString().padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            } catch (e) {
                console.error("Error formatting initial suspendedUntil:", e);
                return "";
            }
        }
        return "";
    });
    const [suspendedReason, setSuspendedReason] = useState(user.suspendedReason || "");
    const [isSaving, setIsSaving] = useState(false);

    useEffect(() => {
        setStatus((user.status || "ACTIVE") as "ACTIVE" | "SUSPENDED" | "BANNED");
        setSuspendedReason(user.suspendedReason || "");
        if (user.suspendedUntil) {
            try {
                const d = new Date(user.suspendedUntil);
                const year = d.getFullYear();
                const month = (d.getMonth() + 1).toString().padStart(2, '0');
                const day = d.getDate().toString().padStart(2, '0');
                const hours = d.getHours().toString().padStart(2, '0');
                const minutes = d.getMinutes().toString().padStart(2, '0');
                setSuspendedUntil(`${year}-${month}-${day}T${hours}:${minutes}`);
            } catch (e) {
                console.error("Error formatting suspendedUntil in effect:", e);
                setSuspendedUntil("");
            }
        } else {
            setSuspendedUntil("");
        }
    }, [user]);

    // Fetch user activity
    const { data: activityData, isLoading: isLoadingActivity } = useQuery({
        queryKey: ["userActivity", user.id],
        queryFn: async () => {
            const response = await fetch(`/api/admin/users/${user.id}/activity`);
            if (!response.ok) throw new Error("Failed to fetch user activity");
            return response.json();
        },
    });

    const handleStatusChange = (newStatus: "ACTIVE" | "SUSPENDED" | "BANNED") => {
        setStatus(newStatus);
    };

    const handleSaveChanges = async () => {
        setIsSaving(true);
        const updates: Partial<iUser> = {
            status,
        };

        if (status === "SUSPENDED") {
            updates.suspendedReason = suspendedReason;
            if (suspendedUntil) {
                try {
                    updates.suspendedUntil = new Date(suspendedUntil);
                } catch (e) {
                    console.error("Invalid date for suspendedUntil on save:", suspendedUntil);
                    updates.suspendedUntil = undefined;
                }
            } else {
                updates.suspendedUntil = undefined;
            }
        } else {
            updates.suspendedReason = undefined;
            updates.suspendedUntil = undefined;
        }

        try {
            await onUpdateUser(user.id, updates);
        } catch (error) {
            console.error("Failed to save user changes:", error);
        } finally {
            setIsSaving(false);
        }
    };

    const formatDate = (dateString?: Date | string) => {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });
    };

    return (
        <Card className="w-full max-w-4xl mx-auto">
            <CardHeader className="flex flex-row items-center justify-between">
                <div className="flex items-center gap-4">
                    <Avatar className="w-16 h-16">
                        <AvatarImage src={user.avatar || ""} alt={user.userName} />
                        <AvatarFallback>
                            {user.firstName[0]}{user.lastName[0]}
                        </AvatarFallback>
                    </Avatar>
                    <div>
                        <CardTitle className="text-2xl">
                            {user.firstName} {user.lastName}
                        </CardTitle>
                        <CardDescription>@{user.userName}</CardDescription>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="details" className="w-full">
                    <TabsList>
                        <TabsTrigger value="details">Details</TabsTrigger>
                        <TabsTrigger value="activity">Activity</TabsTrigger>
                    </TabsList>
                    <TabsContent value="details" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-4">
                                <div className="flex items-center gap-2 text-sm">
                                    <Mail className="w-4 h-4 text-muted-foreground" />
                                    <span>{user.email}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Calendar className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm">Joined: {formatDate(user.createdDate)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileText className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm">Reviews: {user._count?.reviews || 0}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <MessageSquare className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm">Comments: {user._count?.comments || 0}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Package className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm">Products: {user._count?.product || 0}</span>
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label>Status</Label>
                                    <Select value={status} onValueChange={handleStatusChange}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="ACTIVE">Active</SelectItem>
                                            <SelectItem value="SUSPENDED">Suspended</SelectItem>
                                            <SelectItem value="BANNED">Banned</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                {status === "SUSPENDED" && (
                                    <>
                                        <div className="space-y-2">
                                            <Label>Suspended Until</Label>
                                            <Input
                                                type="datetime-local"
                                                value={suspendedUntil}
                                                onChange={(e) => setSuspendedUntil(e.target.value)}
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Suspension Reason</Label>
                                            <Textarea
                                                value={suspendedReason}
                                                onChange={(e) => setSuspendedReason(e.target.value)}
                                                placeholder="Enter reason for suspension..."
                                            />
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value="activity">
                        {isLoadingActivity ? (
                            <div className="flex justify-center p-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                            </div>
                        ) : (
                            <ScrollArea className="h-[400px]">
                                <div className="space-y-4">
                                    {/* Recent Reviews */}
                                    {activityData?.data?.reviews && Array.isArray(activityData.data.reviews) && activityData.data.reviews.length > 0 && (
                                        <div>
                                            <h3 className="font-medium mb-2">Recent Reviews</h3>
                                            <div className="space-y-2">
                                                {activityData.data.reviews.map((review: any) => (
                                                    <Card key={review.id}>
                                                        <CardContent className="p-4">
                                                            <div className="flex justify-between items-start">
                                                                <div>
                                                                    <p className="font-medium">{review.title}</p>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        {formatDate(review.createdDate)}
                                                                    </p>
                                                                </div>
                                                                <Badge variant="outline">
                                                                    Rating: {review.rating}
                                                                </Badge>
                                                            </div>
                                                        </CardContent>
                                                    </Card>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                    {/* Recent Comments */}
                                    {activityData?.data?.comments && Array.isArray(activityData.data.comments) && activityData.data.comments.length > 0 && (
                                        <div>
                                            <h3 className="font-medium mb-2">Recent Comments</h3>
                                            <div className="space-y-2">
                                                {activityData.data.comments.map((comment: any) => (
                                                    <Card key={comment.id}>
                                                        <CardContent className="p-4">
                                                            <p className="text-sm">{comment.body}</p>
                                                            <p className="text-xs text-muted-foreground mt-1">
                                                                {formatDate(comment.createdDate)}
                                                            </p>
                                                        </CardContent>
                                                    </Card>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                    {/* Recent Products */}
                                    {activityData?.data?.product && Array.isArray(activityData.data.product) && activityData.data.product.length > 0 && (
                                        <div>
                                            <h3 className="font-medium mb-2">Recent Products</h3>
                                            <div className="space-y-2">
                                                {activityData.data.product.map((product: any) => (
                                                    <Card key={product.id}>
                                                        <CardContent className="p-4">
                                                            <p className="font-medium">{product.name}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {formatDate(product.createdDate)}
                                                            </p>
                                                        </CardContent>
                                                    </Card>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        )}
                    </TabsContent>
                </Tabs>
            </CardContent>
            <CardFooter className="flex justify-end border-t pt-6">
                <Button onClick={handleSaveChanges} disabled={isSaving}>
                    {isSaving ? "Saving..." : "Save Changes"}
                </Button>
            </CardFooter>
        </Card>
    );
}