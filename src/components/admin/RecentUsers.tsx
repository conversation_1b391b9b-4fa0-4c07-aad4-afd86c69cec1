'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

interface RecentUser {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    createdDate: string;
    avatar: string | null;
}

interface RecentUsersProps {
    users: RecentUser[];
}

import { Separator } from "@/components/ui/separator";
import { Fragment } from 'react';

export function RecentUsers({ users }: RecentUsersProps) {
    return (
        <div className="space-y-6">
            {users.map((user, index) => (
                <Fragment key={user.id}>
                    <div className="flex items-center space-x-4">
                        <Avatar>
                            <AvatarImage src={user.avatar || undefined} />
                            <AvatarFallback>
                                {user.firstName[0]}
                                {user.lastName[0]}
                            </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                            <p className="text-sm font-medium leading-none">
                                {user.firstName} {user.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                                @{user.userName}
                            </p>
                        </div>
                        <div className="text-sm text-muted-foreground">
                            {dayjs(user.createdDate).fromNow()}
                        </div>
                    </div>
                    {index < users.length - 1 && <Separator />}
                </Fragment>
            ))}
        </div>
    );
}