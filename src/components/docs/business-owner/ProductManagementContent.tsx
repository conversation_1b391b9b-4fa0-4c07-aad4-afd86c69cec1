"use client";

import React from "react";
import {
  Package,
  Search,
  Filter,
  Edit3,
  Trash2,
  Eye,
  BarChart3,
  Image,
  Tag,
  Globe,
  CheckCircle,
  AlertTriangle,
  Info,
  TrendingUp,
  Users,
  Star,
  Calendar,
  FileText,
} from "lucide-react";

export default function ProductManagementContent() {
  return (
    <div className="text-gray-700 leading-relaxed">
      {/* Viewing Products Section */}
      <section className="mb-12">
        <h2 id="viewing-products" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Package className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Viewing Products
        </h2>
        <p className="mb-4">
          To see a list of all products, navigate to the "Product Management" tab in your dashboard.
          The product table displays comprehensive information about each product:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-500" />
              Basic Information
            </h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Product Name</strong> - Title and identifier</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Category</strong> - Product classification and grouping</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Status</strong> - Published, draft, pending approval, or archived</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Owner</strong> - Business or user who submitted the product</div>
              </li>
            </ul>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-green-500" />
              Performance Data
            </h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Reviews</strong> - Number of reviews and average rating</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Views</strong> - Product page visit statistics</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Created Date</strong> - When the product was first added</div>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div><strong>Last Modified</strong> - Most recent update timestamp</div>
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Editing Products Section */}
      <section className="mb-12">
        <h2 id="editing-products" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Edit3 className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Editing Products
        </h2>
        <p className="mb-6">
          To edit a product's details, click on the "Edit" button next to the product you want to modify.
          The comprehensive edit form includes:
        </p>

        <div className="space-y-8">
          {/* Basic Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              <FileText className="w-6 h-6 text-blue-600" />
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <Tag className="w-4 h-4 text-blue-600" />
                  <div>
                    <strong>Product Name</strong>
                    <p className="text-sm text-gray-600">Title and display name</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  <FileText className="w-4 h-4 text-blue-600" />
                  <div>
                    <strong>Description</strong>
                    <p className="text-sm text-gray-600">Detailed product information</p>
                  </div>
                </li>
              </ul>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <Package className="w-4 h-4 text-blue-600" />
                  <div>
                    <strong>Category</strong>
                    <p className="text-sm text-gray-600">Product classification</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  <Globe className="w-4 h-4 text-blue-600" />
                  <div>
                    <strong>Website URL</strong>
                    <p className="text-sm text-gray-600">Official product or business website</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          {/* Media and Assets */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              {/* eslint-disable-next-line jsx-a11y/alt-text */}
              <Image className="w-6 h-6 text-green-600" />
              Media and Assets
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image className="w-4 h-4 text-green-600" />
                  <div>
                    <strong>Primary Image</strong>
                    <p className="text-sm text-gray-600">Main product photo</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image className="w-4 h-4 text-green-600" />
                  <div>
                    <strong>Additional Images</strong>
                    <p className="text-sm text-gray-600">Gallery of product photos</p>
                  </div>
                </li>
              </ul>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <Package className="w-4 h-4 text-green-600" />
                  <div>
                    <strong>Logo</strong>
                    <p className="text-sm text-gray-600">Business or product logo</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  <Info className="w-4 h-4 text-green-600" />
                  <div>
                    <strong>Media Guidelines</strong>
                    <p className="text-sm text-gray-600">Image size and format requirements</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          {/* Status and Visibility */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              <Eye className="w-6 h-6 text-purple-600" />
              Status and Visibility
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <CheckCircle className="w-4 h-4 text-purple-600" />
                  <div>
                    <strong>Publication Status</strong>
                    <p className="text-sm text-gray-600">Published, draft, or archived</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  <AlertTriangle className="w-4 h-4 text-purple-600" />
                  <div>
                    <strong>Approval Status</strong>
                    <p className="text-sm text-gray-600">Pending, approved, or rejected</p>
                  </div>
                </li>
              </ul>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <Eye className="w-4 h-4 text-purple-600" />
                  <div>
                    <strong>Visibility Settings</strong>
                    <p className="text-sm text-gray-600">Public, private, or restricted access</p>
                  </div>
                </li>
                <li className="flex items-center gap-3">
                  <Star className="w-4 h-4 text-purple-600" />
                  <div>
                    <strong>Featured Status</strong>
                    <p className="text-sm text-gray-600">Highlight in special sections</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> Once you complete your changes, click "Save" to update the product.
            All modifications are logged for audit purposes.
          </p>
        </div>
      </section>

      {/* Search and Filtering Section */}
      <section className="mb-12">
        <h2 id="search-filtering" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Search className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Product Search and Filtering
        </h2>
        <p className="mb-6">
          The product management dashboard includes powerful search and filtering capabilities to help you
          find and manage products efficiently:
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Search Features */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              <Search className="w-6 h-6 text-blue-600" />
              Search Features
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Text Search</strong>
                  <p className="text-sm text-gray-600">Find products by name, description, or tags</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Fuzzy Matching</strong>
                  <p className="text-sm text-gray-600">Handles typos and partial matches</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Advanced Search</strong>
                  <p className="text-sm text-gray-600">Combine multiple search criteria</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Saved Searches</strong>
                  <p className="text-sm text-gray-600">Store frequently used search queries</p>
                </div>
              </li>
            </ul>
          </div>

          {/* Filter Options */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 flex items-center gap-2">
              <Filter className="w-6 h-6 text-green-600" />
              Filter Options
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Category Filter</strong>
                  <p className="text-sm text-gray-600">Filter by product categories and subcategories</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Status Filter</strong>
                  <p className="text-sm text-gray-600">Published, draft, pending, or archived products</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Date Range</strong>
                  <p className="text-sm text-gray-600">Creation date or last modification date</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Rating Filter</strong>
                  <p className="text-sm text-gray-600">Products within specific rating ranges</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Product Analytics Section */}
      <section className="mb-12">
        <h2 id="product-analytics" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <BarChart3 className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Product Analytics
        </h2>
        <p className="mb-6">
          The product analytics section provides comprehensive insights into product performance and user engagement:
        </p>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 border border-blue-200">
          <h3 className="text-2xl font-bold mb-6 text-gray-900 flex items-center gap-2">
            <TrendingUp className="w-6 h-6 text-blue-600" />
            Performance Metrics
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Eye className="w-5 h-5 text-blue-600" />
                <h4 className="font-semibold text-gray-900">View Statistics</h4>
              </div>
              <p className="text-sm text-gray-600">Page views, unique visitors, and view trends</p>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Users className="w-5 h-5 text-green-600" />
                <h4 className="font-semibold text-gray-900">Engagement Metrics</h4>
              </div>
              <p className="text-sm text-gray-600">Time spent on product pages and interaction rates</p>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Star className="w-5 h-5 text-yellow-600" />
                <h4 className="font-semibold text-gray-900">Review Analytics</h4>
              </div>
              <p className="text-sm text-gray-600">Review submission rates and rating distributions</p>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Globe className="w-5 h-5 text-purple-600" />
                <h4 className="font-semibold text-gray-900">Conversion Tracking</h4>
              </div>
              <p className="text-sm text-gray-600">Click-through rates to business websites</p>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Search className="w-5 h-5 text-indigo-600" />
                <h4 className="font-semibold text-gray-900">Search Performance</h4>
              </div>
              <p className="text-sm text-gray-600">How often products appear in search results</p>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Calendar className="w-5 h-5 text-red-600" />
                <h4 className="font-semibold text-gray-900">Trend Analysis</h4>
              </div>
              <p className="text-sm text-gray-600">Performance changes over time periods</p>
            </div>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Best Practices for Product Management
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Success Tips
            </h3>
            <ul className="space-y-2 text-sm text-green-800">
              <li>• <strong>High-Quality Images:</strong> Use clear, professional product photos</li>
              <li>• <strong>Detailed Descriptions:</strong> Provide comprehensive product information</li>
              <li>• <strong>Regular Updates:</strong> Keep product information current and accurate</li>
              <li>• <strong>SEO Optimization:</strong> Use relevant keywords in titles and descriptions</li>
              <li>• <strong>Monitor Performance:</strong> Regularly check analytics and adjust accordingly</li>
            </ul>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Common Mistakes to Avoid
            </h3>
            <ul className="space-y-2 text-sm text-orange-800">
              <li>• <strong>Poor Image Quality:</strong> Avoid blurry or low-resolution photos</li>
              <li>• <strong>Incomplete Information:</strong> Don't leave important fields empty</li>
              <li>• <strong>Incorrect Categorization:</strong> Ensure products are in the right category</li>
              <li>• <strong>Ignoring Analytics:</strong> Don't neglect performance data insights</li>
              <li>• <strong>Outdated Content:</strong> Keep all product information up to date</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}
