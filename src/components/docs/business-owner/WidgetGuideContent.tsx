"use client";

import React from "react";
import {
  Shield,
  Globe,
  Key,
  CheckCircle,
  AlertTriangle,
  Code,
  Eye,
  Settings,
  BarChart3,
  Copy,
  Download,
  ExternalLink,
  Clock,
  Activity,
  FileText,
  Smartphone,
  Monitor,
  Tablet,
  Lock,
  Unlock,
  Zap,
  Target
} from "lucide-react";

export default function WidgetGuideContent() {
  return (
    <div className="text-gray-700 leading-relaxed">
      {/* Introduction */}
      <section className="mb-8 sm:mb-12">
        <h2 id="introduction" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Code className="inline-block w-6 h-6 sm:w-8 sm:h-8 mr-2 text-blue-600" />
          Widget System Overview
        </h2>
        <p className="mb-4">
          ReviewIt's widget system allows you to embed your business reviews and ratings on external websites. 
          We offer two types of widgets to meet different security and use case requirements:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 sm:p-6">
            <div className="flex items-center mb-3">
              <Globe className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600 mr-2" />
              <h3 className="font-semibold text-orange-900 text-sm sm:text-base">Simple Widget (Public)</h3>
            </div>
            <p className="text-orange-800 mb-3 text-sm sm:text-base">Easy iframe embedding that can be used anywhere.</p>
            <div className="space-y-2">
              <div className="flex items-start text-xs sm:text-sm text-orange-700">
                <CheckCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>Quick setup - just copy and paste</span>
              </div>
              <div className="flex items-start text-xs sm:text-sm text-orange-700">
                <CheckCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>Works on any website immediately</span>
              </div>
              <div className="flex items-start text-xs sm:text-sm text-orange-600">
                <AlertTriangle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>Can be copied and used by anyone</span>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6">
            <div className="flex items-center mb-3">
              <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-green-600 mr-2" />
              <h3 className="font-semibold text-green-900 text-sm sm:text-base">Secure Widget (Domain-Restricted)</h3>
            </div>
            <p className="text-green-800 mb-3">Advanced security with domain verification and token authentication.</p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-green-700">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span>Domain verification required</span>
              </div>
              <div className="flex items-center text-sm text-green-700">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span>Token-based authentication</span>
              </div>
              <div className="flex items-center text-sm text-green-700">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span>Cannot be copied or misused</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Creating Widgets */}
      <section className="mb-12">
        <h2 id="creating-widgets" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Settings className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Creating Widgets
        </h2>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="font-semibold text-blue-900 mb-3">Step-by-Step Widget Creation</h3>
          <ol className="list-decimal list-inside space-y-3 text-blue-800">
            <li>Navigate to the <strong>Widgets</strong> section in your business dashboard</li>
            <li>Click <strong>"Create New Widget"</strong></li>
            <li>Choose your <strong>security level</strong> (Simple or Secure)</li>
            <li>Select a <strong>widget type</strong> that fits your needs</li>
            <li>Customize the <strong>appearance and content</strong></li>
            <li>Configure <strong>security settings</strong> (for secure widgets)</li>
            <li>Generate and copy your <strong>embed code</strong></li>
          </ol>
        </div>

        <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-900">Widget Types Available</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-6">
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Review Carousel</h4>
            <p className="text-xs sm:text-sm text-gray-600">Horizontal scrolling reviews with navigation controls</p>
          </div>
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Review Grid</h4>
            <p className="text-xs sm:text-sm text-gray-600">Grid layout displaying multiple reviews at once</p>
          </div>
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Rating Summary</h4>
            <p className="text-xs sm:text-sm text-gray-600">Overall rating display with review count</p>
          </div>
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Mini Review</h4>
            <p className="text-xs sm:text-sm text-gray-600">Compact single review display</p>
          </div>
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Business Card</h4>
            <p className="text-xs sm:text-sm text-gray-600">Business information with rating overview</p>
          </div>
          <div className="border rounded-lg p-3 sm:p-4">
            <h4 className="font-medium mb-2 text-sm sm:text-base">Trust Badge</h4>
            <p className="text-xs sm:text-sm text-gray-600">Simple trust indicator with rating</p>
          </div>
        </div>
      </section>

      {/* Simple Widget Implementation */}
      <section className="mb-8 sm:mb-12">
        <h2 id="simple-widgets" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Globe className="inline-block w-6 h-6 sm:w-8 sm:h-8 mr-2 text-orange-600" />
          Simple Widget Implementation
        </h2>

        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 sm:p-6 mb-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600 mt-1 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-orange-900 mb-2 text-sm sm:text-base">Security Notice</h3>
              <p className="text-orange-800">
                Simple widgets can be copied and used on any website. Only use this option if you don't need 
                to restrict where your widget appears.
              </p>
            </div>
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">HTML Implementation</h3>
        <div className="bg-gray-100 rounded-lg p-4 mb-4">
          <code className="text-sm">
{`<iframe 
  src="https://reviewit.gy/widgets/iframe/[WIDGET_ID]" 
  width="100%" 
  height="400" 
  frameborder="0"
  title="ReviewIt Widget"
  style="border: none; border-radius: 8px;">
</iframe>`}
          </code>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">WordPress Implementation</h3>
        <ol className="list-decimal list-inside space-y-2 mb-6">
          <li>Go to your WordPress admin dashboard</li>
          <li>Edit the page/post where you want to add the widget</li>
          <li>Add a "Custom HTML" block</li>
          <li>Paste the iframe code into the HTML block</li>
          <li>Save and publish your page</li>
        </ol>
      </section>

      {/* Secure Widget Implementation */}
      <section className="mb-12">
        <h2 id="secure-widgets" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Shield className="inline-block w-8 h-8 mr-2 text-green-600" />
          Secure Widget Implementation
        </h2>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <div className="flex items-start space-x-3">
            <CheckCircle className="w-6 h-6 text-green-600 mt-1" />
            <div>
              <h3 className="font-semibold text-green-900 mb-2">Enhanced Security</h3>
              <p className="text-green-800">
                Secure widgets require domain verification and use token-based authentication. 
                They cannot be copied or used on unauthorized websites.
              </p>
            </div>
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">Domain Verification Process</h3>
        <p className="mb-4">
          Before you can use a secure widget, you must verify ownership of your domain using one of these methods:
        </p>

        <div className="space-y-6 mb-8">
          <div className="border rounded-lg p-6">
            <h4 className="font-semibold mb-3 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-blue-600" />
              Method 1: HTML File Upload
            </h4>
            <p className="mb-3">Upload a verification file to your website's root directory.</p>
            <div className="bg-gray-100 rounded p-3 text-sm font-mono mb-3">
              https://yourdomain.com/reviewit-verification-[CODE].html
            </div>
            <p className="text-sm text-gray-600">
              We'll provide the exact filename and content when you start verification.
            </p>
          </div>

          <div className="border rounded-lg p-6">
            <h4 className="font-semibold mb-3 flex items-center">
              <Globe className="w-5 h-5 mr-2 text-blue-600" />
              Method 2: DNS TXT Record
            </h4>
            <p className="mb-3">Add a TXT record to your domain's DNS settings.</p>
            <div className="bg-gray-100 rounded p-3 text-sm font-mono mb-3">
              TXT record: reviewit-verification=[VERIFICATION_CODE]
            </div>
            <p className="text-sm text-gray-600">
              Contact your hosting provider if you need help adding DNS records.
            </p>
          </div>

          <div className="border rounded-lg p-6">
            <h4 className="font-semibold mb-3 flex items-center">
              <Code className="w-5 h-5 mr-2 text-blue-600" />
              Method 3: Meta Tag
            </h4>
            <p className="mb-3">Add a meta tag to your website's homepage head section.</p>
            <div className="bg-gray-100 rounded p-3 text-sm font-mono mb-3">
              {`<meta name="reviewit-verification" content="[CODE]" />`}
            </div>
            <p className="text-sm text-gray-600">
              Add this tag between the &lt;head&gt; and &lt;/head&gt; tags of your homepage.
            </p>
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">Secure Widget Implementation</h3>
        <p className="mb-4">
          After domain verification, you'll receive a unique embed code with JWT authentication token:
        </p>
        <div className="bg-gray-100 rounded-lg p-4 mb-6">
          <code className="text-sm">
{`<div data-reviewit-secure-widget="[WIDGET_ID]" data-token="[DOMAIN_TOKEN]"></div>
<script src="https://reviewit.gy/widgets/secure-embed.js"></script>`}
          </code>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h4 className="font-semibold text-blue-900 mb-3">Token Security Features</h4>
          <div className="space-y-3 text-blue-800 text-sm">
            <div className="flex items-start">
              <Key className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <div>
                <strong>JWT Authentication:</strong> Tokens are cryptographically signed and contain domain-specific claims
              </div>
            </div>
            <div className="flex items-start">
              <Clock className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <div>
                <strong>Time-Limited:</strong> Tokens automatically expire and can be configured for different durations
              </div>
            </div>
            <div className="flex items-start">
              <Target className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <div>
                <strong>Domain-Bound:</strong> Each token is tied to a specific verified domain and cannot be used elsewhere
              </div>
            </div>
            <div className="flex items-start">
              <Zap className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <div>
                <strong>Rate Limited:</strong> Per-domain rate limiting prevents abuse and ensures fair usage
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Widget Management */}
      <section className="mb-12">
        <h2 id="widget-management" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Settings className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Widget Management
        </h2>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">Security Dashboard</h3>
        <p className="mb-4">
          For secure widgets, use the security dashboard to:
        </p>
        <ul className="list-disc list-inside space-y-2 mb-6">
          <li>Manage verified domains with real-time validation</li>
          <li>Generate and revoke JWT-based authentication tokens</li>
          <li>Configure security settings (token expiry, per-domain rate limits)</li>
          <li>Monitor widget usage and security events with audit logging</li>
          <li>View authentication attempts and blocked requests</li>
          <li>Set up request fingerprinting for enhanced security</li>
          <li>Configure Content Security Policy (CSP) headers</li>
          <li>Enable threat detection and automated responses</li>
        </ul>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <h4 className="font-semibold text-green-900 mb-3 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Enterprise Security Features
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-green-800 text-xs sm:text-sm">
            <div>
              <h5 className="font-medium mb-2">Authentication & Authorization</h5>
              <ul className="space-y-1">
                <li>• JWT token-based authentication</li>
                <li>• Domain-bound token validation</li>
                <li>• Configurable token expiration</li>
                <li>• Real-time token revocation</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium mb-2">Advanced Protection</h5>
              <ul className="space-y-1">
                <li>• Request fingerprinting</li>
                <li>• Per-domain rate limiting</li>
                <li>• CORS policy enforcement</li>
                <li>• X-Frame-Options headers</li>
              </ul>
            </div>
          </div>
        </div>

        <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-900">Widget Customization</h3>
        <p className="mb-4 text-sm sm:text-base">
          Customize your widgets to match your brand:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6">
          <div>
            <h4 className="font-medium mb-2">Styling Options</h4>
            <ul className="list-disc list-inside text-sm space-y-1">
              <li>Theme selection (Light, Dark, Custom)</li>
              <li>Primary color customization</li>
              <li>Border radius adjustment</li>
              <li>Custom CSS integration</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Content Options</h4>
            <ul className="list-disc list-inside text-sm space-y-1">
              <li>Number of reviews to display</li>
              <li>Show/hide rating stars</li>
              <li>Show/hide review text</li>
              <li>Show/hide reviewer names</li>
              <li>Show/hide review dates</li>
              <li>Business logo display</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Analytics and Monitoring */}
      <section className="mb-12">
        <h2 id="analytics" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <BarChart3 className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Analytics and Monitoring
        </h2>

        <p className="mb-6">
          Track your widget performance with comprehensive analytics:
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6">
          <div className="border rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Eye className="w-5 h-5 mr-2 text-blue-600" />
              <h4 className="font-medium">View Metrics</h4>
            </div>
            <ul className="text-sm space-y-1">
              <li>Total widget views</li>
              <li>Daily view trends</li>
              <li>Unique visitors</li>
              <li>Average view duration</li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Activity className="w-5 h-5 mr-2 text-green-600" />
              <h4 className="font-medium">Engagement</h4>
            </div>
            <ul className="text-sm space-y-1">
              <li>Click-through rates</li>
              <li>Interaction events</li>
              <li>Conversion tracking</li>
              <li>User behavior patterns</li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Shield className="w-5 h-5 mr-2 text-red-600" />
              <h4 className="font-medium">Security Events</h4>
            </div>
            <ul className="text-sm space-y-1">
              <li>Blocked requests with reason codes</li>
              <li>Failed authentications and token validation</li>
              <li>Rate limit violations per domain</li>
              <li>Suspicious activity and threat detection</li>
              <li>Domain verification status changes</li>
              <li>Token generation and revocation events</li>
              <li>CORS policy violations</li>
              <li>Request fingerprint anomalies</li>
            </ul>
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">Performance Optimization</h3>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h4 className="font-semibold text-blue-900 mb-3">Best Practices for Widget Performance</h4>
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start">
              <CheckCircle className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <span>Place widgets below the fold to avoid blocking critical content</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <span>Use appropriate widget sizes for your layout</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <span>Limit the number of reviews displayed for faster loading</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-4 h-4 mr-2 mt-1 text-blue-600" />
              <span>Monitor widget analytics to optimize placement and settings</span>
            </li>
          </ul>
        </div>
      </section>

      {/* Troubleshooting */}
      <section className="mb-12">
        <h2 id="troubleshooting" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <AlertTriangle className="inline-block w-8 h-8 mr-2 text-yellow-600" />
          Troubleshooting
        </h2>

        <div className="space-y-6">
          <div className="border rounded-lg p-6">
            <h3 className="font-semibold mb-3 text-red-700">Widget Not Loading</h3>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium mb-1">For Simple Widgets:</h4>
                <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                  <li>Check that the iframe src URL is correct</li>
                  <li>Verify the widget ID in the URL</li>
                  <li>Ensure the widget is active in your dashboard</li>
                  <li>Check for JavaScript errors in browser console</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-1">For Secure Widgets:</h4>
                <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                  <li>Verify domain is properly verified</li>
                  <li>Check that the authentication token is valid and not expired</li>
                  <li>Ensure the secure-embed.js script is loading</li>
                  <li>Verify domain matches the token's authorized domain</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-6">
            <h3 className="font-semibold mb-3 text-red-700">Domain Verification Issues</h3>
            <ul className="list-disc list-inside text-sm space-y-2">
              <li><strong>HTML File Method:</strong> Ensure file is accessible at the exact URL provided</li>
              <li><strong>DNS Method:</strong> DNS changes can take up to 48 hours to propagate</li>
              <li><strong>Meta Tag Method:</strong> Tag must be in the &lt;head&gt; section of your homepage</li>
              <li><strong>HTTPS/HTTP:</strong> Ensure your verification method matches your site's protocol</li>
            </ul>
          </div>

          <div className="border rounded-lg p-6">
            <h3 className="font-semibold mb-3 text-red-700">Authentication Errors</h3>
            <ul className="list-disc list-inside text-sm space-y-2">
              <li><strong>Token expired:</strong> JWT tokens have configurable expiration times. Generate a new token for your domain</li>
              <li><strong>Domain mismatch:</strong> Tokens are domain-bound. Ensure widget is embedded on the exact verified domain</li>
              <li><strong>Rate limit exceeded:</strong> Per-domain rate limits protect against abuse. Wait for reset or contact support for higher limits</li>
              <li><strong>Widget deactivated:</strong> Check widget status and security settings in your dashboard</li>
              <li><strong>Token revoked:</strong> If you revoked the token, generate a new one and update your embed code</li>
              <li><strong>Invalid signature:</strong> Token may be corrupted or tampered with. Generate a fresh token</li>
              <li><strong>CORS violation:</strong> Ensure your domain is properly configured in CORS settings</li>
              <li><strong>Request fingerprint mismatch:</strong> Advanced security detected unusual request patterns</li>
            </ul>
          </div>

          <div className="border rounded-lg p-6">
            <h3 className="font-semibold mb-3 text-red-700">Performance Issues</h3>
            <ul className="list-disc list-inside text-sm space-y-2">
              <li><strong>Slow loading:</strong> Check if rate limits are being hit or if there are network issues</li>
              <li><strong>Token validation delays:</strong> Real-time domain validation may add slight latency</li>
              <li><strong>Script loading errors:</strong> Ensure secure-embed.js is accessible and not blocked by ad blockers</li>
              <li><strong>CSP violations:</strong> Content Security Policy headers may block widget scripts</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Migration Guide */}
      <section className="mb-12">
        <h2 id="migration" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Zap className="inline-block w-8 h-8 mr-2 text-purple-600" />
          Migrating to Secure Widgets
        </h2>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
          <h3 className="font-semibold text-purple-900 mb-3">Why Upgrade to Secure Widgets?</h3>
          <p className="text-purple-800 mb-4">
            Secure widgets provide enterprise-grade security, prevent unauthorized usage, and offer detailed analytics and monitoring capabilities.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-purple-800 text-sm">
            <div>
              <h4 className="font-medium mb-2">Security Benefits</h4>
              <ul className="space-y-1">
                <li>• Domain verification prevents misuse</li>
                <li>• JWT authentication ensures authenticity</li>
                <li>• Rate limiting protects against abuse</li>
                <li>• Audit logging for compliance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Business Benefits</h4>
              <ul className="space-y-1">
                <li>• Control where your widgets appear</li>
                <li>• Detailed analytics and insights</li>
                <li>• Professional appearance and trust</li>
                <li>• Enterprise-ready compliance</li>
              </ul>
            </div>
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-4 text-gray-900">Migration Steps</h3>
        <div className="space-y-4 mb-6">
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <div className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">1</div>
            <div>
              <h4 className="font-medium mb-1">Verify Your Domain</h4>
              <p className="text-sm text-gray-600">Choose from HTML file upload, DNS TXT record, or meta tag verification methods.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <div className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">2</div>
            <div>
              <h4 className="font-medium mb-1">Create Secure Widget</h4>
              <p className="text-sm text-gray-600">Generate a new secure widget with the same settings as your simple widget.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <div className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">3</div>
            <div>
              <h4 className="font-medium mb-1">Generate Authentication Token</h4>
              <p className="text-sm text-gray-600">Create a domain-specific JWT token with appropriate expiration settings.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <div className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">4</div>
            <div>
              <h4 className="font-medium mb-1">Update Embed Code</h4>
              <p className="text-sm text-gray-600">Replace your iframe code with the new secure widget embed code and script.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <div className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">5</div>
            <div>
              <h4 className="font-medium mb-1">Test and Monitor</h4>
              <p className="text-sm text-gray-600">Verify the widget loads correctly and monitor security events in your dashboard.</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-6 h-6 text-yellow-600 mt-1" />
            <div>
              <h3 className="font-semibold text-yellow-900 mb-2">Migration Considerations</h3>
              <ul className="text-yellow-800 text-sm space-y-1">
                <li>• Simple widgets will continue to work during migration</li>
                <li>• Test secure widgets on staging environments first</li>
                <li>• Update all instances of the widget across your sites</li>
                <li>• Consider token expiration times for maintenance schedules</li>
                <li>• Monitor analytics to ensure no traffic loss during migration</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Security Best Practices */}
      <section className="mb-12">
        <h2 id="security-best-practices" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <Lock className="inline-block w-8 h-8 mr-2 text-green-600" />
          Security Best Practices
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="font-semibold text-green-900 mb-3 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Recommended Practices
            </h3>
            <ul className="space-y-2 text-green-800 text-sm">
              <li>• Use secure widgets for production websites</li>
              <li>• Implement JWT token rotation on a regular schedule</li>
              <li>• Configure appropriate token expiration times</li>
              <li>• Monitor widget analytics and security events</li>
              <li>• Keep domain verification methods up to date</li>
              <li>• Use HTTPS for all widget implementations</li>
              <li>• Set per-domain rate limits based on expected traffic</li>
              <li>• Enable request fingerprinting for enhanced security</li>
              <li>• Configure Content Security Policy (CSP) headers</li>
              <li>• Regularly review audit logs for compliance</li>
            </ul>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 className="font-semibold text-red-900 mb-3 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Security Warnings
            </h3>
            <ul className="space-y-2 text-red-800 text-sm">
              <li>• Never share JWT authentication tokens publicly</li>
              <li>• Don't use simple widgets for sensitive or branded content</li>
              <li>• Avoid embedding widgets on untrusted or compromised domains</li>
              <li>• Don't disable security features in production environments</li>
              <li>• Never commit tokens to version control systems</li>
              <li>• Don't use expired or revoked tokens</li>
              <li>• Monitor for unauthorized widget usage and domain spoofing</li>
              <li>• Report suspicious activity and security incidents immediately</li>
              <li>• Don't bypass domain verification for convenience</li>
              <li>• Avoid using overly long token expiration times</li>
            </ul>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="font-semibold text-blue-900 mb-3">Enterprise Security Checklist</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800 text-sm">
            <div>
              <h4 className="font-medium mb-2">Authentication & Authorization</h4>
              <ul className="space-y-1">
                <li>☐ Domain verification completed and verified</li>
                <li>☐ JWT tokens configured with appropriate expiration</li>
                <li>☐ Token rotation schedule established</li>
                <li>☐ Rate limiting configured per domain</li>
                <li>☐ CORS policies properly configured</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Monitoring & Compliance</h4>
              <ul className="space-y-1">
                <li>☐ Security event monitoring enabled</li>
                <li>☐ Audit logging configured for compliance</li>
                <li>☐ Threat detection alerts set up</li>
                <li>☐ Performance monitoring in place</li>
                <li>☐ Regular security reviews scheduled</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Support and Resources */}
      <section className="mb-12">
        <h2 id="support" className="text-xl sm:text-2xl lg:text-3xl font-bold mt-6 sm:mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">
          <ExternalLink className="inline-block w-8 h-8 mr-2 text-blue-600" />
          Support and Resources
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <div className="border rounded-lg p-4 sm:p-6 text-center">
            <FileText className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-3 text-blue-600" />
            <h3 className="font-semibold mb-2 text-sm sm:text-base">Documentation</h3>
            <p className="text-xs sm:text-sm text-gray-600 mb-3">
              Comprehensive guides and API documentation
            </p>
            <button className="text-blue-600 text-xs sm:text-sm hover:underline">
              View Docs →
            </button>
          </div>

          <div className="border rounded-lg p-4 sm:p-6 text-center">
            <Activity className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-3 text-green-600" />
            <h3 className="font-semibold mb-2 text-sm sm:text-base">Live Support</h3>
            <p className="text-xs sm:text-sm text-gray-600 mb-3">
              Get help from our technical support team
            </p>
            <button className="text-green-600 text-xs sm:text-sm hover:underline">
              Contact Support →
            </button>
          </div>

          <div className="border rounded-lg p-4 sm:p-6 text-center">
            <Code className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-3 text-purple-600" />
            <h3 className="font-semibold mb-2 text-sm sm:text-base">Developer Tools</h3>
            <p className="text-xs sm:text-sm text-gray-600 mb-3">
              API access and advanced integration options
            </p>
            <button className="text-purple-600 text-xs sm:text-sm hover:underline">
              Developer Portal →
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
