"use client";

import React from "react";
import {
  Building2,
  BarChart3,
  Package,
  Star,
  Settings,
  CreditCard,
  Users,
  TrendingUp,
  Shield,
  Bell,
  FileText,
  DollarSign,
  Target,
  Calendar,
  MessageSquare,
  Award,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  Zap,
} from "lucide-react";

interface BusinessRouteCardProps {
  title: string;
  path: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  accessLevel: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
}

function BusinessRouteCard({
  title,
  path,
  description,
  icon,
  features,
  accessLevel,
  difficulty,
}: BusinessRouteCardProps) {
  const difficultyColors = {
    Beginner: "bg-green-100 text-green-800 border-green-200",
    Intermediate: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Advanced: "bg-red-100 text-red-800 border-red-200",
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <span className="text-sm text-gray-500 font-mono">{path}</span>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <span
            className={`inline-block px-2 py-1 text-xs rounded border ${difficultyColors[difficulty]}`}
          >
            {difficulty}
          </span>
          <span className="inline-block px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded border border-purple-200">
            {accessLevel}
          </span>
        </div>
      </div>

      <p className="text-gray-600 mb-4">{description}</p>

      <div className="space-y-3">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Key Features:
          </h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center gap-2"
              >
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

interface FeatureSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  routes: BusinessRouteCardProps[];
}

function FeatureSection({
  title,
  description,
  icon,
  routes,
}: FeatureSectionProps) {
  return (
    <section className="mb-12">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {routes.map((route, index) => (
          <BusinessRouteCard key={index} {...route} />
        ))}
      </div>
    </section>
  );
}

export default function BusinessGuideContent() {
  const dashboardRoutes: BusinessRouteCardProps[] = [
    {
      title: "Business Dashboard",
      path: "/owner-admin",
      description:
        "Central hub for business owners to view their products, review counts, ratings, and manage their businesses.",
      icon: <Building2 className="h-5 w-5" />,
      features: [
        "View all your businesses",
        "Product statistics and summaries",
        "Total reviews and average ratings",
        "Recent products overview",
        "Quick access to management tools",
        "Business subscription status",
        "Product view counts",
        "Direct links to key sections",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
    {
      title: "Business Analytics",
      path: "/owner-admin/analytics",
      description:
        "Analytics dashboard showing business performance metrics, traffic sources, and product insights.",
      icon: <BarChart3 className="h-5 w-5" />,
      features: [
        "Business performance overview",
        "Traffic sources and trends",
        "Product performance metrics",
        "Device type analytics",
        "Top performing products",
        "Time-based analytics",
        "Custom date range selection",
        "Data export capabilities",
      ],
      accessLevel: "Business Owner",
      difficulty: "Intermediate",
    },
  ];

  const productRoutes: BusinessRouteCardProps[] = [
    {
      title: "Product Management",
      path: "/owner-admin/products",
      description:
        "View and manage your business products with search, filtering, and basic product information.",
      icon: <Package className="h-5 w-5" />,
      features: [
        "View all products across businesses",
        "Search and filter products",
        "Business-specific product filtering",
        "Product performance overview",
        "View ratings and review counts",
        "Links to individual product pages",
        "Grid and table view options",
        "Basic product statistics",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
    {
      title: "Review Management",
      path: "/owner-admin/reviews",
      description:
        "Monitor and manage customer reviews for your products and business listings.",
      icon: <Star className="h-5 w-5" />,
      features: [
        "View product reviews",
        "Review performance tracking",
        "Rating analytics",
        "Review response capabilities",
        "Filter by business or product",
        "Review moderation tools",
        "Customer feedback overview",
        "Review trend monitoring",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
  ];

  const marketingRoutes: BusinessRouteCardProps[] = [
    {
      title: "Promotions & Campaigns",
      path: "/owner-admin/promotions",
      description:
        "Manage promotional activities and marketing campaigns for your business listings.",
      icon: <Target className="h-5 w-5" />,
      features: [
        "View promotional opportunities",
        "Campaign management interface",
        "Promotional content organization",
        "Marketing material uploads",
        "Campaign scheduling tools",
        "Performance monitoring",
        "Promotional analytics",
        "Marketing resource access",
      ],
      accessLevel: "Business Owner",
      difficulty: "Intermediate",
    },
    {
      title: "Subscription Management",
      path: "/owner-admin/subscription",
      description:
        "View and manage your business subscription plans, billing information, and account status.",
      icon: <CreditCard className="h-5 w-5" />,
      features: [
        "Current subscription overview",
        "Subscription tier comparison",
        "Billing information display",
        "Usage statistics monitoring",
        "Plan upgrade/downgrade options",
        "Payment history tracking",
        "Subscription renewal management",
        "Account status monitoring",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
  ];

  const configurationRoutes: BusinessRouteCardProps[] = [
    {
      title: "Business Settings",
      path: "/owner-admin/settings",
      description:
        "Manage your business account settings, preferences, and configuration options.",
      icon: <Settings className="h-5 w-5" />,
      features: [
        "Account preferences",
        "Business information updates",
        "Notification settings",
        "Privacy configuration",
        "Profile management",
        "System preferences",
        "Security settings",
        "Account management tools",
      ],
      accessLevel: "Business Owner",
      difficulty: "Beginner",
    },
  ];

  const workflowSteps = [
    {
      step: 1,
      title: "Business Registration",
      description: "Add your business to the platform",
      icon: <Building2 className="h-5 w-5" />,
      actions: [
        "Submit business information",
        "Complete business registration",
        "Set up business profile",
        "Access owner dashboard",
      ],
    },
    {
      step: 2,
      title: "Product Management",
      description: "Monitor and manage your products",
      icon: <Package className="h-5 w-5" />,
      actions: [
        "View existing products",
        "Monitor product performance",
        "Track ratings and reviews",
        "Update product information",
      ],
    },
    {
      step: 3,
      title: "Analytics & Monitoring",
      description: "Track business performance",
      icon: <BarChart3 className="h-5 w-5" />,
      actions: [
        "View business analytics",
        "Monitor traffic sources",
        "Track product performance",
        "Analyze customer behavior",
      ],
    },
    {
      step: 4,
      title: "Review Management",
      description: "Engage with customer feedback",
      icon: <TrendingUp className="h-5 w-5" />,
      actions: [
        "Monitor customer reviews",
        "Respond to feedback",
        "Track review trends",
        "Improve customer satisfaction",
      ],
    },
  ];

  return (
    <div>
      {/* Quick Navigation */}
      <div className="mb-12 bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Navigation
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <a
            href="#dashboard-overview"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Building2 className="h-4 w-4" />
            Dashboard Overview
          </a>
          <a
            href="#product-management"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Package className="h-4 w-4" />
            Product Management
          </a>
          <a
            href="#marketing-tools"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Target className="h-4 w-4" />
            Marketing Tools
          </a>
          <a
            href="#business-settings"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline"
          >
            <Settings className="h-4 w-4" />
            Business Settings
          </a>
        </div>
      </div>

      {/* Business Owner Workflow */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Business Owner Workflow
        </h2>
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {workflowSteps.map((step, index) => (
              <div key={index} className="relative">
                <div className="bg-white rounded-lg p-4 shadow-sm border border-white">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
                      {step.step}
                    </div>
                    <div className="p-1 bg-blue-100 rounded text-blue-600">
                      {step.icon}
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    {step.description}
                  </p>
                  <ul className="space-y-1">
                    {step.actions.map((action, actionIndex) => (
                      <li
                        key={actionIndex}
                        className="text-xs text-gray-500 flex items-center gap-1"
                      >
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
                {index < workflowSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <ArrowRight className="h-5 w-5 text-blue-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Dashboard Overview Section */}
      <div id="dashboard-overview">
        <FeatureSection
          title="Dashboard & Analytics"
          description="Central management hub and performance analytics for business owners."
          icon={<Building2 className="h-6 w-6" />}
          routes={dashboardRoutes}
        />
      </div>

      {/* Product Management Section */}
      <div id="product-management">
        <FeatureSection
          title="Product & Review Management"
          description="Comprehensive tools for managing products, inventory, and customer reviews."
          icon={<Package className="h-6 w-6" />}
          routes={productRoutes}
        />
      </div>

      {/* Marketing Tools Section */}
      <div id="marketing-tools">
        <FeatureSection
          title="Marketing & Growth Tools"
          description="Promotional campaigns, subscription management, and business growth features."
          icon={<Target className="h-6 w-6" />}
          routes={marketingRoutes}
        />
      </div>

      {/* Business Settings Section */}
      <div id="business-settings">
        <FeatureSection
          title="Configuration & Settings"
          description="Business profile, operational settings, and platform configuration."
          icon={<Settings className="h-6 w-6" />}
          routes={configurationRoutes}
        />
      </div>

      {/* Getting Started Guide */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Getting Started as a Business Owner
        </h2>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                📋 Setup Checklist
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Register Your Business
                    </strong>
                    <p className="text-sm text-blue-700">
                      Submit your business information through the platform
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Access Owner Dashboard
                    </strong>
                    <p className="text-sm text-blue-700">
                      View your business overview and product statistics
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">Monitor Products</strong>
                    <p className="text-sm text-blue-700">
                      Track your product performance and customer reviews
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
                  <div>
                    <strong className="text-blue-900">
                      Configure Preferences
                    </strong>
                    <p className="text-sm text-blue-700">
                      Set up your account preferences and settings
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                🚀 Quick Start Actions
              </h3>
              <div className="space-y-2">
                <a
                  href="/owner-admin"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Visit Dashboard
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    Access your business dashboard
                  </p>
                </a>
                <a
                  href="/owner-admin/products"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      Manage Products
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    View and monitor your products
                  </p>
                </a>
                <a
                  href="/owner-admin/analytics"
                  className="block p-3 bg-white rounded border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">
                      View Analytics
                    </span>
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-sm text-blue-700">
                    View your business analytics
                  </p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Best Practices for Business Owners
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
              <Award className="h-5 w-5" />
              Success Strategies
            </h3>
            <ul className="space-y-2 text-sm text-green-800">
              <li>
                • <strong>Regular Monitoring:</strong> Check your dashboard
                regularly for performance updates
              </li>
              <li>
                • <strong>Active Engagement:</strong> Respond to customer
                reviews and feedback promptly
              </li>
              <li>
                • <strong>Analytics Review:</strong> Use analytics to understand
                your business performance
              </li>
              <li>
                • <strong>Stay Informed:</strong> Keep track of your product
                ratings and review trends
              </li>
              <li>
                • <strong>Customer Focus:</strong> Maintain good customer
                relationships through quality service
              </li>
            </ul>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Common Pitfalls to Avoid
            </h3>
            <ul className="space-y-2 text-sm text-orange-800">
              <li>
                • <strong>Ignoring Reviews:</strong> Not monitoring or
                responding to customer feedback
              </li>
              <li>
                • <strong>Neglecting Analytics:</strong> Not using available
                data to understand performance
              </li>
              <li>
                • <strong>Infrequent Monitoring:</strong> Not regularly checking
                your business dashboard
              </li>
              <li>
                • <strong>Poor Response Time:</strong> Delayed responses to
                customer inquiries and reviews
              </li>
              <li>
                • <strong>Ignoring Trends:</strong> Missing patterns in customer
                behavior and preferences
              </li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}
