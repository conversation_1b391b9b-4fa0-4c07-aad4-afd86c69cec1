"use client";

import { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

// Back to Docs Button Component
interface BackToDocsButtonProps {
  href?: string;
  text?: string;
}

export function BackToDocsButton({
  href = "/admin/docs",
  text = "← Back to Documentation Home",
}: BackToDocsButtonProps) {
  return (
    <div className="mt-6 text-center">
      <a
        href={href}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        {text}
      </a>
    </div>
  );
}

// Status Badge Component
interface StatusBadgeProps {
  status: "complete" | "in-progress" | "placeholder";
  size?: "sm" | "md" | "lg";
}

export function StatusBadge({ status, size = "md" }: StatusBadgeProps) {
  const statusColors = {
    complete: "bg-green-100 text-green-800 border-green-200",
    "in-progress": "bg-yellow-100 text-yellow-800 border-yellow-200",
    placeholder: "bg-gray-100 text-gray-600 border-gray-200",
  };

  const statusLabels = {
    complete: "Complete",
    "in-progress": "In Progress",
    placeholder: "Coming Soon",
  };

  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1 text-sm",
    lg: "px-4 py-2 text-base",
  };

  return (
    <span
      className={`inline-block rounded border font-medium ${statusColors[status]} ${sizeClasses[size]}`}
    >
      {statusLabels[status]}
    </span>
  );
}

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

export function CollapsibleSection({
  title,
  children,
  defaultOpen = false,
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="border border-gray-200 rounded-lg mb-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-lg"
      >
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {isOpen ? (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronRight className="h-5 w-5 text-gray-500" />
        )}
      </button>
      {isOpen && (
        <div className="px-4 py-3 border-t border-gray-200">{children}</div>
      )}
    </div>
  );
}

// Documentation Header Component
interface DocsHeaderProps {
  title: string;
  description: string;
  status?: "complete" | "in-progress" | "placeholder";
  breadcrumbs?: Array<{ label: string; href: string }>;
}

export function DocsHeader({
  title,
  description,
  status,
  breadcrumbs,
}: DocsHeaderProps) {
  return (
    <div className="mb-6">
      {breadcrumbs && (
        <div className="mb-4 text-sm text-gray-500">
          {breadcrumbs.map((crumb, index) => (
            <span key={index}>
              <a href={crumb.href} className="text-blue-600 hover:underline">
                {crumb.label}
              </a>
              {index < breadcrumbs.length - 1 && (
                <span className="mx-2">/</span>
              )}
            </span>
          ))}
        </div>
      )}

      <div className="flex items-center gap-3 mb-2">
        <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
        {status && <StatusBadge status={status} />}
      </div>

      <p className="text-lg text-gray-600">{description}</p>
    </div>
  );
}

// Info Box Component
interface InfoBoxProps {
  type: "info" | "warning" | "success" | "error";
  title?: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
}

export function InfoBox({ type, title, children, icon }: InfoBoxProps) {
  const typeStyles = {
    info: "bg-blue-50 border-blue-200 text-blue-800",
    warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
    success: "bg-green-50 border-green-200 text-green-800",
    error: "bg-red-50 border-red-200 text-red-800",
  };

  const titleStyles = {
    info: "text-blue-900",
    warning: "text-yellow-900",
    success: "text-green-900",
    error: "text-red-900",
  };

  return (
    <div className={`border rounded-lg p-4 mb-6 ${typeStyles[type]}`}>
      <div className="flex items-start gap-3">
        {icon && <div className="flex-shrink-0 mt-0.5">{icon}</div>}
        <div className="flex-1">
          {title && (
            <h3 className={`font-semibold mb-2 ${titleStyles[type]}`}>
              {title}
            </h3>
          )}
          <div className="text-sm">{children}</div>
        </div>
      </div>
    </div>
  );
}

// Code Block Component
interface CodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
}

export function CodeBlock({
  code,
  language = "typescript",
  filename,
}: CodeBlockProps) {
  return (
    <div className="mb-4">
      {filename && (
        <div className="bg-gray-800 text-gray-300 px-4 py-2 text-sm font-mono rounded-t-lg border-b border-gray-700">
          {filename}
        </div>
      )}
      <pre
        className={`bg-gray-900 text-gray-100 p-4 overflow-x-auto text-sm ${filename ? "rounded-b-lg" : "rounded-lg"}`}
      >
        <code className={`language-${language}`}>{code}</code>
      </pre>
    </div>
  );
}

// Documentation Card Component
interface DocsCardProps {
  title: string;
  description: string;
  href: string;
  status: "complete" | "in-progress" | "placeholder";
  icon?: React.ReactNode;
  features?: string[];
}

export function DocsCard({
  title,
  description,
  href,
  status,
  icon,
  features,
}: DocsCardProps) {
  return (
    <div className="docs-card group">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          {icon && (
            <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
              {icon}
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <StatusBadge status={status} size="sm" />
          </div>
        </div>
      </div>

      <p className="text-gray-600 mb-4">{description}</p>

      {features && features.length > 0 && (
        <div className="space-y-2 mb-4">
          <h4 className="text-sm font-medium text-gray-900">Key Topics:</h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center gap-2"
              >
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      <a
        href={href}
        className={`inline-flex items-center gap-2 text-sm font-medium transition-colors ${
          status === "placeholder"
            ? "text-gray-400 cursor-not-allowed"
            : "text-blue-600 hover:text-blue-700"
        }`}
      >
        {status === "placeholder" ? "Coming Soon" : "Learn More"}
        {status !== "placeholder" && (
          <span className="group-hover:translate-x-1 transition-transform">
            →
          </span>
        )}
      </a>
    </div>
  );
}

// Table of Contents Component
interface TOCItem {
  id: string;
  title: string;
  level: number;
}

interface TableOfContentsProps {
  items: TOCItem[];
}

export function TableOfContents({ items }: TableOfContentsProps) {
  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
      <h3 className="font-semibold text-gray-900 mb-3">Table of Contents</h3>
      <ul className="space-y-1">
        {items.map((item) => (
          <li key={item.id} className={`${item.level > 1 ? "ml-4" : ""}`}>
            <a
              href={`#${item.id}`}
              className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
            >
              {item.title}
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}

// Documentation Container Component
interface DocsContainerProps {
  children: React.ReactNode;
}

export function DocsContainer({ children }: DocsContainerProps) {
  return (
    <div className="docs-container">
      <div className="docs-content">
        <div className="prose max-w-none">{children}</div>
      </div>
    </div>
  );
}
