import { iProduct } from "@/app/util/Interfaces";

export async function updateProduct(product: iProduct): Promise<iProduct> {
    if (!product.id) {
        throw new Error("Product ID is missing");
    }

    const response = await fetch(`/api/update/product/${product.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(product),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update product");
    }

    return response.json();
} 