import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { iUser, iProduct, iReview } from '@/app/util/Interfaces';

/**
 * MIDDLEWARE RELATIONSHIP:
 * 
 * This specialized middleware is NOT automatically run by Next.js.
 * Unlike the main middleware.ts, this is designed to be imported and used
 * within specific API routes that need request body validation.
 * 
 * While the main middleware.ts handles routing and authentication at the request level,
 * this middleware focuses on validating the structure and content of request bodies
 * for specific API endpoints.
 * 
 * This is complementary to the main middleware - they work at different levels:
 * - Main middleware: Request-level auth and routing
 * - Validation middleware: Request body data validation
 */

// Type guards
function isUserUpdate(data: any): data is Partial<iUser> {
    return (
        data &&
        (!data.role || ['USER', 'ADMIN'].includes(data.role)) &&
        (!data.status || ['ACTIVE', 'SUSPENDED', 'BANNED'].includes(data.status)) &&
        (!data.suspendedUntil || data.suspendedUntil instanceof Date) &&
        (!data.suspendedReason || typeof data.suspendedReason === 'string')
    );
}

function isProductUpdate(data: any): data is Partial<iProduct> {
    return (
        data &&
        (!data.name || (typeof data.name === 'string' && data.name.length > 0 && data.name.length <= 255)) &&
        (!data.description || (typeof data.description === 'string' && data.description.length > 0)) &&
        (!data.rating || (typeof data.rating === 'number' && data.rating >= 1 && data.rating <= 5)) &&
        (!data.isDeleted || typeof data.isDeleted === 'boolean') &&
        (!data.featuredPosition || typeof data.featuredPosition === 'number')
    );
}

function isReviewModeration(data: any): data is { action: 'APPROVED' | 'REJECTED' | 'FLAGGED'; reason?: string } {
    return (
        data &&
        ['APPROVED', 'REJECTED', 'FLAGGED'].includes(data.action) &&
        (!data.reason || typeof data.reason === 'string')
    );
}

/**
 * Core validation function that validates request body against a type guard
 */
export function validateRequest<T>(validator: (data: any) => data is T) {
    return async (request: NextRequest) => {
        try {
            const body = await request.json();
            if (!validator(body)) {
                return NextResponse.json(
                    { error: 'Validation failed', details: 'Invalid data format' },
                    { status: 400 }
                );
            }
            return body;
        } catch (error) {
            return NextResponse.json(
                { error: 'Invalid request body' },
                { status: 400 }
            );
        }
    };
}

/**
 * Higher-order function that wraps API routes with validation
 * This is used to validate request bodies for specific API endpoints
 */
export function withValidation<T>(validator: (data: any) => data is T) {
    return (handler: Function) => {
        return async (request: NextRequest) => {
            try {
                // Validate request body
                const validatedData = await validateRequest(validator)(request);

                // Add validated data to request for handler to use
                const validatedRequest = {
                    ...request,
                    validatedData,
                };

                // Proceed with the handler
                return handler(validatedRequest);
            } catch (error) {
                return NextResponse.json(
                    { error: 'Invalid request body' },
                    { status: 400 }
                );
            }
        };
    };
}

// Export validation functions
export const validateUserUpdate = withValidation(isUserUpdate);
export const validateProductUpdate = withValidation(isProductUpdate);
export const validateReviewModeration = withValidation(isReviewModeration); 
