// Type declarations for missing scheduler/tracing module
declare module 'scheduler/tracing' {
  export interface Interaction {
    __count: number;
    id: number;
    name: string;
    timestamp: number;
  }

  export interface InteractionsRef {
    current: Set<Interaction>;
  }

  export interface Subscriber {
    onInteractionScheduledWorkCompleted: (interaction: Interaction) => void;
    onInteractionTraced: (interaction: Interaction) => void;
    onWorkCanceled: (interactions: Set<Interaction>, threadID: number) => void;
    onWorkScheduled: (interactions: Set<Interaction>, threadID: number) => void;
    onWorkStarted: (interactions: Set<Interaction>, threadID: number) => void;
    onWorkStopped: (interactions: Set<Interaction>, threadID: number) => void;
  }

  export const __interactionsRef: InteractionsRef;
  export const __subscriberRef: { current: Subscriber | null };
  export function unstable_clear<T>(callback: () => T): T;
  export function unstable_getCurrent(): Set<Interaction> | null;
  export function unstable_getThreadID(): number;
  export function unstable_subscribe(subscriber: Subscriber): void;
  export function unstable_trace<T>(name: string, timestamp: number, callback: () => T): T;
  export function unstable_unsubscribe(subscriber: Subscriber): void;
  export function unstable_wrap<T extends (...args: any[]) => any>(callback: T): T;
}