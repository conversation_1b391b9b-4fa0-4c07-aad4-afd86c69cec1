// Type declarations for Google Maps MarkerClusterer issues
declare module '@googlemaps/markerclusterer' {
  export interface SuperClusterViewportOptions {
    radius?: number;
    [key: string]: any;
  }

  export interface MarkerClustererOptions {
    algorithm?: any;
    map?: google.maps.Map;
    markers?: google.maps.Marker[];
    renderer?: any;
    onClusterClick?: (event: google.maps.MapMouseEvent, cluster: any, map: google.maps.Map) => void;
  }

  export class MarkerClusterer {
    constructor(options: MarkerClustererOptions);
    addMarker(marker: google.maps.Marker, noDraw?: boolean): void;
    addMarkers(markers: google.maps.Marker[], noDraw?: boolean): void;
    clearMarkers(noDraw?: boolean): void;
    getMarkers(): google.maps.Marker[];
    removeMarker(marker: google.maps.Marker, noDraw?: boolean): boolean;
    removeMarkers(markers: google.maps.Marker[], noDraw?: boolean): boolean;
    render(): void;
    setMap(map: google.maps.Map | null): void;
  }
}