<!DOCTYPE html>
<html>
<head>
    <title>SSE Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { 
            background: #f5f5f5; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
            font-family: monospace;
        }
        .error { color: #dc3545; }
        .success { color: #198754; }
        .info { color: #0d6efd; }
    </style>
</head>
<body>
    <h1>SSE Connection Test</h1>
    <div>
        <button onclick="runTests()">Run Tests</button>
        <button onclick="stopTests()">Stop Tests</button>
    </div>
    <div id="logs"></div>

    <script>
        let eventSource = null;

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toISOString()} - ${message}`;
            document.getElementById('logs').prepend(div);
        }

        async function runTests() {
            // Test 1: Basic fetch
            try {
                log('Running Test 1: Basic fetch request...');
                const response = await fetch('https://notifications.reviewit.gy/notifications?user_id=test123');
                log(`Basic API response status: ${response.status}`, 'success');
                
                // Log response headers
                const headers = {};
                response.headers.forEach((value, key) => headers[key] = value);
                log(`Response headers: ${JSON.stringify(headers, null, 2)}`, 'info');
            } catch (e) {
                log(`Basic API failed: ${e.message}`, 'error');
            }

            // Test 2: EventSource
            try {
                log('Running Test 2: EventSource connection...');
                if (eventSource) {
                    eventSource.close();
                }
                
                eventSource = new EventSource('https://notifications.reviewit.gy/notifications/stream?user_id=test123');
                
                eventSource.onopen = () => {
                    log('EventSource connection opened', 'success');
                };

                eventSource.onmessage = (e) => {
                    log(`Received message: ${e.data}`, 'info');
                };

                eventSource.onerror = (e) => {
                    log(`EventSource error - ReadyState: ${eventSource.readyState}`, 'error');
                    if (eventSource.readyState === EventSource.CLOSED) {
                        log('Connection closed', 'error');
                    } else if (eventSource.readyState === EventSource.CONNECTING) {
                        log('Attempting to reconnect...', 'info');
                    }
                };

            } catch (e) {
                log(`EventSource setup failed: ${e.message}`, 'error');
            }
        }

        function stopTests() {
            if (eventSource) {
                log('Closing EventSource connection...', 'info');
                eventSource.close();
                eventSource = null;
            }
        }
    </script>
</body>
</html>
