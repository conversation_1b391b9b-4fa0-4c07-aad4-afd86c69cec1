(function() {
  'use strict';
  
  // Secure widget embed configuration
  const WIDGET_BASE_URL = window.location.protocol + '//' + window.location.host;
  const SECURE_API_BASE = WIDGET_BASE_URL + '/api/public/widgets/secure/';
  
  // Secure widget class
  class SecureReviewItWidget {
    constructor(config) {
      this.config = {
        widgetId: config.widgetId,
        token: config.token,
        container: config.container || 'reviewit-widget',
        width: config.width || '100%',
        height: config.height || 'auto',
        maxHeight: config.maxHeight || '600px',
        loading: config.loading !== false, // Default to true
        ...config
      };
      
      this.iframe = null;
      this.container = null;
      this.domain = window.location.hostname;
      this.init();
    }
    
    init() {
      // Find container element
      if (typeof this.config.container === 'string') {
        this.container = document.getElementById(this.config.container);
      } else if (this.config.container instanceof HTMLElement) {
        this.container = this.config.container;
      }
      
      if (!this.container) {
        console.error('SecureReviewItWidget: Container not found');
        return;
      }
      
      if (!this.config.widgetId) {
        console.error('SecureReviewItWidget: Widget ID is required');
        this.showError('Widget ID is required');
        return;
      }
      
      if (!this.config.token) {
        console.error('SecureReviewItWidget: Token is required');
        this.showError('Authentication token is required');
        return;
      }
      
      this.authenticateAndLoad();
    }
    
    async authenticateAndLoad() {
      // Show loading state
      if (this.config.loading) {
        this.showLoading();
      }
      
      try {
        // Authenticate with server
        const response = await fetch(SECURE_API_BASE + this.config.widgetId, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
            'X-Domain': this.domain,
            'X-Referrer': document.referrer || window.location.href,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || 'Authentication failed');
        }
        
        // Authentication successful, create secure iframe
        this.createSecureWidget(data.data);
        
      } catch (error) {
        console.error('SecureReviewItWidget authentication failed:', error);
        this.showError(`Authentication failed: ${error.message}`);
      }
    }
    
    createSecureWidget(widgetData) {
      // Create secure iframe with token
      this.iframe = document.createElement('iframe');
      
      // Build secure iframe URL with token
      const iframeUrl = new URL(WIDGET_BASE_URL + '/widgets/secure-iframe/' + this.config.widgetId);
      iframeUrl.searchParams.set('token', this.config.token);
      iframeUrl.searchParams.set('domain', this.domain);
      
      this.iframe.src = iframeUrl.toString();
      this.iframe.style.width = this.config.width;
      this.iframe.style.height = this.config.height === 'auto' ? '200px' : this.config.height;
      this.iframe.style.maxHeight = this.config.maxHeight;
      this.iframe.style.border = 'none';
      this.iframe.style.overflow = 'hidden';
      this.iframe.style.display = 'block';
      this.iframe.setAttribute('scrolling', 'no');
      this.iframe.setAttribute('frameborder', '0');
      this.iframe.setAttribute('allowtransparency', 'true');
      
      // Enhanced security attributes for secure widgets
      this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox');
      this.iframe.setAttribute('referrerpolicy', 'strict-origin-when-cross-origin');
      
      // Handle iframe load
      this.iframe.onload = () => {
        this.hideLoading();
        this.trackEvent('secure-widget-loaded');
        
        // Add security indicator
        this.addSecurityIndicator();
      };
      
      this.iframe.onerror = () => {
        this.showError('Failed to load secure widget');
      };
      
      // Listen for resize messages from iframe
      window.addEventListener('message', (event) => {
        // Verify message origin for security
        if (event.origin !== WIDGET_BASE_URL) return;
        
        if (event.data.type === 'secure-widget-resize' && 
            event.data.widgetId === this.config.widgetId &&
            event.data.token === this.config.token) {
          this.resizeWidget(event.data.height);
        }
      });
      
      // Replace loading with iframe
      if (this.config.loading) {
        setTimeout(() => {
          this.container.innerHTML = '';
          this.container.appendChild(this.iframe);
        }, 100);
      } else {
        this.container.appendChild(this.iframe);
      }
    }
    
    addSecurityIndicator() {
      // Add a small security indicator to show this is a verified widget
      const indicator = document.createElement('div');
      indicator.style.cssText = `
        position: absolute;
        top: 5px;
        right: 5px;
        background: #10b981;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        z-index: 1000;
        pointer-events: none;
      `;
      indicator.textContent = '🔒 Verified';
      indicator.title = 'This widget is domain-verified and secure';
      
      // Make container relative if it's not already
      const containerStyle = window.getComputedStyle(this.container);
      if (containerStyle.position === 'static') {
        this.container.style.position = 'relative';
      }
      
      this.container.appendChild(indicator);
    }
    
    showLoading() {
      this.container.innerHTML = `
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100px;
          padding: 20px;
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          color: #6b7280;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
          "></div>
          <div>
            <div>🔒 Authenticating secure widget...</div>
            <div style="font-size: 12px; margin-top: 4px; opacity: 0.7;">
              Verifying domain permissions
            </div>
          </div>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      `;
    }
    
    hideLoading() {
      // Loading is replaced by iframe, nothing to do
    }
    
    showError(message) {
      this.container.innerHTML = `
        <div style="
          padding: 20px;
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          color: #dc2626;
          text-align: center;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
            <span style="font-size: 20px; margin-right: 8px;">🔒</span>
            <strong>Secure Widget Error</strong>
          </div>
          <div style="margin-bottom: 12px;">${message}</div>
          <div style="font-size: 12px; color: #991b1b;">
            Contact the website owner to resolve this issue.
          </div>
          <details style="margin-top: 12px; text-align: left;">
            <summary style="cursor: pointer; font-size: 12px;">Technical Details</summary>
            <div style="margin-top: 8px; font-size: 11px; background: #fee; padding: 8px; border-radius: 4px;">
              <strong>Domain:</strong> ${this.domain}<br>
              <strong>Widget ID:</strong> ${this.config.widgetId}<br>
              <strong>Token:</strong> ${this.config.token ? '***' + this.config.token.slice(-4) : 'Not provided'}<br>
              <strong>Time:</strong> ${new Date().toISOString()}
            </div>
          </details>
        </div>
      `;
    }
    
    resizeWidget(height) {
      if (this.iframe && this.config.height === 'auto') {
        this.iframe.style.height = Math.min(height, parseInt(this.config.maxHeight)) + 'px';
      }
    }
    
    trackEvent(event) {
      try {
        // Track secure widget events with enhanced security
        fetch(WIDGET_BASE_URL + '/api/public/widgets/' + this.config.widgetId + '/track', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.token}`,
            'X-Domain': this.domain
          },
          body: JSON.stringify({
            event: event,
            referrer: document.referrer || window.location.href,
            userAgent: navigator.userAgent,
            domain: this.domain,
            securityLevel: 'SECURE',
            timestamp: new Date().toISOString()
          })
        }).catch(err => {
          // Silently fail tracking
          console.warn('Secure widget tracking failed:', err);
        });
      } catch (e) {
        // Silently fail
      }
    }
    
    destroy() {
      if (this.iframe && this.iframe.parentNode) {
        this.iframe.parentNode.removeChild(this.iframe);
      }
      this.iframe = null;
      this.container = null;
    }
    
    // Method to refresh token if needed
    async refreshToken() {
      // This would be called if token expires and needs refresh
      // Implementation depends on your token refresh strategy
      console.warn('Token refresh not implemented yet');
    }
  }
  
  // Auto-initialize secure widgets with data attributes
  function autoInitializeSecureWidgets() {
    const widgets = document.querySelectorAll('[data-reviewit-secure-widget]');
    widgets.forEach(element => {
      const widgetId = element.getAttribute('data-reviewit-secure-widget');
      const token = element.getAttribute('data-token');
      const width = element.getAttribute('data-width') || '100%';
      const height = element.getAttribute('data-height') || 'auto';
      const maxHeight = element.getAttribute('data-max-height') || '600px';
      
      if (widgetId && token && !element.hasAttribute('data-reviewit-secure-initialized')) {
        new SecureReviewItWidget({
          widgetId: widgetId,
          token: token,
          container: element,
          width: width,
          height: height,
          maxHeight: maxHeight
        });
        element.setAttribute('data-reviewit-secure-initialized', 'true');
      } else if (widgetId && !token) {
        // Show error for missing token
        element.innerHTML = `
          <div style="
            padding: 20px;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            color: #dc2626;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          ">
            <strong>🔒 Secure Widget Configuration Error</strong><br>
            <small>Missing authentication token. Please contact the website owner.</small>
          </div>
        `;
      }
    });
  }
  
  // Expose global API
  window.SecureReviewItWidget = SecureReviewItWidget;
  window.SecureReviewItWidget.autoInitialize = autoInitializeSecureWidgets;
  
  // Auto-initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInitializeSecureWidgets);
  } else {
    autoInitializeSecureWidgets();
  }
  
  // Re-initialize when new elements are added
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      let shouldReinit = false;
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              if (node.hasAttribute && node.hasAttribute('data-reviewit-secure-widget')) {
                shouldReinit = true;
              } else if (node.querySelector) {
                const widgets = node.querySelectorAll('[data-reviewit-secure-widget]');
                if (widgets.length > 0) {
                  shouldReinit = true;
                }
              }
            }
          });
        }
      });
      
      if (shouldReinit) {
        setTimeout(autoInitializeSecureWidgets, 100);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  // Security: Prevent script tampering
  Object.freeze(SecureReviewItWidget);
  Object.freeze(SecureReviewItWidget.prototype);
  
})();